import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import { AfterViewInit, Component, EventEmitter, Input, NgZone, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { filter, finalize, map, pairwise, throttleTime } from 'rxjs/operators';
import { UntypedFormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { CheckboxConfig, MenuConfig, HclAssetPickerService } from 'hcl-angular-widgets-lib';
import { SubscriptionLike } from 'rxjs';
import { OfferDataService } from '../../../offer-data.service';
import { Offer } from '../../select-offers.model';
import { SelectOffersService } from '../../../select-offers.service';

@Component({
  selector: 'hcl-offer-card-list',
  templateUrl: './offer-card-list.component.html',
  styleUrls: ['./offer-card-list.component.scss']
})
export class OfferCardListComponent implements OnChanges, OnInit, AfterViewInit {
  subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  @ViewChild('scrollViewport') scrollViewport: CdkVirtualScrollViewport;


  @Input() offerResponseData: any;
  @Input() isDataLoading: boolean;
  @Input() chunkSize: number;
  @Output() loadNewOffers: EventEmitter<number> = new EventEmitter();
  @Output() offerSelectionUpdate: EventEmitter<any> = new EventEmitter<any>();


  offerId: number;
  loaderItems = [];
  variantMap = new Map<number, any>();

  constructor(
    private zone: NgZone,
    private translate: TranslateService,
    private offerDataService: OfferDataService,
    private selectOffersService: SelectOffersService,
    private hclAssetPickerService: HclAssetPickerService
  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['offerResponseData']?.currentValue) {
      changes['offerResponseData'].currentValue.content.forEach(offer => {
        if (this.isSelectedItem(offer)) {
          offer.isSelected = true;
        }
      });
    }

      if (changes['chunkSize'] && changes['chunkSize'].currentValue) {
        this.setLoaderItems();
      }
    }
  
   
    setLoaderItems() {
      this.loaderItems = [];
      for (let i = 0; i < 4; i++) {
        const dummyItems = '1'.repeat(this.chunkSize).split('');
        this.loaderItems.push(dummyItems);
      }
    }

  ngOnInit(): void {
    this.selectOffersService.selectedOffersData.forEach(offerData => {
      if (offerData.hasOwnProperty('variantId') && offerData.variantId) {
        this.variantMap.set(+offerData.offerId, offerData);
      }
    });
  }

  ngAfterViewInit(): void {
    this.scrollViewport.elementScrolled().pipe(
      map(() => this.scrollViewport.measureScrollOffset('bottom')),
      pairwise(),
      filter(([y1, y2]) => (y2 < y1 && y2 < 100)),
      throttleTime(500)
    ).subscribe(() => {
      this.zone.run(() => {
        this.loadMoreOffers();
      });
    }
    );
  }


  /*
  Check if user scroll has reached to bottom and data available in DB and trigger next page content
*/
  loadMoreOffers(): void {
    if (!this.isDataLoading) {
      if ((this.offerResponseData.page.pageNumber + 1) < this.offerResponseData.page.totalPages) {
        this.loadNewOffers.emit(this.offerResponseData.page.pageNumber + 1);
      }
    }
  }

  isSelectedItem(item) {
    return this.selectOffersService.selectedOffersData.findIndex(offerData => offerData.offerId === item.id) !== -1;
  }

  // set checkbox config of current asset
  public setAssetCheckboxConf(item: any): CheckboxConfig {
    const checkboxConf: CheckboxConfig = {
      type: 'single',
      formControl: new UntypedFormControl(),
      singleCheckboxData: {
        value: item,
        name: 'offerSelection',
        color: 'primary',
        checked: this.isSelectedItem(item),
        disabled: false
      }
    };
    checkboxConf.formControl.setValue(this.isSelectedItem(item));
    return checkboxConf;
  }

  updateOfferThumbnailState(event, offer: Offer, imageElement) {
    if (event === 'error' && !offer.isImageLoaded) {
      // check if thumbnail is asset picker item and all properties has value 
      const { url, applicationId, objectType, objectId } = offer.thumbnailProperties;
      this.subscriptionList.push(this.offerDataService.getAssetPickerRepositories().subscribe((repos) => {
        if (repos) {
          const selectedRepoForPreview = repos.find(repo => repo.identifier === applicationId);
          const isAnonymousContent = selectedRepoForPreview && !selectedRepoForPreview.anonymousContent;
          if (isAnonymousContent && url && applicationId && objectType && objectId) {
            const resourceUrl = this.hclAssetPickerService.baseUrl + '/' + applicationId + '/download?resourceId=' + objectId +
              '&resource=' + url;
            this.offerDataService.downloadAssetPickerAnonymousContent(resourceUrl).then(data => {
              offer.isImageLoaded = true;
              imageElement['src'] = data;
            }, error => {
              offer.brokenThumbnail = true;
              offer.isImageLoaded = true;
            });
          } else {
            offer.brokenThumbnail = true;
            offer.isImageLoaded = true;
          }
        } else {
          this.offerDataService.getAndSetAssetPickerInstances(true);
        }
      }));
    } else {
      offer.isImageLoaded = true;
    }
  }


  getOfferCodeWithDelimiter(offerCodeArray) {
    return offerCodeArray.join(this.offerDataService.offerApplicationConfig.offerCodeDelimiter);
  }

  getStatusClass(offer: Offer) {
    const stateClass = offer.state === 'PUBLISHED' ? 'published' : offer.state === 'RETIRED' ? 'retired' : 'draft';
    return stateClass;
  }

  getStatusCopy(offer: Offer) {
    const stateClass = offer.state === 'PUBLISHED' ? this.translate.instant('TITLES.PUBLISHED') :
      offer.state === 'RETIRED' ? this.translate.instant('LIST_OFFER_TEMPLATES.HEADERS.RETIRED') : this.translate.instant('TITLES.DRAFT');
    return stateClass;
  }

  returnOfferCodes(row) {
    let codeString = '';
    if (row.offerCodes.length > 0) {
      row.offerCodes.forEach((offerCode, index) => {
        if ((row.offerCodes.length - 1) !== index) {
          codeString += (offerCode + this.offerDataService.offerApplicationConfig.offerCodeDelimiter);
        } else {
          codeString += offerCode;
        }
      });
      return codeString;
    }
  }


  offerClicked(offer) {
    if (this.variantMap.has(+offer.id)) {
      this.selectOffersService.selectedVariantId = this.variantMap.get(+offer.id).variantId;
    } else {
      this.selectOffersService.selectedVariantId = null;
    }

    this.selectOffersService.offerName = offer.displayName;
    this.selectOffersService.viewRoute = 'listOffers';
    this.selectOffersService.sendViewOfferClicked(+offer.id);

  }

  offerCheckboxClicked(event, offer) {
    if (event.checked) {
      offer.isSelected = true;
      this.cardSelected({ data: offer });
    } else {
      offer.isSelected = false;
      this.cardUnSelected({ data: offer });
    }
  }

  cardSelected(data: any) {
    if (data.data && !this.selectOffersService.selectedOffersData.some(offer => offer.offerId === data.data.id)) {
      let offerData;

      if (this.variantMap.has(+data.data.id)) {
        offerData = {
          offerId: data.data.id,
          offerDisplayName: data.data.displayName,
          variantId: this.variantMap.get(+data.data.id).variantId,
          variantDisplayName: this.variantMap.get(+data.data.id).variantDisplayName,
          offerCode: this.returnOfferCodes(data.data),
          variantAttributes: this.variantMap.get(+data.data.id).variantAttributes,
          offerAttributes: null,
          state: data.data.state
        };
      } else {
        offerData = {
          offerId: data.data.id,
          offerDisplayName: data.data.displayName,
          variantId: null,
          variantDisplayName: null,
          offerCode: this.returnOfferCodes(data.data),
          variantAttributes: null,
          offerAttributes: this.returnVariantAttributes(data.data),
          state: data.data.state
        };
      }


      if (this.selectOffersService.selectedOffersData.length) {
        let hasOffer = false;
        hasOffer = this.selectOffersService.selectedOffersData.some((selectedData, index) => {
          if (+selectedData.offerId === +data.data.id) {
            return true;
          }
        });
        if (!hasOffer) {
          if (this.selectOffersService.rowSelectMode === 'single') {
            this.selectOffersService.selectedOffersData = [offerData];
          } else {
            this.selectOffersService.selectedOffersData.push(offerData);
          }
        }
      } else {
        this.selectOffersService.selectedOffersData.push(offerData);
      }
      this.offerSelectionUpdate.emit();
    }
  }

  cardUnSelected(data: any) {
    if (data.data) {
      this.selectOffersService.selectedOffersData.some((selectedData, index) => {
        if (+selectedData.offerId === +data.data.id) {
          this.selectOffersService.selectedOffersData.splice(index, 1);
          return;
        }
      });
      this.offerSelectionUpdate.emit();
    }
  }

  returnVariantAttributes(offer) {
    const allAttributes = [...offer.parameterizedAttributes, ...offer.staticAttributes];
    const requiredAttributes = [];

    let counter = 0;

    allAttributes.some((attribute) => {
      if ((attribute.id === 1 || attribute.id === 10) && counter < 2) {
        requiredAttributes.push(attribute);
        counter++;
      } else {
        return;
      }
    });

    return requiredAttributes;
  }

}
