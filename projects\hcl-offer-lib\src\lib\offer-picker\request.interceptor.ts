import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON>, HttpHandler, HttpInterceptor, HttpRequest, HttpHeaders, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs/internal/Observable';
import { tap, filter, catchError } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { throwError } from 'rxjs';
import { NotificationService } from 'hcl-angular-widgets-lib';
import { GridHeader } from 'hcl-data-grid-lib';
import { OfferDataService } from './offer-data.service';
import { SelectOffersService } from './select-offers.service';


@Injectable()

export class RequestInterceptor implements HttpInterceptor, GridHeader {

    constructor(private notificationService: NotificationService,
        private translate: TranslateService ,  private offerDataService : OfferDataService, private selectOffersService: SelectOffersService) { }

    intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>): Observable<HttpEvent<any>> {
        if (this.isOfferRequest(req.url) ) {
            let apContentpreview = false;
            if (req.headers.has('ap-contentpreview') && req.headers.get('ap-contentpreview')) {
                apContentpreview = true;
            }

            let apCognitiveTags = false;
            if (req.headers.has('ap-cognitiveTags') && req.headers.get('ap-cognitiveTags')) {
                apCognitiveTags = true;
            }

            let apStartUpCall = false;
            if (req.headers.has('ap-startup') && req.headers.get('ap-startup')) {
                apStartUpCall = true;
            }

            let ignore401 = false;
            if (req.headers.has('401') && req.headers.get('401')) {
                ignore401 = true;
            }
            if (!req.headers.has('Accept')) {
                req = req.clone(
                    { headers: req.headers.set('Accept', 'application/json') });
            }

            if (!req.headers.has('Access-Control-Allow-Origin')) {
                req = req.clone({ headers: req.headers.set('Access-Control-Allow-Origin', '*') });
            }

            if (!req.headers.has('m_user_name')) {
                req = req.clone({
                    headers: req.headers.set('m_user_name', this.offerDataService.userName  ?
                    this.offerDataService.userName  : '')
                });
            }

            if (this.selectOffersService.pickerConfig.reLogin) {
                req = req.clone({
                    headers: req.headers.set('m_tokenId', this.selectOffersService.pickerConfig.tokenId ?
                    this.selectOffersService.pickerConfig.tokenId  : '')
                });
            } else {
                if (!req.headers.has('m_tokenId')) {
                    req = req.clone({
                        headers: req.headers.set('m_tokenId', this.offerDataService.tokenId ?
                        this.offerDataService.tokenId  : '')
                    });
                }
            }

            if (!req.headers.has('api_auth_mode')) {
                req = req.clone({ headers: req.headers.set('api_auth_mode', 'manager') });
            }

            if (!req.headers.has('xsrf_token')) {
                req = req.clone({ headers: req.headers.set('xsrf_token', window['_xsrf_token'] || '') });
            }

            req = req.clone({
                headers: req.headers.set('Cache-Control', 'no-cache')
                    .set('Pragma', 'no-cache'),
                withCredentials: false
            });



            return next.handle(req).pipe(
                tap(
                    event => { if (event instanceof HttpResponse) { } },
                    err => { if (event instanceof HttpResponse) { } }
                ),
                catchError((err: any, caught) => {
                    let errorShown = false;
                    if (apStartUpCall) {
                        errorShown = true;
                        // on Offer application load intentionally skipped the error to avoid confusion
                    } else if (err.status === 401 && !ignore401) {
                        errorShown = true;
                        // In case the the response is from a login URL we do not need to display the login screen
                        if (err.url && err.url.indexOf('/login') < 0) {
                            // alert('Session time out error');
                        }
                        let url: string = window.parent.location.href;
                        if (url.indexOf('.') > -1) {
                            url = url.substring(0, url.indexOf('unica') + 5);
                        }
                        window.parent.location.replace(url);
                        return;
                    } else if (err.status === 401 && ignore401) {
                        errorShown = true;
                        return throwError({ status: err.status, error: err.error });
                    } else if (err.status === 403) {
                        if ((err.error[403] && err.error[403][0])) {
                            errorShown = true;
                        }
                    } else if (err.status === 400) {
                        // bad request error
                        const error: any = err.error,
                            errArray: { filed: string, messages: string[] }[] = [];

                        for (const x in error) {
                            if (error.hasOwnProperty(x)) {
                                const field: string = x.indexOf('[') > -1 ? x.substring(0, x.lastIndexOf('[')) : x,
                                    errorMessages: string[] = [];

                                error[x].forEach(s => {
                                    errorMessages.push(s);
                                });
                                errArray.push({
                                    filed: field,
                                    messages: errorMessages
                                });
                            }
                        }
                        if (errArray.length) {
                            errorShown = true;
                        }
                        throw {
                            status: err.status,
                            error: {
                                status: err.status,
                                error: errArray
                            }
                        };
                    } else if (err.status === 500) {
                        errorShown = true;
                    } else if (apContentpreview && err.status === 404) {
                        // intentionally skipped the notification as "Preview not available" message is shown at attribute lavel
                        errorShown = true;
                    } else if (apCognitiveTags && err.status === 502) {
                        errorShown = true;

                    } else if (err.status !== 400 && err.status !== 401) {
                        if (err.error && err.error[err.status] && Array.isArray(err.error[err.status])) {
                            if (err.error[err.status] && err.error[err.status][0]) {
                                errorShown = true;
                                err.error[err.status].forEach(element => {
                                });
                            } else {
                                err.error.key = 'UNKNOWN_ERROR';
                            }
                        }
                    }
                    if (!errorShown) {
                    }
                    return throwError(err.error);
                })
            );
        } else {
            return next.handle(req);
        }

    }

    getHeaders(): HttpHeaders {
        let headers: HttpHeaders = new HttpHeaders();

        headers = headers.set('content-type', 'application/json');
        return headers;
    }


    isOfferRequest(requestUrl: string): boolean {
        const offerString = '/api/offer/v1/';
        return requestUrl.indexOf(offerString) > 0 && this.offerDataService.isOfferWidget ? true : false;
    }
}
