import { getLocaleCurrencySymbol, DatePipe } from '@angular/common';
import { Component, EventEmitter, OnDestroy, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin, SubscriptionLike } from 'rxjs';
import * as _ from 'lodash';
import { AssetpickerApplicationConfig, RichtextObjectData } from '../select-offers.model';
import { PopoverTrigger, PopoverComponent, ProgressSpinner, HclAssetPickerService, ButtonConf, SideBarComponent } from 'hcl-angular-widgets-lib';
import { SelectOffersService } from '../../select-offers.service';
import { OfferDataService } from '../../offer-data.service';
import { HttpHeaders } from '@angular/common/http';

@Component({
  selector: 'hcl-view-offer',
  templateUrl: './view-offer.component.html',
  styleUrls: ['./view-offer.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ViewOfferComponent implements OnInit, OnDestroy {

  @ViewChild(PopoverTrigger) popoverTrigger: PopoverTrigger;
  @ViewChild(PopoverComponent) popover: PopoverComponent;
  @Output() templateDatails = new EventEmitter();
  @Output() offerState = new EventEmitter();


  @ViewChild('richTextSidebar') sideBarComponentRef: SideBarComponent;
  /**
 * Output EventEmitter to handle Error flow
 */
  @Output() errorInfo = new EventEmitter();
  cancleBtnConfig: ButtonConf;
  ssadbAttributeDependencies = [];
  allAttributesMap = new Map<number, any>();
  alwaysHiddenAttributesSet = new Set<number>();
  allSsdbAttributesMap = new Map<number, any>();
  accordionCustomConfig: any;
  dynamicConfigs = {};
  staticAttributes: any;
  parameterisedAttributes: any;
  hiddenAttributes = [];
  offerTemplateID: number;
  offerMetadata = {
    displayName: '',
    policyId: '',
    description: '',
    offerCodes: [null, null, null, null, null]
  };
  readyToRender = false;
  offerTemplateId: string;
  subscriptionList: SubscriptionLike[] = [];
  relevantproductRuleList = [];
  selectedProductList = [];
  dynamicCheckboxData = [];
  selectedTabNumber = null;
  offerTemplateAttributes: any;
  relevantProductsCheckboxConfig = {
    checkboxData: this.dynamicCheckboxData,
    type: 'multiple',
  };
  apItemDetails;
  apItemDetailsState = '';
  apOverlayInputs = {
    popoverPositionY: 'top',
    popoverPositionX: 'start',
    originPositionX: 'end',
    originPositionY: 'top',
    popoverEnterDelay: 0,
    popoverLeaveDelay: 0,
    popoverOffsetY: 0,
    popoverOffsetX: 30,
    noPreview: false,
    loadAt: 'right',
  };

  cpInlineSpinner: ProgressSpinner = {
    isInline: true,
    isLoading: false,
    color: 'primary',
    mode: 'indeterminate',
    value: 75,
    diameter: 20,
    strokeWidth: 3
  };
  selectedRichtextAttribute: any;
  rtSidebarPreviewActive = false;
  selectedRepoForPreview = null;
  variantName: string;

  constructor(private translate: TranslateService,
    public selectOffersService: SelectOffersService,
    private datePipe: DatePipe,
    private hclAssetPickerService: HclAssetPickerService,
    private offerDataService: OfferDataService
  ) { }


  ngOnInit() {
    this.selectOffersService.hiddenAttributesSet = new Set<number>();
    const offerId = +this.selectOffersService.offerId;
    if (this.selectOffersService.loadVariationType === 'viewMaster' && !this.selectOffersService.selectedVariantId) {
      this.variantName = this.translate.instant('OFFER_PICKER.TITLES.MASTER');
      if (this.variantName === '') {
        this.variantName = 'Master';
      }
      this.selectOffersService.variantName = null;
      this.loadMaster(offerId);
    } else if (this.selectOffersService.selectedVariantId) {
      this.loadVariant(offerId);
    }

    this.mapAttributes();
  }

  loadMaster(offerId: number) {
    this.subscriptionList.push(this.selectOffersService.getSingleOffer(offerId).subscribe((offerData: any) => {
      this.subscriptionList.push(this.selectOffersService.getTemplate(offerData.templateId).subscribe(templateData => {
        this.errorInfo.emit({ showError: false, error: '' });
        this.selectOffersService.offerTemplate = templateData;
        const staticAttrIds = templateData.staticAttributes.map(attr => attr.id);
        const parametrisedAttrIds = templateData.parameterisedAttributes.map(attr => attr.id);
        const hiddenAttrIds = templateData.hiddenAttributes.map(attr => attr.id);
        const allAttributes = [...templateData.staticAttributes,
        ...templateData.parameterisedAttributes, ...templateData.hiddenAttributes];
        allAttributes.forEach((attribute) => {
          if (attribute.type.id === 10) {
            this.allSsdbAttributesMap.set(attribute.id, attribute);
          }
        });
        templateData.dependentAttributes.forEach((dependency) => {
          this.ssadbAttributeDependencies.push(
            [{
              id: dependency.dependentAttrId,
              displayName: this.allSsdbAttributesMap.get(dependency.dependentAttrId).displayName
            },
            {
              id: dependency.attributeId,
              displayName: this.allSsdbAttributesMap.get(dependency.attributeId).displayName
            }
            ]
          );
        });
        this.subscriptionList.push(forkJoin([this.selectOffersService.getAttributeListById({ ids: staticAttrIds }),
        this.selectOffersService.getAttributeListById({ ids: parametrisedAttrIds }),
        this.selectOffersService.getAttributeListById({ ids: hiddenAttrIds })])
          .subscribe(([staticAttributeData, parametrisedAttributeData, hiddenAttributeData]) => {
            this.templateDatails.emit(templateData);
            // Translate system defined attributes displayName
            if (parametrisedAttributeData && parametrisedAttributeData.length) {
              parametrisedAttributeData.map(attr => {
                this.updateSystemDefinedAttributes(attr);
              });
            }
            if (staticAttributeData && staticAttributeData.length) {
              staticAttributeData.map(attr => {
                this.updateSystemDefinedAttributes(attr);
              });
            }
            if (hiddenAttributeData && hiddenAttributeData.length) {
              hiddenAttributeData.map(attr => {
                this.updateSystemDefinedAttributes(attr);
              });
            }

            this.selectOffersService.staticAttributes = staticAttributeData;
            this.selectOffersService.parameterisedAttributes = parametrisedAttributeData;
            this.selectOffersService.hiddenAttributes = hiddenAttributeData;
            this.selectOffersService.setOfferData(offerData);
            this.setUpForm();
          }));
      }));
    }, (error) => {
      let errorMsg = '';
      for (const x in error) {
        if (error[x].length) {
          error[x].forEach(e => errorMsg += e);
        }
      }
      this.errorInfo.emit({ showError: true, error: errorMsg });
    }));
  }

  loadVariant(offerId: number) {
    this.subscriptionList.push(this.selectOffersService.getOfferVariantById(offerId, this.selectOffersService.selectedVariantId).subscribe((offerData: any) => {
      this.subscriptionList.push(this.selectOffersService.getTemplate(offerData.templateId).subscribe(templateData => {

        this.variantName = offerData.variantDisplayName;
        this.selectOffersService.variantName = offerData.variantDisplayName;

        this.errorInfo.emit({ showError: false, error: '' });
        this.selectOffersService.offerTemplate = templateData;
        const staticAttrIds = templateData.staticAttributes.map(attr => attr.id);
        const parametrisedAttrIds = templateData.parameterisedAttributes.map(attr => attr.id);
        const hiddenAttrIds = templateData.hiddenAttributes.map(attr => attr.id);
        const allAttributes = [...templateData.staticAttributes,
        ...templateData.parameterisedAttributes, ...templateData.hiddenAttributes];
        allAttributes.forEach((attribute) => {
          if (attribute.type.id === 10) {
            this.allSsdbAttributesMap.set(attribute.id, attribute);
          }
        });
        templateData.dependentAttributes.forEach((dependency) => {
          this.ssadbAttributeDependencies.push(
            [{
              id: dependency.dependentAttrId,
              displayName: this.allSsdbAttributesMap.get(dependency.dependentAttrId).displayName
            },
            {
              id: dependency.attributeId,
              displayName: this.allSsdbAttributesMap.get(dependency.attributeId).displayName
            }
            ]
          );
        });
        this.subscriptionList.push(forkJoin([this.selectOffersService.getAttributeListById({ ids: staticAttrIds }),
        this.selectOffersService.getAttributeListById({ ids: parametrisedAttrIds }),
        this.selectOffersService.getAttributeListById({ ids: hiddenAttrIds })])
          .subscribe(([staticAttributeData, parametrisedAttributeData, hiddenAttributeData]) => {
            this.templateDatails.emit(templateData);
            // Translate system defined attributes displayName
            if (parametrisedAttributeData && parametrisedAttributeData.length) {
              parametrisedAttributeData.map(attr => {
                this.updateSystemDefinedAttributes(attr);
              });
            }
            if (staticAttributeData && staticAttributeData.length) {
              staticAttributeData.map(attr => {
                this.updateSystemDefinedAttributes(attr);
              });
            }
            if (hiddenAttributeData && hiddenAttributeData.length) {
              hiddenAttributeData.map(attr => {
                this.updateSystemDefinedAttributes(attr);
              });
            }

            this.selectOffersService.staticAttributes = staticAttributeData;

            this.selectOffersService.variantParameterisedAttributes = parametrisedAttributeData;
            this.selectOffersService.variantHiddenAttributes = hiddenAttributeData;

            this.selectOffersService.setOfferData(offerData);

            this.selectOffersService.serVariantData(offerData);

            this.setUpForm();
          }));
      }));
    }, (error) => {
      let errorMsg = '';
      for (const x in error) {
        if (error[x].length) {
          error[x].forEach(e => errorMsg += e);
        }
      }
      this.errorInfo.emit({ showError: true, error: errorMsg });
    }));
  }

  updateSystemDefinedAttributes(attr) {
    if (this.isEffectiveExpirationDates(attr.type.id)) {
      attr.type.attributes[0].displayName = this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.EFFECTIVE_DATE.DISPLAY_NAME');
      attr.type.attributes[1].displayName = this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.EXPIRATION_DATE.DISPLAY_NAME');
    } else if (attr.isSystemDefined) {
      attr.displayName = this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.' + attr.id + '.DISPLAY_NAME');
    }
  }

  setUpForm() {
    if (!this.readyToRender) {
      this.staticAttributes = this.selectOffersService.staticAttributes;
      if (!this.selectOffersService.selectedVariantId) {
        this.parameterisedAttributes = this.selectOffersService.parameterisedAttributes;
        this.hiddenAttributes = this.selectOffersService.hiddenAttributes;
      } else {
        this.parameterisedAttributes = this.selectOffersService.variantParameterisedAttributes;
        this.hiddenAttributes = this.selectOffersService.variantHiddenAttributes;
      }
      this.offerTemplateID = this.selectOffersService.offerTemplate.id;
      this.subscriptionList.push(this.selectOffersService.getOfferMetadata().subscribe(data => {
        if (data) {
           this.offerMetadata = { ...this.offerMetadata, ...data }; 
          this.selectOffersService.offerName =  this.offerMetadata.displayName;
      }
      }));
      this.subscriptionList.push(this.selectOffersService.getProductConditions().subscribe(data => {
        this.relevantproductRuleList = data;
        if (data && data.length) {
          this.dynamicCheckboxData = data.map((rule, i) => {
            return {
              label: rule.productCondition,
              value: i,
              name: i,
              checked: false,
              disabled: true
            };
          });
          this.relevantProductsCheckboxConfig.checkboxData = this.dynamicCheckboxData;
          this.relevantProductsCheckboxConfig = _.cloneDeep(this.relevantProductsCheckboxConfig);
        } else {
          this.relevantProductsCheckboxConfig.checkboxData = [];
          this.relevantProductsCheckboxConfig = _.cloneDeep(this.relevantProductsCheckboxConfig);
        }
      }));

      this.offerTemplateAttributes = [...this.staticAttributes, ...this.parameterisedAttributes, ...this.hiddenAttributes];
      this.offerTemplateAttributes.forEach(offerAttribute => {
        if (this.isEffectiveExpirationDates(offerAttribute.type.id)) {
          offerAttribute.type.attributes.forEach((dateAttr, index) => {
            if (index === 0) {
              if (offerAttribute.value && offerAttribute.value.effectiveDate) {
                dateAttr.value = offerAttribute.value.effectiveDate;
              }
            } else if (index === 1) {
              if (offerAttribute.value && offerAttribute.value.expirationDate) {
                dateAttr.value = offerAttribute.value.expirationDate;
              }
            } else if (index === 2) {
              if (offerAttribute.value && offerAttribute.value.expirationDuration) {
                dateAttr.value = offerAttribute.value.expirationDuration;
              }
            }
          })
        } else if (this.isUrlField(offerAttribute.type.id) || this.isCreativeUrlAttribute(offerAttribute.id)) {
          this.dynamicConfigs[offerAttribute.id + 'button'] = {
            name: offerAttribute.id + 'button',
            value: this.translate.instant('BUTTONS.BROWSE'),
            color: 'accent',
            buttonType: 'stroked',
            type: 'button',
            styleClass: 'medium-btn',
            borderRadius: 5
          };
          this.dynamicConfigs[offerAttribute.id + 'loader'] = { ...this.cpInlineSpinner };
        }
      })
      this.setConfiguration();
      this.mapAttributes();
      this.hideUnhideAttributesOnLoad();
      this.readyToRender = true;
    }
  }

  setConfiguration() {
    this.accordionCustomConfig = {
      displayMode: 'default',
      hideToggle: false,
      multi: true,
      step: 0,
      panelActions: false,
      items: [
        {
          label: '',
          headerTemplateName: 'headerTemplate_3',
          descr: '',
          descrTemplateName: 'descriptionTemplate_3',
          content: '',
          contentTemplateName: 'contentTemplate_3',
          disabled: false
        }]
    };

    this.cancleBtnConfig = {
      name: 'cancle',
      value: this.translate.instant('BUTTONS.CLOSE'),
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };
  }

  isDescMultiline(descValueEl) {
    return descValueEl.offsetHeight >= 32 ? true : false;
  }

  isEllipsisApplicable(descValueEl) {
    return descValueEl.offsetHeight >= 33 ? true : false;
  }

  getSecurityPolicy() {
    return this.offerDataService.getSecurityPolicies().find(policy => policy.value === this.selectOffersService.getOfferData().policyId).label;
  }

  getOfferCodes() {
    return this.selectOffersService.getOfferData().offerCodes.join(this.offerDataService.offerApplicationConfig.offerCodeDelimiter);
  }

  getDescription() {
    return this.selectOffersService.getOfferData().description;
  }

  getDateFormat() {
    return this.offerDataService.getDatePipeFormatLocale();
  }

  getFlowChartRunDateValue(effectiveDateAttribute: any) {
    return effectiveDateAttribute.value ? this.translate.instant('FIELDS.FALSE') : this.translate.instant('FIELDS.TRUE');
  }

  getRichTextOptions() {
    const options = this.richTextOptions();
    options.language = this.selectOffersService.getLanguangeForTextEditor();
    options.placeholderText = this.translate.instant('MESSAGES.THE_RICH_TEXT_EDITOR');
    return options;
  }

  richTextOptions(): RichtextObjectData {
    return {
      key: 'BWC6D-16D3E3F3H3D1A6A4wc2DBKSPJ1WKTUCQOd1OURPE1KDc1C-7J2A4D4B4C6D2A1F4G1C1==',
      charCounterCount: false,
      placeholderText: '',
      charCounterMax: -1,
      attribution: false,
      language: '',
      heightMin: 150,
      heightMax: 300,
      fileUpload: false,
      imagePaste: false,
      htmlRemoveTags: ['script', 'style', 'button', 'select'],
      toolbarButtons: {
        'moreText': {
          'buttons': ['bold', 'italic', 'underline', 'strikeThrough',
            'fontSize', 'textColor', 'backgroundColor'], 'buttonsVisible': 7
        },
        'moreParagraph': {
          'buttons': ['paragraphFormat', 'formatOL', 'formatUL']
        },
        'moreRich': { 'buttons': ['insertLink', 'insertTable'] },
        'moreMisc': { 'buttons': ['undo', 'redo'], 'align': 'right', 'buttonsVisible': 4 }
      },
      colorsBackground: [
        '#001423', '#0066b3', '#69b4e1', '#b9e6fa', '#4e0817', '#eb1946',
        '#f58ca2', '#f8d8e0', '#f5821e', '#fab914', '#fcdc89',
        '#fac08e', '#fdead9', '#5a2d91', '#ac96c8', '#e3dcec', '#00afbe',
        '#7fd7de', '#d4f1f4', '#bed732', '#deeb98', '#f4f8dc', 'REMOVE'],
      colorsStep: 8,
      colorsText: ['#001423', '#0066b3', '#69b4e1', '#b9e6fa', '#4e0817',
        '#eb1946', '#f58ca2', '#f8d8e0', '#f5821e', '#fab914', '#fcdc89',
        '#fac08e', '#fdead9', '#5a2d91', '#ac96c8', '#e3dcec', '#00afbe',
        '#7fd7de', '#d4f1f4', '#bed732', '#deeb98', '#f4f8dc', 'REMOVE'],
      tableColors: ['#001423', '#0066b3', '#69b4e1', '#b9e6fa', '#4e0817',
        '#eb1946', '#f58ca2', '#f8d8e0', '#f5821e', '#fab914', '#fcdc89',
        '#fac08e', '#fdead9', '#5a2d91', '#ac96c8', '#e3dcec', '#00afbe',
        '#7fd7de', '#d4f1f4', '#bed732', '#deeb98', '#f4f8dc', 'REMOVE'],
      tableColorsStep: 8,
      linkInsertButtons: ['linkBack'],
      linkEditButtons: ['linkEdit', 'linkRemove'],
      linkAlwaysBlank: true,
      quickInsertButtons: ['table', 'ul', 'ol'],
      wordAllowedStyleProps: [
        'font-size', 'background', 'color', 'width', 'text-align', 'vertical-align', 'background-color', 'padding',
        'margin', 'height', 'margin-top', 'margin-left', 'margin-right', 'margin-bottom', 'text-decoration',
        'font-weight', 'font-style', 'text-indent', 'border', 'border-.*'],
      pasteDeniedTags: ['a', 'input', 'button', 'select'],
      editorClass: 'hcl-custom-text-editor',
      toolbarSticky: false
    };
  }

  getBooleanAttrValue(attribute) {
    return attribute.value ? this.translate.instant('FIELDS.SELECTED') : this.translate.instant('FIELDS.NOT_SELECTED');
  }

  getEffectiveDateValue(attribute) {
    return attribute.type.attributes[0].value ?
      this.datePipe.transform(attribute.type.attributes[0].value, this.offerDataService.getDatePipeFormatLocale())
      : this.translate.instant('CREATE_OFFER_TEMPLATE.LABELS.FLOWCHART_RUN_DATE');
  }

  getExpirationDateValue(attribute) {

    return attribute.type.attributes[1].value ?
      this.datePipe.transform(attribute.type.attributes[1].value, this.offerDataService.getDatePipeFormatLocale())
      : this.translate.instant('CREATE_OFFER.LABELS.DAYS_AFTER_EFFECTIVE_DATE_LABEL', { days: attribute.type.attributes[2].value });
  }

  isComposedField(attribute) {
    return attribute.type.dataType === 'Object';
  }

  getAttributeValue(attribute) {
    return attribute.value
  }

  mapAttributes() {
    if (this.selectOffersService.offerTemplate && this.selectOffersService.offerTemplate.dependentAttributes &&
      this.selectOffersService.offerTemplate.dependentAttributes.length > 0) {
      this.accordionCustomConfig.items.push(
        {
          label: '',
          headerTemplateName: 'headerTemplate_4',
          descr: '',
          descrTemplateName: '',
          content: '',
          contentTemplateName: 'contentTemplate_4',
          disabled: false
        }
      );
    }

    if (this.hiddenAttributes && this.hiddenAttributes.length) {
      if (!this.accordionItemAvailable(5)) {
        this.accordionCustomConfig.items.unshift(
          {
            label: '',
            headerTemplateName: 'headerTemplate_5',
            descr: '',
            descrTemplateName: '',
            content: '',
            contentTemplateName: 'contentTemplate_5',
            disabled: false
          }
        );
      }
    }

    if (this.parameterisedAttributes && this.parameterisedAttributes.length) {
      if (!this.accordionItemAvailable(2)) {
        this.accordionCustomConfig.items.unshift(
          {
            label: '',
            headerTemplateName: 'headerTemplate_2',
            descr: '',
            descrTemplateName: '',
            content: '',
            contentTemplateName: 'contentTemplate_2',
            disabled: false
          }
        );
      }
    }

    if (this.staticAttributes && this.staticAttributes.length) {
      if (!this.accordionItemAvailable(1)) {
        this.accordionCustomConfig.items.unshift(
          {
            label: '',
            headerTemplateName: 'headerTemplate_1',
            descr: '',
            descrTemplateName: '',
            content: '',
            contentTemplateName: 'contentTemplate_1',
            disabled: false
          }
        );
      }
    }
  }

  accordionItemAvailable(itemNumber: number) {
    return this.accordionCustomConfig.items.some(accordionItem => +accordionItem.headerTemplateName.split('_')[1] === +itemNumber);
  }

  isAssetpickerInstalled() {
    return this.offerDataService.platformConfig.isAssetPickerAppInstalled;
  }

  isUrlField(id) {
    return id === 8;
  }

  isEffectiveExpirationDates(id) {
    return id === 200;
  }

  isRichTextField(id): boolean {
    return id === 11;
  }

  isDateField(id): boolean {
    return id === 3;
  }

  isSSDBField(id): boolean {
    return id === 10;
  }

  isBooleanField(id): boolean {
    return id === 5;
  }

  isCurrencyField(id) {
    return id === 4;
  }

  getCurrencySymbol() {
    return getLocaleCurrencySymbol(this.offerDataService.getCurrencyLocale() || getLocaleCurrencySymbol('en_US'));
  }

  isNumberField(id): boolean {
    return id === 2 ||
      id === 6 ||
      id === 4;
  }

  isSelectBoxField(id): boolean {
    return id === 100;
  }

  isStringField(id): boolean {
    return id === 1;
  }

  isCreativeUrlAttribute(id) {
    return id === 15;
  }

  isValidUrl(url: string) {
    return url.substring(0, 4) === 'http';
  }

  ngOnDestroy() {
    this.readyToRender = false;
    this.subscriptionList.forEach(sub => { sub.unsubscribe(); });
  }

  getImageUrl() {
    return this.apItemDetails.presentationDetails.multimedia.resourceUrl;
  }

  getThumbnailSource() {
    return this.apItemDetails.presentationDetails.multimedia.thumbnailUrl;
  }

  getThumbnailBackupSource() {
    return this.offerDataService.offerApplicationConfig.offerServerURL + '/om/assets/images/thumbnail-backup_1.png';
  }

  getImageBackupSource() {
    return this.offerDataService.offerApplicationConfig.offerServerURL + '/om/assets/images/image-backup_1.png';
  }

  getImageSource() {
    return this.getImageUrl();
  }

  getIcon(mimeType) {
    return this.hclAssetPickerService.documents.get(mimeType) || 'hcl-icon-unknown-type-2';
  }

  isImage(mimeType) {
    return this.hclAssetPickerService.imageMimeTypes.indexOf(mimeType) !== -1;
  }

  getContentPreviewTitle() {
    return this.apItemDetails.presentationDetails.textual.heading || this.apItemDetails.presentationDetails.textual.name;
  }

  closeApItemDetails() {
    this.popoverTrigger.closePopover();
  }

  apItemDetailsClosed() {
    this.apItemDetails = '';
    this.apItemDetailsState = '';
    this.apOverlayInputs.popoverOffsetY = 0;
    this.apOverlayInputs.popoverOffsetX = 30;
    this.apOverlayInputs.popoverPositionY = 'top';
    this.apOverlayInputs.popoverPositionX = 'start';
    this.apOverlayInputs.originPositionY = 'top';
    this.apOverlayInputs.originPositionX = 'end';
    this.apOverlayInputs.loadAt = '';
    this.apOverlayInputs.noPreview = false;
    this.selectedRepoForPreview = null;
  }

  setOverlayPositions(el, noPreview) {
    const elPositions = el.getBoundingClientRect();
    const winWidth = window.innerWidth;

    if (elPositions.left < (winWidth / 2)) {
      this.apOverlayInputs.popoverOffsetX = 40;
      this.apOverlayInputs.popoverOffsetY = 0;
      this.apOverlayInputs.popoverPositionX = 'start';
      this.apOverlayInputs.originPositionX = 'end';
      this.apOverlayInputs.loadAt = 'right';
    } else {
      this.apOverlayInputs.popoverOffsetX = -10;
      this.apOverlayInputs.popoverOffsetY = 0;
      this.apOverlayInputs.popoverPositionX = 'end';
      this.apOverlayInputs.originPositionX = 'start';
      this.apOverlayInputs.loadAt = 'left';
    }

    if (noPreview) {
      this.apOverlayInputs.popoverPositionY = 'bottom';
      this.apOverlayInputs.originPositionY = 'top';
      this.apOverlayInputs.popoverOffsetX = elPositions.left < (winWidth / 2) ? 5 : 30;
      this.apOverlayInputs.popoverOffsetY = 0;
    }
  }

  resourceNeedsToBeLoaded() {
    return this.selectedRepoForPreview && !this.selectedRepoForPreview.anonymousContent;
  }

  openApItemDetail(event, attribute) {
    const currentTargetElement = event.currentTarget;
    if (attribute.value.applicationId && attribute.value.objectId && attribute.value.url) {
      this.dynamicConfigs[attribute.id + 'loader'].isLoading = true;
      this.subscriptionList.push(this.offerDataService.getAssetPickerRepositories().subscribe((repos: any) => {
        if (repos) {
          this.selectedRepoForPreview = repos.find(repo => repo.identifier === attribute.value.applicationId);
          if (this.selectedRepoForPreview) {
            this.subscriptionList.push(this.offerDataService.getAssetpickerApplicationConfig(true)
              .subscribe((config: AssetpickerApplicationConfig) => {
                let headers: HttpHeaders = this.getAPHeaders(config);
                headers = headers.set('ap-contentPreview', 'true').set('ignoreLoader', 'true');
                this.hclAssetPickerService.headers = headers;
                this.hclAssetPickerService.baseUrl = config.url;
                this.subscriptionList.push(this.hclAssetPickerService.getItemDetails(attribute.value).subscribe(data => {
                  this.apItemDetails = data;
                  this.apItemDetails.url = this.getImageSource();
                  this.apItemDetails.applicationId = attribute.value.applicationId;
                  this.setOverlayPositions(currentTargetElement, false);
                  if (this.resourceNeedsToBeLoaded()) {
                    this.apItemDetails.presentationDetails.multimedia.resourceUrl = this.hclAssetPickerService.baseUrl + '/'
                      + this.apItemDetails.applicationId + '/download?resourceId=' + this.apItemDetails.presentationDetails.multimedia.id +
                      '&resource=' + this.apItemDetails.url;

                    this.hclAssetPickerService.downloadContentFromResourceId(this.apItemDetails.presentationDetails.multimedia.resourceUrl).subscribe((resp) => {
                      const reader = new FileReader();
                      reader.readAsDataURL(resp);
                      reader.onload = () => {
                        this.apItemDetails.contentUrl = reader.result;
                        this.apItemDetails.isImageLoaded = true;

                        this.apItemDetailsState = 'loaded';
                        setTimeout(() => {
                          this.dynamicConfigs[attribute.id + 'loader'].isLoading = false;
                          currentTargetElement.previousElementSibling.click();
                        }, 100);
                      };
                    });
                  } else {
                    this.apItemDetailsState = 'loaded';
                    setTimeout(() => {
                      this.dynamicConfigs[attribute.id + 'loader'].isLoading = false;
                      currentTargetElement.previousElementSibling.click();
                    }, 100);
                  }
                }, (error) => {
                  this.dynamicConfigs[attribute.id + 'loader'].isLoading = false;
                  this.triggerApErrorMessage(currentTargetElement, 'no-preview');
                }));
              }));
          } else {
            setTimeout(() => {
              this.dynamicConfigs[attribute.id + 'loader'].isLoading = false;
              this.triggerApErrorMessage(currentTargetElement, 'no-repo');
            }, 100);
          }
        } else {
          this.subscriptionList.push(this.offerDataService.getAssetpickerApplicationConfig(true)
            .subscribe((config: AssetpickerApplicationConfig) => {
              let headers: HttpHeaders = this.getAPHeaders(config);
              headers = headers.set('ap-instances', 'true').set('ignoreLoader', 'true');
              this.hclAssetPickerService.headers = headers;
              this.hclAssetPickerService.baseUrl = config.url;
              this.hclAssetPickerService.getInstances().subscribe(repos => {
                this.offerDataService.setAssetPickerRepositories(repos);
                this.hclAssetPickerService.repositories = repos;
              })
            }));
        }
      }));
    } else {
      this.triggerApErrorMessage(currentTargetElement, 'no-content');
    }
  }

  triggerApErrorMessage(currEl, state: string) {
    this.setOverlayPositions(currEl, true);
    this.apItemDetailsState = state;
    this.apOverlayInputs.noPreview = true;
    setTimeout(() => {
      currEl.previousElementSibling.click();
      setTimeout(() => {
        this.closeApItemDetails();
      }, 4000);
    }, 100);
  }

  hideUnhideAttributesOnLoad() {
    const allAttributes = [...this.parameterisedAttributes,
    ...this.staticAttributes, ...this.hiddenAttributes];
    allAttributes.forEach((attribute) => {
      this.allAttributesMap.set(+attribute.id, attribute);
    });
    if (this.selectOffersService.offerTemplate && this.selectOffersService.offerTemplate.rules) {

      this.selectOffersService.offerTemplate.rules.some(rule => {
        if (rule.hasOwnProperty('expression')) {
          rule.action.identifiers.forEach(id => {
            this.alwaysHiddenAttributesSet.add(+id);
          });
          return;
        }
      });

      this.selectOffersService.offerTemplate.rules.forEach(rule => {
        if (rule.hasOwnProperty('nestedCondition')) {
          if (!this.checkRuleConditions(rule.nestedCondition.conditions)) {
            this.hideAttributes(rule.action.identifiers);
          }
        }
      });
    }
  }

  checkRuleConditions(conditionsData) {
    let alwaysHiddenAttributeInCondition = false;

    alwaysHiddenAttributeInCondition = conditionsData.some(condition => {
      if (this.alwaysHiddenAttributesSet.has(+condition.param)) {
        return true;
      }
    });

    if (alwaysHiddenAttributeInCondition) {
      return false;
    }

    let anyConditionIsTrue = false;

    anyConditionIsTrue = conditionsData.some(condition => {
      if (condition.op === 'in') {
        let flag = false;
        flag = condition.value.some((item) => {
          if (item === this.returnValueAccordingToType(this.allAttributesMap.get(+condition.param))) {
            return true;
          }
        });
        if (flag) {
          return true;
        }
      } else {
        let flag = false;
        flag = condition.value.some((item) => {
          if (item === this.returnValueAccordingToType(this.allAttributesMap.get(+condition.param))) {
            return true;
          }
        });
        if (!flag) {
          return true;
        }
      }
    });
    return anyConditionIsTrue;
  }

  returnValueAccordingToType(attribute) {
    if (attribute && attribute.hasOwnProperty('value')) {
      if (attribute.type.id === 100 || attribute.type.id === 5) {
        return attribute.value;
      } else if (attribute.type.id === 10 && attribute.value && attribute.value.idColumnValue) {
        return attribute.value.idColumnValue;
      }
    }
    return null;
  }

  hideAttributes(attributesData) {
    attributesData.forEach(id => {
      this.selectOffersService.hiddenAttributesSet.add(+id);
      const attribute = this.allAttributesMap.get(+id);
      if (attribute && attribute.value) {
        attribute.value = null;
      }
    });
  }

  getAPHeaders(config) {
    const headers: HttpHeaders = new HttpHeaders()
      .set('m_user_name', this.offerDataService.userConfig.displayName)
      .set('m_tokenId', config.token)
      .set('api_auth_mode', 'manager')
      .set('401', 'ignore')
      .set('client_app_id', this.offerDataService.offerApplicationConfig.offerApplicationId.toString());
    return headers;
  }


  openRichTextView(attribute) {
    this.selectedRichtextAttribute = attribute;
    this.sideBarComponentRef.openSideBar();
    this.rtSidebarPreviewActive = true;
  }

  closeSidebar() {
    this.sideBarComponentRef.close('close');
    this.selectedRichtextAttribute = null;
    this.rtSidebarPreviewActive = false;
  }
}

