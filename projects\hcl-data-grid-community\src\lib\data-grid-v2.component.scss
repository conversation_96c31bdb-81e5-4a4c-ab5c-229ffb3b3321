.hcl-grid-container {
    height: calc(100% - 50px);
    background-color: #f5f5f5;
    padding: 0 10px;
    .agGridContainer {
      height: 100%;
      width: 100%;
      background-color: #f5f5f5;
    }
  
    // .ag-theme-material .ag-cell {
    //   margin-left: 10px;
    // }
  
    // .ag-theme-material .ag-header-cell,
    .ag-theme-material {
      .ag-header-cell {
        background-color: #f5f5f5 !important;
        border-bottom: 2px solid #f5f5f5;
        .ag-header-cell-resize {
          border-right: 1px solid #e0e0e0;
          height: 39px;
          right: 2px;
          top: 10px;
        }
        .ag-cell-label-container {
          .ag-header-cell-label {
            justify-content: center;
          }
          .ag-header-cell-text {
            font-family: Montserrat-bold, Montserrat, sans-serif;
            font-weight: 600;
            font-size: 16px;
            align-items: center;
            color: #6d7692;
            letter-spacing: 0.2px;
            justify-content: center;
            text-align: center;
            float: right;
          }
        }
        &:hover {
          background-color: #fde6d2 !important;
          border-bottom: 2px solid #f5821e !important;
        }
      }
    }
  
    .ag-header {
      background-color: #f5f5f5 !important;
      border-bottom: 2px solid #e2e2e2;
  
      .ag-header-row {
        //padding-top: 10px;
  
        .ag-header-cell {
          // border-right: 1px solid #e0e0e0;
          height: 100%;
          //margin-left: 10px;
  
          &[col-id="dragAndDrop"],
          &[col-id="checkbox"],
          &[col-id="actions"] {
            border: none;
            & .ag-header-cell-menu-button {
              display: none;
            }
          }
        }
      }
      & .ag-pinned-right-header {
        border: none;
      }
    }
  
    .ag-cell {
      @media only screen and (-webkit-min-device-pixel-ratio: 2),
        only screen and (min--moz-device-pixel-ratio: 2),
        only screen and (-o-min-device-pixel-ratio: 2/1),
        only screen and (min-device-pixel-ratio: 2),
        only screen and (min-resolution: 192dpi),
        only screen and (min-resolution: 2dppx) {
        /* Retina-specific stuff here */
        &::after {
          content: "";
          display: block;
        }
      }
    }
  
    .ag-row {
      border: 0;
      border-bottom: 1px solid #e0e0e0 !important;
      box-sizing: border-box;
  
      &:hover {
        background-color: #ececec;
        box-shadow: 6px 6px 10px 0 rgba(0, 0, 0, 0.3);
        z-index: 1;
      }
  
      &::after {
        box-sizing: inherit;
        content: "";
        width: 100%;
        position: absolute;
        opacity: 0.5;
        bottom: -2px;
        transform-origin: center; // Ensure scaling is done from the center (expands outwards)
        border-bottom: 1.5px solid #f5821e;
        transform: scale3d(0, 1, 1); // Shrink only width
        transition: linear transform 0.7s;
        z-index: 1000;
      }
  
      &:hover::after {
        transform: scale3d(1, 1, 1); // Show full-size
        transition: linear transform 0.7s;
        opacity: 1;
      }
  
      .hcl-hover-icons {
        opacity: 0;
        transition: opacity 0.5s;
  
        i {
          margin: 14px 10px;
          &:hover {
            color: #f5821e;
          }
          &:active {
            color: #7a400e;
          }
        }
  
        &:hover {
          cursor: hand;
          cursor: pointer;
          i:last-child {
            margin-right: 0;
          }
        }
      }
  
      // .ag-cell.ag-no-bordered-cell {
      //   border: 0 !important;
      //   background: transparent;
      //   // user-select: text; // To select and copy text from grid
  
      //   &:focus {
      //     /* border: 1px solid #f5821ea3 !important; */
      //     border: 0 !important;
      //     background: transparent;
      //   }
      // }
    }
  
    #hoverActionContainer:hover .hcl-hover-icons {
      color: #959595;
      font-size: 14px;
      padding-top: 3.5px;
      position: absolute;
      text-align: center;
      transition: opacity 1s;
      opacity: 1;
      z-index: 5;
      border-left: 2px solid #f5821e;
      transform: scale3d(1, 1, 1); // Shrink only width
      transition: linear transform 0.7s;
      transform-origin: left;
      display: block;
  
      i:first {
        margin-left: 13px;
      }
  
      i:last-child {
        margin-right: 5px;
      }
    }
  
    .ag-row-hover .hcl-icon-drag {
      transition: opacity 1s;
      opacity: 1;
      z-index: 5;
    }
  
    .ag-row:hover
      > .ag-cell-not-inline-editing
      > .ag-cell-wrapper
      > .ag-selection-checkbox
      > .ag-icon.ag-icon-checkbox-unchecked {
      color: #f5821e;
    }
  
    /* checbox size - as discussed */
    .ag-theme-material .ag-icon-checkbox-checked,
    .ag-theme-material .ag-icon-checkbox-checked-readonly,
    .ag-theme-material .ag-icon-checkbox-unchecked,
    .ag-theme-material .ag-icon-checkbox-unchecked-readonly,
    .ag-theme-material .ag-icon-checkbox-indeterminate,
    .ag-theme-material .ag-icon-checkbox-indeterminate-readonly,
    .ag-theme-material .ag-icon-radio-button-on,
    .ag-theme-material .ag-icon-radio-button-off {
      font-size: 16px;
      height: 16px;
      line-height: 16px;
      width: 16px;
    }
  
    .bck-grd-grey {
      background-color: lightgrey;
    }
  
    .bdr-bottom {
      border-bottom: 1px solid #979797 !important;
    }
  
    .row-disabled {
      pointer-events: none;
  
      .ag-cell {
        opacity: 0.5;
      }
  
      .ag-selection-checkbox {
        visibility: hidden;
      }
    }
  
    _:-ms-lang(x),
    .ag-row::after {
      bottom: -2px;
      border-bottom: 2px solid #f5821e;
    }
  
    .ag-row-group-contracted {
      .ag-group-contracted {
        top: auto;
        right: 5px;
        z-index: 9;
        cursor: pointer;
        position: absolute;
        & .ag-icon-contracted {
          color: #007bff;
          &:hover {
            color: orange;
          }
        }
        .ag-icon-contracted:before {
          font-family: fontello;
          content: "\f107";
        }
      }
    }
  
    .ag-row-group-expanded {
      .ag-group-expanded {
        top: auto;
        right: 5px;
        position: absolute;
        z-index: 9;
        cursor: pointer;
        & .ag-icon-expanded {
          color: #007bff;
          &:hover {
            color: orange;
          }
        }
        .ag-icon-expanded:before {
          font-family: fontello;
          content: "\f106";
        }
      }
    }
  
    .ag-group-child-count {
      display: none;
    }
  
    .ag-theme-material .ag-header-cell,
    .ag-theme-material .ag-header-group-cell {
      padding: 0 10px;
    }
    .ag-theme-material .ag-cell {
      padding: 0 10px;
  
      &[col-id="actions"] {
        border-left: none !important;
      }
    }
  
    @supports (-moz-appearance: meterbar) {
      .ag-row::after {
        bottom: -1px;
        border-bottom: 2px solid #f5821e;
      }
    }
  }
  
  .ag-cell-first-right-pinned {
    height: 48px !important;
    position: static !important;
  }
  
  .ag-icon-checkbox-checked,
  .ag-icon-checkbox-indeterminate {
    font-size: 20px !important;
    min-width: 20px;
    color: #f5821e !important;
    cursor: pointer;
  }
  
  .ag-icon-checkbox-unchecked {
    font-size: 20px !important;
    min-width: 20px;
    color: rgba(0, 0, 0, 0.4) !important;
    cursor: pointer;
  }
  