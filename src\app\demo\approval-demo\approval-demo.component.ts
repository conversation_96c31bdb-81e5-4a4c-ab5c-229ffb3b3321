import { Component, OnInit, ViewChild } from '@angular/core';
import { ButtonConf, SideBarComponent } from 'projects/hcl-angular-widgets-lib/src/public_api';
import { ApprovalListConfig } from 'projects/hcl-angular-approval-lib/src/public-api';

@Component({
  selector: 'app-approval-demo',
  templateUrl: './approval-demo.component.html',
  styleUrls: ['./approval-demo.component.scss']
})
export class ApprovalDemoComponent implements OnInit {

  openButtonConfig: ButtonConf = {
    buttonType: 'stroked',
    color: 'accent',
    borderRadius: 5,
    name: 'openBtn',
    type: 'button',
    styleClass: 'medium-btn',
    value: 'Open'
  };
  showApprovalPanel: boolean = false;
  @ViewChild(SideBarComponent) approvalPanelContainer: SideBarComponent;
  config: ApprovalListConfig = {
    approvalIdList: [ 321, 355, 373, 371, 320 ],
    readOnlyMode: true,
    approvalListGridConf: {
      rowHeight: 48,
      columns: [],
      noDataFlag: false,
      noRowsTemplate: '',
      data: [],
      scrollHeight: 250,
      isClientSideRowModel: true
    },
    pageHeader: 'Associated Approvals',
    url: 'http://lp1-ap-51801847.prod.hclpnp.com:1103/plan',
    username: 'asm_admin'
  };

  constructor() { }

  ngOnInit(): void {
  }

  openPanel(event) {
    this.showApprovalPanel = true;
    this.approvalPanelContainer.openSideBar();
  }

  closeApprovalContainerPanel(event) {
    console.log('closing main panel', event);
    this.approvalPanelContainer.close('close');
    this.showApprovalPanel = false;
  }

}
