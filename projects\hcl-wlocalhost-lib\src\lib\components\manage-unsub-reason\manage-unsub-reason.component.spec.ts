import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageUnsubReasonComponent } from './manage-unsub-reason.component';

describe('ManageUnsubReasonComponent', () => {
  let component: ManageUnsubReasonComponent;
  let fixture: ComponentFixture<ManageUnsubReasonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
    declarations: [ManageUnsubReasonComponent],
    teardown: { destroyAfterEach: false }
})
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageUnsubReasonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
