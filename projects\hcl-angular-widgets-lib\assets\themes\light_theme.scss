@import "@angular/material/theming";
@include mat-core();
$icon-selected-color: #f5821e;
$icon-unselected-color: #d2d6e1;
$md-brandsecondary: (
    50: #e0effa,
    100: #b3d7f3,
    200: #80bcec,
    300: #4da1e4,
    400: #268cde,
    500: #0078d8,
    600: #0070d4,
    700: #0065ce,
    800: #005bc8,
    900: #0048bf,
    A100: #e7efff,
    A200: #b4cbff,
    A400: #81a8ff,
    A700: #6896ff,
    contrast: (50: #000000,
        100: #000000,
        200: #000000,
        300: #000000,
        400: #ffffff,
        500: #ffffff,
        600: #ffffff,
        700: #ffffff,
        800: #ffffff,
        900: #ffffff,
        A100: #000000,
        A200: #000000,
        A400: #000000,
        A700: #000000,
    ),
);
$md-brandprimary: (
    50: #fef0e4,
    100: #fcdabc,
    200: #fac18f,
    300: #f8a862,
    400: #f79540,
    500: #f5821e,
    600: #f47a1a,
    700: #f26f16,
    800: #f06512,
    900: #ee520a,
    A100: #ffffff,
    A200: #ffebe4,
    A400: #ffc6b1,
    A700: #ffb397,
    contrast: (50: #000000,
        100: #000000,
        200: #000000,
        300: #000000,
        400: #000000,
        500: #000000,
        600: #000000,
        700: #000000,
        800: #000000,
        900: #ffffff,
        A100: #000000,
        A200: #000000,
        A400: #000000,
        A700: #000000,
    ),
);
$md-brandwarn: (
    50: #f9e0e0,
    100: #f0b3b3,
    200: #e68080,
    300: #db4d4d,
    400: #d42626,
    500: #cc0000,
    600: #c70000,
    700: #c00000,
    800: #b90000,
    900: #ad0000,
    A100: #ffd7d7,
    A200: #ffa4a4,
    A400: #ff7171,
    A700: #ff5858,
    contrast: (50: #000000,
        100: #000000,
        200: #000000,
        300: #ffffff,
        400: #ffffff,
        500: #ffffff,
        600: #ffffff,
        700: #ffffff,
        800: #ffffff,
        900: #ffffff,
        A100: #000000,
        A200: #000000,
        A400: #000000,
        A700: #000000,
    ),
);

.alternate-theme {
    $alternate-primary: mat-palette($md-brandprimary);
    $alternate-accent: mat-palette($md-brandsecondary);
    $alternate-warn: mat-palette($md-brandwarn);
    $alternate-theme: mat-light-theme($alternate-primary, $alternate-accent, $alternate-warn);
    @include angular-material-theme($alternate-theme);

    .mat-flat-button.mat-accent,
    .mat-raised-button.mat-accent,
    .mat-fab.mat-accent,
    .mat-mini-fab.mat-accent {

        &:hover,
        &:focus {
            background-color: #f5821e;
        }

        &:active {
            background-color: #7a400e;
        }

        .mat-button-focus-overlay {
            background: transparent;
        }

        &[disabled] {
            background: #d2d6e1;
        }
    }

    .mat-button.mat-accent,
    .mat-icon-button.mat-accent,
    .mat-stroked-button.mat-accent {
        border-color: #0078d8;

        &:hover,
        &:focus {
            color: #f5821e;
            border-color: #f5821e;
        }

        &:active {
            color: #7a400e;
            border-color: #7a400e;
        }

        .mat-button-focus-overlay {
            background: transparent;
        }

        &[disabled] {
            color: #d2d6e1;
            border-color: #d2d6e1;
        }
    }

    .mat-stroked-button.mat-accent {
        border-width: 2px;
    }

    .mat-button-ripple {
        display: none;
    }

    :focus {
        outline: none;
    }

    .mat-tab-label {
        color: #6d7692;
        opacity: 1;
        width: auto;
        min-width: auto;
        font-weight: bold;

        &.cdk-keyboard-focused {
            background-color: transparent !important;
        }

        &.cdk-focused {
            background-color: transparent !important;
        }

        &.mat-tab-label-active {
            color: $icon-selected-color;
        }

        & .mat-ripple-element {
            display: none;
        }

        & .mat-tab-label-content {
            font-family: "Montserrat";
            font-weight: 500;
            margin-top: 2px;
            font-size: 16px;
        }
    }

    .mat-radio-button {
        display: block;
        max-width: 200px;

        .mat-radio-inner-circle {
            background-color: #f5821e !important;
        }

        .mat-radio-outer-circle {
            border-color: #f5821e !important;
        }
    }

    .mat-radio-button:not(.mat-radio-checked) {
        .mat-radio-outer-circle {
            border-color: #959595 !important;
        }
    }

    .mat-accent:not(.mat-slider-disabled) {

        .mat-slider-thumb,
        .mat-slider-thumb-label,
        .mat-slider-track-fill {
            background-color: #f5821e;
        }
    }

    .mat-checkbox {
        &.mat-checkbox-checked {
            .mat-checkbox-background {
                background-color: #f5821e;
            }
        }

        .mat-checkbox-frame {
            border: 1.5px solid #959595 !important;
        }
    }

    .mat-form-field-label,
    .mat-form-field-required-marker {
        color: #6d7692 !important;
        letter-spacing: 0.7px;
    }

    .mat-form-field.mat-focused {

        .mat-form-field-label,
        .mat-form-field-required-marker {
            color: #f5821e !important;
        }
    }

    .mat-form-field.mat-form-field-invalid {

        .mat-form-field-label,
        .mat-form-field-required-marker {
            color: #c00 !important;
        }
    }



    .mat-form-field-appearance-legacy.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,
    .mat-form-field-appearance-legacy.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label {
        transform: translateY(-1.28125em) scale(0.95) perspective(100px) translateZ(0.001px);
    }

    .mat-input-element {
        color: #444 !important;
    }

    .mat-step-header {
        font-family: "Montserrat";
        font-weight: 400;
    }
    .mat-form-field-label-wrapper {
        width: calc(100% - 25px); //To accomodate ellipsis for long text
    }
}

$hover-back-ground-color: #ececec;

.ag-theme-material {
    .ag-icon {

        &.ag-icon-checkbox-checked,
        &.ag-icon-checkbox-indeterminate {
            background-color: transparent;
            border-radius: 0;
            color: $icon-selected-color;
        }

        &.ag-icon-checkbox-unchecked {
            color: $icon-unselected-color;
        }
    }
}