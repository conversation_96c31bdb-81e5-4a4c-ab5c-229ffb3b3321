export interface RadarChartConfig {
    chartContainerId?: string;
    numofSide?: number;
    numofLevels?: number;
    size?: number;
    drawYAxis?: boolean;
    drawTicks?: boolean;
    drawDataTextLabels?: boolean;
    startGridLinesFromCenter?: boolean;
    chartBgColor?: string;
    gridLineColor?: string;
    levelsStrokeColor?: string;
    dataFillColor?: string;
    dataStrokeColor?: string;
    labelsTextColor?: string;
    radiusForDataPointsCircle?: number;
    dataPointCircleColor?: string;
    tickTextColor?: string;
  }

export const defaultChartColor = 'rgba(143,205,176,0.8)';

  