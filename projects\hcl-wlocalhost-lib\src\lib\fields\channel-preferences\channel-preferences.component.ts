import { Component, Input, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ChannelPreferencesField } from '../../classes/Fields';

@Component({
  selector: 'ip-channel-preferences',
  templateUrl: './channel-preferences.component.html',
  styleUrls: ['./channel-preferences.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ChannelPreferencesComponent {
  @Input() field: ChannelPreferencesField;
  defaultSelectMessage: String = this.translate.instant('settings.select-channel-message');
  defaultTitle: String = this.translate.instant('settings.channel-pref-title');

  constructor(private translate: TranslateService) {}

}
