import { Rule } from '../types'

export default {
  id: 'attr-value-not-empty',
  description: 'All attributes must have values.',
  init(parser, reporter, rulePresent, translations: {[key: string]: string}) {
    parser.addListener('tagstart', (event) => {
      const attrs = event.attrs
      let attr
      const col = event.col + event.tagName.length + 1

      for (let i = 0, l = attrs.length; i < l; i++) {
        attr = attrs[i]

        if (attr.quote === '' && attr.value === '') {
          reporter.warn(
            reporter.buildMessage(translations['tamhav'], attr.name),
            event.line,
            col + attr.index,
            this,
            attr.raw
          )
        }
      }
    })
  },
} as Rule
