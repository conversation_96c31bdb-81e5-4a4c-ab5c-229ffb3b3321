import { Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ButtonConf, ModalConfig, ModalService } from  'hcl-angular-widgets-lib';
import { IpEmailBuilderService } from '../../../ip-email-builder.service';


@Component({
  selector: 'version-details-modal',
  templateUrl: './version-details-modal.component.html',
  styleUrls: ['./version-details-modal.component.scss']
})
export class VersionDetailsModalComponent implements OnInit {

  okBtnConfig: ButtonConf = {
    value: '',
    buttonType: 'flat',
    borderRadius: 4,
    name: 'add-copy-edit',
    color: 'accent',
    styleClass: 'hcl-lg-button'
  };

    /** template refernce for close Actions */
    @ViewChild('closeActions') closeActions: TemplateRef<ElementRef>;
    @ViewChild('closeContent') closeContent: TemplateRef<ElementRef>;
    versionDetailsObj: any;

    // dialog Config object
    dialogConfig: ModalConfig = {
      disableClose: true,
      backdropClass: '',
      hasBackdrop: true,
      width: '560px',
    //  height: '280px'
    };

    constructor(private modalService: ModalService, private translate: TranslateService, public ngb: IpEmailBuilderService) {
    }

  ngOnInit(): void {
  }

  ngAfterViewInit() {
    this.dialogConfig.actionsTemplate = this.closeActions;
    this.dialogConfig.title = this.translate.instant('settings.Manage_unsubscribe_update');
    this.okBtnConfig.value = this.translate.instant('BUTTONS.OK');
  }

  /**
   * function to handle the opening of version details Modal
   */
   openDialog() {
    this.dialogConfig.contentTemplate = this.closeContent;
    this.modalService.openDialog(this.dialogConfig);
    this.versionDetailsObj = this.ngb.latestEmail.changeLog[0];
  }

  /**
   * function to handle the cancel of version details Modal
   */
  cancelClose() {
    this.modalService.closeDialog();
  }
}
