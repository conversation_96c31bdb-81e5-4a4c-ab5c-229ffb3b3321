import { Component, ContentChildren, ElementRef, EventEmitter, Input, OnInit, Output, QueryList, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { AllModules, Module } from '@ag-grid-enterprise/all-modules';
import { DataGridColumnConf, DataGridConf, InfiniteDatasource, InfiniteGetRowsParams, ServerSideDatasource, ServerSideGetRowsParams } from './data-grid.conf';
import { HclTemplateDirective, PaginatorComponent } from 'hcl-angular-widgets-lib';
import { AgDataGridAdapter } from './AgDataGridAdapter';
import { HttpClient } from '@angular/common/http';
import * as _ from 'lodash';

@Component({
  selector: 'hcl-data-grid-v2',
  templateUrl: './data-grid-v2.component.html',
  styleUrls: ['./data-grid-v2.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class DataGridV2Component implements OnInit {
  public modules: Module[] = AllModules;
  /**
   * The configuration that defines how the grid needs to be displayed
   */
  @Input() config: DataGridConf;
  /**
   * This is an event emitter object that will emit an event when the rendering
   * of the grid is complete & now user can call the APIs of the grid.
   */
  @Output() gridReady = new EventEmitter<any>();
  /**
   * When the reshuffling of the column happens, this event will be fired, user can listen to this
   * event & update the server that the layout of the column has been changed
   */
  @Output() columnMoved = new EventEmitter<any>();
  /**
   * When a row is selected, this function will emit an event that will
   * help us identify which row was selected
   */
  @Output() rowSelected = new EventEmitter<any>();
  /**
   * When a row is un-selected, this function will emit an event that will
   * help us identify which row was selected
   */
  @Output() rowUnSelected = new EventEmitter<any>();
  /**
   * When a row is clicked
   */
  @Output() rowClicked = new EventEmitter<any>();
  /**
   * When a cell is clicked
   */
  @Output() cellClicked = new EventEmitter<any>();
  /**
   * an emitter that will emmit a event whren the data is loaded using URL
   * type {EventEmitter<any>}
   */
  @Output() dataLoadedFromUrl = new EventEmitter<any>();
  /**
   * an emitter that will emmit a event whren the data has failed using URL
   * type {EventEmitter<any>}
   */
  @Output() dataFailedFromUrl = new EventEmitter<any>();
  @Output() pageSizeChanged = new EventEmitter();
  @Output() pageChanged = new EventEmitter();
  @Output() columnSorting = new EventEmitter();
  @Output() firstDataRender = new EventEmitter();
  @Output() rowDataUpdate = new EventEmitter();
  @Output() rowDataChange = new EventEmitter();

  @Output() initialRowRenderCompleteTrigger = new EventEmitter();
  @Output() onBodyScrollTrigger = new EventEmitter();
  @Output() cellEditingStarted = new EventEmitter();
  @Output() cellEditingStopped = new EventEmitter();

  /**
   * emit event to grid parent, when item is dropped on grid.
   */
  @Output() gridDrop = new EventEmitter();
  /**
   * This variable will contain all the list of templates that the
   * user has set when initializing the component
   */
  @ContentChildren(HclTemplateDirective, { descendants: true }) templates: QueryList<HclTemplateDirective>;
  /**
   * The grid will utilize the pagination module that is build in this library.
   * this variable is the instance of the paginatior component
   */
  @ViewChild('paginator') paginator: PaginatorComponent;
  /**
   * The Adapter that will convert the config data into the supported 3rd party configuration
   */
  _adapter: AgDataGridAdapter = null;
  infiniteDatasource: InfiniteDatasource = {
    getRows: (params) => {
      if (!isNaN(params.startRow) && !isNaN(params.endRow)) {
        this.fetchData(this.appendInfiniteScrollParams(this.config.dataUrl, params), params);
      }
    }
  };
  initialRowRenderComplete = false;
  /**
     * Varibale to store Params provided by Grid Ready Event
     */
  params: any;
  // Server side Scrolling Datasource, to fetch rows for the grid.
  ServerSideDataSource: ServerSideDatasource = {
    // grid calls this to get rows // to fetch rows from the server.
    // called by the grid when more rows are required
    getRows: (params) => {
      let param: InfiniteGetRowsParams;
      this.fetchData(this.appendServerScrollParams(this.config.dataUrl, params), param, params);
    }
  };
  /**
   * USer of this component can define cell template that needs to be rendered in a cell
   * This function will contain the map of the template & the corresponding template name associated
   * to it.
   * type {Map<string, TemplateRef<any>>}
   */
  _cellRendererMap: Map<string, TemplateRef<any>> = new Map<string, TemplateRef<any>>();

  /**
   * USer of this component can define custom row template that needs to be rendered in a cell
   * This function will contain the map of the template & the corresponding template name associated
   * to it.
   * type {Map<string, TemplateRef<any>>}
   */
  _fullWidthRendererMap: Map<string, TemplateRef<any>> = new Map<string, TemplateRef<any>>();
  /**
   * User of this component can define header template that needs to be rendered in a header
   * This function will contain the map of the template & the corresponding template name associated
   * to it.
   * type {Map<string, TemplateRef<any>>}
   */
  _headerRendererMap: Map<string, TemplateRef<any>> = new Map<string, TemplateRef<any>>();
  _popoverRendererMap: Map<string, TemplateRef<any>> = new Map<string, TemplateRef<any>>();
  /**
   * The default constructor for this component
   */
  constructor(private http: HttpClient, public element: ElementRef) { }
  /**
   * This function is called at getRowHeight of data grid can be used for custom heights of grid rows
   */
  rowHeight = function () {
    return this.config.rowHeight;
  }.bind(this);
  /**
   * during the initialization we will initialize the _adapter
   */
  ngOnInit() {
    this.config.noDataFlag = false;
    if (this.config.rowGroupType && this.config.rowGroupType.groupRowInnerRenderer) {
      this.config.rowGroupType.groupRowInnerRenderer = 'groupRowInnerRenderer';
    }
  }
  /**
   * This is a function that will be invoked when the grid rendering is complete
   * param params
   */
  onGridReady(params: any) {
    this.params = params;
    // make sure the _adapter does what is needed
    this._adapter.componentReady(params);
    // TODO: While emitting this function should send the data in a particular format
    // rather then the object that is sent by 3rd party
    this.gridReady.emit(this);
    // The default tooltip value needs to be changed for faster tooltip reterivals
    try {
      (params.api as any).context.beanWrappers.tooltipManager.beanInstance.MOUSEOVER_SHOW_TOOLTIP_TIMEOUT = 500;
    } catch (e) {
      console.error(e);
    }
    if (this.config.infiniteScroll) {
      params.api.setDatasource(this.infiniteDatasource);
    }
    if (this.config.serverSideScroll) {
      params.api.setServerSideDatasource(this.ServerSideDataSource);
    }
  }

  public showActions(state: boolean): void {
    this._adapter.showActions(state);
  }

  public autoFit(): void {
    this._adapter.autoFit();
  }

  onFirstDataLoad(data) {
    this.firstDataRender.emit(data);
  }

  /**
   * This method is triggered on row render
   * param event
   */
  viewportChanged(event) {
    if (event.lastRow !== -1 && !this.initialRowRenderComplete) {
      this.initialRowRenderCompleteTrigger.emit();
      this.initialRowRenderComplete = true;
    }
  }

  /**
   * This is a listener to the Grid Scroll Event
   * param event
   */
  onBodyScroll(event) {
    if (event.api.getRenderedNodes().length > 0 && event.api.getRenderedNodes()[0].data) {
      this.onBodyScrollTrigger.emit(event.api.getRenderedNodes()[0].rowIndex);
    }
  }

  /**
   * This method returns the dataUrl with page number and size.
   * param event
   */
  appendServerScrollParams(url, params) {
    if (this.config.serverSideScroll) {
      return url + '?page=' + Math.ceil(params.request.startRow / this.config.serverSideScroll.cacheBlockSize) +
        '&size=' + this.config.serverSideScroll.cacheBlockSize;
    }
  }


  /**
   * This method returns the dataUrl with start and end params
   * param event
   */
  appendInfiniteScrollParams(url, params) {
    if (this.config.infiniteScroll) {
      return url + '?page=' + Math.ceil(params.startRow / this.config.infiniteScroll.cacheBlockSize) +
        '&size=' + this.config.infiniteScroll.cacheBlockSize;
    }
  }

  // gives the row model of grid.
  getRowModel() {
    if (this.config.infiniteScroll) {
      return 'infinite';
    } else if (this.config.serverSideScroll) {
      return 'serverSide';
    } else {
      return null;
    }
  }

  onRowDataUpdate(data) {
    this.rowDataUpdate.emit(data);
  }

  onRowDataChange(data) {
    this.rowDataChange.emit(data);
  }
  /**
   * This function will return the list of all the columns and the current positions & states
   * return {DataGridColumnConf[]}
   */
  getAllColumns(): DataGridColumnConf[] {
    return this._adapter.getAllColumns();
  }
  /**
   * This function will show a column in the grid
   */
  showColumn(colId: string): void {
    this._adapter.showColumn(colId);
  }
  /**
   * This function will show a column in the grid
   */
  hideColumn(colId: string): void {
    this._adapter.hideColumn(colId);
  }
  /**
   * This function will update the data in the data grid
   * param  data
   */
  setData(data) {
    this.config.noDataFlag = false;
    if (data && data.content && data.content.length <= 0) {
      this.config.noDataFlag = true;
    }
    this._adapter.setData(data);
  }
  /**
   * Called by the adapter when a row is selected
   * private
   */
  _rowSelected(data: any): void {
    this.rowSelected.emit(data);
  }
  /**
   * Called by the adapter when a row is un-selected
   * private
   */
  _rowUnSelected(data: any): void {
    this.rowUnSelected.emit(data);
  }


  _rowClicked(data: any): void {
    this.rowClicked.emit(data);
  }

  _cellClicked(data: any): void {
    this.cellClicked.emit(data);
  }
  // Make sure the actions column is at last and checbox column is 1st one in all scenarios of column drag
  onDragStop($event) {
    this._adapter.fixedColumns();
    this.columnMoved.emit();
  }

  /**
   * Called when we drag item on grid
   * param event :- dropEvent
*/
  gridDragOver(event) {
    const dragSupported = event.dataTransfer.types.length;
    if (dragSupported) {
      event.dataTransfer.dropEffect = 'copy';
      event.preventDefault();
    }
  }

  /**
   * Programmatically select a row
   * param {number} rowIndex
   */
  selectIndex(rowIndex: number, suppressEvents: boolean): void {
    this._adapter.selectIndex(rowIndex, suppressEvents);
  }

  deSelectAllRows(): void {
    this._adapter.deSelectAllRows();
  }

  /**
   * Called when  data item from grid's outside is dropped on grid
   * param event :- dropEvent
  */
  onGridDrop(event) {
    event.preventDefault();
    // let the parent apply its logic on grid drop.
    this.gridDrop.emit(event);
  }

  /**
   * Remove the selected index from teh grid
   * param {number} index
   */
  public removeRow(index: number[]): any[] {
    return this._adapter.removeRow(index);
  }


  /**
   * Add custom column definition dynamically in the grid
   * param {DataGridColumnConf} column
   */
  public addColumnDefination(column: DataGridColumnConf): void {
    column._templateRef = this._cellRendererMap.get(column.rendererTemplateName);

    this._adapter.addColumnDefination(column);
  }


  /**
   * Remove the selected index from teh grid
   * param {number} index
   */
  public removeRowObject(object: any): any {
    return this._adapter.removeRowObject(object);
  }

  /**
   * This function will remove a row from the grid
   * param {number} index
   */
  public addRow(index: number, data: any): void {
    this._adapter.addRow(index, data);
  }
  /**
   * This function will display the data from the corresponding page
   * param {number} pageNumber
   */
  private loadDataFromUrl(pageNumber: number) {
    // fetch the data
    if (this.config.infiniteScroll) {
      //Do nothing here, the grid will handle it
    } else {
      this.fetchData(this.addPaginationParams(pageNumber, -1));
    }
  }
  /**
   * this function will create the data URL & pass it back to the
   */
  addPaginationParams(pageNumber: number, size: number): string {
    if (this.config.pagination) {
      if (pageNumber < 0) {
        pageNumber = this.config.pagination.currentPageIndex;
      }
      if (size < 0) {
        size = this.config.pagination.rowsPerPage;
      }

      if (pageNumber === 0) {
        return this.config.dataUrl + '?page=' + pageNumber + '&size=' + size;
      } else {
        return this.config.dataUrl + '?page=' + (pageNumber - 1) + '&size=' + size;
      }
    } else {
      return this.config.dataUrl;
    }
  }

  /**
   * This function will actually fetch the data from the server & display it in the grid
   * param {string} url
   */
  fetchData(url: string, iGetRowsParams?: InfiniteGetRowsParams, serverGetRowsParams?: ServerSideGetRowsParams) {
    //  we have a URL, so data needs to be fetched from that URL
    let requestHeaders;
    let newUrl;
    if (this.config.gridHeader) {
      requestHeaders = this.config.gridHeader.getHeaders();
    }

    if (this.config.queryParams) {
      if (this.config.pagination) {
        newUrl = url + this.createUrlString();
      } else if (this.config.infiniteScroll) {
        newUrl = url + this.createUrlString();
      } else {
        newUrl = url + '?' + this.createUrlString();
      }
    } else {
      newUrl = url;
    }

    if (this.config.dataUrl) {
      if (this.config.infiniteScroll) {
        this._adapter.showLoadingOverlay();
      }

      this.http.get<any[]>(newUrl, {
        headers: requestHeaders,
        observe: 'response'
      }).subscribe(
        (response: any) => {
          this._adapter.setNoRowTemplate(response.body.content);
          // check if user want to do some processing on the data, before the grid picks it up for rendering
          if (this.config.massageData) {
            response.body.content = this.config.massageData(response.body.content);
          }
          if (this.config.infiniteScroll) {
            iGetRowsParams.successCallback(
              response.body.content,
              response.body.page && response.body.page.totalElements);
          } else if (this.config.serverSideScroll) {
            // success callback, pass the rows back the grid asked for, supply rows for requested block to grid.
            if (serverGetRowsParams) {
              serverGetRowsParams.successCallback(
                response.body.content, response.body.page && response.body.page.totalElements);
            }
          } else {
            this.setData(response.body);
            if (this.config.pagination) {
              if (response.body.page.totalPages > response.body.page.pageNumber) {
                this.paginator.updateTotalRecords(response.body.page.totalElements);
                this.updatePaginationInformation(response.body.page);
                this.paginator.refreshPage();
              } else {
                const prevIndex = this.paginator.config.currentPageIndex;
                this.paginator.config.totalRecords = response.body.page.totalElements;
                this.paginator.config.currentPageIndex = prevIndex > 0 ?
                  this.paginator.config.currentPageIndex - 1 : 0;
                this.paginator.config.rowsPerPage = response.body.page.size;
                if (prevIndex > 1) {
                  this.refreshData();
                  return;
                }
              }
            }
          }
          // emmit the data loaded event
          this.dataLoadedFromUrl.emit(response.body);
        }, (error) => {
          // emmit the data failed event
          this.dataFailedFromUrl.emit(error ? error : {});
        });
    }
  }

  createUrlString() {
    let queryParamString = '';
    let key;
    for (key in this.config.queryParams) {
      if (Array.isArray(this.config.queryParams[key])) {
        this.config.queryParams[key].forEach(
          (element) => {
            queryParamString += '&' + key + '=' + element;
          }
        );
      } else {
        queryParamString += '&' + key + '=' + this.config.queryParams[key];
      }
    }
    const hasMoreThanAscii = queryParamString.split('').some(function (char) { return char.charCodeAt(0) > 127; });
    if (hasMoreThanAscii && queryParamString.indexOf('sort') > -1) {
      return encodeURIComponent(queryParamString);
    } else {
      return queryParamString;
    }
  }
  /**
   * This function will read the header data & get the pagination information
   * & update the pagination component
   * param headers
   */
  private updatePaginationInformation(page) {
    if (page) {
      this.config.pagination.totalRecords = page.totalElements;
    }
  }
  /**
   * This function will create & instantiate the data-grid adapter
   */
  private createAndInitDataGridAdapter(): void {
    this._adapter = new AgDataGridAdapter(this, this.config);
    //  We have populated the column def, now chk if we have a URL for data
    if (this.config.dataUrl) {
      //  Load the data from teh page 0
      if (!this.config.infiniteScroll) {
        this.loadDataFromUrl(0);
      }
    }
  }
  /**
   * All the templates that the user will have will be passed to us as a part of
   * ContentChildren, we will have to put it into a map, based on the name & the type
   * this function will iterate the children's & put it in the cellRenderer map
   * or the template renderer map
   */
  private populateTemplateMap(): void {
    //  Iterate & update the map
    this.templates.forEach((item: HclTemplateDirective) => {
      switch (item.getType()) {
        //  The custom row full width templates
        case 'full-width':
          this._fullWidthRendererMap.set(item.getName(), item.getTemplate());
          break;
        //  The cell templates
        case 'cell-renderer':
          this._cellRendererMap.set(item.getName(), item.getTemplate());
          break;
        //  The header templates
        case 'header-renderer':
          this._headerRendererMap.set(item.getName(), item.getTemplate());
          break;
        //  The popover templates
        case 'popover-renderer':
          this._popoverRendererMap.set(item.getName(), item.getTemplate());
          break;
      }
    });
  }
  /**
   * In the configuration the user will pass the template name, this function will get the
   * template name & if there is a template instance associated to that template in the map
   * this function will update the columndef in the conf variable with the template instance
   */
  private updateTemplateInstanceInConfig(): void {
    // If we have template's only then do this
    if (this.templates.length > 0) {

      // tslint:disable-next-line: no-unused-expression
      this.config.topRowData && this.config.topRowData.forEach((customRow) => {
        if (customRow.rowTemplateName) {
          customRow._templateRef = this._fullWidthRendererMap.get(customRow.rowTemplateName);
        }
      });

      this.config.columns.forEach((col: DataGridColumnConf) => {
        // If there is a template defined for the header, lets set it
        if (col.headerRendererTemplateName) {
          col._headerTemplateRef = this._headerRendererMap.get(col.headerRendererTemplateName);
        }
        // If we have a template for cells, lets set it
        if (col.rendererTemplateName) {
          col._templateRef = this._cellRendererMap.get(col.rendererTemplateName);
        }
        // If we have a template for popover, lets set it
        if (col.popoverTemplateName) {
          col._popoverTemplateRef = this._popoverRendererMap.get(col.popoverTemplateName);
        }
      });
    }
  }
  /**
   * This function will initiate a column sorting for different scenarios
   * param {string} colId
   */
  public sort(column: DataGridColumnConf) {
    if (this.config.queryParams) {
      if (this.config.multipleSort) {
        this.multipleSort(column);
      } else {
        if (this.config.queryParams.sort) {
          if (typeof this.config.queryParams.sort === 'string') {
            this.singleSort(column);
          }
        } else {
          this.config.queryParams.sort = column.colId + ',' + column._sorting;
        }
      }
    } else {
      this.config.queryParams = {
        sort: column.colId + ',' + column._sorting
      };
    }
    // Check if we have a dataURL, in that case we do the sorting
    if (this.config.dataUrl && this.config.pagination) {
      this.config.pagination.currentPageIndex = 0;
      this.fetchData(this.addPaginationParams(0, this.config.pagination.rowsPerPage));
    } else if (this.config.infiniteScroll) {
      this.replaceDataSource();
    } else {
      this.fetchData(this.config.dataUrl);
    }
    // Lets Emit the event
    this.columnSorting.emit(column);
  }
  /**
   * This function will initiate a column sorting on the specific column for the single column sort Scenario
   * param {string} colId
   */
  private singleSort(column) {
    if (column.colId === this.config.queryParams.sort.split(',')[0]) {
      if (column._sorting === 'NONE') {
        delete this.config.queryParams.sort;
      } else {
        this.config.queryParams.sort = this.config.queryParams.sort.split(',')[0] + ',' + column._sorting;
      }
    } else {
      this._adapter.columnDefinitionsArray.forEach((element, index) => {
        if ((element.colId !== column.colId) && element._sorting && (element._sorting !== 'NONE')) {
          this._adapter.columnDefinitionsArray[index]._sorting = 'NONE';
        }
      });
      this.config.queryParams.sort = column.colId + ',' + column._sorting;
    }
  }

  /**
   * function to update current column definitions
   */
  setColumnDefinitionsArray(columnDefinitionsArray) {
    this._adapter.columnDefinitionsArray = columnDefinitionsArray;
  }

  /**
   * function to get column current definitions
   */
  getColumnDefinitionsArray() {
    return this._adapter.columnDefinitionsArray;
  }

  /**
   * This function will initiate a column sorting on the specific column for the multiple column sort Scenario
   * param {string} colId
   */
  private multipleSort(column) {
    let flag = false;
    if (this.config.queryParams.sort) {
      if (this.config.queryParams.sort.length > 0) {
        this.config.queryParams.sort.forEach(
          (element, index) => {
            if (column.colId === element.split(',')[0]) {
              this.config.queryParams.sort[index] = element.split(',')[0] + ',' + column._sorting;
              flag = true;
            } else if ((index === (this.config.queryParams.sort.length - 1)) && !flag) {
              this.config.queryParams.sort.push(column.colId + ',' + column._sorting);
            }
          }
        );
      } else {
        this.config.queryParams.sort.push(column.colId + ',' + column._sorting);
      }
    } else {
      this.config.queryParams.sort = [column.colId + ',' + column._sorting];
    }
  }

  /**
   * This function will resent the request & refresh the current page
   */
  public refreshData(): void {
    if (this.config && this.config.dataUrl) {
      // we have data URL, so lets get sorting data
      if (this.config.pagination) {
        this.fetchData(this.addPaginationParams(this.config.pagination.currentPageIndex, this.config.pagination.rowsPerPage));
      } else if (this.config.infiniteScroll) {
        this.params.api.refreshInfiniteCache();
      } else {
        this.fetchData(this.addPaginationParams(0, 1000));
      }
    }
  }

  /**
   * This function will replace the current datasource with new one corresponding to new config set
   */
  public replaceDataSource(): void {
    if (this.config.infiniteScroll && this.params && this.params.api) {
      this.params.api.setDatasource(this.infiniteDatasource);
    }
  }

  /**
   * In case the grid is hidden and now the grid is displayed, we need to redraw the grid
   */
  public redrawGridRows(): void {
    this.params.api.redrawRows();
  }

  /**
   * code to set columnDefs at the runtime and redraw with the grid with new columnDefs
   * @param columnDefList
   */
  public setCustomColumnDefs(columnDefList: Array<any>) {
    if (this.config.actions) {
      columnDefList.push({
        headerName: '',
        field: '',
        width: 24,
        resizable: false,
        suppressMovable: true,
        cellRenderer: 'cellRenderer',
        actions: this.config.actions,
        hide: this.config.hideActions,
        colId: 'actions',
        editable: false,
        cellRendererParams: this.config.actions,
        suppressSizeToFit: true,
        pinned: 'right',
        lockPinned: true,
        cellClass: ['ag-custom-action-cell'] // , 'lock-pinned']
      });
    }
    this.params.api.setColumnDefs(columnDefList);
  }
  /**
   * Resize the columns to fit
   * returns {void}
   */
  public resizeColumnsToFit(): void {
    if (this.params && this.params.api) {
      this.params.api.sizeColumnsToFit();
    }
  }
  /**
   * This function will return an array of objects representing column and rows under the column in trange selection
   */
  public getSelection(isMultipleRange = false) {
    const cellRanges = this.params.api.getCellRanges();
    const retVal = [];
    if (cellRanges && cellRanges.length > 0) {
      if (isMultipleRange === false) {
        const firstRange = cellRanges[0],
          startRow = _.min([firstRange.startRow.rowIndex, firstRange.endRow.rowIndex]),
          endRow = _.max([firstRange.startRow.rowIndex, firstRange.endRow.rowIndex]);
        let rowModel: any, rowNode: any;
        _.forEach(firstRange.columns, (column) => {
          retVal.push({ column: column.colDef.field, value: [] });
          for (let rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
            rowModel = this.params.api.getModel();
            rowNode = rowModel.getRow(rowIndex);
            retVal[retVal.length - 1].value.push(rowNode.data);
          }
        });
      } else if (isMultipleRange === true) {
        let firstRange: any, startRow: number, endRow: number, colIndex: number, rowModel: any, rowNode: any;
        _.forEach(cellRanges, (cellRange) => {
          firstRange = cellRange;
          startRow = _.min([firstRange.startRow.rowIndex, firstRange.endRow.rowIndex]);
          endRow = _.max([firstRange.startRow.rowIndex, firstRange.endRow.rowIndex]);
          colIndex = -1;
          _.forEach(firstRange.columns, (column) => {
            colIndex = _.findIndex(retVal, function (x) { return x.column === column.colDef.field; });
            if (colIndex === -1) {
              retVal.push({ column: column.colDef.field, value: [] });
              colIndex = retVal.length - 1;
            }
            for (let rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
              rowModel = this.params.api.getModel();
              rowNode = rowModel.getRow(rowIndex);
              retVal[colIndex].value.push(rowNode.data);
            }
          });
        });
      }
    }
    // this.onRangeSelectionChanged.emit(retVal);
    return retVal;
  }

  /**
   * This method will remove the selection, i.e it'll reset the selection to none
   */
  public clearSelection() {
    this.params.api.clearRangeSelection();
  }

  /**
   * This method will add the range of cells as selected range
   */
  public addSelection(rangeCells) {
    this.params.api.addCellRange(rangeCells);
  }

  /**
   * This function will be called when cell editing is started, this will emit an event with data linked to cell
   * param event
   */
  onCellEditingStarted(event) {
    this.cellEditingStarted.emit({ data: event.data, column: event.colDef });
  }

  /**
   * This function will be called when cell editing is stopped, this will emit an event with value changed
   * param event
   */
  onCellEditingStopped(event) {
    this.cellEditingStopped.emit({ data: event.data, column: event.colDef });
  }

  /**
   * This funtion will clear selected cell
   */
  public clearSelectedData() {
    const cellRanges = this.params.api.getCellRanges();
    if (cellRanges) {
      const firstRange = cellRanges[0];
      const startRow = Math.min(firstRange.startRow.rowIndex, firstRange.endRow.rowIndex);
      const endRow = Math.max(firstRange.startRow.rowIndex, firstRange.endRow.rowIndex);
      for (let rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
        firstRange.columns.forEach((column) => {
          const rowModel = this.params.api.getModel();
          const rowNode = rowModel.getRow(rowIndex);
          rowNode.setDataValue(column, null);
        });
      }
    }
  }

  /**
   * Called when the page is changed in the paginator
   */
  onPageChange(event: any) {
    if (!this.config.pagination.isCustomHandler) {
      this.fetchData(this.addPaginationParams(event.currentPageIndex, event.rowsPerPage));
    }

    this.pageChanged.emit(event);
  }

  /**
   * Called when the page size is changed in the paginator
   */
  onpageSizeChange(event: any) {
    if (!this.config.pagination.isCustomHandler) {
      this.fetchData(this.addPaginationParams(event.currentPageIndex, event.rowsPerPage));
    }

    this.pageSizeChanged.emit(event);
  }
  /**
   * After the contents have been initialized we have to read the templates that have been passed by
   * the user of this component & then render it accordingly
   */
  ngAfterContentInit(): void {
    // Lets get all the template instances
    this.populateTemplateMap();
    // for each column we have to map the columns
    this.updateTemplateInstanceInConfig();
    // Lets create & instantiate the data-grid adapter
    this.createAndInitDataGridAdapter();
  }

  isRowSelectable(rowNode: any) {
    return rowNode.data ? true : false;
  }

  // update row data using  column ids
  updateCellData(colIds, value, index) {
    this._adapter.updateCellData(colIds, value, index);
  }

  /**
   * This function will update the grid row data
   */
  public updateRowData(data: any[]): void {
    this._adapter.updateRowData(data);

  }

}
