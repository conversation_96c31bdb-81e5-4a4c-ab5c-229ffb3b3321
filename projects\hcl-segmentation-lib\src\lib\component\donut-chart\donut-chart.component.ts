import { Component, Input, OnInit, SimpleChanges } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { DonutChartConfig, SegmentRunStats } from "../../models/segment";
import { ApacheDonutChartConfig } from "hcl-angular-charts-lib";

@Component({
  selector: "hcl-seg-donut-chart",
  templateUrl: "./donut-chart.component.html",
  styleUrls: ["./donut-chart.component.scss"],
})
export class DonutChartComponent implements OnInit {
  @Input() segmentStats: SegmentRunStats;
  @Input() segmentType: string;
  @Input() config: DonutChartConfig;

  segmentDonutConfig: ApacheDonutChartConfig;
  segmentStatsWithCount: string;
  percentage: string;

  constructor(private translate: TranslateService) {}
  
  ngOnInit(): void {
    this.setConfiguration();
    this.updateDonutValues();

    if (this.segmentStats.segmentCount >= 0) {
      this.percentage =
        (
          (100 * this.segmentStats.segmentCount) /
          this.segmentStats.totalCount
        ).toFixed(2) + "%";
    } else {
      this.percentage = "0%";
    }
  }

  setConfiguration() {
    const containerDimentions = document
      .getElementById("chartContainer")
      .getBoundingClientRect();
      
    this.segmentDonutConfig = {
      title: {
        id: "segmentChart",
        left: 'left',
      },
      legend: {
        top: '5%',
        left: 'center'
      },
      series: [{
        name: '',
        type: 'pie',
        color: [
          '#FEBB21',
          '#E4E4F0',
          '#FEBB21',
          '#E4E4F0'
        ],
        data: [0, 0, 0, 0],
        radius: ['65%', '90%'],
        center: ['50%', '75%'],
        startAngle: 180,
        endAngle: 360,
        labelLine: {
          show: false
        },
        silent: true,
        emphasis: {
          disabled: true
        }
      }]
    };
  }

  updateDonutValues() {
    this.segmentDonutConfig.series[0].data[0] = this.segmentStats.segmentCount;
    this.segmentDonutConfig.series[0].data[1] = this.segmentStats.totalCount - this.segmentStats.segmentCount;
    if (
      this.segmentStats.segmentCount == 0 &&
      this.segmentStats.totalCount == 0
    ) {
      this.segmentDonutConfig.series[0].data[2] = 1;
    }
    if (this.segmentStats.segmentCount < 0) {
      this.segmentDonutConfig.series[0].data[0] = this.segmentStats.segmentCount + 1;
    }
  }
}
