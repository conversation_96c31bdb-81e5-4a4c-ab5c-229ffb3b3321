@import "src/assets/scss/_variables.scss";

 hcl-side-bar {
    aside {
      padding: 45px;
      width: 700px;
      background-color: #ECECEC;
      box-shadow: -15px 0 10px -2px rgba(0, 0, 0, 0.3);
      h2 {
        font-size: 20px;
        font-weight: 600;
        font-family: Montserrat;
        color: #6D7692;
      }
      .right {
        display: flex;
        flex-direction: row-reverse;
      }
      .btn-container {
        display: flex;
        flex-direction: row-reverse;
        position: absolute;
        bottom: 20px;
        right: 0px;
        width: 90%;
        hcl-button {
          margin: 10px 5px;
        }
      }
      .mat-stroked-button.mat-primary {
        color: #0078d8;
        border-color:#0078d8;
        border-width: 2px;
      }
      .item-container {
        max-height: 43%;
        overflow: auto;
      }
      .item-label {
        width: 85%;
        line-height: 25px;
        // white-space: nowrap;
        // overflow-x: clip;
        // text-overflow: ellipsis;
      }
      .start-end {
        display:flex;
        justify-content:space-between;
      }
      .action-item {
        color: $page-header;
        font-size: $font-size-16;
      }
    }
    .list-item {
      // height: 32px;
      margin-bottom: 12px;
      height: auto;
      word-break: break-word;
    }
    .list-item:hover {
      background-color: #ffffff;
      cursor: pointer;
    }
  }