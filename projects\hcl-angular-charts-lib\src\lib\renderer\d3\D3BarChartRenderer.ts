import { D3<PERSON><PERSON><PERSON><PERSON><PERSON> } from './D3ChartRenderer';

export class D3BarChartRenderer extends D3ChartRenderer {
  // task group in which all task bars and other elments should be appended
  private taskGroup: any;

  /**
   * this function will populate the data in the chart
   */
  protected plotChart(): void {
    this.taskGroup =  this.chart.append('g')
      .attr('class', 'taskbarGroup')
      .selectAll('g')
      .data(this.chartConfig.series[0].data)
      .enter()
      .append('g')
      .attr('class', 'taskbar');

    this.renderRawDataRectangle();
  }
  /**
   * this function will return bars of bar chart with required width and height and position
   */
  protected renderRawDataRectangle(): any {
    let animationDuration: number = 0;
    if (this.chartConfig.animation && this.chartConfig.animation.duration) {
      animationDuration = this.chartConfig.animation.duration;
    }

    return this.taskGroup.append('rect')
      .attr('class', 'hcl-bar-node')
      .attr('x', (d, i): any => {
        return this.categoryAxisRenderer.getPosition( this.chartConfig.series[0].getCategory(d, i));
      })
      .attr('y', (d, i): any => {
        return this.linearAxisRenderer.getPosition(this.chartConfig.series[0].getMaxValue(d, i));
      })
      .attr('width', this.categoryAxisRenderer.getBandWidth())
      // .transition().duration(animationDuration)
      .attr('height', (d, i): any => {
        return this.linearAxisRenderer.getPosition(0) - this.linearAxisRenderer.getPosition(this.chartConfig.series[0].getMaxValue(d, i));
      });
  }
}
