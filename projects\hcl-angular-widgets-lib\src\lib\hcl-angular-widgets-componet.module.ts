import { NgModule } from "@angular/core";
import { HttpClientModule } from "@angular/common/http";
import { ToolbarComponent } from "./component/toolbar/toolbar.component";
import { CommonModule } from "@angular/common";
import { SideNavComponent } from "./component/side-nav/side-nav.component";
import { PanelComponent } from "./component/panel/panel.component";
import { DragDropComponent } from "./component/drag-drop/drag-drop.component";
import { RouterModule } from "@angular/router";
import { GridsterModule } from "angular-gridster2";
import { ModalComponent } from "./component/modal/modal.component";
import { HclTemplateDirective } from "./directive/hcl-template.directive";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { A11yModule } from "@angular/cdk/a11y";
import { MenuComponent } from "./component/menu/menu.component";
import { StepperComponent } from "./component/stepper/stepper.component";
import { DropDownComponent } from "./component/drop-down/drop-down.component";
import { PaginatorComponent } from "./component/paginator/paginator.component";
import { AutoCompleteComponent } from "./component/auto-complete/auto-complete.component";
import { FileInputComponent } from "./component/file-input/file-input.component";
import { TabsComponent } from "./component/tabs/tabs.component";
import { CheckboxComponent } from "./component/checkbox/checkbox.component";
import { MultiSelectComponent } from "./component/multi-select/multi-select.component";
import { BreadcrumbComponent } from "./component/breadcrumb/breadcrumb.component";
import { AccordionComponent } from "./component/accordion/accordion.component";
import { DropDownMenuComponent } from "./component/drop-down-menu/drop-down-menu.component";
import { ButtonComponent } from "./component/button/button.component";
import { DraggableDirective } from "./directive/drag-drop.directive";
import { SuccessBarComponent } from "./component/success-bar/success-bar.component";
import { SideNavContainerComponent } from "./component/side-nav-container/side-nav-container.component";
import { ContainerSideNavComponent } from "./component/side-nav-container/container-side-nav/container-side-nav.component";
import { ContainerBodyComponent } from "./component/side-nav-container/container-body/container-body.component";
import { SideBarComponent } from "./component/side-bar/side-bar.component";
import { AngularMaterialModule } from "./angular-material/angular-material.module";
import { AgGridModule } from "ag-grid-angular";
import { InputComponent } from "./component/input/input.component";
import { ProgressSpinnerComponent } from "./component/progress-spinner/progress-spinner.component";
import { ProgressBarComponent } from "./component/progress-bar/progress-bar.component";
import { NotificationComponent } from "./component/notification/notification.component";
import { TextareaComponent } from "./component/textarea/textarea.component";
import { CdkStepperModule } from "@angular/cdk/stepper";
import { QueryBuilderComponent } from "./component/query-builder/query-builder.component";

import { DatePickerComponent } from "./component/date-picker/date-picker.component";
import { SharedModule } from "./calendarModule/common/shared";
import { Calendar } from "./calendarModule/calendar";
import { TreeComponent } from "./component/tree/tree.component";
import { PopupMenuComponent } from "./component/popup-menu/popup-menu.component";
import { SlideToggleComponent } from "./component/slide-toggle/slide-toggle.component";
import { RadioComponent } from "./component/radio/radio.component";
import { HclTooltipDirective } from "./directive/hcl-tooltip.directive";
import { HclTooltipComponent } from "./component/tooltip/tooltip.component";
import { SliderComponent } from "./component/slider/slider.component";
import { SlideOutComponent } from "./component/slide-out/slide-out.component";
import { SlideOutPanelComponent } from "./component/slide-out-panel/slide-out-panel.component";
import { NaturalNumberDirective } from "./directive/hcl-number-directive";

import { NgxTributeModule } from "ngx-tribute";
import { TourComponent } from "./component/tour/tour.component";
import { SplitContainerComponent } from "./component/split-container/split-container.component";
import { SplitAreaComponent } from "./component/split-container/split-area/split-area.component";
import { HCLTourDirective } from "./directive/hcl-tour.directive";
import { AssetPickerComponent } from "./component/asset-picker/asset-picker.component";
import { AssetResultComponent } from "./component/asset-result/asset-result.component";
import { FoldersComponent } from "./component/folders/folders.component";
// tslint:disable-next-line: max-line-length
import { AutoCompleteTreeComponent } from "./component/auto-complete-tree/auto-complete-tree";
import { TimePickerComponent } from "./component/time-picker/time-picker.component";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { ContextMenuComponent } from "./component/context-menu/context-menu.component";
import { PopoverComponent } from "./component/popover/popover.component";
import { PopoverTrigger } from "./directive/popover.directive";
import { IntegerDirective } from "./directive/hcl-integer-directive";
import { SelectBoxComponent } from "./component/select-box/select-box.component";
import { HclImagePreloadDirective } from "./directive/hcl-image-preload.directive";
import { FolderV2Component } from "./component/folder-v2/folder-v2.component";
import { FolderV2ListingComponent } from "./component/folder-v2/folder-v2-listing/folder-v2-listing.component";
import { FolderV2ListingElementComponent } from "./component/folder-v2/folder-v2-listing-element/folder-v2-listing-element.component";
import { FolderV2SortComponent } from "./component/folder-v2/folder-v2-sort/folder-v2-sort.component";
import { FolderV2AddComponent } from "./component/folder-v2/folder-v2-add/folder-v2-add.component";
import { HclSuggestionsDirective } from "./directive/hcl-suggestions.directive";
import { NgxSkeletonLoaderModule } from "ngx-skeleton-loader";
import { SkeletonLoaderComponent } from "./component/skeleton-loader/skeleton-loader.component";
import { PickerModule } from "@ctrl/ngx-emoji-mart";
import { EmojiPickerComponent } from "./component/emoji-picker/emoji-picker.component";
import { TreeV2Component } from "./component/tree-v2/tree-v2.component";
import { ActionMetadataComponent } from "./component/cognitive-tagging/action-metadata/action-metadata.component";
import { AttributeMappingComponent } from "./component/cognitive-tagging/attribute-mapping/attribute-mapping.component";
import { CognitiveTaggingComponent } from "./component/cognitive-tagging/cognitive-tagging.component";
import { WeekSelectorComponent } from "./component/week-selector/week-selector.component";
import { QueryBuilderV2Component } from "./component/query-builder-v2/query-builder-v2.component";
import { FolderSelectionComponent } from "./component/folder-selection/folder-selection.component";
import { AutoCompleteSearchV2Component } from "./component/auto-complete-search-v2/auto-complete-search-v2";

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    GridsterModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    CdkStepperModule,
    AngularMaterialModule,
    DragDropModule,
    A11yModule,
    SharedModule,
    NgxTributeModule,
    NgxSkeletonLoaderModule,
    PickerModule,
  ],
  declarations: [
    ToolbarComponent,
    SideNavComponent,
    PanelComponent,
    DragDropComponent,
    ModalComponent,
    HclTemplateDirective,
    MenuComponent,
    StepperComponent,
    DropDownComponent,
    PaginatorComponent,
    AutoCompleteComponent,
    FileInputComponent,
    TabsComponent,
    CheckboxComponent,
    AccordionComponent,
    CheckboxComponent,
    MultiSelectComponent,
    BreadcrumbComponent,
    DropDownMenuComponent,
    DraggableDirective,
    SuccessBarComponent,
    SideNavContainerComponent,
    ContainerSideNavComponent,
    ContainerBodyComponent,
    SideBarComponent,
    InputComponent,
    ButtonComponent,
    ProgressSpinnerComponent,
    ProgressBarComponent,
    QueryBuilderComponent,
    NotificationComponent,
    TextareaComponent,
    Calendar,
    AssetPickerComponent,
    AssetResultComponent,
    DatePickerComponent,
    TreeComponent,
    PopupMenuComponent,
    SlideToggleComponent,
    RadioComponent,
    HclTooltipDirective,
    HclTooltipComponent,
    SliderComponent,
    SlideOutComponent,
    SlideOutPanelComponent,
    NaturalNumberDirective,
    IntegerDirective,
    TourComponent,
    SplitContainerComponent,
    SplitAreaComponent,
    HCLTourDirective,
    FoldersComponent,
    AutoCompleteTreeComponent,
    TimePickerComponent,
    ContextMenuComponent,
    PopoverComponent,
    PopoverTrigger,
    SelectBoxComponent,
    HclImagePreloadDirective,
    FolderV2Component,
    FolderV2ListingComponent,
    FolderV2ListingElementComponent,
    FolderV2SortComponent,
    FolderV2AddComponent,
    HclSuggestionsDirective,
    SkeletonLoaderComponent,
    EmojiPickerComponent,
    TreeV2Component,
    CognitiveTaggingComponent,
    AttributeMappingComponent,
    ActionMetadataComponent,
    WeekSelectorComponent,
    QueryBuilderV2Component,
    FolderSelectionComponent,
    AutoCompleteSearchV2Component
  ],
  exports: [
    ToolbarComponent,
    SideNavComponent,
    PanelComponent,
    DragDropComponent,
    ModalComponent,
    HclTemplateDirective,
    MenuComponent,
    StepperComponent,
    DropDownComponent,
    PaginatorComponent,
    AutoCompleteComponent,
    FileInputComponent,
    TabsComponent,
    CheckboxComponent,
    AccordionComponent,
    CheckboxComponent,
    MultiSelectComponent,
    BreadcrumbComponent,
    DropDownMenuComponent,
    DraggableDirective,
    DragDropModule,
    SuccessBarComponent,
    SideNavContainerComponent,
    ContainerSideNavComponent,
    ContainerBodyComponent,
    SideBarComponent,
    InputComponent,
    ButtonComponent,
    ProgressSpinnerComponent,
    ProgressBarComponent,
    QueryBuilderComponent,
    NotificationComponent,
    TextareaComponent,
    Calendar,
    AssetPickerComponent,
    DatePickerComponent,
    SharedModule,
    TreeComponent,
    PopupMenuComponent,
    SlideToggleComponent,
    RadioComponent,
    HclTooltipDirective,
    HclTooltipComponent,
    SliderComponent,
    SlideOutComponent,
    SlideOutPanelComponent,
    NaturalNumberDirective,
    IntegerDirective,
    NgxTributeModule,
    TourComponent,
    SplitContainerComponent,
    SplitAreaComponent,
    HCLTourDirective,
    FoldersComponent,
    AutoCompleteTreeComponent,
    TimePickerComponent,
    ContextMenuComponent,
    PopoverTrigger,
    PopoverComponent,
    SelectBoxComponent,
    HclImagePreloadDirective,
    FolderV2Component,
    FolderV2AddComponent,
    HclSuggestionsDirective,
    SkeletonLoaderComponent,
    EmojiPickerComponent,
    TreeV2Component,
    CognitiveTaggingComponent,
    AttributeMappingComponent,
    ActionMetadataComponent,
    WeekSelectorComponent,
    QueryBuilderV2Component,
    FolderSelectionComponent,
    AutoCompleteSearchV2Component
  ],
  providers: [],
})
export class HclAngularWidgetsLibComponentModule {}
