.folder-label-minimized {
    -webkit-writing-mode: vertical-lr;
    -ms-writing-mode: tb-lr;
    writing-mode: vertical-lr;
    transform: rotate(180deg);
    line-height: 20px;
    font-size: 20px;
    color: #6d7692;
    -webkit-text-orientation: sideways;
    text-orientation: sideways;
  }
  
  .minimized-container {
    display: flex;
    align-items: center;
  }
  
  .folder-container {
    &.folder-default-state {
      height: 100%;
      width: 400px;
      -webkit-transition: width 0.5s linear;
      transition: width 0.5s linear;
    }
    &.folder-minimized-state {
      height: 100%;
      width: 60px !important;
      -webkit-transition: width 0.5s linear;
      transition: width 0.5s linear;
    }
  
    &.hide-folder-panel {
      width: 0 !important;
      visibility: hidden;
      -webkit-transition: width 0.5s linear;
      transition: width 0.5s linear;
    }
    .hcl-card {
      background-color: #f5f5f5;
      height: 100%;
      display: block;
      overflow: hidden;
      box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.125);
      border: 1px solid rgba(0, 0, 0, 0.125);
      // border-radius: 0.25em;
      .hcl-card-body {
        display: block;
        height: 100%;
        padding: 20px;
        .folder-body {
          width: 100%;
          height: calc(100% - 45px);
  
          .folder-v2-main-container {
            height: 100%;
            .folder-v2-container {
              overflow-y: hidden;
              // height: calc(100% - 125px) !important;
            }
          }
        }
      }
    }
  }
  
  @media only screen and (-webkit-min-device-pixel-ratio: 2),
    only screen and (min--moz-device-pixel-ratio: 2),
    only screen and (-o-min-device-pixel-ratio: 2/1),
    only screen and (min-device-pixel-ratio: 2),
    only screen and (min-resolution: 192dpi),
    only screen and (min-resolution: 2dppx) {
    /* Retina-specific stuff here */
    .folder-container {
      .hcl-card {
        .folder-v2-container {
          // height: 420px !important;
        }
      }
    }
  }
  
  .folder-header {
    height: 30px;
    width: 100%;
    display: block;
    transition: width 0.5s;
    font-family: Roboto;
    color: #6d7692;
    .folder-list-label {
      width: auto;
      font-size: 20px;
      line-height: 24px;
      display: table;
      float: left;
      height: 100%;
      & > span {
        display: table-cell;
        vertical-align: middle;
      }
    }
    .icon-container {
      float: right;
      display: inline-flex;
      .line-seperator {
        width: 1px;
        height: 30px;
        background-color: #bcbbbb;
      }
      .nav-icon-container {
        margin-left: 8px;
        margin-top: 4px;
        font-size: 14px;
        i {
          cursor: pointer;
        }
      }
    }
  }
  