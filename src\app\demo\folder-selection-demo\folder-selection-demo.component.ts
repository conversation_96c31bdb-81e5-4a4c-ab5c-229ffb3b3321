import { HttpHeaders } from "@angular/common/http";
import { Component, OnInit, ViewChild } from "@angular/core";
import { HclFolderSelectionService } from "projects/hcl-angular-widgets-lib/src/lib/service/hcl-folder-selection.service";
import {
  ButtonConf,
  FolderSelectionConf,
  SideBarComponent,
} from "projects/hcl-angular-widgets-lib/src/public_api";

@Component({
  selector: "app-folder-selection-demo",
  templateUrl: "./folder-selection-demo.component.html",
  styleUrls: ["./folder-selection-demo.component.scss"],
})
export class FolderSelectionDemoComponent implements OnInit {
  @ViewChild(SideBarComponent) sidebarContainer: SideBarComponent;

  config: FolderSelectionConf;

  openButtonConfig: ButtonConf = {
    buttonType: "stroked",
    color: "accent",
    borderRadius: 5,
    name: "openBtn",
    type: "button",
    styleClass: "medium-btn",
    value: "Open",
  };
  showSidebar: boolean = false;

  dummyData = [
    {
      id: 7,
      displayName: "Custom policy",
    },
  ];

  constructor(private folderService: HclFolderSelectionService) {}

  ngOnInit(): void {
    const headers: HttpHeaders = new HttpHeaders()
      .set("m_user_name", "pn")
      .set("m_tokenId", "1680518816246-4-TuFNea0t-GLYX-14NVYyk3-ZI1v-AnZBDuO1")
      .set("api_auth_mode", "manager")
      .set("401", "ignore")
      .set("client_app_id", "segmentcentral");

    this.config = {
      showSelectedItemDetails: true,
      itemType: "segments",
      applicationRootFolder: 3,
      applicationBaseURL:
        "http://lp2-ap-51815377.prod.hclpnp.com:7010/SegmentCentral",
      applicationHeaders: headers,
      translations: {
        selectedItemSectionTitle: "Selected segments",
        selectionSectionTitle: "Move here",
        cancle: "Cancle",
        selectButtonCopy: "Move here",
        itemTypeLabel: "segments",
        modelTitle: "Move segments",
        rootFolderName: "All segments",
        unableToFetchData: "Unable to fetch folder data. Please try again.",
        noFolderData: "No folders available.",
      },
    };
  }

  openPanel() {
    this.showSidebar = true;
    this.sidebarContainer.openSideBar();
    setTimeout(() => {
      this.folderService.sendItemsToMoveFolders(this.dummyData);
    }, 100);
  }

  closeSidebarPanel() {
    console.log("closing main panel");
    this.sidebarContainer.close("close");
    this.showSidebar = false;
  }

  folderSelected(event) {
    // business logic goes here to handle if side bar needs to be closed
    console.log("selected folder details : ", event);
  }
}
