canvas {
  position: absolute;
  left: -100%;
}

.grabbable {
  cursor: move; /* fallback if grab cursor is unsupported */
  cursor: grab;
  cursor: -moz-grab;
  cursor: -webkit-grab;
}

/* (Optional) Apply a "closed-hand" cursor during drag operation. */
.grabbable:active {
  cursor: grabbing;
  cursor: -moz-grabbing;
  cursor: -webkit-grabbing;
}

.cell-tooltip {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 10px;
}

.cell-drag-icon {
  font-size: 10px;
  color: #959595;
  line-height: 46px;
  opacity: 0;
  padding-top: 0.5px;
  position: absolute;
  text-align: center;
  margin-left: -5px;
}

.ag-cell.ag-cell-first-right-pinned:not(.ag-cell-range-left) {
  height: 48px !important;
  position: static !important;
}

#hoverActionContainer {
  width: 24px;
  height: 100%;

  .hoverContainer {
    position: absolute;
    background: #ececec;
    z-index: 1000;
    height: 100%;
    top: 0;
    right: 23px !important;
    display: none;
  }

  .hcl-icon-kabab {
    font-size: 18px;
    line-height: 46px;
    padding-top: 3.5px;
    position: absolute;
    text-align: center;
    margin-left: -9px;
    width: 24px;
    cursor: pointer;

    color: #0078d8;

    &:hover {
      color: #f5821e;
    }
  }
  .disable-hover-icon {
    pointer-events: none;
    opacity: 0.5;
  }
}

.visible-actions {
  opacity: 1;
  pointer-events: all;
  display: flex !important;
  padding: 0 10px;
  background: #c6c6c6 !important;
  align-items: center;
}

hcl-custom-cell {
  font-family: Roboto-regular, Roboto, sans-serif;
  font-size: 14px;
  //line-height: 16px;
}

@media only screen and (-webkit-min-device-pixel-ratio: 2),
  only screen and (min--moz-device-pixel-ratio: 2),
  only screen and (-o-min-device-pixel-ratio: 2/1),
  only screen and (min-device-pixel-ratio: 2),
  only screen and (min-resolution: 192dpi),
  only screen and (min-resolution: 2dppx) {
  /* Retina-specific stuff here */
  .cell-tooltip {
    &::after {
      content: "";
      display: block;
    }
  }
}
