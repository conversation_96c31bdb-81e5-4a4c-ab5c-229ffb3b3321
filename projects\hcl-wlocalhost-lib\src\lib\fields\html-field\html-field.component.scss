.error-container {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 24px;
}

.html-snippet {
  .h1, .h2, .h3, .h4, .h5, .h6 {
    font-weight: inherit;
    line-height: inherit;
  }
  h1, h2, h3, h4, h5, h6 {
    font-weight: bold;
  }
  h1 {
    font-size: 2em;
    margin: 0.67em 0;
  }
  h2 {
    font-size: 1.5em;
    margin: 0.83em 0;
  }
  h3 {
    font-size: 1.17em;
    margin: 1em 0;
  }
  h4 {
    font-size: 1em;
    margin: 1.33em 0;
  }
  h5 {
    font-size: .83em;
    margin: 1.67em 0;
  }
  h6 {
    font-size: .67em;
    margin: 2.33em 0;
  }
  p {
    margin: 1em 0;
  }
  code, kbd, samp {
    font-family: monospace, monospace;
    font-size: 1em;
  }
  dfn {
    font-style: italic;
  }
  blockquote {
    display: block;
    margin: 1em 40px;
  }
  ul {
    display: block;
    list-style-type: disc;
    margin: 1em 0;
    padding-left: 40px;
  }
  li {
    display: list-item;
    text-align: -webkit-match-parent;
  }
  ol {
    display: block;
    list-style-type: decimal;
    margin: 1em 0;
    padding-left: 40px;
  }
  dl {
    display: block;
    margin: 1em 0;
  }
  s a span {
    text-decoration: inherit !important;
  }
  u a span {
    text-decoration: inherit !important;
  }
  span a.droppable.custom-link.link-droppable {
    color: inherit;
  }    
  a {
    color: #0020EE;
    text-decoration: none;
    cursor: pointer;
  }
  .required-input-field::after {
    content: '*';
    color: red;
    padding: 2px;
  }
  .textblock {
    background-color: lightblue;
  }
  ul {
    display: block;
    list-style-type: disc;
    margin-top: 0px;
    margin-bottom: 0px;
    margin-block-start: 0px;
    margin-block-end: 0px;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 40px;
  }
  ul>li {
    padding-left: 0px;
    list-style-type:default;
  } 
      
  .init-msg{
    display: flex;
    width: 100%;
    justify-content: center;
    color: #959595;
    text-align: center;
    font-family: Montserrat;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    height: 40px;
    width: 200px;
    margin: 0 40%;
    padding-top: 10px;
    position: absolute;
    top: 10px;
  }

   .rule-count-container{
    width: 27px;
    height: 27px;   
    position: absolute;   
    left: 35px;
    top: -14px;
    z-index: 1;

    span.rule-count{
      width: 27px;
      height: 27px;   
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      background: #3f51b5;
      box-shadow: 0 0 0 1.5px #fff;
      border-radius: 50%;
      color: white;
    }

    .dynamic-rule-button {
      visibility:hidden;
      border: 1px solid #fff;
      z-index: -1;
      top: -1px;
      left: 18px;
    }

  } 
}
