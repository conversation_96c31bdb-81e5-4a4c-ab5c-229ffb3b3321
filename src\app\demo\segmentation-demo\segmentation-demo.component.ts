import { HttpHeaders } from "@angular/common/http";
import { Component, OnInit } from "@angular/core";
import { SegmentationConfig } from "projects/hcl-segmentation-lib/src/lib/segmentation-config";
import { HclSegmentationLibService } from "projects/hcl-segmentation-lib/src/public-api";

@Component({
  selector: "app-segmentation-demo",
  templateUrl: "./segmentation-demo.component.html",
  styleUrls: ["./segmentation-demo.component.scss"],
})
export class SegmentationDemoComponent implements OnInit {
  config: SegmentationConfig;
  // segHeaders: HttpHeaders;
  constructor(private hclSegmentationLibService: HclSegmentationLibService) {}

  ngOnInit(): void {
    // this.segHeaders = new HttpHeaders()
    //   .set("m_user_name", "pn")
    //   .set("m_tokenId", "1680518816246-4-TuFNea0t-GLYX-14NVYyk3-ZI1v-AnZBDuO1")
    //   .set("api_auth_mode", "manager")
    //   .set("401", "ignore")
    //   .set("client_app_id", "segmentcentral");

    this.config = {
      applicationMode: "create",
      audienceName: "Customer",
      audienceTableName: "dbo.UA_Audiences",
      audienceTablePhysicalName: "dbo.UA_Audiences",
      audienceTableId: 1,
      audienceDataSource: "jndiTest",
      segmentBaseUrl:
        "http://lp2-ap-51815377.prod.hclpnp.com:7010/SegmentCentral",
      // audienceBaseUrl:
      //   "http://lp2-ap-51815377.prod.hclpnp.com:7010/AudienceCentral",
      // segmentHeaders: this.segHeaders,
      applicationUser: "asm_admin",
      segmentToken: "1681104934354-2-U40e7u2L-wVeg-o0McZpbq-PXWV-jAPLIpy3",
      // audienceToken: "1680781425300-2-YYwKVtEw-UH8n-OhWjm4Z7-EA7d-dWPXLiaR",
      segRootFolderId: 3,
      platformBaseUrl: "http://lp2-ap-51815377.prod.hclpnp.com:7001/unica",
      reLogin: (callback?: () => void) => {
        this.hclSegmentationLibService.segmentHeaders = new HttpHeaders();
        if (callback) {
          callback();
        }
      },
      // audReLogin: (callback?: () => void) => {
      //   this.hclSegmentationLibService.audienceHeaders = new HttpHeaders();
      //   if (callback) {
      //     callback();
      //   }
      // },
      translations: {
        pageTitle: "Create Segment",
        audienceLevel: "Audience level",
        sourceTable: "Source table",
        ruleBuilder: "Rule builder",
        qualifiedRecords: "Qualified records",
        cancel: "Cancel",
        testRun: "Test run",
        save: "Save",
        savePublish: "Save and publish",
        selectFolderToSaveSegment: "Select folder to save segment",
        browseFolder: "Browse folder",
        unableToFetchData: "Unable to fetch data, please try again",
        segmentSuccessMessage: "Segment created successfully",
        segSavePublishSuccessMessage:
          "Segment saved and published successfully",
        permissionError: "This action requires proper privileges.",
        unsavedChanges: "Unsaved Changes",
        confirm: "Are you sure?",
        confirmUnsavedChanges:
          "there are some unsaved changes; after cancelling, you will not be able to recover them from system",
        yes: "Yes",
        no: "No",
      },
      donutChartConfig: {
        withoutTimeDatePipeFormat: "MM/dd/yyyy",
        translations: {
          buildRulesAndRunToView: "Build rules and run to view",
          ofTotalRecords: "of total records",
        },
      },
      queryBuilderConfig: {
        jsonData: null,
        columnFields: [
          // {
          //   fieldLabel: "ID",
          //   fieldValue: "ID",
          //   fieldDataType: "Integer",
          //   fieldLength: 19,
          // },
          // {
          //   fieldLabel: "Name",
          //   fieldValue: "Name",
          //   fieldDataType: "String",
          //   fieldLength: 64,
          // },
          // {
          //   fieldLabel: "Fields",
          //   fieldValue: "Fields",
          //   fieldDataType: "String",
          //   fieldLength: 1024,
          // },
          // {
          //   fieldLabel: "CHTableName",
          //   fieldValue: "CHTableName",
          //   fieldDataType: "String",
          //   fieldLength: 64,
          // },
          // {
          //   fieldLabel: "DCHTableName",
          //   fieldValue: "DCHTableName",
          //   fieldDataType: "String",
          //   fieldLength: 64,
          // },
          // {
          //   fieldLabel: "RHTableName",
          //   fieldValue: "RHTableName",
          //   fieldDataType: "String",
          //   fieldLength: 64,
          // },
          // {
          //   fieldLabel: "SEGTableName",
          //   fieldValue: "SEGTableName",
          //   fieldDataType: "String",
          //   fieldLength: 64,
          // },
        ],
        timeZone: "Asia/Calcutta",
        conditionConfigExpressionType: [
          { label: "is equal to", value: "eq" },
          { label: "not equal to", value: "neq" },
        ],
        conditionConfigStringType: [
          { label: "is equal to", value: "eq" },
          { label: "not equal to", value: "neq" },
          // { label: 'begins with', value: 'beginswith' },
          // { label: 'ends with', value: 'endswith' },
          // { label: 'contains', value: 'contains' },
          { label: "in", value: "in" },
          { label: "is null", value: "null" },
          { label: "is not null", value: "notnull" },
        ],
        conditionConfigNumberType: [
          { label: "is equal to", value: "eq" },
          { label: "not equal to", value: "neq" },
          // { label: 'is null', value: 'null' },
          // { label: 'is not null', value: 'notnull' },
          { label: "is greater than", value: "gt" },
          { label: "is greater than or equal to", value: "gte" },
          { label: "is less than", value: "lt" },
          { label: "is less than or equal to", value: "lte" },
          { label: "is between", value: "between" },
          // { label: 'is not in between', value: 'notbetween' },
          { label: "in", value: "in" },
          { label: "not in", value: "notin" },
        ],
        conditionConfigDateType: [
          { label: "is equal to", value: "eq" },
          { label: "not equal to", value: "neq" },
          { label: "in", value: "in" },
          { label: "after", value: "gt" },
          { label: "after or equal to", value: "gte" },
          { label: "before", value: "lt" },
          { label: "before or equal to", value: "lte" },
          { label: "is between", value: "between" },
          { label: "is not in between", value: "notbetween" },
        ],
        conditionConfigBooleanType: [
          { label: "is true", value: "true" },
          { label: "is false", value: "false" },
        ],
        emitQueryJsonOnFormChange: true,
        keepOneRulePresent: false,
        // enableExpressionBuilder: true,
        disableQueryBuilder: false,
        showDatePicker: true,
        dropdownColumns: [],
        dynamicValues: [],
        needGroupName: false,
        translations: {
          cancelModalBtnLabel: "Cancel",
          deleteGroupModalMsg:
            "After deleting this group, you will not be able to recover it from the system.",
          deleteNestedGroupsModalMsg:
            "After deleting this group, all the nested groups will be deleted. You will not be able to recover it from the system.",
          areYouSure: "Are you sure?",
          addRuleLabel: "Add Rule",
          addGroupLabel: "Add Group",
          deleteGroupLabel: "Delete Group",
          deleteGroupsLabel: "Delete Groups",
          andLabel: "AND",
          orLabel: "OR",
          noResultFoundMsg: "No results found!",
          filterFieldsPlaceholder: "Type to search",
          fieldNamePlaceHolder: "Field",
          conditionPlaceHolder: "Condition",
          conditionValuePlaceHolder: "Value",
          requiredField: "Required field",
          fieldOptionsDidNotLoad: "Field options did not load",
          add: "Add",
          valueAlreadyPresent: "Value already present",
          toLabel: "to",
          more: "more",
          close: "Close",
          invalidDigitsRangeMsg: "From and to digits are out of range",
          invalidDateFormat: "please enter date in correct format",
          save: "Save",
          deleteExpressionLabel: "Delete Expression",
          deleteExpressionModalMsg:
            "You are deleting an expression you have build, you will not be able to recover it if deleted.",
          delete: "Delete",
          expressionInputTitle: "Expression (Write your function)",
          validExpression: "Expression is valid",
          subgroupLabel: "Subgroup(s)",
          ruleLabel: "Rule(s)",
        },
        dateConfigObj: {
          dateConfig: {
            monthNavigator: true,
            yearNavigator: true,
            isUCTDate: false,
            showTime: true,
            selectionMode: "",
            dateFormat: "dd/mm/yy",
            yearRange: "",
            showButtons: false,
            customHeader: "",
            customFooter: "",
            noOfMonth: 1,
            isInline: false,
            appendTo: "body",
            // name: 'datePicker',
            dateInputConfig: {
              placeholder: "value",
              value: "",
              autoComplete: "none",
              suffixIconClass: "hcl-icon-calendar",
              name: "search",
            },
            errorList: [
              {
                errorCondition: "required",
                errorMsg: "Required field",
              },
            ],
          },
          datePipeFormat: "MM/dd/yyyy hh:mm",
          dateLocaleJson: {
            firstDayOfWeek: 0,
            dayNames: [
              "Sunday",
              "Monday",
              "Tuesday",
              "Wednesday",
              "Thursday",
              "Friday",
              "Saturday",
            ],
            dayNamesShort: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
            dayNamesMin: ["S", "M", "T", "W", "Th", "F", "St"],
            monthNames: [
              "January",
              "February",
              "March",
              "April",
              "May",
              "June",
              "July",
              "August",
              "September",
              "October",
              "November",
              "December",
            ],
            monthNamesShort: [
              "Jan",
              "Feb",
              "Mar",
              "Apr",
              "May",
              "Jun",
              "Jul",
              "Aug",
              "Sept",
              "Oct",
              "Nov",
              "Dec",
            ],
            today: "Today",
            clear: "Clear",
            reset: "Reset",
            submit: "Submit",
            dateFormat: "Dateformat",
            weekHeader: "Weekheader",
            am: "am",
            pm: "pm",
          },
        },
      },
      segmentMetadatConfig: {
        textSizeValidationType: "",
        securityPolicies: {},
        securityPoliciesForSegment: {},
        securityPoliciesForFolder: {},
        translations: {
          segmentName: "Segment name",
          segmentNameRequired: "Segment name required",
          description: "Description",
          selectFolderToSaveSegment: "Select folder to save segment",
          browseFolder: "Browse folder",
          securityPolicy: "Security policy",
          usageCategory: "Usage category",
          exceedsMasLimit: "Exceeds max limit.",
          add: "Add",
          requiredField: "Required field.",
          unableToFetchData: "Unable to fetch folder data. Please try again.",
          invalidCharacters:
            "Invalid character(s) (%*?|:,<>&\\/\"'+$<tab>) found.",
        },
      },
      folderSelectionConf: {
        showSelectedItemDetails: false,
        itemType: "segments",
        applicationRootFolder: 3,
        applicationBaseURL:
          "http://lp2-ap-51815377.prod.hclpnp.com:7010/SegmentCentral",
        applicationHeaders: new HttpHeaders(),
        translations: {
          selectedItemSectionTitle: "Selected segments",
          selectionSectionTitle: "Select folder",
          cancle: "Cancle",
          selectButtonCopy: "Select",
          itemTypeLabel: "segments",
          // modelTitle: "Select file",
          rootFolderName: "All segments",
          unableToFetchData: "Unable to fetch folder data. Please try again.",
          folderCreateSegError:
            "The selected folder does not have permission for segment creation. Select an appropriate folder.",
          noFolderData: "No folders available.",
        },
      },
    };
  }
}
