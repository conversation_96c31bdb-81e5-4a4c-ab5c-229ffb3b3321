$title-font-color: #6d7692;
$title-font-family: <PERSON>ser<PERSON>;
$value-font-color: #444444;
$value-font-family: <PERSON>o;
$link-font-color: #0078d8;

.approval-items-info-container {
    padding-top: 35px;
    font-size: 14px;
    .items-available {
        font-size: 12px;
        font-family: $value-font-family;
        color: $title-font-color;
    }
    .items-to-approve-list {
        height: 85%;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 0;

        li {
            list-style: none;
            padding-bottom: 30px;
            .icon-container {
                flex: 1;
                font-size: 16px;
                color: $title-font-color;
            }
            .item-details-container {
                flex: 9;
                color: $title-font-color;
                .file-name {
                    font-family: $title-font-family;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                }
                .item-detail {
                    font-family: $value-font-family;
                    color: $title-font-color;
                    font-size: 12px;
                    padding-bottom: 5px;
                }
                .item-action {
                    .list-action-link {
                        color: $link-font-color;
                        font-family: $value-font-family;
                        font-size: 12px;
                    }
                }
                .item-notes {
                    max-height: 50px;
                    max-width: 500px;
                    width: auto;
                    overflow: hidden;
                    float: left;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }

        .hcl-icon-unknown-type,
        .hcl-icon-ms-word-type,
        .hcl-icon-pdf-type,
        .hcl-icon-ms-ppt-type,
        .hcl-icon-image-type,
        .hcl-icon-psd-type,
        .hcl-icon-csv-type,
        .hcl-icon-ms-excel-type,
        .hcl-icon-text-type,
        .hcl-icon-html-type,
        .hcl-icon-url-type,
        .hcl-icon-form-type,
        .hcl-icon-flowchart-type {
            &:before {
                font-size: 175%;
                padding: 15px;
                padding-top: 7px;
                cursor: pointer;
            }
        }
    }
    .no-data-container {
        text-align: center;
        position: absolute;
        top: 50%;
        left: 35%;
        .no-attachment-found {
            height: 29px;
            color: $title-font-color;
            font-family: $title-font-family;
            font-size: 24px;
            letter-spacing: 0;
            line-height: 29px;
        }
        .no-item-msg {
            height: 16px;
            color: $value-font-color;
            font-family: $value-font-family;
            font-size: 14px;
            letter-spacing: 0;
            line-height: 16px;
            padding-top: 10px;
        }
    }
}
