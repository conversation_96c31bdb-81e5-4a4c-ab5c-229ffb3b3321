import { Component, OnInit, OnD<PERSON>roy, Input } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { Approval } from '../../models/approval';
import { ApprovalSharedDataService } from '../../service/approval-shared-data.service';

@Component({
  selector: 'hcl-approval-basic-info',
  templateUrl: './approval-basic-info.component.html',
  styleUrls: ['./approval-basic-info.component.scss']
})
export class ApprovalBasicInfoComponent implements OnInit, OnDestroy {

  @Input() approval: Approval;
  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  utcTimezone: string = '(UTC +10:30)';

  constructor(private _sharedDataService: ApprovalSharedDataService) { }

  ngOnInit(): void {
  }

  transformStatus(data: Approval): string {
    return this._sharedDataService.getApprovalStatus(data);
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => {
      sub.unsubscribe();
    });
  }

}
