import { NgModule } from '@angular/core';
import { MessagesComponent } from './component/messages/messages.component';
import { MessageContainerComponent } from './component/message-container/message-container.component';
import {CommonModule} from '@angular/common';
import {HclAngularWidgetsLibModule} from 'hcl-angular-widgets-lib';
import { MessageContainerToolbarComponent } from './component/message-container-toolbar/message-container-toolbar.component';
import { MessengerComponent } from './component/messenger/messenger.component';
import { MessengerToolbarComponent } from './component/messenger-toolbar/messenger-toolbar.component';
import { SendMessageEditorComponent } from './component/send-message-editor/send-message-editor.component';



@NgModule({
  declarations: [
    MessagesComponent,
    MessageContainerComponent,
    MessageContainerToolbarComponent,
    MessengerComponent,
    MessengerToolbarComponent,
    SendMessageEditorComponent
  ],
  imports: [
    CommonModule,
    HclAngularWidgetsLibModule
  ],
  exports: [
    MessengerComponent
  ]
})
export class HclAngularMessengerLibModule { }
