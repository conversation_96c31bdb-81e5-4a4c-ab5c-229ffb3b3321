import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StackedSingleBarChartDemoComponent } from './stacked-single-bar-chart-demo.component';

describe('StackedSingleBarChartDemoComponent', () => {
  let component: StackedSingleBarChartDemoComponent;
  let fixture: ComponentFixture<StackedSingleBarChartDemoComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ StackedSingleBarChartDemoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StackedSingleBarChartDemoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});