import {
  AfterViewInit,
  Component, Do<PERSON>heck,
  ElementRef,
  EventEmitter,
  Input, OnChanges, OnDestroy,
  OnInit,
  Output,
  Renderer2, SimpleChanges,
  ViewEncapsulation
} from '@angular/core';
import {BaseChartRenderer} from '../../renderer/BaseChartRenderer';
import {CHART_TYPE, RendererFactory} from '../../renderer/RendererFactory';
import {ChartConfig, ChartSeries} from '../../config/chart-config';
import { Subscription} from 'rxjs';

@Component({
  selector: 'hcl-gantt-chart-v2',
  templateUrl: './gantt-chart.component.html',
  styleUrls: ['./gantt-chart.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class GanttChartComponent implements OnInit, AfterViewInit, OnDestroy {

  /**
   * The default chart renderer
   */
  private chartRenderer: BaseChartRenderer;
  /**
   * the subscription array that we have acquired & which we will need to clear on destroy
   * type {any[]}
   */
  private subscription: Subscription[] = [];
  /**
   * The configuration of the chart
   */
  @Input() config: ChartConfig;
  /**
   * A event emitter that will be invoked when the location of a node is updated
   * type {EventEmitter<{series: ChartSeries; data: any; value: {min: number; max: number}}>}
   */
  @Output() positionChanged: EventEmitter<{series: ChartSeries, data: any, value: {min: number, max: number}}>
                          = new  EventEmitter<{series: ChartSeries, data: any, value: {min: number, max: number}}>();
  /**
   * A event emitter that will be invoked when the dependency of a object is deleted
   * type {EventEmitter<{series: ChartSeries; data: any; parentId: string}>}
   */
  @Output() dependencyDeleted: EventEmitter<{series: ChartSeries, child: any, parent: any}>
                          = new  EventEmitter<{series: ChartSeries, child: any, parent: any}>();
  /**
   * A event emitter that will be invoked when the dependency is added on the object
   * type {EventEmitter<{series: ChartSeries; data: any; parentId: string}>}
   */
  @Output() dependencyAdded: EventEmitter<{series: ChartSeries, child: any, parent: any}>
                          = new  EventEmitter<{series: ChartSeries, child: any, parent: any}>();
  /**
   * The default constructor
   * renderer: we need to play with the DOM so we need the Renderer
   * element: we will add the Charting to the current element
   */
  constructor(private renderer: Renderer2,
              private element: ElementRef) { }

  /**
   * We need to create the renderer
   */
  ngOnInit() {
    // get the default renderer
    this.chartRenderer = (new RendererFactory()).getRenderer(CHART_TYPE.GANTT_CHART);
    // if we have the renderer then we will call the init & then render
    if (this.chartRenderer) {
      this.chartRenderer.init(this.config);
      // in case the data node position is changed we need to know
      this.subscription.push(this.chartRenderer.onDataNodePositionChange().subscribe(this.dataNodePositionUpdate.bind(this)));
      this.subscription.push(this.chartRenderer.onDependencyDeleted().subscribe(this.dataNodeDependencyDeleted.bind(this)));
      this.subscription.push(this.chartRenderer.onDependencyAdded().subscribe(this.dataNodeDependencyAdded.bind(this)));
    } else {
      console.error('No renderer found for the bar-chart');
    }
  }

  public refreshDataForIndex(index: number): void {
    this.chartRenderer.refreshDataForIndex(index);
  }

  /**
   * This function will re-render the chart
   */
  public reRender(): void {
    this.chartRenderer.reRender();
  }

  /**
   *
   * param {number} index
   */
  public hideDataAtIndex(index: number[]): void {
    this.chartRenderer.hideDataAtIndex(index);
  }

  /**
   * Called when the data node position is updated
   */
  private dataNodePositionUpdate(event: {series: ChartSeries, data: any, value: {min: number, max: number}}): void {
    this.positionChanged.emit(event);
  }
  /**
   * Called when the data node dependency is deleted
   */
  private dataNodeDependencyDeleted(event: {series: ChartSeries, child: any, parent: any}): void {
    this.dependencyDeleted.emit(event);
  }
  /**
   * Called when the data node dependency is added
   */
  private dataNodeDependencyAdded(event: {series: ChartSeries, child: any, parent: any}): void {
    this.dependencyAdded.emit(event);
  }
  /**
   * this function will update the series scale
   * param {string} displayIn
   */
  changeSeriesTimeScale(series: ChartSeries, displayIn: string): void {
    this.chartRenderer.changeAxisScale(series, displayIn);
  }
  /**
   * After the initialization we need to render the chart
   */
  ngAfterViewInit(): void {
    // if we have the renderer then we will call the init & then render
    if (this.chartRenderer) {
      this.chartRenderer.render(this.element);
    }
  }

  /**
   * Clear all the subscriptions
   */
  ngOnDestroy(): void {
    this.subscription.forEach((s: Subscription) => s.unsubscribe());
  }
}
