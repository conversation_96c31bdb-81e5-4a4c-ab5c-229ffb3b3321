<hcl-data-grid-v2 #offerListsGrid class="w-100 com-offer-grid" [config]="offerListsGridConf"
    (rowSelected)="rowSelected($event)" (rowUnSelected)="rowUnSelected($event)"
    (dataLoadedFromUrl)="gridDataLoaded($event)" (gridReady)="onGridReady($event)">
    <ng-template hclTemplate hclTemplateName="displayName" type="cell-renderer" let-cell>
        <div *ngIf="cell && cell.row" class="ellipsis" (click)="onCellClicked(cell)">
            <span *ngIf="cell.row.state" class="bullet-round mr-2" [ngClass]="{'green-bullet': cell.row.state ==='PUBLISHED',
        'grey-bullet': cell.row.state ==='RETIRED','orange-bullet': cell.row.state ==='DRAFT'}"></span>
            <span class="link">{{cell.row && cell.row[cell.col.field]}}</span>
        </div>
    </ng-template>
</hcl-data-grid-v2>
<div class="manage-selections">
    <div class="selected-offers" hclTooltip="{{ selectedOffersAndOls() }}">{{ selectedOffersAndOls() }}</div>
    <div class="manage-selections-button">
        <hcl-button class="mr-10px" [config]="manageSelectionsButtonConf" (click)="manageSelections($event)">
        </hcl-button>
    </div>
</div> 