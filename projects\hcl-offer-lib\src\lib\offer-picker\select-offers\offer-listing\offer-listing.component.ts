import { DatePipe } from '@angular/common';
import { Component, OnDestroy, OnInit, Output, ViewChild, EventEmitter, Input, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subscription, SubscriptionLike } from 'rxjs';

import * as _ from 'lodash';
import { DataGridV2Component } from 'hcl-data-grid-lib';
import { SideBarComponent } from 'hcl-angular-widgets-lib';
import { DataGridConf, DataGridPagination, HoverIcon } from 'hcl-data-grid-lib';
import { OfferDataService } from '../../offer-data.service';
import { SelectOffersService } from '../../select-offers.service';
import { ButtonConf } from 'hcl-angular-widgets-lib';


@Component({
  selector: 'hcl-offer-listing',
  templateUrl: './offer-listing.component.html',
  styleUrls: ['./offer-listing.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class OfferListingComponent implements OnInit, OnD<PERSON>roy {

  @ViewChild('offersGrid', { static: true }) offersGrid: DataGridV2Component;
  @ViewChild(SideBarComponent) sideBarComponentRef: SideBarComponent;

  @Output() offerSelectionUpdate: EventEmitter<any> = new EventEmitter<any>();
  @Input() config;

  paginatorConfig: DataGridPagination;
  offersGridConf: DataGridConf;
  hoverActions: HoverIcon[];
  offerId: number;
  defaultFolderId: number;
  subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  offerPermissionSubscription: Subscription;
  offerViewPermissionSubscription: Subscription;
  offersGridApi: any;
  manageSelectionsButtonConf: ButtonConf;
  variantMap = new Map<number, any>();

  constructor(private translate: TranslateService,
    private offerDataService: OfferDataService,
    public selectOffersService: SelectOffersService,
    private datePipe: DatePipe) {
  }

  ngOnInit() {
    this.setConfiguration();
    this.selectOffersService.selectedOffersData.forEach(offerData => {
      if (offerData.hasOwnProperty('variantId') && offerData.variantId) {
        this.variantMap.set(+offerData.offerId, offerData);
      }
    });
    this.subscriptionList.push(this.selectOffersService.getFolderId().subscribe(obj => {
      if (obj[0] === 'offers') {
        this.defaultFolderId = obj[1];
        this.offersGridConf.dataUrl = this.selectOffersService.getOffersBaseUrl(this.defaultFolderId);
        if (this.selectOffersService.lastFolderClicked === null || this.selectOffersService.lastFolderClicked !== this.defaultFolderId) {
          this.paginatorConfig.currentPageIndex = 0;
          this.selectOffersService.lastFolderClicked = this.defaultFolderId;
        }
        this.offersGrid.refreshData();
        this.selectOffersService.clearFolderId();
      }
    }));
    this.subscriptionList.push(this.selectOffersService.offerGridResize.subscribe(obj => {
      // we need to resize the grid
      if (this.offersGrid) {
        this.offersGrid.resizeColumnsToFit();
      }
    }));
    this.subscriptionList.push(this.selectOffersService.getOffersSearchData().subscribe(obj => {
      if (obj.length > 0) {
        this.offersGridConf.queryParams = {
          sort: this.selectOffersService.offersSortAndColumn,
          state: this.selectOffersService.offerAndOfferListState, search_param: encodeURI(obj[0]), lite: true
        };
        this.offersGridConf.dataUrl = this.selectOffersService.getOffersUrl();
        // this.paginatorConfig.currentPageIndex = 0;
        this.offersGrid.refreshData();
        this.selectOffersService.clearOffersSearchData();
      }
    }));
  }

  setConfiguration() {
    //  this.defaultFolderId = this.foldersService.offersFolder;
    this.paginatorConfig = {
      rowsPerPage: this.selectOffersService.offersPageSize,
      pageSizeArray: [10, 20, 50, 100],
      optionLabels: [10, 20, 50, 100].map(option => this.translate.instant('FIELDS.ROWS_OPTION', { value: option })),
      currentPageIndex: 0,
      rowsPerPageSuffix: this.translate.instant('FIELDS.ROWS'),
      total: this.translate.instant('ASSETPICKER.TOTAL'),
      firstLabelString: this.translate.instant('BUTTONS.FIRST'),
      prevLabelString: this.translate.instant('BUTTONS.PREV'),
      nextLabelString: this.translate.instant('BUTTONS.NEXT'),
      lastLabelString: this.translate.instant('BUTTONS.LAST')
    };

    this.offersGridConf = {
      scrollHeight: 380,
      massageData: (data: any) => {
        return data;
      },
      columns: [
        // {
        //   field: 'icon',
        //   header: this.translate.instant('LIST_OFFER_TEMPLATES.HEADERS.OFFER_TEMPLATE_NAME'),
        //   colId: 'icon',
        //   sortable: true
        // },
        {
          field: 'displayName',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.OFFER_NAME'),
          colId: 'displayName',
          sortable: true,
          autoResizeToFit: true,
          minWidth: 150,
          rendererTemplateName: 'displayName',
          tooltip: {
            getTooltip: (attr: any) => {
              return attr.displayName;
            }
          },
          cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.isRetired === true;
            }
          }
        },
        {
          field: 'offerCodes',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.OFFER_CODE'),
          colId: 'offerCodes',
          autoResizeToFit: true,
          sortable: true,
          minWidth: 150,
          useDefaultRenderer: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return this.returnOfferCodes(attr);
            }
          },
          dataFormatter: (attr: any) => {
            return this.returnOfferCodes(attr.data);
          },
          cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.isRetired === true;
            }
          }
        },
        {
          field: 'description',
          header: this.translate.instant('CREATE_OFFER.LABELS.DESCRIPTION'),
          colId: 'description',
          autoResizeToFit: true,
          sortable: true,
          minWidth: 150,
          useDefaultRenderer: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return attr.description;
            }
          },
          cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.isRetired === true;
            }
          }
        },
        {
          field: 'parameterizedAttributes',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.CHANNELS'),
          colId: 'parameterizedAttributes',
          useDefaultRenderer: true,
          tooltip: {
            getTooltip: (attr: any) => {
              if (this.variantMap.has(+attr.id)) {
                return this.variantChannel(this.variantMap.get(+attr.id));
              } else if (this.returnChannel(attr)) {
                return this.returnChannel(attr);
              }
            }
          },
          dataFormatter: (attr: any) => {
            if (this.variantMap.has(+attr.data.id)) {
              return this.variantChannel(this.variantMap.get(+attr.data.id));
            } else if (this.returnChannel(attr.data)) {
              return this.returnChannel(attr.data);
            } else {
              return '';
            }
          },
          autoResizeToFit: true,
          minWidth: 150,
          cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.isRetired === true;
            }
          }
        },
        {
          field: 'state',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.STATUS'),
          colId: 'state',
          autoResizeToFit: true,
          minWidth: 150,
          useDefaultRenderer: true,
          tooltip: {
            getTooltip: (attr: any) => {
              if (attr.state === 'RETIRED') {
                return this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.RETIRED');
              } else if (attr.state === 'PUBLISHED') {
                return this.translate.instant('TITLES.PUBLISHED');
              } else if (attr.state === 'DRAFT') {
                return this.translate.instant('TITLES.DRAFT');
              }
            }
          },
          dataFormatter: (attr: any) => {
            if (attr.data.state === 'RETIRED') {
              return this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.RETIRED');
            } else if (attr.data.state === 'PUBLISHED') {
              return this.translate.instant('TITLES.PUBLISHED');
            } else if (attr.data.state === 'DRAFT') {
              return this.translate.instant('TITLES.DRAFT');
            }
          },
          cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.isRetired === true;
            }
          }
        },
        {
          field: 'effectiveDateFlag',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.EFF_EXP_DATES'),
          colId: 'effectiveDateFlag',
          autoResizeToFit: true,
          minWidth: 150,
          useDefaultRenderer: true,
          tooltip: {
            getTooltip: (attr: any) => {
              if (this.variantMap.has(+attr.id)) {
                return this.returnVariantEffAndExpDate(attr);
              } else {
                return this.returnEffAndExpDate(attr);
              }
            }
          },
          dataFormatter: (attr: any) => {
            if (this.variantMap.has(+attr.data.id)) {
              return this.returnVariantEffAndExpDate(attr.data);
            } else {
              return this.returnEffAndExpDate(attr.data);
            }
          },
          cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.isRetired === true;
            }
          }
        }
      ],
      dataUrl: this.selectOffersService.getOffersBaseUrl(this.selectOffersService.offersFolder ?
        this.selectOffersService.offersFolder : this.defaultFolderId),
      rowSelectMode: this.selectOffersService.rowSelectMode,
      pagination: this.paginatorConfig,
      noRowsTemplate: this.translate.instant('MESSAGES.NO_DATA_ON_HTTP_RESPONSE'),
      loadingTemplate: this.translate.instant('MESSAGES.LOADING'),
      suppressRowClickSelection: true,
      suppressDragLeaveHidesColumns: true,
      queryParams: { sort: this.selectOffersService.offersSortAndColumn, lite: true, state: this.selectOffersService.offerAndOfferListState }
    };

    this.manageSelectionsButtonConf = {
      color: 'accent',
      buttonType: 'mat',
      type: 'button',
      value: this.translate.instant('OFFER_PICKER.TITLES.MANAGE_SELECTIONS'),
      name: 'removeMapping',
      disabled: false
    };
  }

  variantChannel(offerData) {
    let channel = '';
    if (offerData.hasOwnProperty('variantAttributes') && offerData.variantAttributes.length) {
      offerData.variantAttributes.some((attribute) => {
        if (attribute.id === 10) {
          channel = attribute.value;
          return;
        }
      });
    }
    return channel;
  }



  onCellClicked(cell) {
    if (cell.row && cell.row.id) {
      if (this.variantMap.has(+cell.row.id)) {
        this.selectOffersService.selectedVariantId = this.variantMap.get(+cell.row.id).variantId;
      } else {
        this.selectOffersService.selectedVariantId = null;
      }

      this.selectOffersService.offerName = cell.row.displayName;
      this.selectOffersService.viewRoute = 'listOffers';
      this.selectOffersService.sendViewOfferClicked(+cell.row.id);
    }
  }

  /**
  * A callback that will be called when the grid is ready and loaded
  * @param data
  */
  onGridReady(data) {
    this.offersGridApi = data.params.api;
  }

  /**
 * A callback that will be called whenever data for the grid is loaded
 */
  gridDataLoaded(data) {
    if (this.selectOffersService.selectedOffersData.length > 0) {
      const commonRows: any[] = data.content.filter(d => this.selectOffersService.selectedOffersData.some((row) => d.id === row.offerId));
      if (commonRows.length > 0) {
        commonRows.forEach((d) => {
          setTimeout(() => {
            this.offersGridApi.forEachNode((node) => {
              if (node.data.id === d.id) {
                node.setSelected(true);
              }
            });
          }, 300);
        });
      }
    }
  }

  rowSelected(data: any) {
    if (data.data && !this.selectOffersService.selectedOffersData.some(offer => offer.offerId === data.data.id)) {
      let offerData;

      if (this.variantMap.has(+data.data.id)) {
        offerData = {
          offerId: data.data.id,
          offerDisplayName: data.data.displayName,
          variantId: this.variantMap.get(+data.data.id).variantId,
          variantDisplayName: this.variantMap.get(+data.data.id).variantDisplayName,
          offerCode: this.returnOfferCodes(data.data),
          variantAttributes: this.variantMap.get(+data.data.id).variantAttributes,
          offerAttributes: null,
          state: data.data.state
        };
      } else {
        offerData = {
          offerId: data.data.id,
          offerDisplayName: data.data.displayName,
          variantId: null,
          variantDisplayName: null,
          offerCode: this.returnOfferCodes(data.data),
          variantAttributes: null,
          offerAttributes: this.returnVariantAttributes(data.data),
          state: data.data.state
        };
      }


      if (this.selectOffersService.selectedOffersData.length) {
        let hasOffer = false;
        hasOffer = this.selectOffersService.selectedOffersData.some((selectedData, index) => {
          if (+selectedData.offerId === +data.data.id) {
            return true;
          }
        });
        if (!hasOffer) {
          this.selectOffersService.selectedOffersData.push(offerData);
        }
      } else {
        this.selectOffersService.selectedOffersData.push(offerData);
      }
      this.offerSelectionUpdate.emit();
    }
  }

  rowUnSelected(data: any) {
    if (data.data) {
      this.selectOffersService.selectedOffersData.some((selectedData, index) => {
        if (+selectedData.offerId === +data.data.id) {
          this.selectOffersService.selectedOffersData.splice(index, 1);
          return;
        }
      });
      this.offerSelectionUpdate.emit();
    }
  }

  returnVariantAttributes(offer) {
    const allAttributes = [...offer.parameterizedAttributes, ...offer.staticAttributes];
    const requiredAttributes = [];

    let counter = 0;

    allAttributes.some((attribute) => {
      if ((attribute.id === 1 || attribute.id === 10) && counter < 2) {
        requiredAttributes.push(attribute);
        counter++;
      } else {
        return;
      }
    });

    return requiredAttributes;
  }

  returnChannel(row: any) {
    const obj = _.find(row.parameterizedAttributes, { id: 10 }) ||
      _.find(row.hiddenAttributes, { id: 10 }) ||
      _.find(row.staticAttributes, { id: 10 });

    return obj ? obj.value : '';
  }

  extractDates(value) {
    let str = '';
    if (value.effectiveDate) {
      const date = new Date(value.effectiveDate);
      str += this.datePipe.transform(date, this.offerDataService.getDatePipeFormatLocale()) + '-';
    } else if (value.isFlowChartRunDate) {
      str += this.translate.instant('CREATE_OFFER.LABELS.FLOWCHART_RUN_DATE') + '-';
    } else {
      str += '- /';
    }
    if (value.expirationDate) {
      const date = new Date(value.expirationDate);
      str += this.datePipe.transform(date, this.offerDataService.getDatePipeFormatLocale());
    } else if (value.expirationDuration) {
      str += value.expirationDuration + ' ' + this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.DAYS_LATER');
    } else {
      str += '-';
    }
    return str;
  }

  returnEffAndExpDate(row) {
    if (row.parameterizedAttributes.length !== 0) {
      for (let i = 0; i < row.parameterizedAttributes.length; i++) {
        if (row.parameterizedAttributes[i].type.id === 200) {
          return this.extractDates(row.parameterizedAttributes[i].value);
        }
      }
    }

    if (row.staticAttributes.length !== 200) {
      for (let i = 0; i < row.staticAttributes.length; i++) {
        if (row.staticAttributes[i].type.id === 200) {
          return this.extractDates(row.staticAttributes[i].value);
        }
      }
    }
    return '- / -';
  }

  returnVariantEffAndExpDate(offerData) {
    let date = '- / -';
    if (offerData.hasOwnProperty('variantAttributes') && offerData.variantAttributes.length) {
      offerData.variantAttributes.some((attribute) => {
        if (attribute.type.id === 200) {
          date = this.extractDates(attribute.value);
          return;
        }
      });
    }
    return date;
  }

  returnOfferCodes(row) {
    let codeString = '';
    if (row.offerCodes.length > 0) {
      row.offerCodes.forEach((offerCode, index) => {
        if ((row.offerCodes.length - 1) !== index) {
          codeString += (offerCode + this.offerDataService.offerApplicationConfig.offerCodeDelimiter);
        } else {
          codeString += offerCode;
        }
      });
      return codeString;
    }
  }

  columnaSorting(event) {
    // if (event.colId && event._sorting) {
    //   this.selectOffersService.offersSortAndColumn = event.colId + ',' + event._sorting;
    // }
  }

  pageSizeChanged(event) {
    // if (event.rowsPerPage) {
    //   this.selectOffersService.offersPageSize = +event.rowsPerPage;
    // }
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => { sub.unsubscribe(); });
  }

  manageSelections(event) {
    this.selectOffersService.sendloadNext('manageOffers');
  }

  selectedOffersAndOls() {
    if (this.selectOffersService.loadOfferOrOfferLists === 'both') {
      return `${this.selectOffersService.selectedOffersData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')} | 
      ${this.selectOffersService.selectedOlData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS')} 
      ${this.translate.instant('FIELDS.SELECTED')}`;
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offerLists') {
      return `${this.selectOffersService.selectedOlData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS')} 
      ${this.translate.instant('FIELDS.SELECTED')}`;
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offers') {
      return `${this.selectOffersService.selectedOffersData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')} 
      ${this.translate.instant('FIELDS.SELECTED')}`;
    }
  }

}

