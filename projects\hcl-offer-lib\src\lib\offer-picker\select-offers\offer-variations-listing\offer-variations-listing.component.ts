import { Component, EventEmitter, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { DataGridConf } from 'hcl-data-grid-lib';
import { InputConfig } from 'hcl-angular-widgets-lib';
import { SelectOffersService } from '../../select-offers.service';
import { TranslateService } from '@ngx-translate/core';
import { UntypedFormControl } from '@angular/forms';
import * as _ from 'lodash';

@Component({
  selector: 'hcl-offer-variations-listing',
  templateUrl: './offer-variations-listing.component.html',
  styleUrls: ['./offer-variations-listing.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class OfferVariationsListingComponent implements OnInit {

  variationsGridApi: any;
  variationsGridConfig: DataGridConf;
  variationsGridColumnApi: any;
  subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  elements: any;
  loadTemplate: boolean = false;
  searchBoxConfig: InputConfig;
  selectedVariantId: number = -1;
  @Output() reload: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Output() disableSelect: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor(
    public selectOffersService: SelectOffersService,
    private translate: TranslateService,
  ) { }

  ngOnInit() {
    this.subscriptionList.push(this.selectOffersService.getOfferVariants(+this.selectOffersService.offerId).subscribe((variants: any) => {
      this.selectedVariantId = this.selectOffersService.selectedVariantId ? this.selectOffersService.selectedVariantId : -1;
      this.selectButtonEvent();
      this.elements = variants;
      let masterName = this.translate.instant('OFFER_PICKER.TITLES.MASTER');
      if (masterName === '') {
        masterName = 'Master';
      }
      this.elements.unshift({ id: -1, displayName: masterName});
      this.setConfiguration();
      this.loadTemplate = true;
    }));
  }

  setConfiguration() {
    this.variationsGridConfig = {
      scrollHeight: 330,
      isClientSideRowModel: true,
      columns: [
        {
          field: 'displayName',
          header: '',
          colId: 'displayName',
          sortable: true,
          minWidth: 150,
          autoResizeToFit: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return attr.displayName;
            }
          },
          dataFormatter: (attr: any) => {
            return attr.data.displayName;
          }
        }
      ],
      data: [],
      suppressRowClickSelection: true,
      suppressDragLeaveHidesColumns: true,
      noRowsTemplate: this.translate.instant('MESSAGES.NO_DATA_ON_HTTP_RESPONSE'),
      loadingTemplate: this.translate.instant('MESSAGES.LOADING'),
      rowClassRules: {
        'row-selected': (node) => {
          if (node && node.data && node.data.id && +this.selectedVariantId === +node.data.id) {
            return true;
          }
          return false;
        }
      }
    };

    this.searchBoxConfig = {
      formControlName: new UntypedFormControl(''),
      placeholder: this.translate.instant('BUTTONS.SEARCH_VARIANT'),
      icon: 'hcl-icon-search',
      type: 'text',
      name: 'search'
    };
  }


  variationsGridReady(data) {
    this.variationsGridApi = data.params.api;
    this.variationsGridColumnApi = data.params.columnApi;

    this.elements.forEach(element => {
      this.variationsGridConfig.data.push(element);
    });
    this.variationsGridApi.setRowData(this.variationsGridConfig.data);
  }

  onFilterTextBoxChanged() {
    const customData = this.filterGridData(this.searchBoxConfig.formControlName.value, this.variationsGridConfig.data);
    this.variationsGridApi.setRowData(customData);
    if (this.variationsGridApi.rowModel.rowsToDisplay.length === 0) {
      this.variationsGridApi.showNoRowsOverlay();
    } else {
      this.variationsGridApi.hideOverlay();
    }
  }

  /**
  * Filters grid data using text which user has entered
  */
  private filterGridData(text: string, data: any[]): any[] {
    const collection = data;
    const results = _.filter(collection, (obj) => {
      return obj.displayName.toLowerCase().indexOf(text.toLowerCase()) !== -1;
    });
    return results;
  }

  valueEntered() {
    if (!this.searchBoxConfig.formControlName.value) {
      this.onFilterTextBoxChanged();
    }
  }

  onCellClicked(cell) {
    this.selectedVariantId = +cell.data.id;
    this.selectButtonEvent();
    if (this.selectedVariantId === -1) {
      this.selectOffersService.selectedVariantId = null;
      this.selectOffersService.loadVariationType = 'viewMaster';
    } else {
      this.selectOffersService.selectedVariantId = this.selectedVariantId;
      this.selectOffersService.loadVariationType = 'viewVariation';
    }
    this.reload.emit(true);
    this.variationsGridApi.redrawRows();
  }

  selectButtonEvent() {
    if (this.selectOffersService.disableVariants) {
      if (this.selectedVariantId !== -1) {
        this.disableSelect.emit(true);
      } else {
        this.disableSelect.emit(false);
      }
    }
  }
}

