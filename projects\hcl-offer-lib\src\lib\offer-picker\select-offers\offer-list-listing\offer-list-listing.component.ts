import { Component, On<PERSON><PERSON>roy, OnInit, Output, ViewChild, EventEmitter, Input, OnChanges } from '@angular/core';
import { DataGridV2Component } from 'hcl-data-grid-lib';
import { Actions, DataGridConf, DataGridPagination, HoverIcon } from 'hcl-data-grid-lib';
import { ModalConfig } from 'hcl-angular-widgets-lib';
import { ButtonConf } from 'hcl-angular-widgets-lib';
import { Subscription, SubscriptionLike } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { SelectOffersService } from '../../select-offers.service';
import { SelectOffersConstant } from '../select-offers.constant';

@Component({
  selector: 'hcl-offer-list-listing',
  templateUrl: './offer-list-listing.component.html',
  styleUrls: ['./offer-list-listing.component.scss']
})
export class OfferListListingComponent implements OnInit, OnDestroy, OnChanges {

  @ViewChild('offerListsGrid', { static: true }) offerListsGrid: DataGridV2Component;
  @Input() offerListType: string;
  @Output() offerListSelectionUpdate: EventEmitter<any> = new EventEmitter<any>();

  offerListsGridConf: DataGridConf;
  paginatorConfig: DataGridPagination;
  hoverActions: HoverIcon[];
  actions: Actions;
  offerListId: number;
  retireOfferListDialogConfig: ModalConfig;
  deleteOfferListDialogConfig: ModalConfig;
  manageSelectionsButtonConf: ButtonConf;
  noActionConfig: ButtonConf;
  defaultFolderId: number;
  subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  currentlySelectedRows: any;
  editOfferListPermissionSubscription: Subscription;
  viewOfferListPermissionSubscription: Subscription;
  dataUrl: string;
  offerListsGridApi: any;

  constructor(private translate: TranslateService,
    public selectOffersService: SelectOffersService) {
  }

  ngOnChanges() {
    if (this.offerListsGridConf && this.offerListsGridConf.queryParams) {
      this.setTypeQueryParam();
      this.filterDataByType();
    }
  }

  /**
   * Set offerListType queryparam
   */
  private setTypeQueryParam() {
    switch (this.offerListType) {
      case SelectOffersConstant.all:
        delete this.offerListsGridConf.queryParams.offerListType;
        break;
      case SelectOffersConstant.smartOfferListSmallCase:
        this.offerListsGridConf.queryParams.offerListType = SelectOffersConstant.smartOfferListCapital;
        break;
      case SelectOffersConstant.staticOfferListSmallCase:
        this.offerListsGridConf.queryParams.offerListType = SelectOffersConstant.staticOfferListCapital;
        break;
    }
  }

  /**
   * Get offer list data as per filter and search
   */
  private filterDataByType() {
    if (this.selectOffersService.offerListGlobalSearchData) {
      this.selectOffersService.sendOfferListsSearchData(this.selectOffersService.offerListGlobalSearchData);
    } else {
      this.selectOffersService.sendFolderId(SelectOffersConstant.offerlists, this.selectOffersService.offersFolder);
    }
  }

  ngOnInit() {
    this.setConfiguration();

    this.subscriptionList.push(this.selectOffersService.getFolderId().subscribe(obj => {
      if (obj[0] === 'offerlists') {
        this.defaultFolderId = obj[1];
        this.offerListsGridConf.dataUrl = this.selectOffersService.getOfferListsBaseUrl(this.defaultFolderId);
        if (this.selectOffersService.lastFolderClicked === null || this.selectOffersService.lastFolderClicked !== this.defaultFolderId) {
          this.paginatorConfig.currentPageIndex = 0;
          this.selectOffersService.lastFolderClicked = this.defaultFolderId;
        }
        this.offerListsGrid.refreshData();
        this.selectOffersService.clearFolderId();
      }
    }));
    this.subscriptionList.push(this.selectOffersService.offerGridResize.subscribe(obj => {
      // we need to resize the grid
      if (this.offerListsGrid) {
        this.offerListsGrid.resizeColumnsToFit();
      }
    }));
    this.subscriptionList.push(this.selectOffersService.getOfferListsSearchData().subscribe(obj => {
      if (obj.length > 0) {
        this.offerListsGridConf.queryParams.search_param = encodeURI(obj[0]);
        this.setTypeQueryParam();
        this.offerListsGridConf.dataUrl = this.selectOffersService.getOfferListsUrl();
        this.offerListsGrid.refreshData();
        this.selectOffersService.clearOfferListsSearchData();
      }
    }));

    if (this.selectOffersService.selectedOlData && this.selectOffersService.selectedOlData.length > 0) {
      this.offerListSelectionUpdate.emit();
    }
  }

  setConfiguration() {
    this.paginatorConfig = {
      rowsPerPage: this.selectOffersService.offersPageSize,
      pageSizeArray: [10, 20, 50, 100],
      optionLabels: [10, 20, 50, 100].map(option => this.translate.instant('FIELDS.ROWS_OPTION', { value: option })),
      currentPageIndex: 0,
      rowsPerPageSuffix: this.translate.instant('FIELDS.ROWS'),
      total: this.translate.instant('ASSETPICKER.TOTAL'),
      firstLabelString: this.translate.instant('BUTTONS.FIRST'),
      prevLabelString: this.translate.instant('BUTTONS.PREV'),
      nextLabelString: this.translate.instant('BUTTONS.NEXT'),
      lastLabelString: this.translate.instant('BUTTONS.LAST')
    };
    this.offerListsGridConf = {
      scrollHeight: 380,
      columns: [{
        field: 'displayName',
        header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.OFFER_LIST_NAME'),
        colId: 'displayName',
        sortable: true,
        autoResizeToFit: true,
        minWidth: 150,
        rendererTemplateName: 'displayName',
        tooltip: {
          getTooltip: (attr: any) => {
            return attr.displayName;
          }
        },
        cellClassRules: {
          ['retire-row-text-color']: function (node) {
            return node.data && node.data.isRetired === true;
          }
        }
      },
      {
        field: 'type',
        header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.OFFER_LIST_TYPE'),
        colId: 'type',
        autoResizeToFit: true,
        minWidth: 150,
        useDefaultRenderer: true,
        tooltip: {
          getTooltip: (attr: any) => {
            return this.returnOfferListType(attr.type);
          }
        },
        dataFormatter: (attr: any) => {
          return this.returnOfferListType(attr.data.type);
        },
        cellClassRules: {
          ['retire-row-text-color']: function (node) {
            return node.data && node.data.isRetired === true;
          }
        }
      },
      {
        field: 'description',
        header: this.translate.instant('CREATE_OFFER.LABELS.DESCRIPTION'),
        colId: 'description',
        minWidth: 260,
        sortable: true,
        useDefaultRenderer: false,
        tooltip: {
          getTooltip: (attr: any) => {
            return attr.description;
          }
        },
        cellClassRules: {
        }
      },
      {
        field: 'state',
        header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.STATUS'),
        colId: 'state',
        autoResizeToFit: true,
        minWidth: 120,
        useDefaultRenderer: true,
        tooltip: {
          getTooltip: (attr: any) => {
            if (attr.state === 'RETIRED') {
              return this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.RETIRED');
            } else if (attr.state === 'PUBLISHED') {
              return this.translate.instant('TITLES.PUBLISHED');
            } else if (attr.state === 'DRAFT') {
              return this.translate.instant('TITLES.DRAFT');
            }
          }
        },
        dataFormatter: (attr: any) => {
          if (attr.data.state === 'RETIRED') {
            return this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.RETIRED');
          } else if (attr.data.state === 'PUBLISHED') {
            return this.translate.instant('TITLES.PUBLISHED');
          } else if (attr.data.state === 'DRAFT') {
            return this.translate.instant('TITLES.DRAFT');
          }
        },
        cellClassRules: {
          ['retire-row-text-color']: function (node) {
            return node.data && node.data.isRetired === true;
          }
        }
      }
      ],
      dataUrl: this.selectOffersService.getOfferListsBaseUrl(this.selectOffersService.offersFolder ?
        this.selectOffersService.offersFolder : this.defaultFolderId),
      rowSelectMode: this.selectOffersService.rowSelectMode,
      pagination: this.paginatorConfig,
      noRowsTemplate: this.translate.instant('MESSAGES.NO_DATA_ON_HTTP_RESPONSE'),
      loadingTemplate: this.translate.instant('MESSAGES.LOADING'),
      suppressRowClickSelection: true,
      suppressDragLeaveHidesColumns: true,
      queryParams: { sort: this.selectOffersService.offerListsSortAndColumn, state: this.selectOffersService.offerAndOfferListState }
    };
    this.noActionConfig = {
      name: 'no',
      value: this.translate.instant('BUTTONS.CANCEL'),
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };
    this.manageSelectionsButtonConf = {
      color: 'accent',
      buttonType: 'mat',
      type: 'button',
      value: this.translate.instant('OFFER_PICKER.TITLES.MANAGE_SELECTIONS'),
      name: 'removeMapping',
      disabled: false
    };
  }

  /**
  * A callback that will be called when the grid is ready and loaded
  * @param data
  */
  onGridReady(data) {
    this.offerListsGridApi = data.params.api;
  }

  /**
  * A callback that will be called whenever data for the grid is loaded
  */
  gridDataLoaded(data) {
    if (this.selectOffersService.selectedOlData.length > 0) {
      const commonRows: any[] = data.content.filter(
        (d) => {
          return this.selectOffersService.selectedOlData.some((row) => {
            if (d.id === row.offerListId) {
              row['offerListDisplayName'] = d.displayName;
              row['type'] = d.type.id;
              row['state'] = d.state;
              return true;
            };
          });
        });
      if (commonRows.length > 0) {
        commonRows.forEach((d) => {
          setTimeout(() => {
            this.offerListsGridApi.forEachNode((node) => {
              if (node.data.id === d.id) {
                node.setSelected(true);
              }
            });
          }, 300);
        });
      }
    }
  }


  returnOfferListType(data) {
    if (data.id === 'STATIC') {
      return this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.STATIC');
    } else {
      return this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.SMART');
    }
  }

  rowSelected(data: any) {
    if (data.data && !this.selectOffersService.selectedOlData.some(ol => ol.offerListId === data.data.id)) {
      let offerListData;

      offerListData = {
        offerListId: +data.data.id,
        offerListDisplayName: data.data.displayName,
        type: data.data.type.id,
        state: data.data.state
      };

      this.selectOffersService.selectedOlData.push(offerListData);
      this.offerListSelectionUpdate.emit();
    }
  }

  rowUnSelected(data: any) {
    if (data.data) {
      this.selectOffersService.selectedOlData.some((selectedData, index) => {
        if (+selectedData.offerListId === +data.data.id) {
          this.selectOffersService.selectedOlData.splice(index, 1);
          return;
        }
      });
      this.offerListSelectionUpdate.emit();
    }
  }

  onCellClicked(cell) {
    if (cell.row && cell.row.id) {
      this.selectOffersService.viewRoute = 'listOfferLists';
      this.selectOffersService.offerListId = +cell.row.id;
      this.selectOffersService.sendViewOfferListClicked(+cell.row.id);
    }
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => { sub.unsubscribe(); });
  }

  selectedOffersAndOls() {
    if (this.selectOffersService.loadOfferOrOfferLists === 'both') {
      return `${this.selectOffersService.selectedOffersData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')} | 
      ${this.selectOffersService.selectedOlData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS')} 
      ${this.translate.instant('FIELDS.SELECTED')}`;
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offerLists') {
      return `${this.selectOffersService.selectedOlData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS')} 
      ${this.translate.instant('FIELDS.SELECTED')}`;
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offers') {
      return `${this.selectOffersService.selectedOffersData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')} 
      ${this.translate.instant('FIELDS.SELECTED')}`;
    }
  }

  manageSelections(event) {
    this.selectOffersService.sendloadNext('manageOffers');
  }

}
