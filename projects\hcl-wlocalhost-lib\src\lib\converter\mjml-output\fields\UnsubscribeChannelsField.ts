import { IInputFieldOptions, InputLayoutField, IUnsubscribeChannelsFieldOptions, RenderingClass, UnsubscribeChannelsLayoutField } from '../interfaces';
import { createPadding, createLineHeight, ignoreHTMLMinParse, createBorder, uniqueId, createWidthHeight,formatPfValue } from '../utils';
import { IpEmailBuilderService } from '../../../ip-email-builder.service';

export class UnsubscribeChannelsFields implements UnsubscribeChannelsLayoutField, RenderingClass {
  constructor(public emailPf: string, public phoneNumberPf: string, public pushIdPf: string, public options: IUnsubscribeChannelsFieldOptions, public _ngb: IpEmailBuilderService) { }

  render() {
    
  this.phoneNumberPf = formatPfValue(this.phoneNumberPf);
  this.emailPf = formatPfValue(this.emailPf);
  this.pushIdPf = formatPfValue(this.pushIdPf);
    const { font, padding, color, lineHeight, align } = this.options;
    const uniqueIdForInput = uniqueId();
    const unsubscribeChannelsTemplate = `
    <mj-text
      css-class="unsubscribe-channels-field input_${uniqueIdForInput} hide-on-${this.options.hideOn}"
      font-size="0px">
        <div style="font-family: ${font.family}, ${font.fallback};font-size:${font.size}px;font-weight:${font.weight};
                    align:${align};color: ${color};
                    letter-spacing:${font.style};line-height:${lineHeight.value}${lineHeight.unit};">
        
          <div style="display: ${this.emailPf || this.phoneNumberPf ? 'block' : 'none'};padding: ${createPadding(padding)};">
            <img ${!this.emailPf ? 'style="display:none;"':''} src="${window['CommEditorGlobalFonts']}Angular/assets/images/icon_Mail.svg" />
            <div style="position:relative;top:-7px;color:${color};font-size:${font.size}px;${!this.emailPf ? 'display:none;':'display: inline-block;'}"><span class="ip-unsubscribe-channels label-droppable droppable">${this.emailPf}</span></div>
            <img  ${!this.phoneNumberPf ? 'style="display:none;"':''} src="${window['CommEditorGlobalFonts']}Angular/assets/images/icon_mobile.svg" />
            <div style="position:relative;top:-7px;color:${color};font-size:${font.size}px;${!this.phoneNumberPf ? 'display:none;':'display: inline-block;'}"><span class="ip-unsubscribe-channels label-droppable droppable">${this.phoneNumberPf}</span></div>
            <input type="hidden" name="input_email_pf" value="${this.emailPf}" class="ip-input-field input-droppable droppable" />
            <input type="hidden" name="input_phone_pf" value="${this.phoneNumberPf}" class="ip-input-field input-droppable droppable" />
            <input type="hidden" name="input_push_pf" value="${this.pushIdPf}" class="ip-input-field input-droppable droppable" />
            <input type="hidden" name="input_template_type" value="${this._ngb.TemplateType}" class="ip-input-field input-droppable droppable" />
          </div>
        </div>
    </mj-text>`;

    if (this.options.hideOn === 'desktop') {
      return `
        <mj-raw>
        <!--[if !mso]><!-- --></mj-raw>
        ${unsubscribeChannelsTemplate}
        <mj-raw><!--<![endif]--></mj-raw>
      `;
    } else {
      return unsubscribeChannelsTemplate;
    }    

  }
}
