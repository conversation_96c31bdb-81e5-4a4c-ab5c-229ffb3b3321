export interface SankeyJSON {
  nodes: { id: string }[],
  links: { source: string, target: string, value: number, toolTip?: string }[]
}

export interface SankeyConfig {
  margin?: number;
  svgWidth?: number;
  svgHeight?: number;
  svgBackground?: string; //'#eee'
  svgBorder?: string; //"1px solid #333";
  nodeWidth?: number;
  nodePadding?: number;
  nodeOpacity?: number;
  linkOpacity?: number;
  nodeDarkenFactor?: number;
  nodeStrokeWidth?: number;
  separator?: string;
  isDragableNode?: Boolean;
  needGradient?: Boolean;
  nodeGap?: number;
  fontSize?: number;
}
