import { AfterViewInit, Component, ElementRef, Input, OnInit, Renderer2, ViewEncapsulation, EventEmitter, Output, OnChanges, ChangeDetectorRef } from '@angular/core';
import { ChartConfig, DonutChartConfig, DonutChartData } from '../../config/chart-config';
import { BaseChartRenderer } from '../../renderer/BaseChartRenderer';
import { CHART_TYPE, RendererFactory } from '../../renderer/RendererFactory';
import * as d3 from 'd3';

@Component({
    selector: 'hcl-donut-chart-v2',
    templateUrl: './donut-chart.component.html',
    styleUrls: ['./donut-chart.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class DonutChartComponent implements OnInit , OnChanges, AfterViewInit {
    @Input() chartConfig: DonutChartConfig;
    @Output() public centerImageEvent = new EventEmitter();
    selectedId: any;
    donutMouseOver: Function = () => {};
    donutMouseMove: Function = () => {};
    donutMouseLeave: Function = () => {};


    constructor(private cdr: ChangeDetectorRef) {
    }

    public ngAfterViewInit(): void {
        // this.createAnimatedD3Chart();
    }

    public ngOnInit() {
    }

    public ngOnChanges(){
        this.cdr.detectChanges();
        if (document.querySelector('#' + this.chartConfig.chartID)?.childNodes?.length > 0) {
            return;
        }
        if (this.chartConfig.showTooltip) {
            this.setupBarMouseEvents();
        }
        this.renderChart();
    }

    setupBarMouseEvents() {
        const tooltipContainer = document.createElement("div");
        tooltipContainer.className = "donut-chart-tooltip";
        const tooltipArrowElement = document.createElement("div");
        tooltipArrowElement.className = "donut-chart-tooltip-arrow";
        const tooltipContentsElement = document.createElement("div");
        tooltipContentsElement.className = "donut-chart-tooltip-contents";
        tooltipContainer.appendChild(tooltipArrowElement);
        tooltipContainer.appendChild(tooltipContentsElement);
        document.body.appendChild(tooltipContainer);  
        
        const tooltip = d3.select( ".donut-chart-tooltip" );
        const tooltipContents = d3.select( ".donut-chart-tooltip .donut-chart-tooltip-contents" );
        this.donutMouseOver = (data, index, node) => {
            tooltip
            .style('display', 'block')
            tooltipContents.html(this.chartConfig.tooltipHTML(data))
            node[index].style.stroke = "#f5821e";
            node[index].style["stroke-width"] = "3"
        };

        this.donutMouseMove = (data, index, node) => {
            tooltip
            .style('left', `${d3.event.clientX + 5}px`)
            .style('top', `${(d3.event.clientY - 42)}px`);
        };
      
        this.donutMouseLeave = (data, index, node) => {
            tooltip.style('display', 'none');
            node[index].style.stroke = "transparent";
        };
      }

    public renderChart() {
        const chartComponent = this;
        const imageWidth = this.chartConfig.iconWidth ? this.chartConfig.iconWidth : 40;
        const imageHeight = this.chartConfig.iconHeight ? this.chartConfig.iconHeight : 40;
        const width = this.chartConfig.width ? this.chartConfig.width : 700;
        const height = this.chartConfig.height ? this.chartConfig.height : 400;
        const radius = 250;
        const piedata = this.chartConfig.data;
        this.chartConfig.outerRadius = this.chartConfig.outerRadius ? this.chartConfig.outerRadius : 150;
        this.chartConfig.innerRadius = this.chartConfig.innerRadius ? this.chartConfig.innerRadius : 70;
        this.chartConfig.spreadSlice = this.chartConfig.spreadSlice ? this.chartConfig.spreadSlice : false;
        const chartID = this.chartConfig.chartID ? this.chartConfig.chartID : 'donutChart';
        const middleText = this.chartConfig.middleText ? this.chartConfig.middleText : '';
        const middleTextColor = this.chartConfig.middleTextColor ? this.chartConfig.middleTextColor : 'black';
        const middleTextFontSize = this.chartConfig.middleTextFontSize ? this.chartConfig.middleTextFontSize : '1em';
        this.chartConfig.outerRadius > 150 ? this.chartConfig.outerRadius = 150 : this.chartConfig.outerRadius;
        let pie;
        if (this.chartConfig.progressChart) {
            pie = d3.pie()
            .value(<any>this.chartConfig.returnValue);
        } else {
            pie = d3.pie()
            .value(<any>this.chartConfig.returnValue).sort(null);
        }
        
        const arc = d3.arc()
            .innerRadius(this.chartConfig.innerRadius)
            .outerRadius(this.chartConfig.outerRadius)
        const arcNew = d3.arc()
            .outerRadius(this.chartConfig.outerRadius + 10)
            .innerRadius(this.chartConfig.innerRadius);

        const svg = d3.select('#' + chartID).append('svg')
            .attr('width', width)
            .attr('height', height)
            .append('g')
            .attr('transform', 'translate(' + (this.chartConfig.outerRadius + 10) + ',' + (this.chartConfig.outerRadius + 10) + ')');

        const multiLineMiddleText = middleText.split('\n');    

        svg.append("text")
            .attr('font-size', middleTextFontSize)
            .attr('y', this.chartConfig.innerRadius * -0.2 * multiLineMiddleText.length)
            .style('fill', middleTextColor)
            .selectAll('tspan').data(multiLineMiddleText)
            .enter().append('tspan')
            .text(function(text) {
                return text.trim();
            })
            .attr('dy', middleTextFontSize).attr('x', '0')
            .attr("text-anchor", "middle");

        let g = svg.selectAll('.arc')
            .data(pie(piedata))
            .enter().append('g')
            .attr('class', 'arc');

        g.append('path')
            .attr('d', <any>arc)
            .style('stroke', (d: any, i): number => {
                if (d.data.value > 0) {
                    return d.data.color;
                } else {
                    return;
                }
            }).style('fill', (d: any, i): number => {
                if (d.data.value > 0) {
                    return d.data.color;
                } else {
                    return;
                }
            })
            .attr('id', (d: any) => {
                return 'iconId' + d.data.id;
            })
            .attr('cursor', this.chartConfig.spreadSlice ? 'pointer' : 'default')
            .on('click', (d: any) => {
                d3.selectAll('path').transition()
                    .duration(50)
                    .attr('d', (d: any) => {
                        if (this.selectedId === d.data.id) {
                            d.data.expanded = true;
                            this.selectedId = null;
                            return arc(d);
                        } else {
                            d.data.expanded = false;
                            this.selectedId = null;
                            return arc(d);
                        }
                    })
                if ((<any>chartComponent).spreadSlice) {
                    d3.select((<any>chartComponent)).transition()
                        .duration(50)
                        .attr('d', (d: any) => {
                            if (d.data.expanded) {
                                this.selectedId = null;
                                d.data.expanded = false;
                                return arc(d);
                            } else {
                                d.data.expanded = true;
                                this.selectedId = d.data.id;
                                return arcNew(d);
                            }
                        });
                }
            })
            .on('mouseenter', (data, index, node) => {this.donutMouseOver(data, index, node);})
            .on('mousemove', (data, index, node) => {this.donutMouseMove(data, index, node);})
            .on('mouseout', (data, index, node) => {this.donutMouseLeave(data, index, node)});


        if (this.chartConfig.barImage) {
            g.append('g')
                .attr('transform', (d: any) => {
                    return 'translate(' + arc.centroid(d) + ')';
                })
                .append('svg:image')
                .attr('xlink:href', (d: any) => {
                    return d.data.iconImage;
                })
                .attr('id', (d: any) => {
                    return d.data.id;
                })
                .attr('width', imageWidth)
                .attr('height', imageHeight)
                .attr('x', -1 * imageWidth / 2)
                .attr('y', -1 * imageHeight / 2)
                .attr('cursor', this.chartConfig.spreadSlice ? 'pointer' : 'default')
        }

        g.on('click', (d: any) => {
            d3.selectAll('path').transition()
                .duration(50)
                .attr('d', (d: any) => {
                    if (this.selectedId == d.data.id) {
                        d.data.expanded = true;
                        this.selectedId = null;
                        return arc(d);
                    } else {
                        d.data.expanded = false;
                        this.selectedId = null;
                        return arc(d);
                    }
                });
            if (this.chartConfig.spreadSlice) {
                d3.select('path#iconId' + d.data.id).transition()
                    .duration(50)
                    .attr('d', (d: any) => {
                        if (d.data.expanded) {
                            this.selectedId = null;
                            d.data.expanded = false;
                            return arc(d);
                        } else {
                            d.data.expanded = true;
                            this.selectedId = d.data.id;
                            return arcNew(d);
                        }
                    });
            }
        });
        if (this.chartConfig.centerImage) {
            svg.append('svg:image')
                .attr('id', 'center_image')
                .attr('x', -60)
                .attr('y', -60)
                .attr('width', 120)
                .attr('height', 120)
                .attr('cursor', 'pointer')
                .attr('xlink:href', this.chartConfig.centerImage)
                .on('click', function click(d) {
                    chartComponent.centerImageEvent.emit()
                });
        }

        if (this.chartConfig.showLegend) {
            const legendLabels = [];
            const legendColors = [];
            for (let i = 0; i < piedata.length; i++) {
                legendLabels.push(piedata[i].label);
                legendColors.push(piedata[i].color);
            }
            const color = d3.scaleOrdinal()
                .domain(legendLabels)
                .range(legendColors);
            const legendItemSize = 18;
            const legendSpacing = 4;
    
            const legend = svg
                .selectAll('.legend')
                .data(color.domain())
                .enter()
                .append('g')
                .attr('class', 'legend')
                .attr('transform', (d, i) => {
                    const outerRadiusCircle: any = this.chartConfig.outerRadius;
                    const multiplicationFactor = outerRadiusCircle > 100 ? (outerRadiusCircle < 130 ? 12 : 13) : 12
                    const height = legendItemSize + legendSpacing;
                    const offset = height * color.domain().length / 2;
                    const x = legendItemSize * multiplicationFactor;
                    const y = ((i * height) - offset);
                    return `translate(${x}, ${y})`
                })
    
            legend
                .append('rect')
                .attr('width', legendItemSize)
                .attr('height', legendItemSize)
                .style('fill', <any>color);
    
            legend
                .append('text')
                .attr('x', legendItemSize + legendSpacing)
                .attr('y', legendItemSize - legendSpacing)
                .text((d) => d)
        }
    }



    updateChartData(data) {
        const pie = d3.pie()
        .value(<any>this.chartConfig.returnValue)(data);

        const path = d3.select('#' + this.chartConfig.chartID)
        .selectAll("path")
        .data(pie);

        const arc = d3.arc()
            .innerRadius(this.chartConfig.innerRadius)
            .outerRadius(this.chartConfig.outerRadius);
        path.transition().duration(500).attr("d", <any>arc); // redrawing the path with a smooth transition

        const multiLineMiddleText = this.chartConfig.middleText.split('\n');    

        const svg = d3.select('#' + this.chartConfig.chartID)
        svg.select("text")
            .attr('font-size', this.chartConfig.middleTextFontSize)
            .attr('y', this.chartConfig.innerRadius * -0.2 * multiLineMiddleText.length)
            .style('fill', this.chartConfig.middleTextColor)
            .selectAll('tspan').data(multiLineMiddleText)
            .text(function(text) {
                return text.trim();
            })
            .attr('dy', this.chartConfig.middleTextFontSize).attr('x', '0')
    }

}