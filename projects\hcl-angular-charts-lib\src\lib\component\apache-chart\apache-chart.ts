
/* Various Options Configurations Available at https://echarts.apache.org/en/option.html#title */
/* [key: string]: unknown;  allows to add any additional properties to the configuration object. */

export interface ApacheColumnChartConfig {
    backgroundColor?: string;
    legend?: Legend;
    title: Title;
    grid?: Grid;
    yAxis: Axis;
    dataZoom?: DataZoom[];
    tooltip?: Tooltip;
    xAxis: Axis;
    series: ColumnSeries[];
    [key: string]: unknown;
}

export interface ApacheLineChartConfig extends Omit<ApacheColumnChartConfig, 'series'>{
    series: LineSeries[];
}

export interface ApacheFunnelChartConfig extends Omit<ApacheColumnChartConfig, 'series'>{
    series: FunnelSeries[];
}

type Title =  {
    id: string;
    mainType?: 'title';
    show?: boolean;
    text?: string;
    padding?: number | number[];
    itemGap?: number;
    triggerEvent?: boolean;
    borderRadius?: number | number[];
    [key: string]: unknown;
}

type Grid = {
    left?: string | number;
    right?: string | number;
    bottom?: string | number;
    top?: string | number;
    containLabel?: boolean;
    height?: string | number;
    width?: string | number;
    [key: string]: unknown;
}

type Axis =  {
    type: 'category' | 'value';
    boundaryGap?: boolean;
    axisLabel?: {
        show?: boolean;
        interval?: number;
        rotate?: number;
        [key:string]: unknown;
    };
    axisTick?: {
        show?: boolean;
        interval?: number;
        [key: string]: unknown;
    };
    scale?: boolean;
    splitNumber?: number;
    splitLine?: {
        show?: boolean;
        lineStyle?: {
            type?: 'solid' | 'dashed' | 'dotted' | number[];
            color?: string;
        };
    };
    data?: any[];
    [key: string]: unknown;
}

interface DataZoom {
    type: 'slider' | 'inside';
    show?: boolean;
    xAxisIndex?: number[];
    yAxisIndex?: number[];
    start?: number;
    end?: number;
    zoomLock?: boolean;
    moveOnMouseWheel?: string;
    left?: string;
    right?: string;
    bottom?: string;
    top?: string;
    height?: number;
    width?: number;
    [key: string]: unknown;
}

interface Tooltip {
    trigger?: 'item' | 'axis' | 'none';
    formatter?: string | Function;
    showContent?: boolean;
    [key: string]: any;
}

interface Legend {
    data?: string[];
    show?: boolean;
    left?: string;
    right?: string;
    top?: string;
    bottom?: string;
    orient?: 'horizontal' | 'vertical';
    padding?: number;
    itemGap?: number;
    itemWidth?: number;
    itemHeight?: number;
    [key: string]: unknown;
}

interface Toolbox {
    show?: boolean;
    feature?: {
        saveAsImage?: any;
    };
    [key: string]: unknown;
}


interface ColumnSeries {
    type:  'bar';
    name?: string;
    data: any;
    stack?: boolean | string;
    symbol?: 'emptyCircle' | 'circle' | 'none';
    symbolSize?: number;
    showAllSymbol?: boolean;
    lineStyle?: {
        width?: number;
    };
    itemStyle?: {
        color?: string;
    };
    barMaxWidth?: string,
    [key: string]: unknown;
}

interface LineSeries extends Omit<ColumnSeries, 'type'> {
   type: 'line';
}
interface FunnelSeries extends Omit<ColumnSeries, 'type'> {
    type: 'funnel';
 }

export interface ApacheDonutChartConfig {
    backgroundColor?: string;
    title?: Title;
    tooltip?: Tooltip;
    legend?: Legend;
    series: DonutSeries[];
    [key: string]: unknown;
}

interface DonutSeries {
    type: 'pie';
    name?: string;
    radius?: string | number | (string | number)[];
    center?: string | number | (string | number)[];
    data: DonutData[] | number[] ;
    [key: string]: unknown;
}

interface DonutData {
    name?: string;
    value: number;
    [key: string]: unknown;
}