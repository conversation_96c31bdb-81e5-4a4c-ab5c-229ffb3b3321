import { Component, OnInit, Input, TemplateRef, ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'hcl-custom-popover',
  templateUrl: './custom-popover.component.html',
  styleUrls: ['./custom-popover.component.css']
})
export class CustomPopoverComponent implements OnInit {
  /**
   * the flag for custom popover
   */
  @Input() showPopover: boolean;
  /**
   * this is template reference for custom popover
   */
  @Input() customPopoverTemplate: TemplateRef<any>;

  constructor() { }

  ngOnInit() {
    this.showPopover = false;
  }
}
