import { Component, Input, OnInit, Output, EventEmitter, ViewEncapsulation, OnChanges, ViewChild } from '@angular/core';
import { AutoCompleteV2Conf } from './auto-complete-search-v2-config';
import { FormControl } from '@angular/forms';
import { Observable, Subject } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { ProgressSpinner } from '../progress-spinner/progress-spinner-conf';
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';

@Component({
  selector: 'hcl-auto-complete-search-v2',
  templateUrl: './auto-complete-search-v2.html',
  styleUrls: ['./auto-complete-search-v2.scss'],
  encapsulation: ViewEncapsulation.None
})

export class AutoCompleteSearchV2Component implements OnInit, OnChanges {

  @Input() config: AutoCompleteV2Conf;

  @Output() optClick = new EventEmitter();

  @Output() iconClick = new EventEmitter();

  @Output() autoClick = new EventEmitter();

  @Output() blur = new EventEmitter();

  @Output() focused = new EventEmitter();

  @Output() keyPressed = new EventEmitter();

  @Output() optionIconClick = new EventEmitter();

  @Output() clearOptionclick = new EventEmitter();

  filteredOptions: Observable<string[]>;

  errorMessage = '';

  loadingText: string;

  flag = false;

  hoverFlag = false;

  @ViewChild(CdkVirtualScrollViewport) cdkVirtualScrollViewPort: CdkVirtualScrollViewport;

  inlineSpinnerConfig: ProgressSpinner = {
    color: 'primary', // available options "primary", "accent" and "warn"
    mode: 'indeterminate', // available options indeterminate and determinate
    value: 75, // progress value - max 100
    diameter: 25, // minimum should be 40 and max 100
    strokeWidth: 3, // minimum should be 1 and max 20
    isInline: true,
    isLoading: true
  };

  constructor() { }

  ngOnInit() {
    if (this.config) {
      this.loadingText = this.config.loadingText ? this.config.loadingText : 'Loading';
    }
  }

  filterOptionList() {
    this.filteredOptions = this.config.formControl.valueChanges
      .pipe(
        startWith(''),
        map(value => this._filter(value))
      );
  }

  ngOnChanges() {
    if (this.config.suggestions && this.config.suggestions.length > 0 && this.config.formControl && this.config.canFilter) {
      this.filterOptionList();
    }
  }

  trackByIndex(index: number) { return index; }

  getErrorMessage() {
    for (const error of this.config.errorList) {
      if (this.config.formControl.hasError(error.errorCondition)) {
        this.errorMessage = error.errorMsg;
        return this.errorMessage;
      } else {
        this.errorMessage = '';
      }
    }
  }

  private _filter(value: string): string[] {
    const filterValue = value.toLowerCase();
    return this.config.suggestions.filter(option => option.toLowerCase().includes(filterValue));
  }

  onOptionClick(event: any, value: any) {
    let option = '';
    if (this.config.suggestions) {
      const isStaticOptions = this.config.autoComplete === 'staticOptions';
      this.config.suggestions.forEach(opt => {
        if (typeof opt.label === 'number') {
          opt = opt.label.toString();
        }
        if (isStaticOptions) {
          if (this.isOptionObjectType(opt)) {
            if (event.option.viewValue.includes(opt.label)) {
              option = opt.value;
            }
          } else {
            if (event.option.viewValue.includes(opt)) {
              option = opt;
            }
          }
        } else {
          if (event.option.viewValue === opt) {
            option = opt;
          }else if (this.isOptionObjectType(opt)) {
            if (event.option.viewValue.includes(opt.label)) {
              option = opt.label;
            }
          }
        }
      });
    }
    if (value) {
      this.optClick.emit({ val: value, opt: option });
    } else {
      this.optClick.emit(option);
    }
  }

  onAutoClick(value: any) {

  }

  onIconClick(event, value, clickFrom) {
    if (this.config.iconClickStopPropagation) {
      event.stopPropagation();
    }
    this.iconClick.emit({ value, clickFrom });
  }

  onOptionIconClick(event, value) {
    event.stopPropagation();
    this.optionIconClick.emit(value);
  }

  onOptionHover(option: any): void {
    this.hoverFlag = option;
  }

  onOptionLeave(option: any): void {
    if (this.hoverFlag === option) {
      this.hoverFlag = null;
    }
  }

  onBlurEvent(e: any, isEnter) {
    this.flag = false;
    /**
     * this event will be emitted when you need inputbox value on 'enter' key press event
     */
    if (this.config.isBlurOrEnter) {
      const tmpObj = {
        enterPressed: isEnter,
        value: e.target.value
      };
      if (isEnter) {
        if (e.charCode === 13) {
          this.blur.emit(tmpObj);
        }
      } else {
        this.blur.emit(tmpObj);
      }
    }
  }

  onInputFocused(e) {
    if (!this.flag) {
      this.flag = true;
      if (e && this.cdkVirtualScrollViewPort) {
        this.cdkVirtualScrollViewPort.scrollToIndex(0);
        this.cdkVirtualScrollViewPort.checkViewportSize();
      }
    }
    this.focused.emit(e);
  }

  isOptionObjectType(option: any) {
    return typeof option === 'object';
  }

  onKeyPressEvent(e) {
    this.keyPressed.emit(e);
  }
  
  clearInput(): void {
    if (this.config.formControl) {
      this.config.formControl.reset();
      this.config.suggestions = [];
    } else {
      this.config.value = '';
    }
    this.clearOptionclick.emit(this.config);
  }
}

