import { HttpHeaders } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ButtonConf, SideBarComponent } from 'hcl-angular-widgets-lib';
import { ExportObjectConf } from 'projects/hcl-export-object-cif-lib/src/public_api';

@Component({
  selector: 'app-cif-export-object-mapping-demo',
  templateUrl: './cif-export-object-mapping-demo.component.html',
  styleUrls: ['./cif-export-object-mapping-demo.component.scss']
})
export class CifExportObjectMappingDemoComponent implements OnInit {
  @ViewChild('sideBar', { static: false }) sideBarComponentRef: SideBarComponent;

  previousData = {
    "createDate": 1666253513841,
    "createdBy": 2,
    "updateDate": 1666253513841,
    "updatedBy": 2,
    "mappingId": 8,
    "partition1Id": 1,
    "object1": {
      "applicationId": "journey",
      "objectType": "EntrySource",
      "objectId": "2",
      "schema": "{\"$schema\":\"https://json-schema.org/draft/2019-09/schema\",\"$defs\":null,\"type\":\"object\",\"properties\":{\"Email\":{\"$id\":\"Email\",\"title\":\"Email (Email ID)\",\"type\":\"string\",\"format\":\"email\",\"jsonPointer\":\"/Email\",\"maxItems\":1},\"PhoneNUmber\":{\"$id\":\"PhoneNUmber\",\"title\":\"PhoneNUmber (Mobile number)\",\"type\":\"string\",\"jsonPointer\":\"/PhoneNUmber\",\"maxItems\":1},\"FirstName\":{\"$id\":\"FirstName\",\"title\":\"FirstName\",\"type\":\"string\",\"jsonPointer\":\"/FirstName\",\"maxItems\":1},\"LastName\":{\"$id\":\"LastName\",\"title\":\"LastName\",\"type\":\"string\",\"jsonPointer\":\"/LastName\",\"maxItems\":1},\"CustomerId\":{\"$id\":\"CustomerId\",\"title\":\"CustomerId\",\"type\":\"number\",\"jsonPointer\":\"/CustomerId\",\"maxItems\":1},\"Birthday\":{\"$id\":\"Birthday\",\"title\":\"Birthday\",\"type\":\"integer\",\"format\":\"datetime\",\"pattern\":\"dd/MM/yyyy\",\"jsonPointer\":\"/Birthday\",\"maxItems\":1},\"Gender\":{\"$id\":\"Gender\",\"title\":\"Gender\",\"type\":\"string\",\"jsonPointer\":\"/Gender\",\"maxItems\":1},\"Salary\":{\"$id\":\"Salary\",\"title\":\"Salary\",\"type\":\"number\",\"jsonPointer\":\"/Salary\",\"maxItems\":1},\"City\":{\"$id\":\"City\",\"title\":\"City\",\"type\":\"string\",\"jsonPointer\":\"/City\",\"maxItems\":1},\"State\":{\"$id\":\"State\",\"title\":\"State\",\"type\":\"string\",\"jsonPointer\":\"/State\",\"maxItems\":1},\"Country\":{\"$id\":\"Country\",\"title\":\"Country\",\"type\":\"string\",\"jsonPointer\":\"/Country\",\"maxItems\":1},\"CompanyName\":{\"$id\":\"CompanyName\",\"title\":\"CompanyName\",\"type\":\"string\",\"jsonPointer\":\"/CompanyName\",\"maxItems\":1},\"HomepageURL\":{\"$id\":\"HomepageURL\",\"title\":\"HomepageURL\",\"type\":\"string\",\"jsonPointer\":\"/HomepageURL\",\"maxItems\":1},\"Chanel\":{\"$id\":\"Chanel\",\"title\":\"Chanel\",\"type\":\"string\",\"jsonPointer\":\"/Chanel\",\"maxItems\":1}}}"
    },
    "partition2Id": 1,
    "object2": {
      "applicationId": "SegmentCentral",
      "objectType": "8-T2",
      "objectId": null,
      "schema": "{\"$schema\":\"https://json-schema.org/draft/2019-09/schema\",\"$defs\":null,\"type\":\"object\",\"properties\":{\"fields.CUSTOMERID\":{\"$id\":\"fields.CUSTOMERID\",\"title\":\"CustomerID\",\"type\":\"integer\",\"jsonPointer\":\"/fields/CUSTOMERID\",\"maxItems\":1},\"fields.ACTIONDATETIME\":{\"$id\":\"fields.ACTIONDATETIME\",\"title\":\"ActionDateTime\",\"type\":\"integer\",\"format\":\"datetime\",\"jsonPointer\":\"/fields/ACTIONDATETIME\",\"maxItems\":1},\"fields.RESPONSECHANNEL\":{\"$id\":\"fields.RESPONSECHANNEL\",\"title\":\"ResponseChannel\",\"type\":\"string\",\"jsonPointer\":\"/fields/RESPONSECHANNEL\",\"maxItems\":1},\"fields.CAMPAIGNCODE\":{\"$id\":\"fields.CAMPAIGNCODE\",\"title\":\"CampaignCode\",\"type\":\"string\",\"jsonPointer\":\"/fields/CAMPAIGNCODE\",\"maxItems\":1},\"fields.OFFERCODE\":{\"$id\":\"fields.OFFERCODE\",\"title\":\"OfferCode\",\"type\":\"string\",\"jsonPointer\":\"/fields/OFFERCODE\",\"maxItems\":1},\"fields.CELLCODE\":{\"$id\":\"fields.CELLCODE\",\"title\":\"CellCode\",\"type\":\"string\",\"jsonPointer\":\"/fields/CELLCODE\",\"maxItems\":1},\"fields.TREATMENTCODE\":{\"$id\":\"fields.TREATMENTCODE\",\"title\":\"TreatmentCode\",\"type\":\"string\",\"jsonPointer\":\"/fields/TREATMENTCODE\",\"maxItems\":1},\"fields.PRODUCTID\":{\"$id\":\"fields.PRODUCTID\",\"title\":\"ProductID\",\"type\":\"integer\",\"jsonPointer\":\"/fields/PRODUCTID\",\"maxItems\":1},\"fields.RESPONSETYPECODE\":{\"$id\":\"fields.RESPONSETYPECODE\",\"title\":\"ResponseTypeCode\",\"type\":\"string\",\"jsonPointer\":\"/fields/RESPONSETYPECODE\",\"maxItems\":1}}}"
    },
    "attributeMappings": [
      {
        "createDate": 1666253513867,
        "createdBy": 2,
        "updateDate": 1666253513867,
        "updatedBy": 2,
        "attributeMappingId": 29,
        "attribute1Id": "PhoneNUmber",
        "attribute2Id": "fields.CUSTOMERID"
      },
      {
        "createDate": 1666253513869,
        "createdBy": 2,
        "updateDate": 1666253513869,
        "updatedBy": 2,
        "attributeMappingId": 30,
        "attribute1Id": "FirstName",
        "attribute2Id": "fields.RESPONSECHANNEL"
      },
      {
        "createDate": 1666253513870,
        "createdBy": 2,
        "updateDate": 1666253513870,
        "updatedBy": 2,
        "attributeMappingId": 31,
        "attribute1Id": "LastName",
        "attribute2Id": "fields.RESPONSECHANNEL"
      },
      {
        "createDate": 1666253513871,
        "createdBy": 2,
        "updateDate": 1666253513871,
        "updatedBy": 2,
        "attributeMappingId": 32,
        "attribute1Id": "Birthday",
        "attribute2Id": "fields.ACTIONDATETIME"
      },
      {
        "createDate": 1666253513872,
        "createdBy": 2,
        "updateDate": 1666253513872,
        "updatedBy": 2,
        "attributeMappingId": 33,
        "attribute1Id": "CustomerId",
        "attribute2Id": "fields.CUSTOMERID"
      }
    ],
    "autoSynchronizationSupport": "INBOUND",
    "context": {
      "targetCategoryType": "DataDefinition",
      "targetCategoryId": "3",
      "targetCategoryLabel": "PN_DD",
      "targetObjectLabel": "second Entry source",
      "targetObjectType": "EntrySource"
    },
    "label": "Test 2",
    "autoSynchronizedInstancesCount": null
  };

  config: ExportObjectConf = {
    // sourceApplicationAlias: 'UnicaSegmentCentral',
    targetCategoryFilter: 'AudienceSink',
    sourceSubcategoryId: 1,
    cifBaseUrl: 'http://lp2-ap-51815377.prod.hclpnp.com:7004/asset-viewer',
    cifHeaders: null,
    previousData: this.previousData,
    applicationMode: 'CREATE',
    translations: {
      exportMappingAppTitle: 'Map target system',
      name: 'Name',
      // sourceSelectionTitle: 'Segment selection',
      sourceSubCatSelectionLabel: 'Select audience table',
      targetSelectionTitle: 'Target system selections',
      targetCatSelectionLabel: 'Select external source',
      externalSourceSubCat1Label: 'Select journey data defination',
      externalSourceSubCat2Label: 'Select journey entry source',
      browse: 'Browse',
      cancel: 'Cancel',
      save: 'Save',
      // createModeTitle: 'Add source',
      // editModeTitle: 'Edit source',
      // viewModeTitle: 'View source',
      // selectRepository: 'Select source repository',
      // selectAudienceEventType: 'Select audience or event type',
      noRowsToShow: 'No rows to show.',
      loading: 'Loading...',
      replace: 'Replace',
      selectApplicationData: 'Select Journey data definition',
      fieldMapping: 'Field mapping',
      mapFieldsToExternalSource: 'Map only those filelds that you want to send to external source.',
      targetAppFieldTitle: 'Journey fields',
      sourceAppFieldTitle: 'Segemnt fields',
      // saveClose: 'Save & close',
      select: 'Select',
      search: 'Search',
      selectAttribute: 'Select attribute',
      unableToFetchData: 'Unable to fetch data. Please try again.',
      noCompatibleAttrAvailableForMapping: 'No compatible attribute available for mapping',
      generalError: 'Something went wrong. Please contact system administrator, if problem persist.',
      noResultFound: 'No results found!',
      selectedFolder: 'Selected Folder :',
      appTitle: '',
      gridTitle: '',
      gridSearchPlaceholder: '',
      previousMappingError: 'Previously mapped attribute is no longer valid here. Please map another attribute.',
      noOptionAvailable: 'No option available',
      allCategories: 'All categories',
      allContents: 'All contents',
      targetObjectIdSelectionAppTitle: 'Select data definition',
      targetObjectIdSelectionGridTitle: 'Data Definitions',
      targetObjectIdSelectionGridSearchLabel: 'Search data definition',
      targetObjectTypeSelectionAppTitle: 'Select entry source',
      targetObjectTypeSelectionGridTitle: 'Entry sources',
      targetObjectTypeSelectionGridSearchLabel: 'Search entry source',
      rows: 'Rows',
      total: 'Total',
      first: 'First',
      last: 'Last',
      prev: 'Previous',
      next: 'Next',
      requiredField: 'Required field',
      createMappingSuccessful: 'External source mapped successfully',
      updateMappingSuccessful: 'External source mapping updated successfully',
      configurationsMissing: 'No repositories to map. Please check Segment(source configuration) and at least 1 external source is configured properly',
      // invalidCharacters: 'Invalid character(s) "<" or ">" found.',
      maxLimitError: 'Exceeds max limit.',
      invalidCharacter: 'Invalid character "<"',
      duplicateMappingError: 'Mapping for the given object pair already exists.',
      selectedEntityLabel: 'Selected data definition:',

      folders: {
        panelHeader: 'Folders list',
        sortBy: 'Sort by',
        ascendingLabel: 'Ascending',
        descendingLabel: 'Descending',
        sortByName: 'Name',
        createdBy: 'Created'
      }
    }
  };

  readyToRender = false;
  buttonConfig: ButtonConf;

  constructor() { }

  ngOnInit(): void {

    this.buttonConfig = {
      name: 'previous',
      value: 'Open Export mapping',
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5,
      // disabled: true
    };

    const headers: HttpHeaders = new HttpHeaders()
      .set('m_user_name', 'asm_admin')
      .set('m_tokenId', '1678277725670-2-t4GK8HJ9-DaaB-JxomHQtL-j2gW-urOcAqa2')
      .set('api_auth_mode', 'manager')
      .set('401', 'ignore')
      .set('client_app_id', 'segmentcentral');

    this.config.cifHeaders = headers;
    // this.readyToRender = true;
    // setTimeout(() => {
    //     this.sideBarComponentRef1.openSideBar();
    // }, 1000);
  }


  openExportMapping() {
    this.readyToRender = true;
    setTimeout(() => {
      this.sideBarComponentRef.openSideBar();
    }, 1000);
  }

  mappedObject(data) {
    console.log(data);
  }

  cancleMapping() {
    this.readyToRender = false;
    this.sideBarComponentRef.close('close');
    console.log('External source mapping cancled!');
  }

}
