import { Component, OnInit } from '@angular/core';
import { LineChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/line-chart/line-chart';
import moment from 'moment';
import * as d3 from 'd3';

@Component({
  selector: 'app-line-chart-demo',
  templateUrl: './line-chart-demo.component.html',
  styleUrls: ['./line-chart-demo.component.scss']
})
export class LineChartDemoComponent implements OnInit {

  config: LineChartConfig = {
    id: 'lineChart',
    height: 700,
    width: 1000,
    margin: 50,
    type: 'curveMonotoneX',
    xAxis: {
      type: 'DATETIME',
      gridLines: true,
      labelFormat: (d) => moment(d).format('MMMM DD')
    },
    yAxis: {
      type: 'NUMBER',
      isInteger: true,
      gridLines: true
    },
    crosshair: {
      horizontal: {
        color: 'purple',
        strokeWidth: 2
      },
      vertical: {
        color: 'purple',
        strokeWidth: 2
      }
    },
    series: [
      {
        xName: 'date',
        yName: 'value',
        color: 'green',
        data: [{
          'value': 82,
          'date': new Date('2020-06-17T12:19:00+00:00').setHours(0,0,0,0)
        },
        {
          'value': 55,
          'date': '2020-06-19T12:19:00+00:00'
        },
        {
          'value': 35,
          'date': '2020-06-21T12:19:00+00:00'
        },
        {
          'value': 34,
          'date': '2020-06-23T12:19:00+00:00'
        },
        {
          'value': 45,
          'date': '2020-06-25T12:19:00+00:00'
        },
        {
          'value': 58,
          'date': '2020-06-27T12:19:00+00:00'
        },
        {
          'value': 34,
          'date': '2020-06-29T12:19:00+00:00'
        },
        {
          'value': 60,
          'date': '2020-07-01T12:19:00+00:00'
        },
        {
          'value': 75,
          'date': '2020-07-03T12:19:00+00:00'
        },
        {
          'value': 80,
          'date': new Date('2020-07-05T12:19:00+00:00').setHours(0,0,0,0)
        }]
      },


      {
        xName: 'date',
        yName: 'value',
        color: 'blue',
        data: [
          {
            'value': 20,
            'date': '2020-05-12T12:19:00+00:00'
          },
          {
            'value': 50,
            'date': '2020-05-14T12:19:00+00:00'
          },
          {
            'value': 30,
            'date': '2020-05-16T12:19:00+00:00'
          },
          {
            'value': 80,
            'date': '2020-05-18T12:19:00+00:00'
          },
          {
            'value': 55,
            'date': '2020-05-20T12:19:00+00:00'
          },
          {
            'value': 60,
            'date': '2020-05-22T12:19:00+00:00'
          },
          {
            'value': 45,
            'date': '2020-05-24T12:19:00+00:00'
          },
          {
            'value': 30,
            'date': '2020-05-26T12:19:00+00:00'
          },
          {
            'value': 40,
            'date': '2020-05-28T12:19:00+00:00'
          },
          {
            'value': 70,
            'date': '2020-05-30T12:19:00+00:00'
          },
          {
            'value': 63,
            'date': '2020-06-01T12:19:00+00:00'
          },
          {
            'value': 40,
            'date': '2020-06-03T12:19:00+00:00'
          },
          {
            'value': 50,
            'date': '2020-06-05T12:19:00+00:00'
          },
          {
            'value': 75,
            'date': '2020-06-07T12:19:00+00:00'
          },
          {
            'value': 20,
            'date': '2020-06-09T12:19:00+00:00'
          },
          {
            'value': 50,
            'date': '2020-06-11T12:19:00+00:00'
          },
          {
            'value': 80,
            'date': '2020-06-13T12:19:00+00:00'
          },
          {
            'value': 75,
            'date': '2020-06-15T12:19:00+00:00'
          }
        ]
      },


      {
        xName: 'date',
        yName: 'value',
        color: 'yellow',
        data: [
          {
            'value': 29,
            'date': '2020-07-07T12:19:00+00:00'
          },
          {
            'value': 40,
            'date': '2020-07-09T12:19:00+00:00'
          },
          {
            'value': 54,
            'date': '2020-07-11T12:19:00+00:00'
          },
          {
            'value': 67,
            'date': '2020-07-13T12:19:00+00:00'
          },
          {
            'value': 90,
            'date': '2020-07-15T12:19:00+00:00'
          },
          {
            'value': 84,
            'date': '2020-07-17T12:19:00+00:00'
          },
          {
            'value': 43,
            'date': '2020-07-19T12:19:00+00:00'
          }
        ]
      }
    ]
  }

  constructor() {
    moment.locale('de-DE');
  }

  ngOnInit(): void {
  }

  pointClicked(series) {
    console.log(series);
  }

}
