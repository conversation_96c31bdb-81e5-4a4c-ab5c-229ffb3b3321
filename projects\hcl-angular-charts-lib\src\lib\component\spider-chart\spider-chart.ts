export interface SpiderChartConfig {
    chartContainerId?: string;
    numofLevels?: number;
    size?: number;
    width?: number;
    drawDataTextLabels?: boolean;
    chartBgColor?: string;
    gridLineColor?: string;
    levelsStrokeColor?: string;
    gradientColor1?: string;
    gradientColor2?: string;
    gradientColor3?: string;
    labelsTextColor?: string;
    radiusForDataPointsCircle?: number;
    dataPointCircleColor?: string;
    tickTextColor?: string;
    gradientOffset1?: number;
    gradientOffset2?: number;
    gradientOffset3?: number;
  }

  