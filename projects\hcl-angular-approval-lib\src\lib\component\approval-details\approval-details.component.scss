$title-font-family: <PERSON><PERSON><PERSON>;
$content-font-family: <PERSON><PERSON>;
$title-font-color: #6d7692;

.approval-details-container {
    .basic-info-wrapper {
        margin-bottom: 20px;
    }
    .approval-tabs-wrapper {
        height: calc(100% - 240px);
        mat-tab-group {
            height: 100%;
            .mat-tab-body-wrapper {
                height: calc(100% - 40px);
                margin-bottom: 15px;
            }
            .mat-tab-label-content {
                font-family: $title-font-family;
                font-weight: bold;
                font-size: 16px;
            }
        }
    }
    .error-msg-container {
        display: flex;
        width: 100%;
        height: 80%;
        align-items: center;
        justify-content: center;

        span {
            font-family: $title-font-family;
            color: $title-font-color;
            font-weight: bold;
            font-size: 16px;
        }
    }
    .close-btn-container {
        bottom: 4%;
        right: 0;
    }
}
