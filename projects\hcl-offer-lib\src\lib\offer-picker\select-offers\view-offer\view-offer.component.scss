.default-values {
  height: 100%;
  @mixin two-column-layout {
    display: flex;
    flex-wrap: wrap;
    align-items: end;
    & > *:not(.hcl-full-width) {
      flex: 0 1 48%;
      margin-bottom: 10px;
      overflow: hidden;

      &:nth-child(even) {
        margin-left: 4%;
      }
    }
  }

  .attributes-container {
    overflow: auto;
    height: calc(100% - 24px);
  }

  .variation-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .variation-name {
      margin: 0 0 0 5px;
      color: #6d7692;
      font-family: "Montserrat";
      font-size: 16px;
      letter-spacing: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100px;
    }
  }

  .header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      color: #6d7692;
      font-family: Montserrat;
      font-size: 20px;
      font-weight: bold;
      line-height: 24px;
    }
  }

  .metadata {
    display: flex;
    // flex-direction: column;
    margin: 10px 0 10px;
    padding: 20px;
    background-color: #f5f5f5;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
    position: relative;
    @include two-column-layout;
    height: 29%;

    .form-field {
      // width: 33%;
      min-height: 30px;
      .ro-label {
        flex: none;
        padding: 0;
        width: 25%;
      }
      .ro-value {
        padding: 0;
        width: 75%;
        padding-right: 10px;
      }
    }

    .description {
      // width: 33%;
      // margin-top: 22px;
      font-family: Roboto;
      font-size: 14px;
      letter-spacing: 0;
      line-height: 16px;

      .ro-label {
        color: #b8b7b7;
        padding-right: 10px;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 25%;
        position: relative;
        &.multi-line {
          top: -15px;
        }
      }

      .ro-value {
        color: #15161c;
        display: inline-block;
        overflow: hidden;
        width: 75%;
        padding-right: 10px;
        word-break: break-all;
        position: relative;

        &.desc-ellipsis {
          height: 33px;
          &::after {
            position: absolute;
            content: "...";
            bottom: 0;
            right: 0;
          }
        }
      }
    }
  }
  .default-attributes {
    padding: 20px 0px;
    height: auto;
    flex-wrap: wrap;
    align-content: baseline;
    @include two-column-layout;

    .attribute {
      // width: 30%;
      // margin-right: 1.5%;
      display: inline-block;
    }
  }
  .dependent-attribute-container {
    display: flex;
    padding: 15px 0;
    flex-wrap: wrap;
    @include two-column-layout;

    .content-item {
      display: flex;
      align-items: center;
      padding: 5px 0;
      span {
        width: 40%;
        color: #444444;
        font-family: Roboto;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.depends-on {
          color: #959595;
          width: 20%;
        }
      }

      .hcl-icon-link {
        font-size: 20px;
        color: #959595;
        padding: 0 10px;
        cursor: auto;
        width: 20%;
        text-align: left;
      }
    }
  }

  .relevant-products-list {
    padding: 20px 0px;
    height: auto;
    // min-height: 250px;
    overflow-y: auto;

    hcl-checkbox {
      .checkbox-section {
        padding: 0;
      }
      section {
        flex-wrap: wrap;
        flex-wrap: wrap;

        > div {
          display: block;
        }

        .checkbox-data {
          width: 48%;
          margin: 0 0 10px 0;
          display: inline-flex;
          align-items: flex-start;

          &:nth-child(even) {
            margin-left: 4%;
          }

          .checkbox-label {
            margin-left: 15px;
            display: block;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
            flex-grow: 1;
            color: #444444;
            font-family: Roboto;
            font-size: 14px;
            line-height: 16px;
          }
        }
      }
    }
  }
  mat-radio-group {
    align-items: center;
  }

  .mat-accordion {
    .mat-expansion-panel {
      .mat-expansion-panel-header {
        padding: 0 20px;
      }
      .mat-expansion-panel-body {
        padding: 0 20px 16px;
      }
      overflow: visible;
      .ui-calendar {
        .ui-datepicker {
          min-width: 320px;
          max-width: 320px;
        }
      }
    }
  }

  .form-field {
    font-family: Roboto;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 16px;
    display: flex;

    .ro-label {
      color: #b8b7b7;
      // margin-right: 10px;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      // flex: 1;
      padding: 8px 10px 8px 0;
      width: 25%;
    }

    .ro-value {
      color: #15161c;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      // flex: 1;
      padding: 8px 18px 8px 0;
      width: 75%;

      .hcl-icon-view {
        color: #959595;
      }
    }
  }
}
.relevant-products-btns {
  display: flex;
  flex-direction: row-reverse;
  width: 100%;
}

.mat-content {
  align-items: center;
}

.hcl-sidebar {
  .mat-drawer-transition .mat-drawer-backdrop {
    position: fixed;
  }
}

.day .mat-horizontal-stepper-content {
  overflow-y: auto;
  overflow-x: hidden;
}

.relevant-products-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 75px;
  color: #b8b7b7;
  font-family: Roboto, sans-serif;
  font-size: 13px;
}

.rt-preview-container {
  width: 600px;
  height: 145px;
  position: relative;
  background: rgba(0, 0, 0, 0.8);
  overflow: hidden;
  margin: auto;
  padding: 10px;
  cursor: default;
  caret-color: transparent;

  .view {
    position: absolute;
    right: 15px;
    top: 6px;
    color: #f5f5f5;
    font-family: Roboto;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 16px;
  }

  .disabled-editor {
    width: 100%;
    height: 300px;
    // transform: scale(0.5, 0.8) translate(-50%, -7%);

    .fr-wrapper {
      height: 125px;
      max-height: 125px;
      overflow: auto;
    }
  }
}

.disabled-editor {
  opacity: 0.9;

  .fr-toolbar {
    display: none;
  }
  .fr-element.fr-view {
    pointer-events: none;
    border: none !important;
  }
}

#offer-info-sidebar {
  .hcl-sidebar {
    .mat-drawer-transition .mat-drawer-backdrop {
      position: fixed;
    }
    .mat-drawer-inner-container {
      width: 150vh !important;
      overflow: hidden;
    }
  }
}

.rt-preview-container {
  width: 600px;
  height: 140px;
  position: relative;
  background: rgba(0, 0, 0, 0.8);
  overflow: hidden;
  margin: auto;
  padding: 15px 12px 0;
  cursor: pointer;
  caret-color: transparent;

  .view {
    position: absolute;
    right: 15px;
    top: 6px;
    color: #f5f5f5;
    font-family: Roboto;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 16px;
  }

  .disabled-editor {
    width: 1150px;
    height: 300px;
    transform: scale(0.5, 0.8) translate(-50%, -7%);

    .fr-wrapper {
      height: 125px;
      max-height: 125px;
      overflow: hidden !important;
    }
  }
}

.rt-sidebar-container {
  height: 100%;
  padding: 30px;
  background-color: #ececec;
  box-shadow: -15px 0 10px -2px rgba(0, 0, 0, 0.3);
  caret-color: transparent;
  position: relative;

  .title {
    color: #6d7692;
    font-family: Montserrat-bold;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 19px;
    margin-bottom: 15px;
  }

  .hcl-custom-text-editor {
    height: 100%;
  }

  .fr-wrapper {
    max-height: calc(100% - 100px) !important;
    height: calc(100% - 100px);
    border: none !important;
  }

  .fr-second-toolbar {
    display: none;
  }

  hcl-button {
    position: absolute;
    right: 30px;
    bottom: 20px;
  }
}

.ellipsis {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ap-popover-container {
  position: relative;
  left: -10px;

  .inline-loader {
    position: absolute;
    top: -3px;
    left: -30px;
  }

  [class^="hcl-icon"] {
    font-size: 15px;
    color: #959595;
    margin: 0;
    position: relative;

    &.d-popover-icon {
      pointer-events: none;
      opacity: 0.7;
    }
  }

  .hcl-icon-offer-tags {
    left: -8px;
    font-size: 18px;
  }

  .hcl-icon-view {
    left: -3px;
  }
}

.ap-item-details-container {
  max-width: 450px;
  height: auto;
  max-height: 540px;
  position: relative;
  background: rgba(0, 0, 0, 0.8);
  overflow: hidden;
  margin: auto;

  &.no-preview-available {
    display: inline-block;
    padding: 12px 12px 30px;
    background: transparent;
  }

  .close-pop-over {
    position: absolute;
    top: 7px;
    right: 7px;

    span {
      color: #f5f5f5;
    }
  }

  .ap-item-scroll-container {
    padding: 30px 0;
    width: 450px;
    .ap-item-details {
      display: block;
      width: 100%;
      padding: 0 30px 0;
      position: relative;
      color: #f5f5f5;
      font-family: "Helvetica";
      font-size: 14px;
      letter-spacing: 0.17px;
      line-height: 21px;
      height: auto;
      max-height: 480px;
      overflow-y: auto;

      .thumbnail {
        margin-bottom: 25px;
        display: block;
        background-color: #d8d8d8;
        padding: 10px;
        img {
          max-width: 100%;
          display: block;
          margin: 0 auto;
          max-height: 300px;
        }
        [class^="hcl-icon"] {
          font-size: 225px;
          color: #959595;
          cursor: auto;
        }
      }

      .metadata {
        position: relative;
        // margin-bottom: 20px;

        .fileName {
          font-size: 16px;
          display: flex;
          a {
            color: #f5f5f5;
            margin-right: 5px;
            &:hover {
              text-decoration: none;
            }
          }
        }

        .size {
          p {
            margin-bottom: 3px;
          }
        }

        .mime-type {
          display: inline-block;
          bottom: 0;
          right: 0;
          border-radius: 7.5px;
          background-color: #d8d8d8;
          padding: 3px 10px;
          max-width: 390px;
          margin-top: 10px;

          span {
            color: #6d7692;
            font-family: Roboto;
            font-size: 12px;
            letter-spacing: 0.4px;
            line-height: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            display: block;
          }
        }
      }

      .description {
        margin-top: 10px;
      }
    }
  }
  .error-container {
    background: rgba(0, 0, 0, 0.7);
    .ap-no-preview {
      // background-color: rgba(0, 0, 0, 0.7);
      display: inline-block;
      padding: 10px 12px;
      color: #f5f5f5;
      font-family: Roboto;
      font-size: 14px;
      letter-spacing: 0;
      line-height: 18px;
      position: relative;
      max-width: 600px;

      &.left-arrow {
        &::after {
          content: "";
          border-left: 0px solid transparent;
          border-right: 12px solid transparent;
          border-top: 20px solid rgba(0, 0, 0, 0.7);
          position: absolute;
          bottom: -20px;
          left: 0;
        }
      }
      &.right-arrow {
        &::after {
          content: "";
          border-left: 12px solid transparent;
          border-right: 0px solid transparent;
          border-top: 20px solid rgba(0, 0, 0, 0.7);
          position: absolute;
          bottom: -20px;
          right: 0;
        }
      }
    }
  }
}

[class^="hcl-icon-"] {
  cursor: pointer;
  font-family: "fontello";
  font-style: normal;
  font-weight: normal;
  display: inline-block;
  text-decoration: inherit;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
