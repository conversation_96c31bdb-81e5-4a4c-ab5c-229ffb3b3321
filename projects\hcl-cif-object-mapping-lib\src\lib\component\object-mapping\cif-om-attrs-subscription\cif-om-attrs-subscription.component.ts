import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { SubscriptionLike } from 'rxjs';
import { HclCifObjectMappingLibService } from '../../../hcl-cif-object-mapping-lib.service';

@Component({
  selector: 'hcl-cif-om-attrs-subscription',
  templateUrl: './cif-om-attrs-subscription.component.html',
  styleUrls: ['./cif-om-attrs-subscription.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class CifOmAttrsSubscriptionComponent implements OnInit, OnDestroy {

  @Input() config: any;
  @Input() isEditMode: boolean;
  @Output() mappedData: EventEmitter<any> = new EventEmitter<any>();

  subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();

  loadContent = false;
  serviceSchema: any;
  applicationSchema: any;
  serviceRepositoryDetails: any;
  applicationSchemaAttributes = []; // attributes from application schema api
  serviceAttributeDropdownOptions = []; // service attributes combined with html to show as dropdown option
  serviceAttributesMap = new Map<string, string>(); // Service attribute id-title mapping
  crossAttributesIdMappingMap = new Map<string, string>(); // service attr id-application attr id mapping
  contentMappingMap = new Map<number, any>(); // indexed map of mapped attributes with both service and application id & titles
  contentMappingConfigData = [];

  constructor(private hclCifObjectMappingLibService: HclCifObjectMappingLibService) { }

  ngOnInit(): void {
    this.serviceSchema = JSON.parse(this.hclCifObjectMappingLibService.serviceSchema);
    this.applicationSchema = JSON.parse(this.hclCifObjectMappingLibService.applicationSchema);
    this.serviceRepositoryDetails = this.hclCifObjectMappingLibService.selectedCmsRepository;
    // this.subscriptionList.push(
    //   this.hclCifObjectMappingLibService.getSelectedCmsRepository().subscribe(data => {
    //   }));
    this.loadAttributes();
  }

  loadAttributes() {
    this.loadContent = false;
    this.applicationSchemaAttributes = [];
    this.serviceAttributeDropdownOptions = [];
    if (this.applicationSchema && this.applicationSchema.properties) {
      for (const property in this.applicationSchema.properties) {
        if (this.applicationSchema.properties[property]) {
          this.applicationSchemaAttributes.push(this.applicationSchema.properties[property]);
        }
      }
    }

    if (this.config.applicationMode === 'VIEW' || this.config.applicationMode === 'EDIT') {
      this.hclCifObjectMappingLibService.mappingDataMap.forEach((value, key) => {
        this.crossAttributesIdMappingMap.set(value.attribute1Id, value.attribute2Id);
      });
    }

    if (this.serviceSchema && this.serviceSchema.properties) {
      for (const property in this.serviceSchema.properties) {
        if (this.serviceSchema.properties[property]) {
          const item = this.serviceSchema.properties[property];
          this.serviceAttributeDropdownOptions.push({
            label: `${item.title} (${this.capitalizeFirstLetter(item.format || item.type + '')})`, value: item.$id,
            toolTipHTML: `<span>${item.title} (${this.capitalizeFirstLetter(item.format || item.type + '')})</span>`,
            item: item
          });
          this.serviceAttributesMap.set(item.$id, item.title);
        }
      }
    }

    if (this.applicationSchemaAttributes.length) {
      this.applicationSchemaAttributes.forEach((item, index) => {
        this.loadContentItems(item, index);
      });
      this.emitMappedData();
    }

    if (this.config.applicationMode === 'VIEW') {
      this.contentMappingConfigData.map((configData: any) => {
        configData[1].dropdownConfig.disabled = true;
      });
    }

    this.loadContent = true;
  }

  loadContentItems(attribute, index) {
    const newFormGroup = new UntypedFormGroup({
      mappedAttribute: new UntypedFormControl(null)
    });

    const options = this.returnOptionsAccordingToType(attribute);

    if (options && options.length) {
      options.unshift({
        label: `--${this.config.translations.select}--`,
        value: null,
      });
    }

    const config = [
      {
        displayTitle: `${attribute.title} (${this.capitalizeFirstLetter(
          (attribute.format && attribute.format !== this.hclCifObjectMappingLibService.applicationConstants.lowerCaseAuto && attribute.format)
          || attribute.type + '')})`, ...attribute
      }, {
        dropdownConfig: {
          options: [...options],
          baseOptions: [...options],
          isToolTip: true,
          placeholder: options && options.length ? this.config.translations.selectAttribute :
            this.config.translations.noCompatibleAttrAvailableForMapping,
          name: 'mappedAttribute',
          formControl: newFormGroup.controls.mappedAttribute,
          dynamicAction: { templateName: 'mappedAttribute', template: null },
          disabled: options && options.length ? false : true,
          // errorList: [{
          //   errorCondition: 'required',
          //   errorMsg: this.translate.instant('MESSAGES.REQUIRED_FIELD')
          // }],
          previousMappingError: this.isEditMode ? this.checkAndUpdatePreviousMappingMap(attribute.$id, options) : false
        },
        searchInput: {
          name: 'mappedAttributeSearch',
          placeholder: this.config.translations.search,
          formControlName: new UntypedFormControl(''),
          icon: 'hcl-icon-search',
          type: 'text',
          autofocus: true
        }
      }, {
        // templateEdit: this.hclCifObjectMappingLibService.previousObjectMappingData ? true : false,
        edited: false
      }];


    if (this.crossAttributesIdMappingMap.size && this.crossAttributesIdMappingMap.has(attribute.$id)) {
      newFormGroup.controls.mappedAttribute.setValue(this.crossAttributesIdMappingMap.get(attribute.$id));
      const attributeMapping = {
        attribute1Id: attribute.$id,
        attribute1Title: attribute.title,
        attribute2Id: this.crossAttributesIdMappingMap.get(attribute.$id),
        attribute2Title: this.serviceAttributesMap.get(this.crossAttributesIdMappingMap.get(attribute.$id))
      };
      this.contentMappingMap.set(index, attributeMapping);
    } else {
      newFormGroup.controls.mappedAttribute.setValue('');
    }
    this.setMapping(config);
  }

  returnOptionsAccordingToType(attribute) {
    let options = [];
    switch (attribute.type.toUpperCase()) {
      case this.hclCifObjectMappingLibService.applicationConstants.capitalString():
        if (attribute.hasOwnProperty('format') &&
          attribute.format.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalUrl()) {
          options = this.serviceAttributeDropdownOptions
            .filter(item => item.item && item.item.type && item.item.format &&
              (item.item.format.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalUrl)
              && item.item.type.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalString);
        } else if (attribute.hasOwnProperty('format') &&
          attribute.format.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalHtml()) {
          options = [...this.serviceAttributeDropdownOptions];
        } else if (attribute.hasOwnProperty('format') &&
          attribute.format.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalEmail()) {
          options = this.serviceAttributeDropdownOptions
            .filter(item => item.item && item.item.type && item.item.format &&
              (item.item.format.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalEmail()));
        } else {
          const htmlFilteredAttributes = this.serviceAttributeDropdownOptions.filter(attr => {
            return !attr.item.format ||
              attr.item.format.toUpperCase() !== this.hclCifObjectMappingLibService.applicationConstants.capitalHtml();
          });
          options = [...htmlFilteredAttributes];
        }
        return options;
      case this.hclCifObjectMappingLibService.applicationConstants.capitalNumber():
        options = this.serviceAttributeDropdownOptions
          .filter(item => item.item && item.item.type &&
            (item.item.type.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalInteger() ||
              item.item.type.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalNumber()));
        return options;
      case this.hclCifObjectMappingLibService.applicationConstants.capitalInteger():
        if (attribute.hasOwnProperty('format') &&
          attribute.format.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalDateTime()) {
          options = this.serviceAttributeDropdownOptions
            .filter(item => item.item && item.item.type && item.item.format &&
              (item.item.format.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalDateTime())
              && item.item.type.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalInteger());
        } else {
          options = this.serviceAttributeDropdownOptions
            .filter(item => item.item && item.item.type &&
              item.item.type.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalInteger());
        }
        return options;
      case this.hclCifObjectMappingLibService.applicationConstants.capitalBoolean():
        options = this.serviceAttributeDropdownOptions
          .filter(item => item.item && item.item.type &&
            item.item.type.toUpperCase() === this.hclCifObjectMappingLibService.applicationConstants.capitalBoolean());
        return options;
    }
  }

  checkAndUpdatePreviousMappingMap(comAttribute: string, options) {
    const cmsAttribute = this.serviceAttributesMap.get(comAttribute);
    const previousMapping = cmsAttribute && options.find((option: any) => option.item && option.item.$id === cmsAttribute);
    if (cmsAttribute && !previousMapping) {
      this.hclCifObjectMappingLibService.invalidPreviousMappingMap.set(comAttribute, true);
      this.serviceAttributesMap.delete(comAttribute);
      return true;
    }
    return false;
  }

  mappedAttributeSelection(attributeValue, config, index) {
    config[1].dropdownConfig.previousMappingError = false;
    this.hclCifObjectMappingLibService.invalidPreviousMappingMap.delete(config[0]['$id']);
    if (attributeValue) {
      const attributeMapping = {
        attribute1Id: config[0]['$id'],
        attribute1Title: config[0]['title'],
        attribute2Id: attributeValue,
        attribute2Title: this.serviceAttributesMap.has(attributeValue) ? this.serviceAttributesMap.get(attributeValue) : ''
      };
      this.contentMappingMap.set(index, attributeMapping);
    } else {
      if (this.contentMappingMap.has(index)) {
        this.contentMappingMap.delete(index);
      }
    }

    config[1].searchInput.icon = 'hcl-icon-search';
    this.emitMappedData();
  }


  emitMappedData() {
    if (this.contentMappingMap.size) {
      this.mappedData.emit({ contentMapping: this.contentMappingMap });
    } else {
      this.mappedData.emit();
    }
  }

  clearMappedAttributeSearch(mappingConfig) {
    mappingConfig.dropdownConfig.options = [...mappingConfig.dropdownConfig.baseOptions];
    mappingConfig.searchInput.formControlName.setValue('');
  }

  filterMappedAttribute(searchString, mappingConfig) {
    if (typeof searchString === 'string') {
      mappingConfig.dropdownConfig.options = [...mappingConfig.dropdownConfig.baseOptions];
      if (searchString.length > 0) {
        mappingConfig.dropdownConfig.options = mappingConfig.dropdownConfig.options
          .filter(element => (element.label + '').toUpperCase().includes((searchString + '').toUpperCase()));
      }
      this.setIcon(searchString, mappingConfig.searchInput);
    }
  }

  setIcon(searchString, inputConfig) {
    if (searchString) {
      inputConfig.icon = 'hcl-icon-close-x';
    } else {
      inputConfig.icon = 'hcl-icon-search';
    }
  }

  setMapping(attributeConfig: any) {
    this.contentMappingConfigData.push(attributeConfig);
  }


  capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  ngOnDestroy(): void {
    this.subscriptionList.forEach(sub => { sub.unsubscribe(); });
  }
}
