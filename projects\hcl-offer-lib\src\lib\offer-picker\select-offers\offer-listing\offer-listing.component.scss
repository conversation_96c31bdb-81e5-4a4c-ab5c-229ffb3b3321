.overflow-class {
  overflow: hidden;
  text-overflow: ellipsis;
}

.link {
  color: #0078d8;
  &:hover {
    cursor: pointer;
  }
}

.manage-selections {
  position: absolute;
  bottom: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 380px;
  background-color: #f8fbff;
  .selected-offers {
    width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0 0 0 5px;
    font-size: 14px;
    color: #6d7692;
  }
  .manage-selections-button {
    width: 150px;
  }
}

.top-label {
  position: absolute;
  top: -10px;
}

.bottom-label {
  position: absolute;
  top: 10px;
  font-family: "Roboto";
  font-size: 12px;
  letter-spacing: 0.4px;
}

.bullet-round {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  display: inline-block;
}

.green-bullet {
  background-color: #c4d056;
}
.grey-bullet {
  background-color: #bcbbbb;
}
.orange-bullet {
  background-color: #ffb755;
}
