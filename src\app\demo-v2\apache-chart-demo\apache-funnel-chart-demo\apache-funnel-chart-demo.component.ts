import { Component, OnInit, ViewChild } from '@angular/core';
import { ApacheChartComponent } from 'projects/hcl-angular-charts-lib/src/public-api';
import { ApacheFunnelChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/apache-chart/apache-chart';

@Component({
  selector: 'app-apache-funnel-chart-demo',
  templateUrl: './apache-funnel-chart-demo.component.html',
  styleUrls: ['./apache-funnel-chart-demo.component.scss']
})
export class ApacheFunnelChartDemoComponent implements OnInit {


  @ViewChild('apacheFunnelChart') apacheFunnelChart: ApacheChartComponent;

  apacheFunnelChartConfig: ApacheFunnelChartConfig = {
    title: {
      text: 'Funnel Chart',
      id: "apacheFunnelChart",
    },
    tooltip: {
      show: false
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    series: [
      {
        name: 'Funnel',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        emphasis: {
          disabled: true
        },
        minSize: '15%',
        maxSize: '60%',
        sort: 'descending',
        gap: 0,
        color: [
          '#B6F38F',
          '#A4E68F',
          '#8BD38F',
          '#6CBC8F',
          '#56AB8F',
          '#3B978F',
          '#28898F'
        ],
        dimensions: [
          'funnelSize',
          'achievedCount',
          'achievedPercentage',
          'days',
          'hours',
          'min'
        ],
        label: {
          show: true,
          position: 'inside',
          formatter: [
            ' {a|Milestone achieved by - {@achievedPercentage}%}',
            '{b|{@achievedCount}}',
            '{c|Avg time : {@days} days {@hours} hours {@min} mins}'
          ].join('\n'),

          rich: {
            a: {
              color: '#fff',
              fontSize: '12px',
              fontFamily: 'Segoe UI',
              fontWeight: 'normal'
            },
            b: {
              color: '#fff',
              fontSize: '20px',
              fontFamily: 'Segoe UI',
              fontWeight: 'normal'
            },
            c: {
              color: '#fff',
              fontSize: '11px',
              fontFamily: 'Segoe UI',
              fontWeight: 'normal'
            }
          }
        },
        itemStyle: {
          borderWidth: 0
        },
        data: [
          [5, 10, 100, 0, 0, 0],
          [40, 4, 40, 0, 0, 0],
          [60, 4, 100, 0, 0, 0],
          [80, 5, 125, 0, 0, 0],
          [100, 1, 20, 0, 0, 0],
          [120, 3, 300, 0, 0, 0],
          [140, 1, 33.33, 0, 0, 0]
        ]
      }
    ]
  }

  constructor() { }

  ngOnInit(): void {
  }

}
