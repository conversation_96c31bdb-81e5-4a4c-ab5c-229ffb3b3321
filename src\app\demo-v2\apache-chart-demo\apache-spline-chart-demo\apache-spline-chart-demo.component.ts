import { Component, OnInit, ViewChild } from '@angular/core';
import { ApacheChartComponent } from 'projects/hcl-angular-charts-lib/src/public-api';
import { ApacheLineChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/apache-chart/apache-chart';

@Component({
  selector: 'app-apache-spline-chart-demo',
  templateUrl: './apache-spline-chart-demo.component.html',
  styleUrls: ['./apache-spline-chart-demo.component.scss']
})
export class ApacheSplineChartDemoComponent implements OnInit {

  @ViewChild('apacheLineChart') apacheSpineChart: ApacheChartComponent;

  apacheSpineChartConfig: ApacheLineChartConfig = {
    title: {
      id: 'goalsAnalysisSPLineChart'
    },
    tooltip: {
      showContent: true,
      trigger: 'item',
    },
    grid: {
      left: '1%',
      right: '1%',
      bottom: '10%',
      top: '4%',
      height: 'auto',
      width: 'auto'
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: ['Order Dispatched to Delivery',
        'Payment Confirmation Received',
        'PO Released ',
        'New buyers for the month'],
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 0,
          margin: 8,
          fontSize: 12,
          width: 100,
          overflow: "truncate",
      },
      triggerEvent: true 
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [{
      type: 'line' as 'line',
      name: "Spline1",
      data: [4,5,8,3],
      symbol: 'emptyCircle',
      symbolSize: 10,
      smooth: true,
      lineStyle: { width: 0 },
      itemStyle: { color: '#FF9272' },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgb(255, 146, 114)' },
            { offset: 1, color: 'rgb(243, 113, 154)' }
          ],
          opacity: 15,
        }
      }
    }]
  }
  constructor() { }

  ngOnInit(): void {
  }

}
