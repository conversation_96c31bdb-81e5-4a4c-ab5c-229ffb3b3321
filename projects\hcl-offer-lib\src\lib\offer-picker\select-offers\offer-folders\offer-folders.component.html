<div #sidePaneContainerPlaceHolder style="height: 100%">
    <template #placeHolder></template>
</div>

<ng-template #topActionsTemplate>
    <div class="top-actions-container">
        <ul class="sort-menu-container">
            <li>{{'FOLDERS.TITLES.SORT_BY'| translate}}</li>
            <li class="sort-by-item" (click)="sortFoldersByItem($event, 'displayName')">{{'FOLDERS.TITLES.NAME'| translate}}
                <div class="icon-container">
                    <i class="hcl-icon-up-dir"
                        [ngStyle]="{'color':sortOrder === 'ASC' && sortBy === 'displayName'? '#f5821e' : '#959595' }"></i>
                    <i class="hcl-icon-down-dir"
                        [ngStyle]="{'color':sortOrder === 'DESC' && sortBy === 'displayName'? '#f5821e' : '#959595' }"></i>
                </div>
            </li>
            <li class="sort-by-item" (click)="sortFoldersByItem($event, 'createdTimeStamp')">
                {{'FOLDERS.TITLES.DATE'| translate}}
                <div class="icon-container">
                    <i class="hcl-icon-up-dir"
                        [ngStyle]="{'color':sortOrder === 'ASC' && sortBy === 'createdTimeStamp'? '#f5821e' : '#959595' }"></i>
                    <i class="hcl-icon-down-dir"
                        [ngStyle]="{'color':sortOrder === 'DESC' && sortBy === 'createdTimeStamp'? '#f5821e' : '#959595' }"></i>
                </div>
            </li>
            <li class="d-none">{{'FOLDERS.TITLES.OWNER'| translate}}
                <div class="icon-container">
                    <i class="hcl-icon-up-dir"></i>
                    <i class="hcl-icon-down-dir"></i>
                </div>
            </li>
        </ul>
    </div>
</ng-template>