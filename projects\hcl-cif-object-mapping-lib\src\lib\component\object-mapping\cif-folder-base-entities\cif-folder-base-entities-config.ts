

export interface CifFolderBaseEntitiesConf {
    translations: { [key: string]: any };
    applicationService: any;
    cifRepository?: string;
    previousSelectedCategoryId?: number | string;
    previousSelectedItemLable?: string;
    assetContext?: 'content' | 'category';
    rootFolder: { id: number, displayName: string, parentFolderId?: number };
    cifBaseUrl?: string;
    isPaginated?: boolean;
    removeGlobalSearch?: boolean;
    rootFolderAsRootFolderParent?: boolean;
}
