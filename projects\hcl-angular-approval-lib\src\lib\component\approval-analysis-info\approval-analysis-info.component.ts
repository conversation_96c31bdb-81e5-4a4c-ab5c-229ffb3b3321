import { Component, OnInit, OnDestroy, Input, ViewEncapsulation } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { Approval } from '../../models/approval';
import { DataGridColumnConf, DataGridConf } from 'hcl-data-grid-lib';
import { TranslateService } from '@ngx-translate/core';
import { ApprovalSharedDataService } from '../../service/approval-shared-data.service';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'hcl-approval-analysis-info',
  templateUrl: './approval-analysis-info.component.html',
  styleUrls: ['./approval-analysis-info.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ApprovalAnalysisInfoComponent implements OnInit, OnDestroy {

  @Input() approval: Approval;
  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  analysisGridColumns: DataGridColumnConf[] = [];
  analysisGridApi: any;
  analysisGridConf: DataGridConf = {
    rowHeight: 34,
    columns: [],
    noDataFlag: false,
    noRowsTemplate: this._translate.instant('APPROVALPICKER.TITLES.NO_ROWS_TO_SHOW'),
    data: []
  };

  constructor(private _translate: TranslateService,
    private _datePipe: DatePipe,
    private _sharedDataService: ApprovalSharedDataService) {
    this.initGridColumns();
  }

  ngOnInit(): void {
    this.subscriptionList.push(this._sharedDataService.getApprovalResponseHistory().subscribe((res: any[]) => this.populateAnalysisData(res)));
  }

  private initGridColumns() {
    this.analysisGridColumns = [{
      field: 'user',
      header: this._translate.instant('APPROVALPICKER.TITLES.USER'),
      colId: 'user',
      rendererTemplateName: 'userCell',
      suppressColumnReorder: true,
      autoResizeToFit: true,
      minWidth: 526
    },
    {
      field: 'action',
      header: this._translate.instant('APPROVALPICKER.TITLES.ACTION'),
      colId: 'action',
      rendererTemplateName: 'actionCell',
      suppressColumnReorder: true,
      autoResizeToFit: true,
      minWidth: 526
    }];

    this.analysisGridConf.columns = this.analysisGridColumns;
  }

  private populateAnalysisData(data) {
    this.analysisGridConf.data = this.generateAnalysisData(data);
    if (this.analysisGridApi) {
      this.analysisGridApi.setRowData(this.analysisGridConf.data);
    }
  }

  /**
  * A callback that will be called when the grid is ready and loaded
  * @param data
  */
  onGridReady(data) {
    this.analysisGridApi = data.params.api;
    if (this.analysisGridApi) {
      this.analysisGridApi.setRowData([...this.analysisGridConf.data]);
    }
  }

  /**
   * this function will parse the aray & create a array with user & actions & return the same
   * param {ApprovalHistory} data
   * returns {{user: string; action}[]}
   */
  private generateAnalysisData(data: any[]) {
    const resp: { user: string, modifiedDate: string, action }[] = [];
    for (let i: number = data.length - 1; i >= 0; i--) {
      let userName: string = data[i].user ? data[i].user.nameWithTimeZone : data[i].team.name;
      // if there is a on behalf of we have to add that
      if (data[i].onBehalfUser) {
        userName += ' ' + this._translate.instant('APPROVALPICKER.TITLES.ON_BEHALF_OF') + ' ' + data[i].onBehalfUser.nameWithTimeZone;
      }

      // if we have a current state & it is diffrent from the prev state
      let action: string = '';
      if (data[i].curState && data[i].prevState && data[i].curState !== data[i].prevState) {
        // the status has changed it seems
        action += this._translate.instant('APPROVALPICKER.TITLES.STATUS_CHANGED',
          {
            from: this._translate.instant('APPROVALPICKER.APPROVAL_STATUS_VALUES.' + data[i].prevState), to:
              this._translate.instant('APPROVALPICKER.APPROVAL_STATUS_VALUES.' + data[i].curState)
          })
          + '. ';
      }
      // if we have a deny reason id & its greater then 0 anf detailedHistory is not empty
      if (data[i].denyReasonId && data[i].denyReasonId > 0 && data[i].detailedHistory) {
        // lets print it
        action += this._translate.instant('APPROVALPICKER.TITLES.DENY_REASON') + ': ' + data[i].detailedHistory + ' ';
      }
      // if we have comments we have to add them as well
      if (data[i].comments) {
        action += data[i].comments;
      } else if (data[i].detailedHistory && !data[i].denyReasonId) {
        // if there are no comments but we have detailes history
        action += data[i].detailedHistory;
      } else {
        // we have nothing display no user comments
        action += this._translate.instant('APPROVALPICKER.TITLES.NO_USER_COMMENTS');
      }
      resp.push({
        user: userName,
        modifiedDate: this.printResponseDate(data[i].lastModDate),
        action: action
      });
    }
    return resp;
  }

  printResponseDate(data) {
    const localDate = this._datePipe.transform(data, 'M/d/YYYY hh:mm a')
    const dateObj = localDate.toString();
    const date = dateObj.substr(0, dateObj.indexOf(' '));
    const time = dateObj.substr(dateObj.indexOf(' ') + 1);
    let dateString: string = '';
    this.subscriptionList.push(this._translate.stream('APPROVALPICKER').subscribe(
      (value) => {
        dateString = ` ${value.TITLES.ON} ${date} ${value.TITLES.AT} ${time}`;
      }
    ));
    return dateString;
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => {
      sub.unsubscribe();
    });
  }

}
