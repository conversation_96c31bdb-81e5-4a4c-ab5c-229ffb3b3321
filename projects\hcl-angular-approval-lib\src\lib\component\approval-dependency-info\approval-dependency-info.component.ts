import { Compo<PERSON>, OnInit, On<PERSON><PERSON>roy, Input } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { Approval } from '../../models/approval';

@Component({
  selector: 'hcl-approval-dependency-info',
  templateUrl: './approval-dependency-info.component.html',
  styleUrls: ['./approval-dependency-info.component.scss']
})
export class ApprovalDependencyInfoComponent implements OnInit, OnDestroy {

  @Input() approval: Approval;
  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();

  constructor() { }

  ngOnInit(): void {
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => {
      sub.unsubscribe();
    });
  }

}
