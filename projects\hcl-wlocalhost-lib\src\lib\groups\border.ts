import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { IBorder } from '../interfaces';

@Component({
  selector: 'ip-border',
  template: `
    <div class="ce-group" [ngClass]="isEven() ? 'two' : 'three'" [class.three]="isEven()">
      <mat-form-field appearance="legacy" *ngIf="hasOwnProperty('width')">
        <mat-label>{{ 'general.width'| translate }}</mat-label>
        <input
          matInput
          [(ngModel)]="border.width"
          type="number"
          min="0"
          step="1"
          placeholder="{{ 'general.width'| translate }}"
        />
      </mat-form-field>
      <mat-form-field appearance="legacy" *ngIf="hasOwnProperty('radius')">
        <mat-label>{{ 'general.radius'| translate }}</mat-label>
        <input
          matInput
          [(ngModel)]="border.radius"
          type="number"
          min="0"
          step="1"
          placeholder="{{ 'general.radius'| translate }}"
        />
      </mat-form-field>
      <mat-form-field
        appearance="legacy"
        *ngIf="hasOwnProperty('style')"
        style="min-width: 90px"
      >
        <mat-label>{{ 'font.style'| translate }}</mat-label>
        <mat-select
          placeholder="{{ 'font.style'| translate }}"
          [disabled]="!border.width"
          [(value)]="border.style"
          disableRipple
        >
          <mat-option
            *ngFor="let style of ['solid', 'dashed', 'dotted']"
            [value]="style"
          >
            {{ 'margin'+'.'+style | translate }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <ip-color
      [model]="border"
      [showTransparent]="false"
      [disabled]="!border.width"
      *ngIf="hasOwnProperty('color')"
    ></ip-color>
  `,
  styles: [
    `
      host: {
        display: block;
        width: 100%;
      }
      .ce-group{
        display:flex;
        justify-content: space-between;
      }
      .ce-group mat-form-field{
        width: 48%;
      }
      .ce-group.three mat-form-field{
        width: 30%;
      }
    `
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BorderComponent {
  @Input() border: IBorder;

  isEven(): boolean {
    return Object.keys(this.border).length % 2 === 0;
  }

  hasOwnProperty(property: string): boolean {
    return this.border.hasOwnProperty(property);
  }
}
