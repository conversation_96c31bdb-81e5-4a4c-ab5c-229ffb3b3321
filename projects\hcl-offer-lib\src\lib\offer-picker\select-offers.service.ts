import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ApplicationConfigurationService } from 'hcl-angular-widgets-lib';
import { OfferDataService } from './offer-data.service';
import { CustomAttribute, OfferGetResponse, OfferListGetResponse, OfferTemplate } from './select-offers/select-offers.model';
import { SelectOffersConfig } from './select-offers/select-offers.config';

@Injectable({
  providedIn: 'root'
})
export class SelectOffersService {

  private _MAX_LOGIN_ATTEMPTS: number = 2;
  public _loginAttempts: number = 0;

  private loginTimerId: any;
  private _pickerConfig: SelectOffersConfig;

  private _offerListSearchData: string;
  private _offerSearchData: any;
  private _offersFolder: number;
  // private _offerListsFolder: number;
  private _lastFolderClicked = null;
  private _offersSortAndColumn: string = 'displayName,ASC';
  private _offerListsSortAndColumn: string = 'displayName,ASC';
  private _offersPageSize: number = 10;
  private _offerId: number;
  private _offerName: string;
  private _offerListId: number;
  private _offerState: string;
  private _offerTemplate: OfferTemplate;
  private _selectedVariantId: number = null;
  private _selectedOffersData = [];
  private _selectedOlData = [];
  private _variantName = null;
  private _viewRoute: 'manageOffers' | 'manageOfferLists' | 'listOffers' | 'listOfferLists';
  private _variantParameterisedAttributes: CustomAttribute[] = [];
  private _variantHiddenAttributes: CustomAttribute[] = [];
  private _staticAttributes: CustomAttribute[] = [];
  private _parameterisedAttributes: CustomAttribute[] = [];
  private _hiddenAttributes: CustomAttribute[] = [];
  private _hiddenAttributesSet = new Set<number>();
  private _breadcrumbData = new BehaviorSubject([]);
  private _resizeOffersGrid = new BehaviorSubject([]);
  private _offerListsSearchData = new BehaviorSubject([]);
  private _refreshFolders = new BehaviorSubject([]);
  private _offersSearchData = new BehaviorSubject([]);
  private listingDetails = new BehaviorSubject([]);
  private _offerMetadata = new BehaviorSubject(null);
  private _productConditions = new BehaviorSubject([]);
  private _viewOfferClicked = new BehaviorSubject([]);
  private _viewOfferListClicked = new BehaviorSubject([]);
  private _loadNext = new BehaviorSubject([]);
  public offerGridResize = this._resizeOffersGrid.asObservable();
  private _loadVariationType: 'viewMaster' | 'viewVariation';
  private _ssdbValues: Map<number, any>;
  private _loadOfferOrOfferLists: 'offers' | 'offerLists' | 'both';
  private _rowSelectMode: 'single' | 'multiple';
  private _offerAndOfferListState = 'PUBLISHED,DRAFT';
  private _disableVariants: boolean = false;
  private _directPathAccess: boolean = false;
  private _lastTabIndex: number = 0;
  private _foldersCacheData: any[] = [];
  private _offerViewMode: 'Card view' | 'Grid view';
  private _offerlistViewMode: 'Card view' | 'Grid view';
  private _offerListType: string;
  private _olTypeSubject: BehaviorSubject<string> = new BehaviorSubject<string>('');
  public olTypeObservable: Observable<any> = this._olTypeSubject.asObservable();

  constructor(private http: HttpClient,
    private translate: TranslateService,
    private offerDataService: OfferDataService,
    private appConfig: ApplicationConfigurationService) { }

  sendFoldersBreadcrumb(breadcrumb: any) {
    this._breadcrumbData.next([breadcrumb]);
  }

  clearFoldersBreadcrumb() {
    this._breadcrumbData.next([]);
  }

  getFoldersBreadcrumb(): Observable<any> {
    return this._breadcrumbData.asObservable();
  }

  set offerViewMode(value: 'Card view' | 'Grid view') {
    this._offerViewMode = value;
  }

  get offerViewMode(): 'Card view' | 'Grid view' {
    return this._offerViewMode;
  }

  set offerlistViewMode(value: 'Card view' | 'Grid view') {
    this._offerlistViewMode = value;
  }

  get offerlistViewMode(): 'Card view' | 'Grid view' {
    return this._offerlistViewMode;
  }

  get offerListType(): string {
    return this._offerListType;
  }

  set offerListType(value: string) {
    this._offerListType = value;
    this._olTypeSubject.next(this._offerListType);
  }

  sendViewOfferClicked(offerId: number) {
    this._viewOfferClicked.next([offerId]);
  }

  clearViewOfferClicked() {
    this._viewOfferClicked.next([]);
  }

  getViewOfferClicked(): Observable<any> {
    return this._viewOfferClicked.asObservable();
  }

  sendViewOfferListClicked(offerListId: number) {
    this._viewOfferListClicked.next([offerListId]);
  }

  clearViewOfferListClicked() {
    this._viewOfferListClicked.next([]);
  }

  getViewOfferListClicked(): Observable<any> {
    return this._viewOfferListClicked.asObservable();
  }

  getloadNext(): Observable<any> {
    return this._loadNext.asObservable();
  }

  sendloadNext(loadItem: string) {
    this._loadNext.next([loadItem]);
  }

  clearloadNext() {
    this._loadNext.next([]);
  }


  fireOfferGridResizeEvent() {
    this._resizeOffersGrid.next([]);
  }


  sendRefreshFoldersMesaage(refreshMessage: string) {
    this._refreshFolders.next([refreshMessage]);
  }

  clearRefreshFoldersMesaage() {
    this._refreshFolders.next([]);
  }

  subscribeRefreshFoldersMesaage(): Observable<any> {
    return this._refreshFolders.asObservable();
  }

  sendOffersSearchData(searchedtext: string) {
    this._offersSearchData.next([searchedtext]);
  }

  clearOffersSearchData() {
    this._offersSearchData.next([]);
  }

  getOffersSearchData(): Observable<any> {
    return this._offersSearchData.asObservable();
  }

  sendOfferListsSearchData(searchedtext: string) {
    this._offerListsSearchData.next([searchedtext]);
  }

  clearOfferListsSearchData() {
    this._offerListsSearchData.next([]);
  }

  getOfferListsSearchData(): Observable<any> {
    return this._offerListsSearchData.asObservable();
  }

  sendFolderId(updateComponent: string, folderId: number) {
    this.listingDetails.next([updateComponent, folderId]);
  }

  clearFolderId() {
    this.listingDetails.next([]);
  }

  getFolderId(): Observable<any> {
    return this.listingDetails.asObservable();
  }

  get pickerConfig(): SelectOffersConfig {
    return this._pickerConfig;
  }

  set pickerConfig(value: SelectOffersConfig) {
    this._pickerConfig = value;
  }

  set foldersCacheData(data: any[]) {
    this._foldersCacheData = data;
  }

  get foldersCacheData() {
    return this._foldersCacheData;
  }

  set directPathAccess(data: boolean) {
    this._directPathAccess = data;
  }

  get directPathAccess(): boolean {
    return this._directPathAccess;
  }

  get lastTabIndex(): number {
    return this._lastTabIndex;
  }

  set lastTabIndex(value: number) {
    this._lastTabIndex = value;
  }
  set loadVariationType(value: 'viewMaster' | 'viewVariation') {
    this._loadVariationType = value;
  }

  get loadVariationType(): 'viewMaster' | 'viewVariation' {
    return this._loadVariationType;
  }

  set viewRoute(value: 'manageOffers' | 'manageOfferLists' | 'listOffers' | 'listOfferLists') {
    this._viewRoute = value;
  }

  get viewRoute(): 'manageOffers' | 'manageOfferLists' | 'listOffers' | 'listOfferLists' {
    return this._viewRoute;
  }

  set selectedOffersData(value) {
    this._selectedOffersData = value;
  }

  get selectedOffersData() {
    return this._selectedOffersData;
  }

  set selectedOlData(value) {
    this._selectedOlData = value;
  }

  get selectedOlData() {
    return this._selectedOlData;
  }

  get ssdbValues(): Map<number, any> {
    return this._ssdbValues;
  }

  set ssdbValues(value: Map<number, any>) {
    this._ssdbValues = value;
  }

  set offerState(value) {
    this._offerState = value;
  }

  get offerState() {
    return this._offerState;
  }

  set loadOfferOrOfferLists(value: 'offers' | 'offerLists' | 'both') {
    this._loadOfferOrOfferLists = value;
  }

  get loadOfferOrOfferLists(): 'offers' | 'offerLists' | 'both' {
    return this._loadOfferOrOfferLists;
  }

  set rowSelectMode(value: 'single' | 'multiple') {
    this._rowSelectMode = value;
  }

  get rowSelectMode(): 'single' | 'multiple' {
    return this._rowSelectMode;
  }

  set offerAndOfferListState(value) {
    this._offerAndOfferListState = value;
  }

  get offerAndOfferListState() {
    return this._offerAndOfferListState;
  }

  set disableVariants(value: boolean) {
    this._disableVariants = value;
  }

  get disableVariants(): boolean {
    return this._disableVariants;
  }

  get offerListGlobalSearchData(): string {
    return this._offerListSearchData;
  }

  set offerListGlobalSearchData(value: string) {
    this._offerListSearchData = value;
  }

  get variantName(): string {
    return this._variantName;
  }

  set variantName(value: string) {
    this._variantName = value;
  }

  get offerId(): number {
    return this._offerId;
  }

  set offerId(value: number) {
    this._offerId = value;
  }

  get offerName(): string {
    return this._offerName;
  }

  set offerName(value: string) {
    this._offerName = value;
  }

  get offerListId(): number {
    return this._offerListId;
  }

  set offerListId(value: number) {
    this._offerListId = value;
  }

  get selectedVariantId(): number {
    return this._selectedVariantId;
  }

  set selectedVariantId(value: number) {
    this._selectedVariantId = value;
  }

  get offersGlobalSearchData(): string {
    return this._offerSearchData;
  }

  set offersGlobalSearchData(value: string) {
    this._offerSearchData = value;
  }

  get offersFolder(): number {
    return this._offersFolder;
  }

  set offersFolder(value: number) {
    this._offersFolder = value;
  }

  // get offerListsFolder(): number {
  //   return this._offerListsFolder;
  // }

  // set offerListsFolder(value: number) {
  //   this._offerListsFolder = value;
  // }

  set lastFolderClicked(id: number) {
    this._lastFolderClicked = id;
  }

  get lastFolderClicked(): number {
    return this._lastFolderClicked;
  }

  set offersSortAndColumn(value: string) {
    this._offersSortAndColumn = value;
  }

  get offersSortAndColumn(): string {
    return this._offersSortAndColumn;
  }

  set offerListsSortAndColumn(value: string) {
    this._offerListsSortAndColumn = value;
  }

  get offerListsSortAndColumn(): string {
    return this._offerListsSortAndColumn;
  }

  set offersPageSize(value: number) {
    this._offersPageSize = value;
  }

  get offersPageSize(): number {
    return this._offersPageSize;
  }

  get offerTemplate(): OfferTemplate {
    return this._offerTemplate;
  }

  set offerTemplate(value: OfferTemplate) {
    this._offerTemplate = value;
  }

  set staticAttributes(value: CustomAttribute[]) {
    this._staticAttributes = value;
  }

  get staticAttributes(): CustomAttribute[] {
    return this._staticAttributes;
  }

  set parameterisedAttributes(value: CustomAttribute[]) {
    this._parameterisedAttributes = value;
  }

  get parameterisedAttributes(): CustomAttribute[] {
    return this._parameterisedAttributes;
  }

  set hiddenAttributes(value: CustomAttribute[]) {
    this._hiddenAttributes = value;
  }

  get hiddenAttributes(): CustomAttribute[] {
    return this._hiddenAttributes;
  }

  set hiddenAttributesSet(value: Set<number>) {
    this._hiddenAttributesSet = new Set<number>(value);
  }

  get hiddenAttributesSet(): Set<number> {
    return this._hiddenAttributesSet;
  }

  set variantParameterisedAttributes(value: CustomAttribute[]) {
    this._variantParameterisedAttributes = value;
  }

  get variantParameterisedAttributes(): CustomAttribute[] {
    return this._variantParameterisedAttributes;
  }

  set variantHiddenAttributes(value: CustomAttribute[]) {
    this._variantHiddenAttributes = value;
  }

  get variantHiddenAttributes(): CustomAttribute[] {
    return this._variantHiddenAttributes;
  }

  getProductConditions(): any {
    return this._productConditions.asObservable();
  }

  setProductConditions(value: any) {
    this._productConditions.next(value);
  }

  public getAllWithBasicDetails(): Observable<any> {
    return this.http.get<any>(this.offerDataService.url + '/attributes' + '/list', {
      headers: this.getHeaders(),
      observe: 'response'
    });
  }

  getHeaders(): HttpHeaders {
    let headers: HttpHeaders = new HttpHeaders();

    headers = headers.set('content-type', 'application/json');
    return headers;
  }

  public getFolders(id: number): Observable<any> {
    return this.http.get<any>(this.offerDataService.url + '/folders/' + 4 +
      '/tree?objectType=FOLDERS');
  }

  public getOffersBaseUrl(id): string {
    return this.offerDataService.url + '/folders/' + id + '/offers';
  }

  public getOfferListsBaseUrl(id: any): string {
    return this.offerDataService.url + '/folders/' + id + '/offerlists';
  }

  public getSmartOfferListOffers(id: any): Observable<any> {
    return this.http.get<any>(this.offerDataService.url + '/offerlists/smart/' + id + '/offers-basic-details');
  }

  public getOfferListOffers(id: any): Observable<any> {
    return this.http.get<any>(this.offerDataService.url + '/offerlists/' + id + '/offers');
  }

  public getSsdbAttributeListById(idList): Observable<CustomAttribute[]> {
    return this.http.post<CustomAttribute[]>(this.offerDataService.url + '/attributes/ssdb-attribute-options', idList);
  }

  // For non lisiting calls
  public getOffersUrl(): string {
    return this.offerDataService.url + '/offers';
  }

  public getOfferListsUrl(): string {
    return this.offerDataService.url + '/offerlists';
  }

  public getOfferListById(id: Number): Observable<any> {
    return this.http.get<any>(this.getOfferListsUrl() + '/' + id);
  }

  public getOfferLists(folderId: number, page: number, size: number, lite?: boolean,
    sort?: string, state?: string, searchParam?: string): Observable<OfferListGetResponse> {
    let custQueryParams = `?page=${page}&size=${size}`;
    if (lite) {
      custQueryParams = custQueryParams += `&lite=${lite}`;
    }
    if (sort) {
      custQueryParams = custQueryParams += `&sort=${sort}`;
    }
    if (state) {
      custQueryParams = custQueryParams += `&state=${state}`;
    }
    if (searchParam) {
      custQueryParams = custQueryParams += `&search_param=${searchParam}`;
    }
    if (this.offerListType) {
      custQueryParams = custQueryParams += `&offerListType=${this.offerListType.toUpperCase()}`;
    }
    const hostName = searchParam ? this.getOfferListsUrl() : this.getOfferListsBaseUrl(folderId);
    return this.http.get<OfferListGetResponse>(hostName + custQueryParams,
      {
        headers: {
            ignoreLoader: 'true'
        }
      });

  }

  public getBulkOfferListOffersThumbnail(size: number, offerlisdIds: number[]) {
    return this.http.post<any>(this.getOfferListsUrl() + '/actions/getOfferListThumbnails?size=' + size,
      { ids: [...offerlisdIds] },
      {
        headers: {
          ignoreLoader: 'true'
        }
      });
  }

  public getBulkOffers(ids): Observable<any> {
    return this.http.post<any>(this.offerDataService.url + '/offers/actions/bulk-get', ids);
  }

  public getOfferVariants(offerId: number): Observable<any> {
    return this.http.get<any>(this.offerDataService.url + '/offers/' + offerId +
      '/variants');
  }

  public getSingleOffer(offerId: number) {
    return this.http.get<any>(this.offerDataService.url + '/offers/' + offerId);
  }


  public getOffers(folderId: number, page: number, size: number, lite?: boolean, searchParam?: string): Observable<OfferGetResponse> {
    let custQueryParams = `?page=${page}&size=${size}`;
    if (lite) {
      custQueryParams = custQueryParams += `&lite=${lite}`;
    }

    if (this.offersSortAndColumn) {
      custQueryParams = custQueryParams += `&sort=${this.offersSortAndColumn}`;
    }

    custQueryParams = custQueryParams += `&state=${this.offerAndOfferListState}`;
    if (searchParam) {
      custQueryParams = custQueryParams += `&search_param=${searchParam}`;
    }
    const hostName = searchParam ? this.getOffersUrl() : this.getOffersBaseUrl(folderId);
    return this.http.get<OfferGetResponse>(hostName + custQueryParams,  {
      headers: {
          ignoreLoader: 'true'
      }
    });
  }

  public getOfferVariantById(offerId: number, variantId: number): Observable<any> {
    return this.http.get(this.offerDataService.url + '/offers/' + offerId + '/?variantId=' + variantId);
  }

  public getTemplate(id: string): Observable<any> {
    return this.http.get<any>(this.offerDataService.url + '/templates/' + id);
  }


  public getAttributeListById(idList): Observable<CustomAttribute[]> {
    return this.http.post<CustomAttribute[]>(this.offerDataService.url + '/attributes/listByIds', idList);
  }



  setOfferData(offerData: any) {
    const { displayName, policyId, offerCodes, description, state,
      staticAttributes, parameterizedAttributes, hiddenAttributes, suppressionConditions, productConditions, folderId } = offerData;

    this.setOfferMetadata({ displayName, policyId, offerCodes, description });

    this.offerState = state;

    if (this._staticAttributes.length) {
      this._staticAttributes.forEach((tempAttribute, index) => {
        tempAttribute.value = staticAttributes[index].value;
      });
    }

    if (this._parameterisedAttributes.length) {
      this._parameterisedAttributes.forEach((tempAttribute, index) => {
        tempAttribute.value = parameterizedAttributes[index].value;
      });
    }

    if (this._hiddenAttributes.length) {
      this._hiddenAttributes.forEach((tempAttribute, index) => {
        if (hiddenAttributes[index] && hiddenAttributes[index].value) {
          tempAttribute.value = hiddenAttributes[index].value;
        }
      });
    }


    if (suppressionConditions) {
      suppressionConditions.forEach(condition => {
        if (condition.booleanValue === false) {
          condition.booleanValue = this.translate.instant('FIELDS.FALSE');
        } else if (condition.booleanValue === true) {
          condition.booleanValue = this.translate.instant('FIELDS.TRUE');
        }
      });
    }

    if (productConditions) {
      this.setProductConditions(productConditions || []);
    }
  }

  serVariantData(offerData) {

    const { parameterizedAttributes, hiddenAttributes } = offerData;
    if (this._variantParameterisedAttributes.length) {
      this._variantParameterisedAttributes.forEach((tempAttribute, index) => {
        tempAttribute.value = parameterizedAttributes[index].value;
      });
    }

    if (this._variantHiddenAttributes.length) {
      this._variantHiddenAttributes.forEach((tempAttribute, index) => {
        if (hiddenAttributes[index] && hiddenAttributes[index].value) {
          tempAttribute.value = hiddenAttributes[index].value;
        }
      });
    }

  }

  getOfferData() {
    const { displayName, policyId, description, offerCodes } = this._offerMetadata.value;
    const attributes = [];
    const filteredOfferCodes = offerCodes.filter(offerCode => offerCode);

    this._parameterisedAttributes.forEach(attribute => {
      const attributeValue = attribute.id === 1 ? this.getDateComboValue(attribute, 'param') :
        attribute.type.id === 10 ? (attribute.value && attribute.value.idColumnValue) || attribute.value : attribute.value;
      attributes.push({
        id: attribute.id,
        value: attributeValue
      });
    });
    this._hiddenAttributes.forEach(attribute => {
      const attributeValue = attribute.id === 1 ? this.getDateComboValue(attribute, 'hidden') :
        attribute.type.id === 10 ? (attribute.value && attribute.value.idColumnValue) || attribute.value : attribute.value;
      attributes.push({
        id: attribute.id,
        value: attributeValue
      });
    });
    this._staticAttributes.forEach(attribute => {
      const attributeValue = attribute.id === 1 ? this.getDateComboValue(attribute, 'static') :
        attribute.type.id === 10 ? (attribute.value && attribute.value.idColumnValue) ? attribute.value.idColumnValue : null
          || attribute.value || null : attribute.value;
      attributes.push({
        id: attribute.id,
        value: attributeValue
      });
    });

    const offerData = {
      displayName: displayName,
      description: description,
      policyId: policyId,
      offerCodes: filteredOfferCodes,
      attributes: attributes,
      productConditions: this._productConditions.value,
    };

    return offerData;
  }

  getOfferDataForOfferSelection() {
    const { offerCodes, displayName } = this._offerMetadata.value;

    const attributes = [];

    const allOfferAttributes = [...this._staticAttributes, ...this._parameterisedAttributes];

    let counter = 0;

    allOfferAttributes.some((attribute) => {
      if ((attribute.id === 1 || attribute.id === 10) && !this.hiddenAttributesSet.has(+attribute.id) && counter < 2) {
        attributes.push(attribute);
        counter++;
      }

    });

    let codeString = '';
    if (offerCodes.length > 0) {
      offerCodes.forEach((offerCode, index) => {
        if ((offerCodes.length - 1) !== index) {
          codeString += (offerCode + this.offerDataService.offerApplicationConfig.offerCodeDelimiter);
        } else {
          codeString += offerCode;
        }
      });
    }

    const offerData = {
      displayName: displayName,
      offerCode: codeString,
      offerState: this.offerState,
      offerAttributes: attributes
    };

    return offerData;
  }

  getVariantDataForVariantSelection() {
    const attributes = [];

    const allVariantAttributes = [...this._staticAttributes, ...this._variantParameterisedAttributes];

    let counter = 0;

    allVariantAttributes.some((attribute) => {
      if ((attribute.id === 1 || attribute.id === 10) && !this.hiddenAttributesSet.has(+attribute.id) && counter < 2) {
        attributes.push(attribute);
        counter++;
      }

    });

    const variantData = {
      displayName: this.variantName,
      variantAttributes: attributes
    };

    return variantData;
  }


  getDateComboValue(attribute, category) {
    if (category === 'param') {
      if (this.hiddenAttributesSet.has(+attribute.id)) {
        return {
          effectiveDate: null,
          isFlowChartRunDate: null,
          expirationDate: null,
          expirationDuration: null
        };
      } else {
        return {
          effectiveDate: attribute.type.attributes[0].value,
          isFlowChartRunDate: attribute.type.attributes[0].value === null,
          expirationDate: attribute.type.attributes[1].value,
          expirationDuration: attribute.type.attributes[2].value || (attribute.type.attributes[2].value === 0 ? 0 : null)
        };
      }
    } else if (category === 'static') {
      if (this.hiddenAttributesSet.has(+attribute.id)) {
        return {
          effectiveDate: null,
          expirationDate: null
        };
      } else {
        return {
          effectiveDate: attribute.type.attributes[0].value,
          expirationDate: attribute.type.attributes[1].value
        };
      }
    } else {
      if (this.hiddenAttributesSet.has(+attribute.id)) {
        return {
          effectiveDate: null,
          expirationDate: null
        };
      } else {
        return {
          effectiveDate: attribute.value.effectiveDate,
          expirationDate: attribute.value.expirationDate
        };
      }
    }
  }

  public getLanguangeForTextEditor() {
    const id = this.offerDataService.userConfig.locale || this.offerDataService.offerApplicationConfig.defaultLocale || 'en_US';
    switch (id) {
      case 'de_DE':
        return 'de';
      case 'en_GB':
        return 'en_gb';
      case 'en_US':
        return 'en_us';
      case 'es_ES':
        return 'es';
      case 'fr_FR':
        return 'fr';
      case 'it_IT':
        return 'it';
      case 'ja_JP':
        return 'ja';
      case 'ko_KR':
        return 'ko';
      case 'pt_BR':
        return 'pt_br';
      case 'ru_RU':
        return 'ru';
      case 'zh_CN':
        return 'zh_cn';
      case 'zh_TW':
        return 'zh_tw';
    }
  }

  resetOfferData() {
    this._staticAttributes = [];
    this._parameterisedAttributes = [];
    this._hiddenAttributes = [];
    this._offerTemplate = null;
    this._offerMetadata = new BehaviorSubject(null);
    this._offerId = null;
    this._productConditions = new BehaviorSubject([]);
    this.hiddenAttributesSet = new Set<number>();
  }

  clearData() {
    this.staticAttributes = [];
    this.hiddenAttributes = [];
    this.parameterisedAttributes = [];
    this.selectedOffersData = [];
  }

  clearTimer() {
    if (this.loginTimerId) {
      clearInterval(this.loginTimerId);
    }
  }

  setOfferMetadata(value: any) {
    this._offerMetadata.next(value);
  }

  getOfferMetadata(): any {
    return this._offerMetadata.asObservable();
  }

  reloginTimer() {
    this._loginAttempts = 0;
    if (this._pickerConfig.reLogin) {
      let retryInterval = this._pickerConfig.tokenValidity;
      if (retryInterval && retryInterval >= 15) {
        retryInterval = retryInterval * 1000 - 500;
        console.log('start relogin with retryInterval:' + retryInterval);
        this.loginTimerId = setInterval(this.retrylogin.bind(this, this._loginAttempts), retryInterval);
      } else {
        this.loginTimerId = setInterval(this.retrylogin.bind(this, this._loginAttempts), 60000);
      }
    }
  }
  retrylogin() {
    if (this._loginAttempts < this._MAX_LOGIN_ATTEMPTS) {
      this._loginAttempts++;
      this._pickerConfig.reLogin(this.reLoginSuccess());
    }
  }

  reLoginSuccess() {
    this._loginAttempts = 0;
    this.offerDataService.tokenId = this._pickerConfig.tokenId;
    this.offerDataService.userName = this._pickerConfig.userName;
    //console.log('new token:'+this._pickerConfig.tokenId+' at '+ new Date().getMinutes()+','+ new Date().getSeconds());
  }
}
