import { Component, ElementRef, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ButtonConf } from '../button/button-config';
import { DropDownConfig } from '../drop-down/drop-down-conf';
import { NotificationService } from '../../service/notification.service';
import { ModalService } from '../../service/modal.service';
import { ModalConfig } from '../../component/modal/modal.config';

declare const _, moment;

@Component({
  selector: 'hcl-query-builder',
  templateUrl: './query-builder.component.html',
  styleUrls: ['./query-builder.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class QueryBuilderComponent implements OnInit {

  groupArray = [];
  operator = '&&';
  // ruleArray = FormArray;
  toggleConfig = {
    color: 'primary',
    disabled: false,
    name: 'toggle',
    checked: true
  };
  finalQuery = '';
  queryBuilderForm;
  deleteGroupIndex: any;

  @Input() config: any;
  isErrorVar= false;
  @Output('isError') isError: EventEmitter<any> = new EventEmitter<any>();
  // DS implementation
  @ViewChild('actionsTemplate') actionsTemplate: TemplateRef<ElementRef>;

  dialogConfig: ModalConfig = {
    disableClose: false,
    backdropClass: '',
    hasBackdrop: true,
    // title: this.translate.instant('JOURNEY.CANVAS.DECISION_SPLIT.DELETE_GROUP_CONFIRM_TITLE'),
    content: "Deleting this group will delete all the nested groups.",
    width: '383px'
  };

  // group at 0th level - which is always at the 0th position in the array
  newGroup = {
    grplevel: 0, toggleConfig: {
      color: 'primary',
      disabled: true,
      name: 'toggle',
      checked: true
    }
  };
  rule = "{'field':'age', 'condition':'equals', 'value':'20', 'dataType':'NUMBER'}";
  ruleConfig = "{'field': {'options':[], 'disabled': 'false', 'name':'fieldName1', 'formControl': 'new FormControl()' }," +
    "'condition': {'options':[], 'disabled': 'false', 'name':'condition1', 'formControl': 'new FormControl()' }," +
    "'value': {'name': 'value1', 'disabled': 'false', 'formControl': 'new FormControl()'}}"

  // groupInfo/groupArray - { 'grplevel': '0', 'toggleConfig': {color: 'primary', disabled: true, name: 'toggle',checked: true} }
  groupsInfo = [{ 'grplevel': '0', 'toggleInfo': 'AND' }, { 'grplevel': '1', 'toggleInfo': 'AND' }, { 'grplevel': '2', 'toggleInfo': 'AND' }];
  // groupRules = [['rule1', 'rule2'], ['rule1'], ['rule1']]

  // {
  //   'param': 'fieldName', 'opt': 'condition',
  //   'value': 'ele.conditionValue2 ? [ele.conditionValue, ele.conditionValue2] : ele.conditionValue',
  //   'datatype': 'datatype', 'dateFormat': 'dateFormat'
  // }
  grpRules = [];
  // configArray = [['ruleConf1', 'ruleConf2'], ['ruleConf1'], ['ruleConf1']]

  configArray = [];
  splitEquation = '';


  // ruleArray: FormArray

  fieldNameConfig: DropDownConfig = {
    options: [],
    disabled: false,
    placeholder: 'Fieldname',
    disableOptionCentering: true,
    name: 'fieldname',
    formControl: new UntypedFormControl(),
    dynamicAction: { templateName: 'dynamicAction', template: null }
  };
  conditionConfig: DropDownConfig = {
    options: [{ label: 'IS_EQUAL', value: 'eq' },
    { label: 'NOT_EQUAL', value: 'neq' },
    { label: 'IS_NULL', value: 'null' },
    { label: 'IS_NOT_NULL', value: 'notnull' },
    { label: 'IS_GREATER', value: 'gt' },
    { label: 'IS_GREATER_OR_EQUAL', value: 'gte' },
    { label: 'IS_LESS', value: 'lt' },
    { label: 'IS_LESS_OR_EQUAL', value: 'lte' },
    { label: 'IS_BETWEEN', value: 'between' },
    { label: 'IN', value: 'in' },
    { label: 'NOT_IN', value: 'notin' },
    { label: 'BEGINS_WITH', value: 'beginswith' },
    { label: 'ENDS_WITH', value: 'endswith' },
    { label: 'CONTAINS', value: 'contains' }],
    disabled: false,
    placeholder: 'Condition',
    disableOptionCentering: true,
    name: 'condition',
    formControl: new UntypedFormControl()
  };

  cancelModalBtnConf: ButtonConf = {
    value: 'Cancel',
    buttonType: 'flat',
    borderRadius: 5,
    name: 'basicButton'
  };

  deleteGroupBtnConf: ButtonConf = {
    value: 'Delete Group',
    buttonType: 'raised',
    color: 'accent',
    borderRadius: 5,
    name: 'basicButton'
  };

  addRuleLabel = '';
  addGroupLabel = '';
  deleteGroupLabel = '';
  noResultFoundMsg = ''
  andLabel: string;
  orLabel: string;
  splitEquationLabel: string;
  sourceConfig: any;

  // Sample json
  /* {"AND":[
              {"param":"FirstName","opt":"endswith","value":"a","datatype":"STRING","dateFormat":""},
              {"param":"Email","opt":"endswith","value":"com","datatype":"STRING","dateFormat":""},
              {"AND":[{"param":"Phonenumber","opt":"beginswith","value":"91","datatype":"STRING","dateFormat":""},
                    {"AND":[{"param":"Country","opt":"endswith","value":"a","datatype":"STRING","dateFormat":""}]}
                     ]
              }
            ]
      }
  }
  */

  constructor(private fb: UntypedFormBuilder, public modalService: ModalService, private notificationService: NotificationService) { }

  ngOnInit() {
    // console.log('config', this.config);
    this.groupArray[0] = this.newGroup;
    this.cancelModalBtnConf.value = this.config.translations.cancelModalBtnLabel ? this.config.translations.cancelModalBtnLabel : 'Cancel';
    this.deleteGroupBtnConf.value = this.config.translations.deleteGroupModalBtnLabel ? this.config.translations.deleteGroupModalBtnLabel : 'Delete Group';
    this.dialogConfig.content = this.config.translations.deleteGroupModalMsg ? this.config.translations.deleteGroupModalMsg : 'Deleting this group will delete all the nested groups.'
    this.addRuleLabel = this.config.translations.addRuleLabel ? this.config.translations.addRuleLabel : 'Add Rule';
    this.addGroupLabel = this.config.translations.addGroupLabel ? this.config.translations.addGroupLabel : 'Add Group';
    this.deleteGroupLabel = this.config.translations.deleteGroupLabel ? this.config.translations.deleteGroupLabel : 'Delete Group';
    this.noResultFoundMsg = this.config.translations.noResultFoundMsg ? this.config.translations.noResultFoundMsg : 'No Result Found';
    this.andLabel = this.config.translations.andLabel ? this.config.translations.andLabel : 'AND';
    this.orLabel = this.config.translations.orLabel ? this.config.translations.orLabel : 'OR';
    this.splitEquationLabel = this.config.translations.splitEquationLabel ? this.config.translations.splitEquationLabel : 'Split Equation';
    this.config.columnFields.forEach(element => {
      this.fieldNameConfig.options.push({ label: element.fieldName, value: element.fieldName });
    });

    this.sourceConfig = {
      name: 'source',
      placeholder: this.config.translations.filterFieldsPlaceholder ? this.config.translations.filterFieldsPlaceholder : '',
      formControlName: new UntypedFormControl(''),
      icon: '',
      type: 'text',
      autofocus: true
    };

    if (this.config.jsonData) {
      this.constructForm(this.config.jsonData);
    } else {
      this.constructForm(null);
    }
  }

  reset() {
    this.groupArray = [];
    this.groupArray[0] = this.newGroup;
 
    this.queryBuilderForm = this.fb.group({
      'ruleArray': this.fb.array([])
    });

    this.configArray = [];

    this.initRuleRows(1, -1, 0);
    this.splitEquation = '';
  }

  ngAfterViewInit() {
    this.dialogConfig.actionsTemplate = this.actionsTemplate;
  }

  constructForm(data) {
    this.queryBuilderForm = this.fb.group({
      'ruleArray': this.fb.array([])
    });
    if (data) {
      this.getQueryBuildingData(data);
    } else {
      this.initRuleRows(1, -1, 0);
    }
  }

  getQueryBuildingData(resData: any) {
    const condition = resData; // JSON.parse(resData);

    if (condition.param !== '' && condition.opt !== '' && condition.value !== '') {
      this.groupArray[0] = {
        grplevel: 0,
        toggleConfig: {
          color: 'primary',
          disabled: false,
          name: 'toggle',
          checked: _.findLastKey(condition) === 'OR' ? false : true
        }
      };
      const operatorL0 = _.findLastKey(condition);
      if (operatorL0 === 'AND' || operatorL0 === 'OR') {
        this.convertObjArrtoArr(condition[operatorL0], 0);
      } else {
        this.groupArray[0].toggleConfig.disabled = true;
        this.convertObjArrtoArr([condition], 0);
      }
      this.setRuleRows();
    } else {
      this.initRuleRows(1, -1, 0);
    }
  }

  convertObjArrtoArr(arrofObj: any, parentLevel: number) {
    const ruleKey = 'param';
    const andKey = 'AND';
    const orKey = 'OR';
    const currentLevel = parentLevel + 1;
    const newGroup = {
      grplevel: currentLevel, toggleConfig: {
        color: 'primary',
        disabled: true,
        name: 'toggle',
        checked: true
      }
    };
    arrofObj.forEach(element => {
      if (element[ruleKey]) {
        if (this.grpRules.length > 0) {
          this.grpRules[this.grpRules.length - 1].push(element);
        } else {
          this.grpRules[0] = [element];
        }
      } else if (element[andKey]) {
        this.grpRules.push([]);
        newGroup.toggleConfig.checked = true;
        newGroup.toggleConfig.disabled = false;
        this.groupArray.push(newGroup);
        this.convertObjArrtoArr(element[andKey], currentLevel);

      } else if (element[orKey]) {
        this.grpRules.push([]);
        newGroup.toggleConfig.checked = false;
        newGroup.toggleConfig.disabled = false;
        this.groupArray.push(newGroup);
        this.convertObjArrtoArr(element[orKey], currentLevel);
      }
    });
  }

  // Add Rule under particular group at given index 
  addRule(grpIndex) {
    // append the array
    this.newGroup.toggleConfig.disabled = false;
    this.groupArray[grpIndex].toggleConfig.disabled = false;
    if (this.groupArray[0].toggleConfig.disabled = true) {
      this.groupArray[0].toggleConfig.disabled = false;
    }
    this.initRuleRows(1, 0, grpIndex);
    this.getSplitEquation();

  }

  addGroup(group, gindex: number) {
    this.newGroup.toggleConfig.disabled = false;
    this.groupArray[gindex].toggleConfig.disabled = false;
    const newGroup = {
      grplevel: group.grplevel + 1, toggleConfig: {
        color: 'primary',
        disabled: true,
        name: 'toggle',
        checked: true
      }
    };
    if (group.grplevel < this.config.maxGrpLevel) {
      this.initRuleRows(1, -1, gindex + 1);
      this.groupArray.splice(gindex + 1, 0, newGroup);
    }
    this.getSplitEquation();
  }

  setRuleRows() {
    for (let i = 0; i < this.grpRules.length; i++) {
      this.ruleArray.push(this.fb.array([]));
      this.configArray[i] = [];
      for (let j = 0; j < this.grpRules[i].length; j++) {
        (this.ruleArray.at(i) as UntypedFormArray).push(
          this.fb.group({
            fieldName: [''],
            condition: [''],
            conditionValue: [''],
            conditionValue2: [''],
            datatype: '',
            dateFormat: ''
          })
        );
        this.configArray[i].push({
          fieldName: {
            options: this.fieldNameConfig.options,
            disabled: this.config.readOnlyMode ? true : false,
            defaultText: '',
            name: `fieldName${j}`,
            placeholder: this.config.fieldNamePlaceHolder,
            formControl: ((this.ruleArray.at(i) as UntypedFormArray).controls[j] as UntypedFormGroup).controls.fieldName,
            dynamicAction: { templateName: 'dynamicAction', template: null },
            disableOptionCentering: true,
          },
          condition: {
            options: this.conditionConfig.options,
            disabled: this.config.readOnlyMode ? true : false,
            defaultText: '',
            name: `condition${j}`,
            placeholder: this.config.conditionPlaceHolder,
            formControl: ((this.ruleArray.at(i) as UntypedFormArray).controls[j] as UntypedFormGroup).controls.condition
          },
          conditionValue: {
            name: `conditionValue${j}`,
            type: '',
            closeButton: false,
            placeholder: this.config.conditionValue1PlaceHolder,
            disabled: this.config.readOnlyMode ? true : false,
            formControlName: ((this.ruleArray.at(i) as UntypedFormArray).controls[j] as UntypedFormGroup).controls.conditionValue
          },
          conditionValue2: {
            name: `conditionValue2${j}`,
            type: '',
            closeButton: false,
            placeholder: this.config.conditionValue2PlaceHolder,
            disabled: this.config.readOnlyMode ? true : false,
            formControlName: ((this.ruleArray.at(i) as UntypedFormArray).controls[j] as UntypedFormGroup).controls.conditionValue2
          },
          datatype: ((this.ruleArray.at(i) as UntypedFormArray).controls[j] as UntypedFormGroup).controls.datatype,
          dateFormat: ((this.ruleArray.at(i) as UntypedFormArray).controls[j] as UntypedFormGroup).controls.dateFormat
        });
        this.configArray[i][j].fieldName.formControl.setValue(this.grpRules[i][j].param);
        this.onFieldChange(this.grpRules[i][j].param, i, j);
        this.configArray[i][j].condition.formControl.setValue(this.grpRules[i][j].opt);
        ((this.ruleArray.at(i) as UntypedFormArray).controls[j] as UntypedFormGroup).controls.datatype = this.configArray[i][j].datatype;
        if (this.configArray[i][j].condition.formControl.value === 'between' ||
          this.configArray[i][j].condition.formControl.value === 'notbetween') {
          const val2 = this.grpRules[i][j].value[1];
          this.configArray[i][j].conditionValue.formControlName.setValue(this.grpRules[i][j].value[0] as String);
          this.configArray[i][j].conditionValue2.formControlName.setValue(val2);
        } else if (this.configArray[i][j].condition.formControl.value === 'null' ||
          this.configArray[i][j].condition.formControl.value === 'notnull') {
          this.configArray[i][j].conditionValue.formControlName.setValue('');
          this.configArray[i][j].conditionValue.formControlName.disable();
          this.configArray[i][j].conditionValue.placeholder = '';
        } else {
          this.configArray[i][j].conditionValue.formControlName.setValue(this.grpRules[i][j].value);
        }

        if (this.configArray[i][j].datatype === 'DATE') {
          // converting epoch date to utc
          const format = this.configArray[i][j].dateFormat.toUpperCase();
          if (Array.isArray(this.grpRules[i][j].value)) {
            let date = new Date(0);
            const sec = parseInt(this.grpRules[i][j].value[0], 10);
            date = moment(sec).format(format);
            this.configArray[i][j].conditionValue.formControlName.setValue(date.toString());

            let date2 = new Date(0);
            const sec2 = parseInt(this.grpRules[i][j].value[1], 10);
            date2 = moment(sec2).format(format);
            this.configArray[i][j].conditionValue2.formControlName.setValue(date2.toString());
          } else {
            let date = new Date(0);
            const sec = parseInt(this.grpRules[i][j].value, 10);
            date = moment(sec).format(format);
            this.configArray[i][j].conditionValue.formControlName.setValue(date.toString());
          }

        }
      }
    }

    setTimeout(function () {
      $($('.level1')[$('.level1').length - 1]).addClass('hideLevel1');
      $($('.level2')[$('.level2').length - 1]).addClass('hideLevel2');
      $($('.level3')[$('.level3').length - 1]).addClass('hideLevel3');
      $($('.level4')[$('.level4').length - 1]).addClass('hideLevel4');
      $($('.level5')[$('.level5').length - 1]).addClass('hideLevel5');
    }, 1000);
    this.getSplitEquation();
  }

  // When Datadefinition field value is changed from drop down
  onFieldChange(e, grpIndex, ruleIndex) {
    this.config.columnFields.forEach(element => {
      if (element.fieldName === e) {
        this.configArray[grpIndex][ruleIndex].condition.options = [];
        this.configArray[grpIndex][ruleIndex].condition.formControl.setValue('');
        if (element.fieldDataType === 'String') {
          const stringOptions = this.config.conditionConfigStringType ? this.config.conditionConfigStringType : this.config.conditionConfigDefault
          stringOptions.forEach(element => {
            this.configArray[grpIndex][ruleIndex].condition.options.push(element)
          });
          this.configArray[grpIndex][ruleIndex].conditionValue.type = '';
          this.configArray[grpIndex][ruleIndex].conditionValue.formControlName.setValue('');
          this.configArray[grpIndex][ruleIndex].conditionValue2.formControlName.setValue('');
          this.configArray[grpIndex][ruleIndex].condition.formControl.setValue('');
          this.configArray[grpIndex][ruleIndex].datatype = 'STRING';
          this.configArray[grpIndex][ruleIndex].dateFormat = '';
        } else if (element.fieldDataType === 'Numeric') {
          const stringOptions = this.config.conditionConfigNumberType ? this.config.conditionConfigNumberType
            : this.config.conditionConfigDefault;
          stringOptions.forEach(element => {
            this.configArray[grpIndex][ruleIndex].condition.options.push(element)
          });
          this.configArray[grpIndex][ruleIndex].conditionValue.type = 'STRING';
          this.configArray[grpIndex][ruleIndex].conditionValue2.type = 'STRING';
          this.configArray[grpIndex][ruleIndex].conditionValue.formControlName.setValue('');
          this.configArray[grpIndex][ruleIndex].conditionValue2.formControlName.setValue('');
          this.configArray[grpIndex][ruleIndex].datatype = 'NUMBER';
          this.configArray[grpIndex][ruleIndex].dateFormat = '';
        } else if (element.fieldDataType === 'Date') {
          const stringOptions = this.config.conditionConfigDateType ? this.config.conditionConfigDateType : this.config.conditionConfigDefault
          stringOptions.forEach(element => {
            this.configArray[grpIndex][ruleIndex].condition.options.push(element)
          });
          this.configArray[grpIndex][ruleIndex].conditionValue.type = '';
          this.configArray[grpIndex][ruleIndex].conditionValue.formControlName.setValue('');
          this.configArray[grpIndex][ruleIndex].conditionValue2.formControlName.setValue('');
          this.configArray[grpIndex][ruleIndex].datatype = 'DATE';
          this.configArray[grpIndex][ruleIndex].dateFormat = element.dateFormat;
        }
      }
    });
    this.sourceConfig.formControlName.setValue('');
  }

  // When another value from the dropdown of condition option is selected
  onConditionChange(event, grpIndex, ruleIndex) {
    this.configArray[grpIndex][ruleIndex].conditionValue2.formControlName.setValue('');
    if (event === 'null' || event === 'notnull') {
      this.configArray[grpIndex][ruleIndex].conditionValue.formControlName.setValue('');
      this.configArray[grpIndex][ruleIndex].conditionValue.formControlName.disable();
      this.configArray[grpIndex][ruleIndex].conditionValue.placeholder = '';
    } else {
      this.configArray[grpIndex][ruleIndex].conditionValue.formControlName.enable();
      this.configArray[grpIndex][ruleIndex].conditionValue.placeholder = this.config.conditionValue1PlaceHolder;
    }
    if (event === 'in' || event === 'notin') {
      this.configArray[grpIndex][ruleIndex].conditionValue.formControlName.setValue('');
    }
  }

  initRuleRows(noOfRows: number, rowIndex: number, gindex: number) {
    if (this.ruleArray.at(gindex) && this.configArray[gindex]) {
      if (rowIndex < 0) {
        this.ruleArray.insert(gindex, this.fb.array([]));
        this.configArray.splice(gindex, 0, []);
      } else {
        // nothing
      }
    } else {
      this.ruleArray.push(this.fb.array([]));
      this.configArray[gindex] = [];
    }

    const totalRows = this.configArray[gindex].length;
    for (let i = 0; i < noOfRows; i++) {
      (this.ruleArray.at(gindex) as UntypedFormArray).push(
        this.fb.group({
          fieldName: [''],
          condition: [''],
          conditionValue: [''],
          conditionValue2: [''],
          datatype: '',
          dateFormat: ''
        })
      );
      this.configArray[gindex][totalRows + i] = {
        fieldName: {
          options: this.fieldNameConfig.options,
          disabled: false,
          defaultText: '',
          name: `fieldName${i}`,
          placeholder: this.config.fieldNamePlaceHolder,
          formControl: ((this.ruleArray.at(gindex) as UntypedFormArray).controls[totalRows + i] as UntypedFormGroup).controls.fieldName,
          dynamicAction: { templateName: 'dynamicAction', template: null },
          disableOptionCentering: true,
        },
        condition: {
          options: [],
          disabled: false,
          defaultText: '',
          name: `condition${i}`,
          placeholder: this.config.conditionPlaceHolder,
          formControl: ((this.ruleArray.at(gindex) as UntypedFormArray).controls[totalRows + i] as UntypedFormGroup).controls.condition
        },
        conditionValue: {
          name: `conditionValue${i}`,
          type: '',
          closeButton: false,
          disabled: false,
          placeholder: this.config.conditionValue1PlaceHolder,
          formControlName: ((this.ruleArray.at(gindex) as UntypedFormArray).controls[totalRows + i] as UntypedFormGroup).controls.conditionValue
        },
        conditionValue2: {
          name: `conditionValue2${i}`,
          type: '',
          closeButton: false,
          placeholder: this.config.conditionValue2PlaceHolder,
          disabled: false,
          formControlName: ((this.ruleArray.at(gindex) as UntypedFormArray).controls[totalRows + i] as UntypedFormGroup).controls.conditionValue2
        },
        datatype: ((this.ruleArray.at(gindex) as UntypedFormArray).controls[totalRows + i] as UntypedFormGroup).controls.datatype,
        dataFormat: ((this.ruleArray.at(gindex) as UntypedFormArray).controls[totalRows + i] as UntypedFormGroup).controls.dateFormat

      };
    }
    setTimeout(function () {
      $($('.level1')[$('.level1').length - 1]).addClass('hideLevel1');
      $($('.level2')[$('.level2').length - 1]).addClass('hideLevel2');
      $($('.level3')[$('.level3').length - 1]).addClass('hideLevel3');
      $($('.level4')[$('.level4').length - 1]).addClass('hideLevel4');
      $($('.level5')[$('.level5').length - 1]).addClass('hideLevel5');
    }, 1000);
  }

  // Restring user to enter all the special characters along with numbers for Number FieldType
  restrictInput(eve, grpIndex, rowIndex) {
    // accepting decimals
    const regularNumerRegex = '[^0-9\.]';
    const numberForInCondition = '[^0-9,]';
    const type = this.configArray[grpIndex][rowIndex].datatype;
    const condition = this.configArray[grpIndex][rowIndex].condition.formControl.value;
    let confRegex: any;
    this.config.columnFields.forEach(field => {
      if (field.regex) {
        if (field.fieldName === this.configArray[grpIndex][rowIndex].fieldName.formControl.value) {
          confRegex = field.regex;
          this.handleRegex(eve, confRegex, grpIndex, rowIndex);
        }
      } else if (type === 'NUMBER' && condition !== 'in' && condition !== 'notin') {
        confRegex = regularNumerRegex;
        this.handleRegex(eve, confRegex, grpIndex, rowIndex);
      } else if (type === 'NUMBER' && (condition === 'in' || condition === 'notin')) {
        confRegex = numberForInCondition;
        this.handleRegex(eve, confRegex, grpIndex, rowIndex);
      }
    });
  }

  handleRegex(eve, regex, grpIndex, rowIndex) {
    if (eve.clipboardData) {
      // handling paste event in input accepting datatype 'Number'
      let pastedText = eve.clipboardData.getData('text');
      for (let i = 0; i < pastedText.length;) {
        if (pastedText.charAt(i).match(regex)) {
          pastedText = pastedText.slice(0, i) + pastedText.slice(i + 1);
          i = i;
        } else {
          i++;
        }
      }
      eve.stopPropagation();
      eve.preventDefault();
      this.configArray[grpIndex][rowIndex].conditionValue.formControlName.setValue(pastedText);
    } else {
      if (eve.key.match(regex)) {
        eve.stopPropagation();
        if (eve.preventDefault) {
          eve.preventDefault(); // normal browsers
        }
        eve.returnValue = false; // IE
      } else {
        return;
      }
    }
  }

  deleteRule(gIndex, rIndex) {
    if ((this.ruleArray.controls[gIndex] as UntypedFormArray).controls.length === 2) {
      (this.ruleArray.controls[gIndex] as UntypedFormArray).removeAt(rIndex);
      this.configArray[gIndex].splice(rIndex, 1);
      if (gIndex !== 0 && this.configArray.length > 1) {
        if (this.groupArray[gIndex + 1] && this.groupArray[gIndex + 1].grplevel > this.groupArray[gIndex].grplevel) {
          this.groupArray[gIndex].toggleConfig.disabled = false;
        } else {
          this.groupArray[gIndex].toggleConfig.disabled = true;
        }
      } else if (gIndex === 0 && this.configArray.length === 1) {
        this.groupArray[gIndex].toggleConfig.disabled = true;
      } else {
        this.groupArray[gIndex].toggleConfig.disabled = false;
      }
    } else if ((this.ruleArray.controls[gIndex] as UntypedFormArray).controls.length <= 1) {
      this.newGroup.toggleConfig.disabled = true;
    } else {
      (this.ruleArray.controls[gIndex] as UntypedFormArray).removeAt(rIndex);
      this.configArray[gIndex].splice(rIndex, 1);
    }
    this.getSplitEquation();
  }

  get ruleArray() {
    return this.queryBuilderForm.get('ruleArray') as UntypedFormArray;
  }

  deleteGroup(grpindex) {
    if (this.groupArray.length > 1 && grpindex !== 0) {
      if (this.groupArray[grpindex + 1] && this.groupArray[grpindex + 1].grplevel > this.groupArray[grpindex].grplevel) {
        this.deleteGroupIndex = grpindex;
        this.modalService.openDialog(this.dialogConfig);
      } else {
        this.groupArray.splice(grpindex, 1);
        this.configArray.splice(grpindex, 1);
        this.ruleArray.removeAt(grpindex);
        this.grpRules.splice(grpindex, 1);
        if (this.groupArray.length === 1 && (this.ruleArray.controls[0] as UntypedFormArray).controls.length <= 1) {
          this.newGroup.toggleConfig.disabled = true;
        } else if (this.groupArray.length > 1 && !this.groupArray[grpindex]) {
          this.groupArray[grpindex - 1].toggleConfig.disabled = true;
        }
      }
    } else {
      this.newGroup.toggleConfig.disabled = true;
    }
    this.getSplitEquation();
  }

  getSplitEquation() {
    this.splitEquation = '';
    let rowcount = 65;
    let openBrkt = 0;
    this.groupArray.forEach((element, index) => {
      const prevEle = this.groupArray[index - 1];
      const nextEle = this.groupArray[index + 1];
      const operator = element.toggleConfig.checked ? '&&' : '||';

      // check if there are multiple groups
      if (this.groupArray.length > 0) {
        openBrkt = prevEle ? (prevEle.grplevel <= element.grplevel ? openBrkt + 1 : openBrkt) : openBrkt;
        this.splitEquation = prevEle ? (prevEle.grplevel <= element.grplevel ? this.splitEquation + '(' : this.splitEquation)
          : this.splitEquation;
        if (this.configArray[index].length > 0) {
          this.configArray[index].forEach((row, i) => {
            this.splitEquation += String.fromCharCode(rowcount) + ' ' + operator + ' ';
            if (i === this.configArray[index].length - 1 && index === this.groupArray.length - 1) {
              this.splitEquation = this.splitEquation.slice(0, -4);
            }
            rowcount++;
            if (rowcount === 91) {
              rowcount = 64;
            }
          });
        }
      } else {
      }

      // when group is of level greater that of previous group and next ele group
      if ((prevEle && prevEle.grplevel < element.grplevel) && (nextEle && nextEle.grplevel < element.grplevel)) {
        for (let x = 0; x < (element.grplevel - nextEle.grplevel); x++) {
          this.splitEquation += ')';
          if (x === (element.grplevel - nextEle.grplevel) - 1) {
            this.splitEquation = this.splitEquation.slice(0, -4);
            this.splitEquation += ')' + ' ' + operator + ' ';
          }
        }
      } else if (nextEle && nextEle.grplevel === element.grplevel) { // when next group's grp level is same as that of current group
        const prevOperator = prevEle.toggleConfig.checked ? '&&' : '||';
        this.splitEquation = this.splitEquation.slice(0, -4);
        this.splitEquation += ')' + ' ' + prevOperator + ' ';
      } else if (!nextEle) { // when there is no next group i.e. this is the last
        for (let y = element.grplevel; y > 0; y--) {
          this.splitEquation += ')';
        }
      }
      if (nextEle && nextEle.grplevel < element.grplevel) {
        for (let z = 0; z < index; z++) {
          if (this.groupArray[z].grplevel === (nextEle.grplevel - 1)) {
            // this.splitEquation = this.splitEquation.slice(0, -4);
            const indexOfOperator = this.splitEquation.lastIndexOf(operator);
            this.splitEquation = this.splitEquation.substr(0, indexOfOperator) +
              (this.groupArray[z].toggleConfig.checked ? ') && (' : ') || (') + this.splitEquation.substr(indexOfOperator + 2);
          }
        }
      }
    });
  }

  deleteNestedGroups() {
    const grpindex = this.deleteGroupIndex;
    const grpLevel = this.groupArray[grpindex].grplevel;
    for (let i = grpindex; i < this.groupArray.length;) {
      if (this.groupArray[i + 1] && this.groupArray[i + 1].grplevel > grpLevel) {
        // delete the child groups
        this.groupArray.splice(i, 1);
        this.configArray.splice(i, 1);
        this.ruleArray.removeAt(i);
        this.grpRules.splice(i, 1);
        i = i;
        this.getSplitEquation();
      } else {
        this.groupArray.splice(grpindex, 1);
        this.configArray.splice(grpindex, 1);
        this.ruleArray.removeAt(grpindex);
        this.grpRules.splice(grpindex, 1);
        this.getSplitEquation();
        if ((this.groupArray.length === 1 && (this.ruleArray.controls[0] as UntypedFormArray).controls.length <= 1)) {
          this.newGroup.toggleConfig.disabled = true;
        } else if (this.groupArray.length > 1 && (!this.groupArray[grpindex] ||
          (this.groupArray[grpindex] && this.groupArray[grpindex - 1].grplevel === this.groupArray[grpindex].grplevel))) {
          this.groupArray[grpindex - 1].toggleConfig.disabled = true;
        }
        return;
      }
    }
  }

  changeCentury(inputDate, format) {
    let date = new Date(moment(inputDate, format, true));                //parse the date initially

    const dateArray = inputDate.split(/[/ .-]/);    //regex to look for / or - delimited dates
    const index = format.split(/[/ .-]/).indexOf('YY');
    if (index >= 0 && dateArray[index].length === 2)               //if the input has a 2 digit year 
    {
      const fullDateyear = date.getFullYear();
      if (fullDateyear < 2000)                   //and the parser decided it's before 1950
        date.setFullYear(fullDateyear + 100);     //add a century
    }

    return date;
  }

  saveQueryBuilderJson() {
    let condition;
    let finalReqObj;
    if (this.queryBuilderForm.valid) {
      condition = this.populateRowDetails();
      this.finalQuery = condition;
      console.log('final condition', condition);
    }
    if (Object.keys(condition)[0] === 'AND' || Object.keys(condition)[0] === 'OR') { // for multiple rule rows
      this.incompleteRuleError(condition, Object.keys(condition)[0], finalReqObj);
    } else { // for only one condition
      if (condition.param && condition.opt) {
        if (((condition.opt === 'null' || condition.opt === 'notnull') && !condition.value)
          || ((condition.opt === 'between' || condition.opt === 'notbetween') && condition.value && condition.val2)
          || (condition.value || condition.value === 0)) {
          this.isError.emit(false);
          return;
        } else {
          this.isError.emit(true);
          this.notificationService.show({
            message: this.config.translations.completeOrDeleteRuleErrorMsg ? this.config.translations.completeOrDeleteRuleErrorMsg : 'Please complete/delete the rule.',
            type: 'error', close: true, autoHide: 6000
          });
          return;
        }
      } else if (!condition.param && !condition.opt && !condition.value) {
        this.isError.emit(true);
        this.notificationService.show({
          message: this.config.translations.atleastOneRuleRequiredErrorMsg ? this.config.translations.atleastOneRuleRequiredErrorMsg : 'At least one rule is required to save the changes.',
          type: 'error', close: true, autoHide: 6000
        });
        return;
      } else {
        this.isError.emit(true);
        this.notificationService.show({
          message: this.config.translations.completeOrDeleteRuleErrorMsg ? this.config.translations.completeOrDeleteRuleErrorMsg : 'Please complete/delete the rule.',
          type: 'error', close: true, autoHide: 6000
        });
        return;
      }
    }
  }

  toggleChange(event, i) {
    if (event === true) {
      this.operator = '&&';
    } else {
      this.operator = '||';
    }
    this.groupArray[i].toggleConfig.checked = event.checked;
    this.getSplitEquation();
  }

  // show error when any rule is incompletely filled or empty
  incompleteRuleError(condition, key, reqObj) {
    if (!this.isErrorVar) {
      for (let index = 0; index < condition[key].length; index++) {
        let element = condition[key][index];
        if (element.param && element.opt) {
          if (((element.opt === 'null' || element.opt === 'notnull') && !element.value)
            || ((element.opt === 'between' || element.opt === 'notbetween') && element.value && element.val2)
            // || (element.opt === 'in' && ((element.value.length === 1 && element.value[0] !== "") || element.value.length > 1))
            || (element.value || element.value === 0)) {
            if (element.opt === 'in' && (element.value.length === 1 && element.value[0] === "")) {
              this.isError.emit(true);
              this.notificationService.show({
                message: this.config.translations.completeOrDeleteRuleErrorMsg ? this.config.translations.completeOrDeleteRuleErrorMsg : 'Please complete/delete the rule.',
                type: 'error', close: true, autoHide: 6000
              });
              return;
            } else {
              if (index === (condition[key].length - 1)) {
                this.isError.emit(false);
                return;
              }
            }

          } else {
            this.isError.emit(true);
            this.notificationService.show({
              message: this.config.translations.completeOrDeleteRuleErrorMsg ? this.config.translations.completeOrDeleteRuleErrorMsg : 'Please complete/delete the rule.',
              type: 'error', close: true, autoHide: 6000
            });
            return;
          }
        } else if (element['AND'] || element['OR']) {
          this.incompleteRuleError(element, Object.keys(element)[0], reqObj);
        } else {
          this.isError.emit(true);
          this.notificationService.show({
            message: this.config.translations.completeOrDeleteRuleErrorMsg ? this.config.translations.completeOrDeleteRuleErrorMsg : 'Please complete/delete the rule.',
            type: 'error', close: true, autoHide: 6000
          });
          return;
        }
      }
    }
  }
  populateRowDetails() {
    this.grpRules = [];
    let grpJson = {};
    let finalJson = {};
    // to get all the rules value of particular group and returning it to grpRules array
    for (let i = 0; i < this.ruleArray.controls.length; i++) {
      const rules = [];
      const grp = this.ruleArray.controls[i] as UntypedFormArray;
      grp.value.forEach((ele, index) => {
        if (this.configArray[i][index].datatype === 'DATE') {
          ele.conditionValue = (grp.controls[index] as UntypedFormGroup).controls.conditionValue.value.trim();
          const format = this.configArray[i][index].dateFormat.toUpperCase();
          // converting utc to epoch
          let value1 = moment(ele.conditionValue, format, true);
          if (value1.isValid()) {
            ele.conditionValue = this.changeCentury(ele.conditionValue, format);
            value1 = new Date(moment(ele.conditionValue, format)
              .tz(this.config.timeZone ? this.config.timeZone : moment.tz.guess()));
            ele.conditionValue = value1.getTime().toString();
            this.isErrorVar = false;

          } else { // when it doesnt match the provided date format
            this.isErrorVar = true;
            this.isError.emit(true);
            this.notificationService.show({
              message: this.config.translations.dateFormatUnmatchError ? this.config.translations.dateFormatUnmatchError : 'Please enter date as per format',
              type: 'error', close: true, autoHide: 6000
            });
            return;
          }

          if (ele.conditionValue2) {
            let value2 = moment(ele.conditionValue2, format, true);
            ele.conditionValue2 = (grp.controls[index] as UntypedFormGroup).controls.conditionValue2.value;
            if (value2.isValid()) {
              ele.conditionValue2 = this.changeCentury(ele.conditionValue2, format);
              value2 = new Date(moment(ele.conditionValue2, format)
                .tz(this.config.timeZone ? this.config.timeZone : moment.tz.guess()));
              ele.conditionValue2 = value2.getTime().toString();
              this.isErrorVar = false;
            } else {
              this.isErrorVar = true;
              this.isError.emit(true);
              this.notificationService.show({
                message: this.config.translations.dateFormatUnmatchError ? this.config.translations.dateFormatUnmatchError : 'Please enter date as per format',
                type: 'error', close: true, autoHide: 6000
              });
              return;
            }
          }
          if (ele.conditionValue === 'NaN' || ele.conditionValue2 === 'NaN') {
            this.isErrorVar = true;
            this.isError.emit(true);
            this.notificationService.show({
              message: this.config.translations.incorrectDateErrorMsg ? this.config.translations.incorrectDateErrorMsg : 'Please enter the correct date',
              type: 'error', close: true, autoHide: 6000
            });
            return;
          }
        }
        if (ele.condition !== ('in' || 'notin')) {
          rules.push({
            param: ele.fieldName,
            opt: ele.condition,
            value: ele.conditionValue2 ? [ele.conditionValue, ele.conditionValue2] : ele.conditionValue,
            datatype: this.configArray[i][index].datatype,
            dateFormat: this.configArray[i][index].dateFormat
          });
        } else {
          const val = Array.isArray(ele.conditionValue) ? ele.conditionValue : (ele.conditionValue.toString().includes(',') ?
            ele.conditionValue.split(',') : [ele.conditionValue.toString()]);
          rules.push({
            param: ele.fieldName,
            opt: ele.condition,
            value: val ? val : ele.conditionValue,
            datatype: this.configArray[i][index].datatype,
            dateFormat: this.configArray[i][index].dateFormat
          });
        }
      });
      this.grpRules.push(rules);
    }

    // Forming the final condition json to be sent to service
    if (this.grpRules.length > 1 || this.grpRules[0].length > 1) {
      for (let i = 0; i < this.grpRules.length; i++) {
        const operator = this.groupArray[i].toggleConfig.checked === true ? 'AND' : 'OR';
        grpJson = {
          [operator]: []
        };
        for (let j = 0; j < this.grpRules[i].length; j++) {
          grpJson[operator].push(this.grpRules[i][j]);
        }
        if (this.groupArray[i].grplevel === 0) {
          finalJson = grpJson;
        }
        // json objects for different groups at different levels
        for (const key of Object.keys(finalJson)) {
          const objL2 = finalJson[key][finalJson[key].length - 1] ?
            finalJson[key][finalJson[key].length - 1][_.findLastKey(finalJson[key][finalJson[key].length - 1])] : '';

          if (this.groupArray[i].grplevel === 1) {
            finalJson[key].push(grpJson);
          } else if (this.groupArray[i].grplevel === 2) {
            finalJson[key][finalJson[key].length - 1][_.findLastKey(finalJson[key][finalJson[key].length - 1])].push(grpJson);
          } else if (this.groupArray[i].grplevel === 3) {
            objL2[objL2.length - 1][_.findLastKey(objL2[objL2.length - 1])].push(grpJson);
          } else if (this.groupArray[i].grplevel === 4) {
            const objL3 = objL2 ? objL2[objL2.length - 1][_.findLastKey(objL2[objL2.length - 1])] : '';
            objL3[objL3.length - 1][_.findLastKey(objL3[objL3.length - 1])].push(grpJson);
          }
        }
      }
    } else {
      finalJson = this.grpRules[0][0];
    }
    return finalJson;
  }
  
  panelStateChanged(state) {
    if (state === true) {
      this.sourceConfig.autofocus = true;
    }
  }

  filterOptions(searchString, i, j) {
    if (typeof searchString === 'string') {
      let options = this.fieldNameConfig.options
      console.log('options', options);
      this.configArray[i][j].fieldName.options = options.filter(element => element.value.toUpperCase().includes(searchString.toUpperCase()));
    }
  }
}
