import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { ApplicationConfigurationService } from 'hcl-angular-widgets-lib';
import { HclAssetPickerService } from 'hcl-angular-widgets-lib';
import { AssetpickerApplicationConfig, OfferApplicationConfig, PlatformConfig, UserConfig } from './select-offers/select-offers.model';
import * as _ from 'lodash';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class OfferDataService {

  private _platformConfig: PlatformConfig;
  private _userConfig: UserConfig;
  private _offerApplicationConfig: OfferApplicationConfig;
  private _apRepositories: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  private _url: string;
  private _tokenId: string;
  private _userName: string;
  private _isOfferWidget: boolean;
  private _offers: any;


  private _dateFormatLocalMap = new Map([
    ['de_DE', 'dd.mm.yy'], // german
    ['en_GB', 'dd/mm/yy'], // GB
    ['en_US', 'mm/dd/yy'], // english
    ['es_ES', 'dd/mm/yy'], // spanish
    ['fr_FR', 'dd/mm/yy'], // french
    ['it_IT', 'dd/mm/yy'], // italian
    ['ja_JP', 'yy/mm/dd'], // japnese
    ['ko_KR', 'yy/mm/dd'], // korean
    ['pt_BR', 'dd/mm/yy'], // portugese
    ['ru_RU', 'dd.mm.yy'], // russian
    ['zh_CN', 'yy/mm/dd'], // simplified chinses
    ['zh_TW', 'yy/mm/dd'] // chinese
  ]);

  constructor(
    private http: HttpClient,
    private translate: TranslateService,
    private appConfig: ApplicationConfigurationService,
    private hclAssetPickerService: HclAssetPickerService
  ) { }

  get dateFormatLocalMap(): Map<string, string> {
    return this._dateFormatLocalMap;
  }


  setAssetPickerRepositories(value: any) {
    this._apRepositories.next(value);
  }

  getAssetPickerRepositories(): any {
    return this._apRepositories.asObservable();
  }

  set offers(value: any) {
    this._offers = value;
  }

  get offers(): any {
    return this._offers;
  }

  get userConfig(): UserConfig {
    return this._userConfig;
  }

  set userConfig(value: UserConfig) {
    this._userConfig = value;
  }

  get url(): string {
    return this._url;
  }

  set url(value: string) {
    this._url = value;
  }

  get isOfferWidget(): boolean {
    return this._isOfferWidget;
  }

  set isOfferWidget(value: boolean) {
    this._isOfferWidget = value;
  }

  get platformConfig(): PlatformConfig {
    return this._platformConfig;
  }

  set platformConfig(value: PlatformConfig) {
    this._platformConfig = value;
  }

  get offerApplicationConfig(): OfferApplicationConfig {
    return this._offerApplicationConfig;
  }

  set offerApplicationConfig(value: OfferApplicationConfig) {
    this._offerApplicationConfig = value;
  }

  get tokenId(): string {
    return this._tokenId;
  }

  set tokenId(value: string) {
    this._tokenId = value;
  }

  get userName(): string {
    return this._userName;
  }

  set userName(value: string) {
    this._userName = value;
  }


  getCurrencyLocale() {
    return this.offerApplicationConfig.currencyLocale || this.userConfig.locale || this.offerApplicationConfig.defaultLocale;
  }

  getDateFormatLocale(): string {
    return this.dateFormatLocalMap.get(this.userConfig.locale ||
      this.offerApplicationConfig.defaultLocale || 'en_US');
  }

  getDatePipeFormatLocale(): string {
    return this.getDateFormatLocale().replace('yy', 'yyyy').replace('mm', 'MM');
  }

  public getAssetpickerApplicationConfig(ignoreLoader?: boolean) {
    let headers: HttpHeaders = new HttpHeaders();
    if (ignoreLoader) {
      headers = headers.set('ignoreLoader', 'true')
    }
    return this.http.get<AssetpickerApplicationConfig>(this.url + '/assetpicker-app-config', {
      headers: headers
    });
  }


  public setAssetPickerRepoes() {
    this.getAssetpickerApplicationConfig(true).subscribe(config => {
      const headers: HttpHeaders = new HttpHeaders()
        .set('m_user_name', window['_userName'] || this.userConfig.displayName)
        .set('m_tokenId', config.token)
        .set('api_auth_mode', 'manager')
        .set('401', 'ignore')
        .set('ignoreLoader', 'true')
        .set('ap-startup', 'true')
        .set('client_app_id', this.offerApplicationConfig.offerApplicationId.toString());
      this.hclAssetPickerService.headers = headers;
      this.hclAssetPickerService.baseUrl = config.url;
      this.hclAssetPickerService.getInstances().subscribe(repos => {
        this.setAssetPickerRepositories(repos);
        this.hclAssetPickerService.repositories = repos;
      })
    })

  }

  public getSecurityPolicies() {
    const options = [];

    for (const [key, value] of _.entries(this.userConfig.securityPoliciesMap)) {
      options.push({ label: value, value: +key });
    }
    return options;
  }

  public getPlatformConfig(): Observable<PlatformConfig> {
    return this.http.get<PlatformConfig>(this.url + '/platform-config',
      {
        headers: {
          ignoreLoader: 'true'
        }
      });
  }

  public getOfferApplicationConfig(): Observable<OfferApplicationConfig> {
    return this.http.get<OfferApplicationConfig>(this.url + '/offer-app-config',
      {
        headers: {
          ignoreLoader: 'true'
        }
      });
  }

  public getUserConfig(): Observable<UserConfig> {
    return this.http.get<UserConfig>(this.url + '/user-config');
  }

  public getUsers(): Observable<{ name: string, firstName: string, lastName: string, id: number }[]> {
    return this.http.get<{ name: string, firstName: string, lastName: string, id: number }[]>(
      this.url + '/users');
  }


  getConditionOptions(attributeType) {
    switch (attributeType) {
      case 'String':
      case 'Url':
        return [{
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_BE'),
          value: 1
        }, {
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_BEGINS_WITH'),
          value: 7
        }, {
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_CONTAINS'),
          value: 8
        }, {
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_NOT_BEGIN_WITH'),
          value: 9
        }, {
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_NOT_BE'),
          value: 6
        }, {
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_NOT_CONTAIN'),
          value: 10
        }];

      case 'Date':
        return [{
          value: 1,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_ON')
        }, {
          value: 2,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_AFTER')
        }, {
          value: 3,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_ON_OR_AFTER')
        }, {
          value: 4,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_BEFORE')
        }, {
          value: 5,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_ON_OR_BEFORE')
        }, {
          value: 6,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_NOT_ON')
        }];

      case 'String Enum':
      case 'Single Select Database':
        return [{
          value: 'IN',
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_EQUAL_TO')
        }];

      case 'Boolean':
        return [{
          value: 1,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_EQUAL')
        }];

      case 'Number':
      case 'Integer':
      case 'Currency':
        return [{
          value: 1,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_EQUAL')
        }, {
          value: 2,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_GREATER_THAN')
        }, {
          value: 3,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_GREATER_THAN_OR_EQUAL_TO')
        }, {
          value: 4,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_LESS_THAN')
        }, {
          value: 5,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_LESS_THAN_OR_EQUAL_TO')
        }, {
          value: 6,
          label: this.translate.instant('CREATE_OFFER_LIST.VALUES.OPERATOR_NOT_EQUAL')
        }];
    }
  }

  decodeString(data) {
    let outputString = data;

    outputString = outputString.replace(/&amp;/g, '&');
    outputString = outputString.replace(/&quot;/g, `"`);
    outputString = outputString.replace(/&squot;/g, `'`);
    outputString = outputString.replace(/&ast;/g, '*');
    outputString = outputString.replace(/&lcb;/g, '{');
    outputString = outputString.replace(/&rcb;/g, '}');

    return outputString;
  }


  getAndSetAssetPickerInstances(ignoreLoader?: boolean) {
    let getApInstanceSubscription: Subscription;
    this.getAssetPickerConfigAndSetToken(ignoreLoader)
      .then(() => {
        getApInstanceSubscription = this.hclAssetPickerService.getInstances().subscribe(repos => {
          this.setAssetPickerRepositories(repos);
          this.hclAssetPickerService.repositories = repos;
          getApInstanceSubscription.unsubscribe();
        });
      });
  }

  downloadAssetPickerAnonymousContent(resourceUrl: string) {
    let downalodContentSubscription: Subscription;
    return new Promise((resolve, reject) => {
      this.getAssetPickerConfigAndSetToken(true)
        .then(() => {
          downalodContentSubscription = this.hclAssetPickerService.downloadContentFromResourceId(resourceUrl).subscribe((resp) => {
            const reader = new FileReader();
            reader.readAsDataURL(resp);
            reader.onload = () => {
              downalodContentSubscription.unsubscribe();
              resolve(reader.result);
            };
            reader.onerror = () => {
              downalodContentSubscription.unsubscribe();
              reject();
            };
            reader.onabort = () => {
              downalodContentSubscription.unsubscribe();
              reject();
            };
          }, (error) => {
            reject();
          });
        });
    });
  }

  getAssetPickerConfigAndSetToken(ignoreLoader?: boolean) {
    let applicationConfigSubscription: Subscription;
    return new Promise<void>((resolve, reject) => {
      applicationConfigSubscription = this.getAssetpickerApplicationConfig(ignoreLoader)
        .subscribe((config: AssetpickerApplicationConfig) => {
          let headers: HttpHeaders = this.getAPHeaders(config);
          headers = headers.set('ignoreLoader', 'true');
          this.hclAssetPickerService.headers = headers;
          this.hclAssetPickerService.baseUrl = config.url;
          applicationConfigSubscription.unsubscribe();
          resolve();
        }, (error) => {
          reject();
        });

    });
  }

  getAPHeaders(config: AssetpickerApplicationConfig) {
    const headers: HttpHeaders = new HttpHeaders()
      .set('m_user_name', this.userConfig.displayName)
      .set('m_tokenId', config.token)
      .set('api_auth_mode', 'manager')
      .set('401', 'ignore')
      .set('client_app_id', this.offerApplicationConfig.offerApplicationId.toString());
    return headers;
  }

}
