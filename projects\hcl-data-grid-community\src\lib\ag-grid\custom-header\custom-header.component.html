<ng-container
  *ngTemplateOutlet="columnDefinition._headerTemplateRef ? columnDefinition._headerTemplateRef : defaultHeader">
</ng-container>
<ng-template #defaultHeader>
  <div style="display: flex;" class="header-default-container" (click)="doSort($event, columnDefinition)">
    <div hclTooltip="{{columnDefinition.headerName}}" class="column-header-label">
      {{columnDefinition.headerName}}
    </div>
    <div *ngIf="columnDefinition.sortable" class="icon-container">
        <i [ngStyle]="{'color':columnDefinition._sorting === 'ASC'? '#f5821e' : '#959595' }"
          class="hcl-icon-up-dir" [ngClass]="{'active': columnDefinition._sorting === 'ASC'}"></i>
        <i [ngStyle]="{'color':columnDefinition._sorting === 'DESC'? '#f5821e' : '#959595' }"
          class="hcl-icon-down-dir" [ngClass]="{'active': columnDefinition._sorting === 'DESC'}"></i>
    </div>
    <!-- <div class="filter-icon" (click)="displayFilter($event)">
      <svg width="3px" height="13px" viewBox="0 0 3 13" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g id="Contact-Lists-Landing" transform="translate(-533.000000, -222.000000)" fill="#C7C7C7">
            <g id="Group-5" transform="translate(94.000000, 210.000000)">
              <g id="ico_filter" transform="translate(439.000000, 12.000000)">
                <circle id="Oval" cx="1.5" cy="1.5" r="1.5"></circle>
                <circle id="Oval" cx="1.5" cy="6.5" r="1.5"></circle>
                <circle id="Oval" cx="1.5" cy="11.5" r="1.5"></circle>
              </g>
            </g>
          </g>
        </g>
      </svg>
      <hcl-custom-popover [showPopover]="headerPopover" [customPopoverTemplate]="columnDefinition._popoverTemplateRef"></hcl-custom-popover>
    </div> -->
  </div>
</ng-template>
<!--<div *ngIf="showPopover" class="popOver triangle">-->
<!--<div>-->
<!--<ul class='tabMenu'>-->
<!--<li (click)="tabMenuClicked('filter')" class="tabList">Filter-->
<!--</li>-->
<!--<li (click)="tabMenuClicked('sort')" class="tabList">Sort-->
<!--</li>-->
<!--</ul>-->
<!--</div>-->
<!--<hr>-->
<!--<ng-container *ngIf="!showFilter">-->
<!--<div *ngFor="let sort of sortList">-->
<!--<input class="inputRadio" type="radio" />{{sort}}-->
<!--</div>-->
<!--</ng-container>-->


<!--<ng-container *ngIf="showFilter">-->
<!--<div *ngFor="let filter of filterList">-->
<!--<input class="inputRadio" type="checkbox" />{{filter}}-->
<!--</div>-->
<!--</ng-container>-->
<!--<hr>-->
<!--<div class="btnDiv" *ngIf="!showFilter">-->
<!--<div style="width:75%">-->
<!--<input class="popupBtn" type="button" value="Clear Sort">-->
<!--</div>-->
<!--<div>-->
<!--<input class="popupBtn" type="button" value="Apply" (click)="closePopup()">-->
<!--</div>-->
<!--</div>-->
<!--<div class="btnDiv" *ngIf="showFilter">-->
<!--<div style="width:75%">-->
<!--<input class="popupBtn" type="button" value="Clear Filter">-->
<!--</div>-->
<!--<div>-->
<!--<input class="popupBtn" type="button" value="Apply" (click)="closePopup()">-->
<!--</div>-->
<!--</div>-->
<!--</div>-->