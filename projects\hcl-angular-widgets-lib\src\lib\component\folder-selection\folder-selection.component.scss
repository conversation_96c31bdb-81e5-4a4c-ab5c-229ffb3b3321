.all-folders-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  // .folders-search {
  //   width: 200px;
  //   height: 54px;
  // }

  .selected-items-section {
    height: 130px;
    .selected-items {
      margin-top: 5px;
      height: 100px;
      overflow-y: auto;
    }
    .selected-items-section-title {
      color: #6d7692;
      font-weight: 600;
      border-bottom: 1.2px solid #bcbbbb;
      height: 25px;
      margin-bottom: 5px;
      display: flex;
    }
  }

  .folder-selection-content {
    height: calc(100% - 170px);

    &.full-height {
      height: calc(100% - 40px);
    }

    .folder-selection-title {
      color: #6d7692;
      font-weight: 600;
      border-bottom: 1.2px solid #bcbbbb;
      height: 25px;
      margin-bottom: 5px;
      display: flex;
    }

    .breadcrumb-container {
      height: 30px;
      margin: 5px 0;
      position: relative;
      overflow: hidden;
      z-index: 99;
      .custom-breadcrumb-item {
        padding-left: 0;
        display: flex;
        align-items: center;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 80px;
      }
      .breadcrumb-item {
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .breadcrumb {
        flex-wrap: nowrap;
      }
    }

    .folder-listing-body {
      height: calc(100% - 70px);
      overflow-y: auto;
      .folder-list {
        list-style: none;
        padding: 0;
        .folder {
          height: 48px;
          border-bottom: 1px solid #e0e0e0;
          display: flex;
          align-items: center;
          padding: 0 0 0 12px;
          color: #444444;
          font-size: 14px;
          cursor: pointer;
          .folder-name {
            overflow: hidden;
            text-overflow: ellipsis;
            height: 21px;
          }
          i {
            color: #6d7692;
            font-size: 12px;
            margin-right: 8px;
          }
        }
      }
      .folder-disabled {
        pointer-events: none;
        opacity: 0.5;
      }

      .no-folders {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;

        span {
          color: #6d7692;
          font-family: Roboto;
          font-size: 12px;
          letter-spacing: 0.4px;
          line-height: 14px;
        }
      }
    }
  }

  .folder-selection-actions {
    height: 40px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    button {
      margin-left: 20px;
    }
  }
}

.breadcrumb {
  margin-bottom: 0;
  padding: 0;
  margin: 0;
  .bIcon {
    font-size: 9px;
    margin: 0 3px;
  }
  .breadcrumb-item {
    padding-bottom: 0;
  }
  .custom-breadcrumb-item {
    font-size: 13px;
    padding-left: 0;
    display: flex;
    align-items: center;
    font-family: Roboto, Roboto Regular, sans-serif;
  }
  i {
    color: #959595;
  }
}

.rectangular-chip {
  align-items: center;
  display: flex;
  flex-direction: row;
  float: left;
  margin: 0 5px 5px 0;
  max-width: 216px;
  margin-bottom: 10px;
  box-sizing: border-box;
  border: 2px solid #d2d6e1;
  border-radius: 5px;
  background-color: #f5f5f5;
  padding: 5px;
  color: #444444;
  font-size: 14px;
  line-height: 17px;
  // cursor: move;
  // .hcl-icon-drag {
  //   font-size: 10px;
  //   color: #959595;
  //   opacity: 0;
  //   text-align: center;
  // }
  // &:hover .hcl-icon-drag {
  //   transition: opacity 0.2s;
  //   opacity: 1;
  //   z-index: 5;
  // }
  .select-option {
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0 8px;
    width: 100%;
  }

  &.normal-cursor {
    cursor: auto;
  }

  &:hover {
    border: 2px solid #f5821e;
    background-color: #ececec;
    box-shadow: 6px 6px 10px 0 rgba(0, 0, 0, 0.3);
  }
}

[class^="hcl-icon-"] {
  cursor: pointer;
  font-family: "fontello";
  font-style: normal;
  font-weight: normal;
  display: inline-block;
  text-decoration: inherit;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
