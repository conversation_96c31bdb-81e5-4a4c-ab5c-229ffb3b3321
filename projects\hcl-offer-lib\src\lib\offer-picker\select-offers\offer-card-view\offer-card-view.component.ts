import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ButtonConf } from 'hcl-angular-widgets-lib';
import { SubscriptionLike } from 'rxjs';
import { OfferDataService } from '../../offer-data.service';
import { SelectOffersService } from '../../select-offers.service';
import { Offer, OfferGetResponse } from '../select-offers.model';

@Component({
  selector: 'hcl-offer-card-view',
  templateUrl: './offer-card-view.component.html',
  styleUrls: ['./offer-card-view.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class OfferCardViewComponent implements OnInit, OnChanges {

  subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  @Output() offerSelectionUpdate: EventEmitter<any> = new EventEmitter<any>();
  @Input() cvOfferSortState: string;
  @Input() folderPanelState: number;

  manageSelectionsButtonConf: ButtonConf;

  isDataLoading = false;
  offerResponseData: any;
  defaultFolderId: number;
  pageSize = 24;

  chunkSize: number;
  // totalElementsFound = 0;
  parentContainerWidth: number;

  noRecordsFound = false;
  folderWithoutOffers = false;

  constructor(
    private translate: TranslateService,
    public selectOffersService: SelectOffersService,
    // private offerDataService: OfferDataService
  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['cvOfferSortState']?.currentValue !== undefined) {
      this.loadOffers(0);
    }
    if (changes['folderPanelState']?.previousValue !== undefined) {
      if(this.parentContainerWidth > 0){
      this.setUpOfferRowItemChunkSize();
      }
    }
  }

  setUpOfferRowItemChunkSize() {  
    if (this.folderPanelState === 2) {
       //const folderElementWidth = this.parentContainerWidth * 35 / 100;
	   const containerWidth: number = (this.parentContainerWidth * 65 / 100) - 49; // 15 => substraction of result item container left padding 30px and folder panel right margin 15
       this.chunkSize = Math.floor(containerWidth / 183);
       this.pageSize = this.chunkSize * 4;
       this.loadOffers(0);
     } else if (this.folderPanelState === 1) {
       const containerWidth: number = this.parentContainerWidth - 100; // 100 => addition of sidebar 50px, sidebar right margin 15px and result item container left padding 30px and additional 5px for safety
       this.chunkSize = Math.floor(containerWidth / 183);
       this.pageSize = this.chunkSize * 4;
       this.loadOffers(0);
     }    
 }


 

  ngOnInit(): void {
    this.setConfiguration();

    this.defaultFolderId = this.selectOffersService.offersFolder;
    this.loadOffers(0);

    this.subscriptionList.push(this.selectOffersService.getFolderId().subscribe(obj => {
      if (obj[0] === 'offers') {
        this.defaultFolderId = obj[1];
        // if (obj[2]) {
        //   this.offerFilterState = this.offerDataService.offersState;
        // }

        if (this.selectOffersService.lastFolderClicked === null || this.selectOffersService.lastFolderClicked !== this.defaultFolderId) {
          this.selectOffersService.lastFolderClicked = this.defaultFolderId;
        }
        // this.selectedOffersList = [];
        // this.cvSelectedOffer.emit([]);
        this.loadOffers(0);
        this.selectOffersService.clearFolderId();
      }
    }));

    this.subscriptionList.push(this.selectOffersService.getOffersSearchData().subscribe(obj => {
      if (obj.length > 0) {
        // this.selectedOffersList = [];
        // this.cvSelectedOffer.emit([]);
        // if (obj[1]) {
        //   this.offerFilterState = obj[1];
        // }
        this.loadOffers(0, encodeURI(obj[0]));
        this.selectOffersService.clearOffersSearchData();
      }
    }));

    
     // this.parentContainerWidth = document.getElementsByClassName('offers-card-view-container')[0]?.clientWidth;
      // this.resultContainerWidth = document.getElementsByClassName('result')[0]?.clientWidth;
      
    this.parentContainerWidth = document.getElementsByClassName('offers-container')[0]?.clientWidth - 20;
    this.setUpOfferRowItemChunkSize(); 
  

    
  }

  setConfiguration() {
    this.manageSelectionsButtonConf = {
      color: 'accent',
      buttonType: 'mat',
      type: 'button',
      value: this.translate.instant('OFFER_PICKER.TITLES.MANAGE_SELECTIONS'),
      name: 'removeMapping',
      disabled: false
    };
  }

  loadNewOffers(page: number) {
    this.loadOffers(page, '');
  }

  loadOffers(page: number, searchParam?: string) {
    this.isDataLoading = true;
    searchParam = searchParam ? encodeURI(searchParam) : '';
    this.subscriptionList.push(
      this.selectOffersService.getOffers(this.defaultFolderId, page, this.pageSize, true, searchParam)
        .subscribe((offerData: OfferGetResponse) => {
          this.isDataLoading = false;
          if (offerData.content.length) {
            this.noRecordsFound = false;
            this.folderWithoutOffers = false;
           
            const tempList = offerData.content.map((cont: Offer) => ({ ...cont, isImageLoaded: false , brokenThumbnail: false}));
            let i: number, j: number, temporary: Offer[][] = [];
            for (i = 0, j = tempList.length; i < j; i += this.chunkSize) {
              temporary.push(tempList.slice(i, i + this.chunkSize));
            }
            if (!page) {
              this.offerResponseData = { page: offerData.page, content: temporary }
            } else {
              this.offerResponseData.page.pageNumber = page;
              this.offerResponseData.content = [...this.offerResponseData.content, ...temporary];
            }
          } else {
            this.offerResponseData = {};
            if (searchParam) {
              this.noRecordsFound = true;
              this.folderWithoutOffers = false;
            } else {
              this.folderWithoutOffers = true;
              this.noRecordsFound = false;
            }
          }
        })
    );
  }

  manageSelections(event) {
    this.selectOffersService.sendloadNext('manageOffers');
  }

  offerSelection(data) {
    this.offerSelectionUpdate.emit(data);
  }

  selectedOffersAndOls() {
    if (this.selectOffersService.loadOfferOrOfferLists === 'both') {
      return `${this.selectOffersService.selectedOffersData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')} | 
      ${this.selectOffersService.selectedOlData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS')} 
      ${this.translate.instant('FIELDS.SELECTED')}`;
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offerLists') {
      return `${this.selectOffersService.selectedOlData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS')} 
      ${this.translate.instant('FIELDS.SELECTED')}`;
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offers') {
      return `${this.selectOffersService.selectedOffersData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')} 
      ${this.translate.instant('FIELDS.SELECTED')}`;
    }
  }

}
