$title-font-color: #6d7692;
$title-font-family: <PERSON>ser<PERSON>;
$value-font-color: #444444;
$value-font-family: <PERSON><PERSON>;

.responder-history-info-container {
    padding-top: 35px;
    font-size: 14px;
    .info-title {
        font-size: 16px;
        font-weight: bold;
        color: $title-font-color;
        font-family: $title-font-family;
        .total-records {
            font-size: 12px;
            font-weight: normal;
        }
    }
    .grid-container {
        height: 150px;
        width: 100%;
        .hcl-grid-container {
            padding: 0px;
            .agGridContainer {
                background: #ececec !important;
                .ag-status-bar {
                    border: 0px;
                }
                .ag-body-viewport {
                    padding-bottom: 0px;
                }
            }
            .ag-row {
                &:hover {
                    &:after {
                        z-index: 0;
                    }
                }
            }
            .ag-header {
                height: 34px !important;
                min-height: 34px !important;
                background-color: #ececec !important;
                border-bottom: 0px;
                .ag-header-row {
                    .ag-header-cell {
                        height: 34px;
                        background-color: #bcbbbb !important;
                        border-right: 1px solid #ffffff !important;
                        .ag-header-cell-resize {
                            border: 0px;
                        }
                        .header-default-container {
                            .column-header-label {
                                color: $value-font-color;
                                font-weight: bold;
                            }
                        }
                    }
                }
            }
            .ag-cell {
                line-height: 34px;
            }
            hcl-custom-cell {
                div:first-child {
                    span {
                        line-height: 2.5;
                    }
                    hcl-checkbox {
                        position: relative;
                        top: 7px;
                    }
                }
            }
        }
    }
    .response-history-list {
        list-style: none;
        padding: 0;
        height: calc(100% - 280px);
        .history-record {
            padding-bottom: 10px;
            font-family: $title-font-family;
            display: flex;
            span {
                font-weight: bold;
                color: $value-font-color;
                margin-right: 5px;
            }
            .record-status-approved {
                color: #c4d056;
            }
            .record-status-denied {
                color: #f44336;
            }
            .ellipsis {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .hoverable-text {
                max-width: 25%;
            }
        }
        .no-record {
            justify-content: center;
            align-items: center;
            span {
                font-family: $value-font-family;
                font-weight: normal;
            }
        }
    }
}
