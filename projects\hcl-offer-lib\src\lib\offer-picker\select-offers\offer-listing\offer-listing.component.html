<hcl-data-grid-v2 #offersGrid class="w-100 com-offer-grid" [config]="offersGridConf" (rowSelected)="rowSelected($event)"
    (rowUnSelected)="rowUnSelected($event)" (pageSizeChanged)="pageSizeChanged($event)"
    (dataLoadedFromUrl)="gridDataLoaded($event)" (columnSorting)="columnaSorting($event)"
    (gridReady)="onGridReady($event)">
    <ng-template hclTemplate hclTemplateName="displayName" type="cell-renderer" let-cell>
        <div *ngIf="cell && cell.row && !variantMap.has(+cell.row.id)" class="ellipsis" (click)="onCellClicked(cell)">
            <span *ngIf="cell.row.state" class="bullet-round mr-2" [ngClass]="{'green-bullet': cell.row.state ==='PUBLISHED',
            'grey-bullet': cell.row.state ==='RETIRED','orange-bullet': cell.row.state ==='DRAFT'}"></span>
            <span class="link">{{cell.row && cell.row[cell.col.field]}}</span>
        </div>
        <div *ngIf="cell && cell.row && variantMap.has(+cell.row.id)" class="ellipsis" (click)="onCellClicked(cell)">
            <span *ngIf="cell.row.state" class="bullet-round mr-2" [ngClass]="{'green-bullet': cell.row.state ==='PUBLISHED',
            'grey-bullet': cell.row.state ==='RETIRED','orange-bullet': cell.row.state ==='DRAFT'}"></span>
            <span class="top-label">{{variantMap.get(+cell.row.id).variantDisplayName}}</span>
            <span class="bottom-label link">{{variantMap.get(+cell.row.id).offerDisplayName}}</span>
        </div>
    </ng-template>
</hcl-data-grid-v2>

<div class="manage-selections">
    <div class="selected-offers" hclTooltip="{{selectedOffersAndOls()}}">{{ selectedOffersAndOls() }}</div>
    <div class="manage-selections-button">
        <hcl-button class="mr-10px" [config]="manageSelectionsButtonConf" (click)="manageSelections($event)">
        </hcl-button>
    </div>
</div>