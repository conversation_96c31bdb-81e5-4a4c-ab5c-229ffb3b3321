.channel-preferences-section {
    font-family: <PERSON><PERSON>, Arial, Helvetica, sans-serif;
    color: #6d7692;
    font-size: 16px;
    line-height: 19px;
    letter-spacing: normal;
    text-align: left;
    & > div {
        padding: 5px 10px 10px;
    }
    .preferences-title {
        font-weight: bold;
        font-size: 24px;
        line-height: 29px;
        color: #444444;
    }
    .preferences-header {
        background-color: #cccccc;
        div {
            border-right: 1px solid #979797;
            &:last-child {
                border-right: none;
            }
        }
    }
    .pl-20 {
        padding-left: 20px;
    }
    .w-15 {
        width: 15%;
    }
    .w-20 {
        width: 20%;
    }
    .w-30 {
        width: 30%;
    }
    .w-40 {
        width: 40%;
    }
    .pr-15 {
        padding-right: 15px;
    }
    .mat-checkbox-checked.mat-accent .mat-checkbox-background {
        background-color: #f5821e;
    }
    .row-padding {
        padding: 10px 20px 10px 20px;
        border-bottom: 1px solid #cccccc;
    }
}