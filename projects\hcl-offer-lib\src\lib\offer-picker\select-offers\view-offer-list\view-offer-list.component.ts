import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import { MenuComponent, ButtonConf, ModalConfig, TabsConfig } from 'hcl-angular-widgets-lib';
import { DataGridV2Component, DataGridConf } from 'hcl-data-grid-lib';
import { forkJoin, of, Subscription, SubscriptionLike } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { DatePipe } from '@angular/common';
import { OfferDataService } from '../../offer-data.service';
import { catchError } from 'rxjs/operators';
import { SelectOffersService } from '../../select-offers.service';
import * as _ from 'lodash';
import { SelectOffersConstant } from '../select-offers.constant';

@Component({
  selector: 'hcl-view-offer-list',
  templateUrl: './view-offer-list.component.html',
  styleUrls: ['./view-offer-list.component.scss']
})
export class ViewOfferListComponent implements OnInit, OnDestroy {

  @ViewChild('offerListsGrid', { static: false }) offerListsGrid: DataGridV2Component;

  @Output() loadOfferLists = new EventEmitter();
  @Output() closePane = new EventEmitter();

  offerListId: Number;
  readyToRender = false;
  displayName: string;
  securityPolicy: string;
  type: string;
  description: string;
  gridConfig: DataGridConf;
  subfoldersIncluded: boolean;
  queryView = '';
  folders: any;
  gridApi: any;
  offers = [];
  editConfig: ButtonConf;
  noActionConfig: ButtonConf;
  cancelRetireOlConfig: ButtonConf;
  yesRetireOlConfig: ButtonConf;
  expanded = true;
  limit: number;
  orderRef: string;
  previousUrl: string;
  editOfferListPermissionSubscription: Subscription;
  olPermissions: Subscription;
  deletedFolders = [];
  offerViewPermissionSubscription: Subscription;
  deletedFolderIdsMessage: string;
  subscriptionList: SubscriptionLike[] = [];
  ssdbIds = [];
  state: string;
  stateClass: string;
  permissionList = {};
  tabIndex = 0;
  retireOlDialogConfig: ModalConfig;
  loadTabs: boolean = true;

  tabHorizontalConfig: TabsConfig = {
    selectedTab: 0,
    elements: [
      {
        header: this.translate.instant('OFFER_INFO.TITLES.SUMMARY'),
        templateName: 'summary'
      }
    ]
  };

  offerListGridViewActive = true;

  constructor(
    public translate: TranslateService,
    private datePipe: DatePipe,
    private offerDataService: OfferDataService,
    public selectOffersService: SelectOffersService
  ) { }

  ngOnInit() {

    if (!this.selectOffersService.offerlistViewMode) {
      this.selectOffersService.offerlistViewMode = this.offerDataService.userConfig.defaultOfferListingView;
    }

    if (this.selectOffersService.offerlistViewMode !== SelectOffersConstant.gridView) {
      this.offerListGridViewActive = false;
    }

    this.setConfiguration();

    this.offerListId = this.selectOffersService.offerListId;

    this.subscriptionList.push(forkJoin([this.selectOffersService.getOfferListById(this.offerListId),
    this.selectOffersService.getAllWithBasicDetails(),
    this.offerDataService.getUsers()]).subscribe(([offerList, attributes, users]: any) => {

      if (offerList.state) {
        this.updateOfferState(offerList.state);
      }

      const attributeList = [];

      attributes.body.forEach((attribute) => {
        if (attribute.id !== 1) {
          attributeList.push(attribute);
        }
      });

      attributeList.push(...[{
        attributeType: 'Date', id: 1,
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.EFFECTIVE_DATE.DISPLAY_NAME')
      }, {
        attributeType: 'Date', id: 2,
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.EXPIRATION_DATE.DISPLAY_NAME')
      }, {
        attributeType: 'Number', id: 3,
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.EXPIRATION_DURATION.DISPLAY_NAME')
      }, {
        attributeType: 'String Enum', id: 'CreateBy',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.CREATE_BY.DISPLAY_NAME')
      }, {
        attributeType: 'Date', id: 'CreateDate',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.CREATE_DATE.DISPLAY_NAME')
      }, {
        attributeType: 'String Enum', id: 'UpdateBy',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.UPDATE_BY.DISPLAY_NAME')
      }, {
        attributeType: 'Date', id: 'UpdateDate',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.UPDATE_DATE.DISPLAY_NAME')
      }, {
        attributeType: 'String', id: 'Name',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.OFFER_NAME.DISPLAY_NAME')
      }, {
        attributeType: 'String', id: 'Description',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.OFFER_DESCRIPTION.DISPLAY_NAME')
      }, {
        attributeType: 'String', id: 'OfferTemplID',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.OFFER_TEMPLATE.DISPLAY_NAME')
      },
      {
        attributeType: 'String', id: 'OfferCode1',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.OFFER_CODE_1.DISPLAY_NAME')
      },
      {
        attributeType: 'String', id: 'offercode2',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.OFFER_CODE_2.DISPLAY_NAME')
      },
      {
        attributeType: 'String', id: 'offercode3',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.OFFER_CODE_3.DISPLAY_NAME')
      },
      {
        attributeType: 'String', id: 'offercode4',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.OFFER_CODE_4.DISPLAY_NAME')
      },
      {
        attributeType: 'String', id: 'offercode5',
        displayName: this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.OFFER_CODE_5.DISPLAY_NAME')
      }]);

      if (offerList && offerList.type && offerList.type.id && offerList.type.id === 'SMART') {
        this.loadSmartOlOffers(offerList, attributeList, users);
        if (offerList.type && offerList.type.properties && offerList.type.properties.includeSubFolders) {
          this.subfoldersIncluded = offerList.type.properties.includeSubFolders;
        }
      } else {
        this.loadStaticOlOffers();
      }

      this.displayName = offerList.displayName;
      this.description = offerList.description;
      this.limit = (offerList.type.properties.size >= 0) ? offerList.type.properties.size : null;
      this.offerDataService.getSecurityPolicies().some(policy => {
        if (policy.value === offerList.policyId) {
          this.securityPolicy = policy.label;
        }
      });
      this.type = this.isStaticOfferList(offerList.type.id) ?
        this.translate.instant('CREATE_OFFER_LIST.LABELS.STATIC_OFFER_LIST') :
        this.translate.instant('CREATE_OFFER_LIST.LABELS.SMART_OFFER_LIST');
    }));
  }

  changeListingView(view: 'Card view' | 'Grid view') {
    if (view === SelectOffersConstant.gridView && !this.offerListGridViewActive) {
      this.offerListGridViewActive = true;
    }
    if (view === SelectOffersConstant.cardView && this.offerListGridViewActive) {
      this.offerListGridViewActive = false;
    }

    this.selectOffersService.offerlistViewMode = view;
  }

  loadSmartOlOffers(smartOl, attributeList, users) {
    this.subscriptionList.push(this.selectOffersService.getSmartOfferListOffers(this.offerListId).subscribe((offers: any) => {
      this.offers = offers;
      this.offerDataService.offers = offers;
      this.folders = smartOl.type.properties.folderDTOs || [];

      this.extractSsdbAttributeValues(smartOl.type.properties.offerQuery, attributeList, users);

      if (smartOl.type.properties.deletedFolderIds && smartOl.type.properties.deletedFolderIds.length) {
        this.deletedFolderIdsMessage = this.translate.instant('CREATE_OFFER_LIST.LABELS.FOLDERS_REMOVED_ERROR',
          { value: smartOl.type.properties.deletedFolderIds });
      }

      this.readyToRender = true;
    }));
  }

  loadStaticOlOffers() {
    this.subscriptionList.push(this.selectOffersService.getOfferListOffers(this.offerListId).subscribe((offers: any) => {
      this.offers = offers;
      this.offerDataService.offers = offers;
      this.readyToRender = true;
    }));
  }

  updateOfferState(state) {
    this.stateClass = state;
    if (state === 'DRAFT') {
      this.state = this.translate.instant('TITLES.DRAFT');
    } else if (state === 'PUBLISHED') {
      this.state = this.translate.instant('TITLES.PUBLISHED');
    } else if (state === 'RETIRED') {
      this.state = this.translate.instant('LIST_OFFER_TEMPLATES.HEADERS.RETIRED');
    }
  }

  setConfiguration() {
    this.editConfig = {
      name: 'editOffer',
      value: this.translate.instant('LIST_CUSTOM_ATTRIBUTE.HEADERS.EDIT'),
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };
    this.noActionConfig = {
      name: 'no',
      value: this.translate.instant('BUTTONS.CLOSE'),
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };
    this.cancelRetireOlConfig = {
      name: 'no',
      value: this.translate.instant('BUTTONS.CLOSE'),
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };
    this.yesRetireOlConfig = {
      name: 'retireOl',
      value: this.translate.instant('BUTTONS.RETIRE'),
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };
    this.gridConfig = {
      scrollHeight: 330,
      isClientSideRowModel: true,
      columns: [
        {
          field: 'displayName',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.OFFER_NAME'),
          colId: 'displayName',
          sortable: true,
          autoResizeToFit: true,
          rendererTemplateName: 'displayName',
          tooltip: {
            getTooltip: (attr: any) => {
              return attr.displayName;
            }
          },
          cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.isRetired === true;
            }
          }
        },
        {
          field: 'offerCode',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.OFFER_CODE'),
          colId: 'offerCode',
          sortable: true,
          autoResizeToFit: true,
          useDefaultRenderer: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return attr.offerCode;
            }
          },
          dataFormatter: (row: any) => {
            return row.data.offerCode;
          },
          cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.isRetired === true;
            }
          }
        },
        {
          field: 'channel',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.CHANNELS'),
          colId: 'channel',
          useDefaultRenderer: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return attr.channel;
            }
          },
          dataFormatter: (attr: any) => {
            return attr.data.channel;
          },
          autoResizeToFit: true,
          minWidth: 150,
          cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.isRetired === true;
            }
          }
        },
        {
          field: 'effAndExpDate',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.EFF_EXP_DATES'),
          colId: 'effAndExpDate',
          autoResizeToFit: true,
          minWidth: 150,
          useDefaultRenderer: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return attr.effAndExpDate;
            }
          },
          dataFormatter: (attr: any) => {
            return attr.data.effAndExpDate;
          },
          cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.isRetired === true;
            }
          }
        }
      ],
      data: [],
      suppressRowClickSelection: true,
      suppressDragLeaveHidesColumns: true,
      noRowsTemplate: this.translate.instant('MESSAGES.NO_DATA_ON_HTTP_RESPONSE')
    };
  }

  extractSsdbAttributeValues(offerQuery, attributeList, users) {
    this.ssdbIds = [];
    const httpRequestData = [];
    const ssdbValues = new Map<number, any>();
    let queryArray = [];
    if (offerQuery) {
      queryArray = offerQuery.replace(/{/g, '').split('}').filter(Boolean);
    }
    if (queryArray.length > 0) {
      queryArray.forEach(query => {
        const tokens = query.split('*'),
          attributeOption = _.find(attributeList, (item) =>
            tokens[1].toString().toUpperCase() === item.id.toString().toUpperCase());
        if (attributeOption.attributeType === 'Single Select Database' && (this.ssdbIds.indexOf(attributeOption.id) === -1)) {
          this.ssdbIds.push(attributeOption.id);
          httpRequestData.push(this.selectOffersService.getSsdbAttributeListById({ ids: [attributeOption.id] })
            .pipe(
              catchError(err => {
                return of(err);
              }
              )
            )
          );
        }
      });
      if (this.ssdbIds.length > 0) {
        this.subscriptionList.push(forkJoin(httpRequestData).subscribe((data: any) => {
          data.forEach((item, index) => {
            if (item.hasOwnProperty('error')) {

            } else {
              const key = Object.keys(item)[0];
              ssdbValues.set(+key, item[key] ? { values: item[key] } : {});
            }
          });
          this.selectOffersService.ssdbValues = ssdbValues;
          this.generateQueryView(offerQuery, attributeList, users);
        }));
      } else {
        this.generateQueryView(offerQuery, attributeList, users);
      }
    }
  }

  generateQueryView(offerQuery, attributeList, users) {
    if (offerQuery) {
      const queryArray = offerQuery.replace(/{/g, '').split('}').filter(Boolean);

      if (queryArray.length > 0) {
        queryArray.forEach((query, index) => {
          const tokens = query.split('*'),
            attributeOption = _.find(attributeList, (item) => tokens[1].toString().toUpperCase() === item.id.toString().toUpperCase());

          if (tokens.length > 3) {
            const conditionOptions = this.offerDataService.getConditionOptions(attributeOption.attributeType),
              resultOption = _.find(conditionOptions, (option: any) => {
                return tokens[2].toString() === option.value.toString();
              });


            if (attributeOption.id === 'CreateBy' || attributeOption.id === 'UpdateBy') {
              const ids = tokens[3].split('`').filter(Boolean), userList = [];

              ids.forEach(id => {
                users.some(user => {
                  if (user.id === +id) {
                    userList.push(user.name);

                    return true;
                  }
                });
              });

              tokens[3] = _.join(userList.map(str => '"' + this.offerDataService.decodeString(str) + '"'), ' ' +
                this.translate.instant('CREATE_OFFER_LIST.VALUES.OR') + ' ');
            } else if (attributeOption.attributeType === 'Single Select Database') {
              if (this.selectOffersService.ssdbValues.has(+attributeOption.id)) {
                const ssdbIds = tokens[3].split('`').filter(Boolean);
                const ssdbValues = [];
                const ssdbAttribute = this.selectOffersService.ssdbValues.get(+attributeOption.id);
                if (ssdbAttribute.displayColumnType === 'Date') {
                  ssdbIds.forEach(id => {
                    ssdbAttribute.values.some(value => {
                      if (value.idColumnValue === +id) {
                        ssdbValues.push(this.datePipe.transform(value.displayColumnValue, this.offerDataService.getDatePipeFormatLocale()));
                        return true;
                      }
                    });
                  });
                } else {
                  ssdbIds.forEach(id => {
                    ssdbAttribute.values.some(value => {
                      if (value.idColumnValue === +id) {
                        ssdbValues.push(value.displayColumnValue);
                        return true;
                      }
                    });
                  });
                }
                tokens[3] = _.join(ssdbValues.map(str => '"' + this.offerDataService.decodeString('' + str) + '"'), ' ' +
                  this.translate.instant('CREATE_OFFER_LIST.VALUES.OR') + ' ');
              }

            } else {
              tokens[3] = this.extractValue(attributeOption.attributeType, tokens[3]);
            }

            const textNode = `<span>(
              ${(attributeOption.isSystemDefined ? this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.' + attributeOption.id
              + '.DISPLAY_NAME') : attributeOption.displayName)} ${resultOption.label} ${tokens[3]}) </span>`;

            if (index === 0) {
              this.queryView += '<br\>' + textNode;
            } else {
              const andOr = `<span>
              ${(tokens[0] === 'AND' ? this.translate.instant('CREATE_OFFER_LIST.VALUES.AND')
                  : this.translate.instant('CREATE_OFFER_LIST.VALUES.OR'))}</span>`;

              this.queryView += (andOr + '<br\>' + textNode);
            }
          } else {
            this.orderRef = attributeOption.isSystemDefined ? this.translate.instant('SYSTEM_DEFINED_ATTRIBUTES.' + attributeOption.id
              + '.DISPLAY_NAME') : attributeOption.displayName;
            this.orderRef += '- ' + (tokens[2] === 'ASC' ? this.translate.instant('CREATE_OFFER_LIST.LABELS.ASCENDING') :
              this.translate.instant('CREATE_OFFER_LIST.LABELS.DESCENDING'));
          }
        });
      }
    } else {
      this.queryView = '';
    }
  }

  extractValue(type, value) {
    if (type === 'Boolean') {
      let num = Number(value);
      if (Number.isNaN(num)) {
        num = 0;
      }
      return num > 0 ? this.translate.instant('FIELDS.TRUE') : this.translate.instant('FIELDS.FALSE');
    } else if (type === 'String Enum') {
      const OR = this.translate.instant('CREATE_OFFER_LIST.VALUES.OR');
      const newValue = value.split('`').filter(Boolean)
        .map(str => '"' + this.offerDataService.decodeString(str) + '" ' + OR);

      newValue[newValue.length - 1] = newValue[newValue.length - 1].slice(0, -OR.length);

      return newValue.join(' ');
    } else if (type === 'Date') {
      return this.datePipe.transform(+value, this.offerDataService.getDatePipeFormatLocale());
    } else if (type === 'String' || type === 'Url') {
      return '"' + this.offerDataService.decodeString(value) + '"';
    } else {
      return value;
    }
  }

  gridReady(data) {
    this.gridApi = data.params.api;

    this.gridConfig.data = this.offers.map(offer => {
      return {
        displayName: offer.displayName, id: offer.id,
        offerCode: offer.offerCode ? offer.offerCode : offer.offerCodes.reduce((accumlator, value) => {
          return accumlator + this.offerDataService.offerApplicationConfig.offerCodeDelimiter + value;
        }),
        channel: this.returnChannel(offer),
        effAndExpDate: this.returnEffAndExpDate(offer),
        isRetired: offer.isRetired, // if offer is retired, row in grid should be in grey color.
        parameterizedAttributes: offer.parameterizedAttributes,
        state: offer.state
      };
    });

    this.gridApi.setRowData(this.gridConfig.data);
  }

  returnChannel(row: any) {
    const obj = _.find(row.attributes, { id: 10 }) ||
      _.find(row.parameterizedAttributes, { id: 10 }) ||
      _.find(row.hiddenAttributes, { id: 10 }) ||
      _.find(row.staticAttributes, { id: 10 });

    return obj ? obj.value : '';
  }

  extractDates(value) {
    let str = '';
    if (value.effectiveDate) {
      const date = new Date(value.effectiveDate);
      str += this.datePipe.transform(date, this.offerDataService.getDatePipeFormatLocale()) + '-';
    } else if (value.isFlowChartRunDate) {
      str += this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.FLOWCHART_RUN_DATE') + '-';
    } else {
      str += '- / ';
    }
    if (value.expirationDate) {
      const date = new Date(value.expirationDate);
      str += this.datePipe.transform(date, this.offerDataService.getDatePipeFormatLocale());
    } else if (value.expirationDuration) {
      str += value.expirationDuration + ' ' + this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.DAYS_LATER');
    } else {
      str += '-';
    }
    return str;
  }

  returnEffAndExpDate(row) {
    const obj = _.find(row.attributes, { id: 1 }) ||
      _.find(row.parameterizedAttributes, { id: 1 }) ||
      _.find(row.hiddenAttributes, { id: 1 }) ||
      _.find(row.staticAttributes, { id: 1 });

    return obj ? this.extractDates(obj.value) : '- / -';
  }

  onCellClicked(cell) {
  }

  isStaticOfferList(id) {
    return id === 'STATIC';
  }

  isSmartOfferList() {
    return this.translate.instant('CREATE_OFFER_LIST.LABELS.SMART_OFFER_LIST') === this.type;
  }

  cancel() {
    if (!this.selectOffersService.directPathAccess) {
      this.loadOfferLists.emit(this.selectOffersService.viewRoute);
    } else {
      this.closePane.emit();
    }
  }

  toggleWidth() {
    this.expanded = !this.expanded;

    setTimeout(() => {
      if (this.offerListsGrid) {
        this.offerListsGrid.resizeColumnsToFit();
      }
    }, 800);
  }

  verticalLocale() {
    const userLanguage = this.offerDataService.userConfig.locale;
    return userLanguage === 'zh_TW' || userLanguage === 'zh_CN' || userLanguage === 'ja_JP' || userLanguage === 'ko_KR';
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => sub.unsubscribe());
  }
}

