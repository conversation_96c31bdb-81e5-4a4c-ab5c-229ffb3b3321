import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ViewEncapsulation } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { TabsConfig, ButtonConf } from 'hcl-angular-widgets-lib';
import { HclAngularApprovalLibService } from '../../service/hcl-angular-approval-lib.service';
import { Approval } from '../../models/approval';
import { TranslateService } from '@ngx-translate/core';
import { ApprovalSharedDataService } from '../../service/approval-shared-data.service';
import { Role } from '../../models/role';

@Component({
  selector: 'hcl-approval-details',
  templateUrl: './approval-details.component.html',
  styleUrls: ['./approval-details.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ApprovalDetailsComponent implements OnInit, OnDestroy {

  @Input() id: number;
  @Output() closeApprovalDetailsPanel = new EventEmitter();
  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  approvalTabsConfig: TabsConfig = {
    elements: [{ header: this._translate.instant('APPROVALPICKER.TITLES.GENERAL'), templateName: 'hTab1' },
    { header: this._translate.instant('APPROVALPICKER.TITLES.REVIEWERS'), templateName: 'hTab2' },
    { header: this._translate.instant('APPROVALPICKER.TITLES.RULES'), templateName: 'hTab3' },
    { header: this._translate.instant('APPROVALPICKER.TITLES.ITEMS_TO_APPROVE'), templateName: 'hTab4' },
    { header: this._translate.instant('APPROVALPICKER.TITLES.RESPONDER_HISTORY'), templateName: 'hTab5' },
    { header: this._translate.instant('APPROVALPICKER.TITLES.Analysis'), templateName: 'hTab6' }
      // { header: this._translate.instant('APPROVALPICKER.TITLES.DEPENDENCY'), templateName: 'hTab7' }
    ],
    selectedTab: 0
  };
  currentApproval: Approval = null;
  showErrorFlag: boolean = false;
  errorMsg: string = null;

  closeButtonConfig: ButtonConf = {
    buttonType: 'stroked',
    color: 'accent',
    borderRadius: 5,
    name: 'closeBtn',
    type: 'button',
    styleClass: 'medium-btn',
    value: this._translate.instant('APPROVALPICKER.BUTTONS.CLOSE_BTN')
  };

  constructor(private _approvalService: HclAngularApprovalLibService,
    private _translate: TranslateService,
    private _sharedDataService: ApprovalSharedDataService) { }

  ngOnInit(): void {
    this.getCurrentApprovalDetails();
    this.getApprovalHistoryDetails();
    this.getMemberRolesDetails();
    this.getSecurityPoliciesDisplay();
  }

  private getCurrentApprovalDetails() {
    this.subscriptionList.push(this._approvalService.getSingleApproval(this.id).subscribe((res: Approval) => {
      this.currentApproval = res;
      this.showErrorFlag = false;
      this.errorMsg = null;
    }, error => {
      this.showErrorFlag = true;
      this.errorMsg = this._sharedDataService.displayErrorNotification(error);
    }));
  }

  private getApprovalHistoryDetails() {
    this.subscriptionList.push(this._approvalService.getResponseHistory(this.id).subscribe((response: any[]) => {
      this._sharedDataService.setApprovalResponseHistory(response);
    }, error => {
      this._sharedDataService.displayErrorNotification(error);
    }));
  }

  private getSecurityPoliciesDisplay() {
    this.subscriptionList.push(this._approvalService.getSecurityPolicies().subscribe((response: any[]) => {
      this._sharedDataService.setSecurityPolicyOption(response);
    }, error => {
      this._sharedDataService.displayErrorNotification(error);
    }));
  }

  private getMemberRolesDetails() {
    this.subscriptionList.push(this._approvalService.getAvailableRoles().subscribe((res: Role[]) => {
      const roles: Role[] = [...res, {
        id: -1,
        name: this._translate.instant('APPROVALPICKER.TITLES.UNASSIGNED'),
        desc: this._translate.instant('APPROVALPICKER.TITLES.UNASSIGNED')
      }];
      this._sharedDataService.setMemberRoles(roles);
    }, error => {
      this._sharedDataService.displayErrorNotification(error);
    }));
  }

  tabChanged(event) {
  }

  closeInnerPanel() {
    this.closeApprovalDetailsPanel.emit();
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => {
      sub.unsubscribe();
    });
  }

}
