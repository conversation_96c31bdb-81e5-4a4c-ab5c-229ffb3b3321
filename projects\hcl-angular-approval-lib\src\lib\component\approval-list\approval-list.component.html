<div class="w-100 h-100 position-relative approval-list-container" *ngIf="config.approvalIdList && config.approvalIdList.length > 1 else detailsPanel">
    <div class="approval-list-header">{{ config.pageHeader }}</div>

    <div class="approval-grid-container w-100">
        <hcl-data-grid-v2 #approvalListGrid [config]="config.approvalListGridConf"
            *ngIf="config.approvalListGridConf && config.approvalListGridConf.data" (gridReady)="onGridReady($event)">
            <ng-template hclTemplate hclTemplateName="nameCell" type="cell-renderer" let-cell>
                <div *ngIf="cell && cell.row" class="ellipsis" 
                    (click)="onCellClicked(cell.row)">
                    <span class="link" [hclTooltip]="cell.row[cell.col.field]" >{{cell.row && cell.row[cell.col.field]}}</span>
                </div>
            </ng-template>
        </hcl-data-grid-v2>
    </div>

    <div class="close-btn-container position-absolute">
        <hcl-button [config]="closeButtonConfig" (onclick)="closePanel($event)"></hcl-button>
    </div>
</div>

<hcl-side-bar [disableClose]="true" [hidden]="!showChildSideBar">
    <div class="d-flex h-100 flex-row" *ngIf="showChildSideBar">
        <div class="slider-part" (click)="closeApprovalDetails()">
            <h3>{{ config.pageHeader }}</h3>
        </div>
        <aside class="h-100 approval-details-component overflow-hidden">
          <hcl-approval-details [id]="approvalId" (closeApprovalDetailsPanel)="closeApprovalDetails()">
          </hcl-approval-details>
        </aside>
    </div>
</hcl-side-bar>

<ng-template #detailsPanel>
  <hcl-approval-details [id]="approvalId" (closeApprovalDetailsPanel)="closeApprovalDetails()">
  </hcl-approval-details>
</ng-template>
