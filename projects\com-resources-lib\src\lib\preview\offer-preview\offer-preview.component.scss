.default-values {
  min-height: 700px;

  @mixin two-column-layout {
    display: flex;
    flex-wrap: wrap;
    align-items: end;
    & > *:not(.hcl-full-width) {
      flex: 0 1 48%;
      margin-bottom: 10px;
      overflow: hidden;

      &:nth-child(even) {
        margin-left: 4%;
      }
    }
  }

  .title {
    color: #6d7692;
    font-family: "Montserrat-regular" !important;
    font-size: 14px;
  }

  .metadata {
    margin: 10px 0 10px;
    padding: 20px;
    background-color: #f5f5f5;
    box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%), 0px 2px 2px 0px rgb(0 0 0 / 14%), 0px 1px 5px 0px rgb(0 0 0 / 12%);
    position: relative;

    & > div {
      margin-bottom: 0px !important;
      align-items: center;
    }

    .form-field {
      width: 49%;
      min-height: 30px;
      .ro-label {
        flex: none;
        padding: 0;
        width: 25%;
      }
      .ro-value {
        padding: 0;
        width: 75%;
        padding-right: 10px;
      }
    }
  }
  .default-attributes {
    padding: 20px 0;
    height: auto;
    flex-wrap: wrap;
    align-content: baseline;
    @include two-column-layout;

    .attribute {
      // width: 30%;
      // margin-right: 1.5%;
      display: inline-block;
    }
  }
  .dependent-attribute-container {
    display: flex;
    padding: 15px 0;
    flex-wrap: wrap;
    @include two-column-layout;

    .content-item {
      display: flex;
      align-items: center;
      padding: 5px 0;
      span {
        width: 40%;
        color: #444444;
        font-family: "Roboto-regular" !important;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.depends-on {
          color: #959595;
          width: 20%;
        }
      }

      .hcl-icon-link {
        font-size: 20px;
        color: #959595;
        padding: 0 10px;
        cursor: auto;
        width: 20%;
        text-align: left;
      }
    }
  }

  .relevant-products-list {
    padding: 15px 0px;
    display: flex;
    flex-wrap: wrap;

    .checkbox-label {
      border-bottom: 1px solid #e0e0e0;
      color: #444444;
      font-family: "Roboto-regular" !important;
      font-size: 14px;
      margin: 10px;
      width: 48%;
    }
  }

  .mat-accordion {
    .mat-expansion-panel {
      .mat-expansion-panel-header {
        padding: 0 20px;
      }
      .mat-expansion-panel-body {
        padding: 0 20px 16px;
      }
      overflow: visible;
      .ui-calendar {
        .ui-datepicker {
          min-width: 320px;
          max-width: 320px;
        }
      }
    }
  }

  .form-field {
    font-family: "Roboto-regular" !important;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 16px;
    display: flex;

    .ro-label {
      color: #b8b7b7;
      // margin-right: 10px;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      // flex: 1;
      padding: 8px 10px 8px 0;
      width: 25%;
    }

    .ro-value {
      color: #15161c;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      // flex: 1;
      padding: 8px 18px 8px 0;
      width: 75%;

      .hcl-icon-view {
        cursor: pointer;
        color: #959595;
      }
    }
  }

  .custom-action-metadata {
    display: flex;
    padding: 15px 0;
    flex-wrap: wrap;
    @include two-column-layout;

    .ca-metadata-item {
      display: flex;
      padding: 5px 0;
      .field-label {
        color: #b8b7b7;
        font-family: "Roboto-regular" !important;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 16px;
        flex: 1;
      }

      .field-value {
        color: #444444;
        font-family: "Roboto-regular" !important;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 16px;
        flex: 1;

        .highlited {
          color: #0078d8;
          cursor: pointer;
        }
      }
    }
  }

  .dependency-content-association-list,
  .attr-header {
    display: flex;
    padding: 0;
    flex-wrap: wrap;
    @include two-column-layout;
    margin-bottom: 10px;

    .content-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      span {
        width: 40%;
        color: #444444;
        font-family: "Roboto-regular" !important;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.depends-on {
          color: #959595;
          width: 20%;
        }
      }

      .hcl-icon-link {
        font-size: 20px;
        color: #959595;
        padding: 0 10px;
        cursor: auto;
        width: 20%;
        text-align: left;
      }
    }

    .resp-mapping-msg {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 2;
      color: #b8b7b7;
      font-family: "Roboto-regular" !important;
      font-size: 13px;
      margin-bottom: 10px;
    }

    + hr {
      margin: 0;
    }
  }

  .mapping-sec-title {
    color: #15161c;
    font-family: "Roboto-regular" !important;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 16px;
    margin: 20px 0;
  }

  .attr-header {
    padding: 0;
    margin-bottom: 0px;
    .content-item {
      margin: 0;
      padding: 5px 0;

      span {
        color: #b8b7b7;

        &.hcl-icon-link {
          visibility: hidden;
        }
      }
    }
  }

  .ci-sub-heading {
    padding: 5px 10px;
    border-radius: 19px;
    background-color: #e0e0e0;
    margin: 0 5px 0 10px;
    color: #6d7692;
    font-family: "Roboto-regular" !important;
    font-size: 12px;
    letter-spacing: 0.4px;
    line-height: 14px;
  }

  .associated-content-details {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    align-items: center;

    [class^="hcl-icon-"] {
      color: #959595;
    }
  }

  .auto-sync {
    background-color: transparent;

    .sync-state {
      padding: 3px 10px;
      margin-left: 10px;
      border: 2px solid #d2d6e1;
      border-radius: 14px;
      background-color: #f5f5f5;
    }
  }
}

.relevant-products-btns {
  display: flex;
  flex-direction: row-reverse;
  width: 100%;
}

.mat-content {
  align-items: center;
}

.hcl-sidebar {
  .mat-drawer-transition .mat-drawer-backdrop {
    position: fixed;
  }
}

.day .mat-horizontal-stepper-content {
  overflow-y: auto;
  overflow-x: hidden;
}

.relevant-products-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 75px;
  color: #b8b7b7;
  font-family: "Roboto-regular" !important;
  font-size: 13px;
}

.rt-preview-container {
  width: 600px;
  position: relative;
  background: rgba(0, 0, 0, 0.8);
  overflow: hidden;
  margin: auto;
  padding: 15px 12px 0;
  cursor: pointer;
  caret-color: transparent;

  .view {
    position: absolute;
    right: 15px;
    top: 6px;
    color: #f5f5f5;
    font-family: "Roboto-regular" !important;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 16px;
  }

  .disabled-editor {
    margin-top: 15px;
    width: 1150px;
    transform: scale(0.5, 0.8) translate(-50%, -7%);

    .fr-wrapper {
      height: 125px;
      max-height: 125px;
      overflow: hidden !important;
    }
  }
}

#offer-info-sidebar {
  .hcl-sidebar {
    .mat-drawer-transition .mat-drawer-backdrop {
      position: fixed;
    }
  }
}

.all-folders-container {
  .folder-bottom-actions {
    button {
      height: 30px;
    }
  }
}
