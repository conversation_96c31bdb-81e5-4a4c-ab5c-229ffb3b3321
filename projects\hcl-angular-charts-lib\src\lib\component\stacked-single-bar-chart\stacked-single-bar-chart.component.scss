.stacked-single-bar-chart {
  svg {
    .xAxis, .yAxis {
      path {
        stroke: #6D7692;
      }
      .tick {
        text {
          fill: #6D7692;
          font-family: Montserrat;
          font-size: 10px;
          font-weight: 500;
        }
        
        line {
          stroke: #6D7692;
        }
      }
    }
    .xAxisLabel, .yAxisLabel {
      fill: #6D7692;
      font-family: Montserrat;
      font-size: 11px;
      font-weight: 600;
    }
  }
}
.stacked-single-bar-chart-tooltip {
  opacity: 0.9;
  position: absolute;
  overflow: auto;
  padding-left: 10px;
  z-index: 2;
  .stacked-single-bar-chart-tooltip-arrow {
    border-width: 10px;
    border-color: rgba(0, 0, 0, 0.9);
    border-style: solid;
    border-top-color: transparent;
    border-right-color: transparent;
    border-left-color: transparent;
    width: 0px;
    height: 0px;
    position: relative;
    display: block;
    left: 48%;
    top: -10px
  }
  .stacked-single-bar-chart-tooltip-contents {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 3px;
    width: 260px;
    top: -10px;
    position: relative;
    .stacked-single-bar-chart-tooltip-container {
      padding: 5px 10px 6px 10px;
      text-align: center;
      .stacked-single-bar-chart-tooltip-row {
        &.heading {
          padding-top: 10px;
          font-size: 16px;
        }
        &.value {
          padding: 10px 0px 5px 0px;
          border-bottom: 1px solid white;
          font-size: 16px;
        }
        &.description {
          padding: 10px 0px;
          font-size: 12px;
        }
        color: white;
        font-family: 'Montserrat';
      }
    }
  }
}