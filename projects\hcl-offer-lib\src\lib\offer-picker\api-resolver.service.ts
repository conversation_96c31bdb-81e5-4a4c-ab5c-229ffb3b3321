import { Injectable } from '@angular/core';
import { forkJoin } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { OfferDataService } from './offer-data.service';
import { OfferApplicationConfig, PlatformConfig } from './select-offers/select-offers.model';

@Injectable({
  providedIn: 'root',
})
export class ApiResolverService {
  constructor(
    private translate: TranslateService,
    private offerDataService: OfferDataService) { }

  loadApis(): Promise<boolean> {
    forkJoin([this.offerDataService.getPlatformConfig(),
    this.offerDataService.getOfferApplicationConfig()]).subscribe(configList => {
      this.offerDataService.platformConfig = configList[0] as PlatformConfig;
      this.offerDataService.offerApplicationConfig = configList[1] as OfferApplicationConfig;
      if ((configList[0] as PlatformConfig).isAssetPickerAppInstalled) {
        this.offerDataService.setAssetPickerRepoes();
      }
    });

    return new Promise((resolve) => {
      this.offerDataService.getUserConfig().subscribe(userConf => {
        this.offerDataService.userConfig = userConf;
        const langToSet = userConf.locale || 'en_US';
        this.translate.use(langToSet).subscribe(() => {
          console.log(`Successfully initialized '${langToSet}' language.'`);
          resolve(true);
        }, err => {
          console.log(`Problem with '${langToSet}' language initialization.'`);
        });
      });
    });
  }
}
