import {<PERSON><PERSON>hartRenderer} from '../BaseChartRenderer';
import {ElementRef} from '@angular/core';
import * as d3 from 'd3';
import {D3CategoryAxisRenderer} from './D3CategoryAxisRenderer';
import {D3LinearAxisRenderer} from './D3LinearAxisRenderer';
import {ChartSeries} from '../../config/chart-config';
import {D3ReferenceLineRenderer} from './D3ReferenceLineRenderer';

/**
 * The abstract class for the Bar chart
 */
export abstract class D3ChartRenderer extends BaseChartRenderer {
  /**
   * The instance of the SVG tag
   */
  protected svg: any;
  /**
   * The chart group
   */
  protected chart: any;
  /**
   * The category Axis renderer
   */
  protected categoryAxisRenderer: D3CategoryAxisRenderer;
  /**
   * the linear Axis renderer
   */
  protected linearAxisRenderer: D3LinearAxisRenderer;
  /**
   * this function will actually render the chart
   * param {element} the element inside which the chart needs to be rendered
   */
  public render(element: ElementRef): void {
    // lets make the linear Axis as floating
    // lets validate the configuration first
    this.validateAndUpdateConfig(element.nativeElement);
    // lets create the canvas
    this.prepareCanvas(element);
    // add the group inside which we will add the chart
    this.addChartGroup(element);
    // lets create the Category Axis
    this.categoryAxisRenderer = new D3CategoryAxisRenderer(this.chartConfig, this);
    // lets create the Linear/series Axis
    this.linearAxisRenderer = new D3LinearAxisRenderer(this.chartConfig, this.chartConfig.series[0], this);
    // if we want the Axis only then we will render it
    if (!this.chartConfig.categoryAxis.hidden) {
      this.categoryAxisRenderer.renderAxis(this.chart);
    }
    if (!this.chartConfig.series[0].hidden) {
      this.linearAxisRenderer.renderAxis(this.chart);
    }
    // lets plot the chart
    this.plotChart();
  }

  /**
   * This function will re-render the chart
   */
  public reRender(): void {
  }

  /**
   * This will get the min & max range for Category axis
   * returns {{min: number; max: number}}
   */
  public getCategoryAxisMinAndMaxRange(): {min: number, max: number} {
    return this.categoryAxisRenderer.getMinAndMaxRange();
  }
  /**
   * This will get the min & max range for Linear axis
   * returns {{min: number; max: number}}
   */
  public getLinearAxisMinAndMaxRange(): {min: number, max: number} {
    return this.linearAxisRenderer.getMinAndMaxRange();
  }
  /**
   * Do some validation that is specific to bar-chart
   */
  protected validateAndUpdateConfig(element: any): void {
    super.validateAndUpdateConfig(element);
    if (this.chartConfig) {
      // in case no margin is there we will set default
      if (!this.chartConfig.margin) {
        this.chartConfig.margin = {
          left : 50,
          right : 50,
          top : 50,
          bottom : 50
        };
      }
    }
  }
  /**
   * this function will add a chart group to the svg tag that has been created
   */
  protected addChartGroup(element: ElementRef): void {
    // but we will add a "Group" inside which the chart will be rendered
    this.chart = this.svg
      .append('g')
      .attr('class', 'chart-container')
      //  make sure we have a margin
      .attr('transform', `translate(0,0)`);
  }

  /**
   * this function will create the canvas for the charting
   * param {ElementRef} element
   */
  protected prepareCanvas(element: ElementRef): void {
    let className: string = 'hcl-chart-container';
    let backgroundColor: string = null;
    // if we have a class set
    if (this.chartConfig.background) {
      if (this.chartConfig.background.class) {
        className += ' ' + this.chartConfig.background.class;
      }
      if (this.chartConfig.background.bgColor) {
        backgroundColor =  this.chartConfig.background.bgColor;
      }
    }
    // use d3 to create the canvas
    this.svg = d3.select(element.nativeElement)
      .append('svg')
      // add chart css
      .attr('class',  className)
      // set the dimensions of the SVG
      .attr('width', this.chartConfig.dimension.width)
      .attr('height', this.chartConfig.dimension.height)
      .attr('transform', 'translate(0,0)');
    // if we have a bgColor use it
    if (backgroundColor != null) {
      this.svg.style('background-color' , backgroundColor);
    }
  }

  /**
   * This function will create a reference line render
   * returns {D3ReferenceLineRenderer}
   */
  public createReferenceLineRenderer(displayLinearReference: boolean,
                                     displayCatReference: boolean,
                                     currentPosition: {x: number, y: number}): D3ReferenceLineRenderer {
    // create a new Reference line
    return new D3ReferenceLineRenderer(this.chart,
                        displayLinearReference ? this.linearAxisRenderer : null,
                      displayCatReference ? this.categoryAxisRenderer : null,
                                      currentPosition);
  }
  /**
   * This function will change the scale
   * param scale
   */
  public changeAxisScale(series: ChartSeries, displayIn: any): void {
    // tell the linear Axis to change the scale
    // we have to recalcualte the chart dimensions

    // this.linearAxisRenderer.
  }
  /**
   * this function will plot the data in the chart
   */
  protected abstract plotChart(): void;

  /**
   * Refresh the data at index
   * param {number} index
   */
  public refreshDataForIndex(index: number): void {

  }

  public hideDataAtIndex(index: number[]): void {

  }
}
