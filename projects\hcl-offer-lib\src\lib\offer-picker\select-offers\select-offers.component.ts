import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SubscriptionLike } from 'rxjs';
import { MessageService, BreadcrumbComponent, ButtonConf, InputConfig, SideBarComponent, TabsConfig, DropDownConfig, MenuConfig, RadioConfig } from 'hcl-angular-widgets-lib';
import { SelectOffersService } from '../select-offers.service';
import { SelectOffersConfig } from './select-offers.config';
import { SelectOffersConstant } from './select-offers.constant';
import { OfferDataService } from '../offer-data.service';
import { OfferListingComponent } from './offer-listing/offer-listing.component';
import { OfferListListingComponent } from './offer-list-listing/offer-list-listing.component';


@Component({
  selector: 'hcl-select-offers',
  templateUrl: './select-offers.component.html',
  styleUrls: ['./select-offers.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SelectOffersComponent implements OnInit, OnDestroy, AfterViewInit {

  @ViewChild(SideBarComponent) sideBarComponentRef: SideBarComponent;
  @ViewChild('foldersPane') foldersPane: ElementRef;
  @ViewChild('listingContainer') listingContainer: ElementRef;
  @ViewChild('breadCrumb') breadCrumb: BreadcrumbComponent;

  @ViewChild('offergrid') offergridContainer: OfferListingComponent;
  @ViewChild('offerlistgrid') offerlistgridContainer: OfferListListingComponent;

  @Input() config: SelectOffersConfig;
  @Output() closeOffers = new EventEmitter();
  @Output() selectedItems = new EventEmitter();


  // A flag that tells the state of the Folder panel 1=> minimized 2=> norma, 3 => maximized
  folderPanelState: number = 2;
  minimizeFolderIconButton: ButtonConf = {
    color: 'accent',
    buttonType: 'mat',
    value: '',
    borderRadius: 5,
    name: 'iconTextButton',
    styleClass: 'navBtnCls hcl-md-button',
    type: 'reset',
    isIconButton: true,
    icon: 'hcl-icon-left-open'
  };
  maxFolderIconButton: ButtonConf = {
    color: 'accent',
    buttonType: 'mat',
    value: '',
    borderRadius: 5,
    name: 'iconTextButton',
    styleClass: 'navBtnCls hcl-md-button',
    type: 'reset',
    isIconButton: true,
    icon: 'hcl-icon-right-open'
  };
  tabHorizontalConfig: TabsConfig;
  refreshOffersButtonConf: ButtonConf;
  offerActionsConf: ButtonConf;
  cancelActionsConf: ButtonConf;
  selectOffersActionsConf: ButtonConf;
  filterOffersButtonConf: ButtonConf;
  searchOffersConfig: InputConfig;
  searchOfferListConfig: InputConfig;
  olTypeConfig: DropDownConfig;
  disabled: boolean;

  foldersPaneTitle: string;
  allOffersPaneTitle: string;
  selectedFolderLabel: string;
  globalSearchResultsLabel: string;

  effectiveDate: string;
  expirationDate: string;
  channel: string;
  element: any = [];
  selectedOffers = [];
  listingBreadCrumbWidth: number;
  folderPaneWidth: number;

  showGlobalResults = false;

  breadCrumbData = [];

  subscriptionList: SubscriptionLike[] = [];
  olType: string = SelectOffersConstant.all;
  offerGridViewActive = true;
  offerlistGridViewActive = true;
  cvOffersSortMenuConf: MenuConfig;
  cvOfferlistSortMenuConf: MenuConfig;
  cvOffersSortBtnConf: ButtonConf;
  cvSortOptionRadioConf: RadioConfig;
  offerListFilterMenuConf: MenuConfig;
  OLFilterOptionRadioConf: RadioConfig;
  olCvSortOptionRadioConf: RadioConfig;
  cvOfferSortState: string;
  // olTypeFilterState = '';
  offerlistSortState = 'displayName,ASC';
  loadCardView = false;


  constructor(
    private translate: TranslateService,
    private messageService: MessageService,
    private selectOffersService: SelectOffersService,
    private offerDataService: OfferDataService
  ) {
  }
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.loadCardView = true;
    }, 500);
  }

  /**
   * this function will minimze the folder pane
   * param event
   */
  minimizeFolderList(event): void {
    if (this.folderPanelState == 2) {
      // we have to dock the folder pane
      this.folderPanelState = 1;
    } else if (this.folderPanelState == 3) {
      // we have to reset the folder to the default state
      this.folderPanelState = 2;
    } else {
      // we cannot minimize more so we do nothing
    }
    setTimeout(() => {
      this.selectOffersService.fireOfferGridResizeEvent();
      const splitContainer: any = document.getElementsByClassName('split-container-horizontal')[0];
      splitContainer.scrollLeft = splitContainer.scrollWidth - 100;
      this.setBreadCrumbWidth();
    }, 800);
  }

  maximizeFolderList(event): void {
    // this.folderPanelState = 2;
    if (this.folderPanelState === 2) {
      // we have to dock the girid pane
      this.folderPanelState = 3;
    } else if (this.folderPanelState === 3) {
      // we have to reset the folder to the default state
    } else {
      // we cannot minimize more so we do nothing
      this.folderPanelState = 2;
    }
    setTimeout(() => {
      this.selectOffersService.fireOfferGridResizeEvent();
      this.setBreadCrumbWidth();
    }, 800);
  }

  setBreadCrumbWidth() {
    this.folderPaneWidth = this.foldersPane.nativeElement.offsetWidth;
    if (400 < this.folderPaneWidth) {
      this.messageService.sendFoldersBreadCrumbWidth(this.foldersPane.nativeElement.offsetWidth);
    }
    this.listingBreadCrumbWidth = this.listingContainer.nativeElement.offsetWidth;
    if (this.breadCrumbData.length && 900 < this.listingBreadCrumbWidth) {
      this.breadCrumb.resize(this.listingContainer.nativeElement.offsetWidth);
    }
  }

  ngOnInit() {
    if (!this.selectOffersService.offerViewMode) {
      if (this.offerDataService.userConfig) {
        this.selectOffersService.offerViewMode = this.offerDataService.userConfig.defaultOfferListingView;
      }
    }

    if (this.selectOffersService.offerViewMode !== SelectOffersConstant.gridView) {
      this.offerGridViewActive = false;
    }

    if (!this.selectOffersService.offerlistViewMode) {
      if (this.offerDataService.userConfig) {
        this.selectOffersService.offerlistViewMode = this.offerDataService.userConfig.defaultOfferListingView;
      }
    }

    if (this.selectOffersService.offerlistViewMode !== SelectOffersConstant.gridView) {
      this.offerlistGridViewActive = false;
    }

    if (this.config) {
      this.selectOffersService.pickerConfig = this.config;
      this.offerDataService.tokenId = this.config.tokenId;
      this.offerDataService.userName = this.config.userName;

      if (this.config.foldersConfig) {
        this.selectOffersService.offersFolder = this.config.foldersConfig.rootFolderId ? this.config.foldersConfig.rootFolderId : 4;
        this.foldersPaneTitle = this.config.foldersConfig.foldersPaneTitle ? this.config.foldersConfig.foldersPaneTitle : 'Select offers';
        this.selectedFolderLabel = this.config.foldersConfig.selectedFolderLabel ? this.config.foldersConfig.selectedFolderLabel : 'Selected folder:';
        this.globalSearchResultsLabel = this.config.foldersConfig.globalSearchLabel ? this.config.foldersConfig.globalSearchLabel : 'Search results';
      }
      if (this.config.offersGridConfig) {
        this.allOffersPaneTitle = this.config.offersGridConfig.offersPaneTitle ? this.config.offersGridConfig.offersPaneTitle : 'Offers';
      }
    }
    this.selectOffersService.offerListGlobalSearchData = '';
    if (this.breadCrumbData.length === 0) {
      this.breadCrumbData = [{
        folder: {
          displayName: this.config && this.config.foldersConfig
            && this.config.foldersConfig.rootFolderName ? this.config.foldersConfig.rootFolderName : 'All Offers'
        }
      }];
    }
    this.subscriptionList.push(this.selectOffersService.getFoldersBreadcrumb().subscribe(data => {
      if (data[0]) {
        if (data[0].length > 0) {
          this.breadCrumbData = data[0];
        } else {
          this.breadCrumbData = [{
            folder: {
              displayName: this.config && this.config.foldersConfig
                && this.config.foldersConfig.rootFolderName ? this.config.foldersConfig.rootFolderName : 'All Offers'
            }
          }];
        }
        this.selectOffersService.clearFoldersBreadcrumb();
      }
    }));
    this.setConfiguration();

    if (this.selectOffersService.lastTabIndex === 1) {
      this.tabHorizontalConfig.selectedTab = 1;
    }
    //Select should be enable to communicate Removing all selection to caller of widget
    if (this.selectOffersService.selectedOffersData.length || this.offerDataService.isOfferWidget) {
      this.selectOffersActionsConf.disabled = false;
    } else {
      this.selectOffersActionsConf.disabled = true;
    }
  }

  changeOfferListingView(view: 'Card view' | 'Grid view') {
    if (view === SelectOffersConstant.gridView && !this.offerGridViewActive) {
      this.offerGridViewActive = true;
    }
    if (view === SelectOffersConstant.cardView && this.offerGridViewActive) {
      this.offerGridViewActive = false;
    }

    this.selectOffersService.offersSortAndColumn = 'displayName,ASC';
    this.selectOffersService.offerViewMode = view;
    this.removeGlobalSearch();
  }


  changeOfferlistListingView(view: 'Card view' | 'Grid view') {
    if (view === SelectOffersConstant.gridView && !this.offerlistGridViewActive) {
      this.offerlistGridViewActive = true;
      // this.checkOfferlistStateAndReloadGrid();
    }
    if (view === SelectOffersConstant.cardView && this.offerlistGridViewActive) {
      this.offerlistGridViewActive = false;
    }

    this.offerlistSortState = this.selectOffersService.offerListsSortAndColumn;
    this.selectOffersService.offerlistViewMode = view;
    // this.actionOfferLists = [];
    this.removeOfferListGlobalSearch();
  }

  verticalLocale() {
    const userLanguage = this.config.locale ? this.config.locale : 'en_US';
    return userLanguage === 'zh_TW' || userLanguage === 'zh_CN' || userLanguage === 'ja_JP' || userLanguage === 'ko_KR';
  }

  setConfiguration() {
    this.tabHorizontalConfig = {
      selectedTab: 0,
      elements: []
    };

    if (this.selectOffersService.loadOfferOrOfferLists === 'both') {
      this.tabHorizontalConfig.elements.push({
        header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS'),
        templateName: 'offers'
      });
      this.tabHorizontalConfig.elements.push({
        header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS'),
        templateName: 'offerLists'
      });
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offerLists') {
      this.tabHorizontalConfig.elements.push({
        header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS'),
        templateName: 'offerLists'
      });
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offers') {
      this.tabHorizontalConfig.elements.push({
        header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS'),
        templateName: 'offers'
      });
    }

    this.disabled = true;

    this.refreshOffersButtonConf = {
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      icon: 'hcl-icon-refresh',
      value: '',
      styleClass: 'custom-icon',
      name: 'refresh',
      borderRadius: 4
    };

    this.cancelActionsConf = {
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      value: this.translate.instant('BUTTONS.CANCEL'),
      styleClass: 'medium-btn',
      name: 'cancel',
      borderRadius: 4
    };
    this.selectOffersActionsConf = {
      color: 'accent',
      buttonType: 'raised',
      type: 'button',
      value: this.translate.instant('OFFER_PICKER.TITLES.SELECT_OFFERS'),
      styleClass: 'medium-btn',
      name: 'selectOffers',
      borderRadius: 4
    };
    this.searchOffersConfig = {
      formControlName: new UntypedFormControl(''),
      placeholder: this.translate.instant('BUTTONS.OFFER_GLOBAL_SEARCH'),
      icon: 'hcl-icon-search',
      type: 'text',
      name: 'searchOffer'
    };
    this.searchOfferListConfig = {
      formControlName: new UntypedFormControl(''),
      placeholder: this.translate.instant('BUTTONS.OFFER_LISTS_GLOBAL_SEARCH'),
      icon: 'hcl-icon-search',
      type: 'text',
      name: 'searchOfferList'
    };
    this.olTypeConfig = {
      options: [
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.ALL'),
          value: SelectOffersConstant.all,
          templateName: 'olTypeTeamplate'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.SMART'),
          value: SelectOffersConstant.smartOfferListSmallCase,
          templateName: 'olTypeTeamplate'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.STATIC'),
          value: SelectOffersConstant.staticOfferListSmallCase,
          templateName: 'olTypeTeamplate'
        }
      ],
      placeholder: '',
      selectedOption: SelectOffersConstant.all,
      name: 'olType',
      isToolTip: true
    };

    this.filterOffersButtonConf = {
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      icon: 'hcl-icon-filter',
      value: this.translate.instant('BUTTONS.FILTER'),
      styleClass: 'custom-icon',
      name: 'filter',
      borderRadius: 4
    };

    this.cvOffersSortMenuConf = {
      triggerElementTemplateName: 'offerFilterMenutrigger',
      menuItemClass: 'offer-menu-row',
      menuListClass: 'offer-menu-list',
      items: [
        {
          label: this.translate.instant('FOLDERS.TITLES.SORT_BY'),
          title: SelectOffersConstant.sortBy,
          itemTemplateName: 'menuHeader'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.NO_SORT'),
          title: '',
          itemTemplateName: 'menuItem',
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.OFFER_NAME_SORT_ASC'),
          title: SelectOffersConstant.sortByDisplayNameAsc,
          itemTemplateName: 'menuItem'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.OFFER_NAME_SORT_DESC'),
          title: SelectOffersConstant.sortByDisplayNameDesc,
          itemTemplateName: 'menuItem'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.OFFER_CODE_SORT_ASC'),
          title: SelectOffersConstant.sortByOfferCodeAsc,
          itemTemplateName: 'menuItem'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.OFFER_CODE_SORT_DESC'),
          title: SelectOffersConstant.sortByOfferCodeDesc,
          itemTemplateName: 'menuItem'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.DESCRIPTION_SORT_ASC'),
          title: SelectOffersConstant.sortByDescriptionAsc,
          itemTemplateName: 'menuItem'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.DESCRIPTION_SORT_DESC'),
          title: SelectOffersConstant.sortByDescriptionDesc,
          itemTemplateName: 'menuItem'
        }
      ]
    };

    this.cvOfferlistSortMenuConf = {
      triggerElementTemplateName: 'offerlistSortMenutrigger',
      menuItemClass: 'offer-menu-row',
      menuListClass: 'offer-menu-list',
      items: [
        {
          label: this.translate.instant('FOLDERS.TITLES.SORT_BY'),
          title: SelectOffersConstant.sortBy,
          itemTemplateName: 'menuHeader'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.NO_SORT'),
          title: '',
          itemTemplateName: 'menuItem',
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.OFFER_LIST_NAME_SORT_ASC'),
          title: SelectOffersConstant.sortByDisplayNameAsc,
          itemTemplateName: 'menuItem'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.OFFER_LIST_NAME_SORT_DESC'),
          title: SelectOffersConstant.sortByDisplayNameDesc,
          itemTemplateName: 'menuItem'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.DESCRIPTION_SORT_ASC'),
          title: SelectOffersConstant.sortByDescriptionAsc,
          itemTemplateName: 'menuItem'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.DESCRIPTION_SORT_DESC'),
          title: SelectOffersConstant.sortByDescriptionDesc,
          itemTemplateName: 'menuItem'
        }
      ]
    };

    this.cvOffersSortBtnConf = {
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      icon: 'hcl-icon-sort',
      iconRight: 'hcl-icon-down-dir',
      value: this.translate.instant('FOLDERS.TITLES.SORT_BY'),
      styleClass: 'custom-icon',
      name: 'sort',
      borderRadius: 4
    };

    this.cvSortOptionRadioConf = {
      value: this.selectOffersService.offersSortAndColumn,
      name: 'sortOptionItem',
      elements: [
        {
          checked: false, value: this.selectOffersService.offersSortAndColumn,
          label: ''
        }
      ],
      horizontal: false,
      formControl: new UntypedFormControl(this.selectOffersService.offersSortAndColumn)
    };

    this.olCvSortOptionRadioConf = {
      value: this.selectOffersService.offerListsSortAndColumn,
      name: 'sortOptionItem',
      elements: [
        {
          checked: false, value: this.selectOffersService.offerListsSortAndColumn,
          label: ''
        }
      ],
      horizontal: false,
      formControl: new UntypedFormControl(this.selectOffersService.offerListsSortAndColumn)
    };

    this.offerListFilterMenuConf = {
      triggerElementTemplateName: 'offerFilterMenutrigger',
      menuItemClass: 'offer-menu-row',
      menuListClass: 'offer-menu-list',
      items: [
        {
          label: this.translate.instant('LIST_OFFER_TEMPLATES.HEADERS.TYPE'),
          title: SelectOffersConstant.type,
          itemTemplateName: 'menuHeader'
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.ALL'),
          title: '',
          itemTemplateName: 'menuItem',
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.SMART'),
          title: SelectOffersConstant.smartOfferListSmallCase,
          itemTemplateName: 'menuItem',
        },
        {
          label: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.STATIC'),
          title: SelectOffersConstant.staticOfferListSmallCase,
          itemTemplateName: 'menuItem',
        }
      ]
    };

    this.OLFilterOptionRadioConf = {
      value: '',
      name: 'filterOptionItem',
      elements: [
        {
          checked: false, value: '',
          label: ''
        }
      ],
      horizontal: false,
      formControl: new UntypedFormControl('')
    };

  }

  searchOffers() {
    if (this.searchOffersConfig.formControlName.value) {
      this.showGlobalResults = true;
      this.searchOffersConfig.icon = 'hcl-icon-close-x';
      this.selectOffersService.clearRefreshFoldersMesaage();
      this.selectOffersService.sendRefreshFoldersMesaage('refreshOnGlobalSearch');
      this.selectOffersService.sendOffersSearchData(this.searchOffersConfig.formControlName.value);
      this.selectOffersService.offersGlobalSearchData = this.searchOffersConfig.formControlName.value;
      this.refreshOffersButtonConf.disabled = true;
    }
  }

  enterSearchOffer(evt: KeyboardEvent) {
    //prevent Enter in search to be propagated to caller of widget. 
    if (evt && ((evt.code && evt.code === 'Enter') || (evt.keyCode && evt.keyCode === 13))) {
      evt.preventDefault();
      this.searchOffers();
    }
  }

  searchOffersIconClick() {
    if (this.showGlobalResults) {
      this.removeGlobalSearch();
      this.selectOffersService.sendFolderId('offers', this.selectOffersService.offersFolder);
    } else {
      this.searchOffers();
    }
  }

  removeGlobalSearch() {
    if (this.showGlobalResults) {
      this.showGlobalResults = false;
    }
    this.searchOffersConfig.icon = 'hcl-icon-search';
    this.searchOffersConfig.formControlName.reset();
    this.refreshOffersButtonConf.disabled = false;
  }

  returnSelectedOfferIds() {
    return this.selectedOffers.map(attribute => attribute.data.id);
  }

  folderSelected(folder) {
    this.removeGlobalSearch();
  }

  cvOffersSortMenuItemClicked(event) {
    this.cvOfferSortState = event.item.title;
    this.selectOffersService.offersSortAndColumn = event.item.title;
  }

  getSortOptionRadioConf(item) {
    const radioConf = { ...this.cvSortOptionRadioConf };
    radioConf.elements[0].value = item.title;

    if (item.title === this.selectOffersService.offersSortAndColumn) {
      radioConf.elements[0].checked = true;
      radioConf.formControl.setValue(item.title);
      radioConf.value = item.title;
    }
    return radioConf;
  }

  offerListFilterMenuItemClicked(event) {
    this.selectOffersService.offerListType = event.item.title;
    this.olTypeSelection(event.item.title);

  }

  getOLFilterOptionRadioConf(item) {
    const radioConf = { ...this.OLFilterOptionRadioConf };
    radioConf.elements[0].value = item.title;

    if (item.title === this.selectOffersService.offerListType) {
      radioConf.elements[0].checked = true;
      radioConf.formControl.setValue(item.title);
      radioConf.value = item.title;
    }
    return radioConf;
  }

  getOlSortOptionRadioConf(item) {
    const radioConf = { ...this.olCvSortOptionRadioConf };
    radioConf.elements[0].value = item.title;

    if (item.title === this.selectOffersService.offerListsSortAndColumn) {
      radioConf.elements[0].checked = true;
      radioConf.formControl.setValue(item.title);
      radioConf.value = item.title;
    }
    return radioConf;
  }

  cvOfferlistSortMenuItemClicked(event) {
    this.offerlistSortState = event.item.title;
    this.selectOffersService.offerListsSortAndColumn = event.item.title;
  }

  offerSelectionUpdate(event) {
    if (this.config && this.config.rowSelectMode === 'single') {
      if (this.selectOffersService.selectedOffersData.length > 0) {
        this.selectOffersService.selectedOlData = [];
        if (this.offerlistgridContainer && this.offerlistgridContainer.offerListsGridApi) {
          this.offerlistgridContainer.offerListsGridApi.forEachNode((node) => {
            node.setSelected(false);
          });
        }
      }
    }
    this.updateSelectionButtonStatus();
  }

  /**
   * Based on the data available this function will update the select button status
   */
  updateSelectionButtonStatus(): void {
    if (!this.offerDataService.isOfferWidget) {
      // if the length of both are 0 it means we have to disable it
      this.selectOffersActionsConf.disabled =
        (this.selectOffersService.selectedOlData.length == 0 && this.selectOffersService.selectedOffersData.length == 0);
    } else {
      //need to be able to select empty arrays for the user to remove the offers already linked in caller (Plan TCS case)
      this.selectOffersActionsConf.disabled = false;
    }
  }

  offerListSelectionUpdate(event) {
    if (this.config && this.config.rowSelectMode === 'single') {
      if (this.selectOffersService.selectedOlData.length > 0) {
        this.selectOffersService.selectedOffersData = [];
        if (this.offergridContainer && this.offergridContainer.offersGridApi) {
          this.offergridContainer.offersGridApi.forEachNode((node) => {
            node.setSelected(false);
          });
        }
      }
    }
    this.updateSelectionButtonStatus();
  }

  onOffersRefreshClick(event) {
    this.selectOffersService.sendFolderId('offers', this.selectOffersService.offersFolder);
    this.removeGlobalSearch();
  }

  /**
   * Offer list refresh button click handler
   */
  onOfferListRefreshClick() {
    this.selectOffersService.sendFolderId(SelectOffersConstant.offerlists, this.selectOffersService.offersFolder);
    this.removeGlobalSearch();
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => sub.unsubscribe());
  }

  close(event) {
    this.closeOffers.emit();
  }

  getSelectedOffers(event) {
    let data = {
      offers: [],
      offerLists: []
    };
    if (this.selectOffersService.selectedOffersData.length) {
      data.offers = this.selectOffersService.selectedOffersData;
    }
    if (this.selectOffersService.selectedOlData.length) {
      data.offerLists = this.selectOffersService.selectedOlData;
    }
    this.selectedItems.emit(data);
  }

  /**
   * Search for offer list
   */
  searchOfferList() {
    if (this.searchOfferListConfig.formControlName.value) {
      this.showGlobalResults = true;
      this.searchOfferListConfig.icon = 'hcl-icon-close-x';
      this.selectOffersService.clearRefreshFoldersMesaage();
      this.selectOffersService.sendRefreshFoldersMesaage(SelectOffersConstant.refreshOnGlobalSearch);
      this.selectOffersService.sendOfferListsSearchData(this.searchOfferListConfig.formControlName.value);
      this.selectOffersService.offerListGlobalSearchData = this.searchOfferListConfig.formControlName.value;
      this.refreshOffersButtonConf.disabled = true;
    }
  }

  enterSearchOfferList(evt: KeyboardEvent) {
    //prevent Enter in search to be propagated to caller of widget. 
    if (evt && ((evt.code && evt.code === 'Enter') || (evt.keyCode && evt.keyCode === 13))) {
      evt.preventDefault();
      this.searchOfferList();
    }
  }

  /**
   * Search for offer list
   */
  searchOfferListIconClick() {
    if (this.showGlobalResults) {
      this.removeOfferListGlobalSearch();
      this.selectOffersService.sendFolderId(SelectOffersConstant.offerlists, this.selectOffersService.offersFolder);
    } else {
      this.searchOfferList();
    }
  }

  /**
   * Toggle search and clear icon and reset search input
   */
  removeOfferListGlobalSearch() {
    if (this.showGlobalResults) {
      this.showGlobalResults = false;
    }
    this.searchOfferListConfig.icon = 'hcl-icon-search';
    this.searchOfferListConfig.formControlName.reset();
    this.refreshOffersButtonConf.disabled = false;
    this.selectOffersService.offerListGlobalSearchData = '';
  }

  /**
  * Type filter selection callback method
  * @param event
  */
  olTypeSelection(event) {
    if (this.olType !== event) {
      this.olType = event;
    }
  }

  tabSelectChange(event) {
    this.selectOffersService.lastTabIndex = event.index;
  }
}
