import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { SelectOffersService } from '../../select-offers.service';
import { ButtonConf } from 'hcl-angular-widgets-lib';
import { OfferDataService } from '../../offer-data.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'hcl-offer-variation',
  templateUrl: './offer-variation.component.html',
  styleUrls: ['./offer-variation.component.scss']
})
export class OfferVariationComponent implements OnInit {

  minimizeVariationsIconButton: ButtonConf;
  maxVariationsIconButton: ButtonConf;
  cancelActionsConf: ButtonConf;
  selectVariantButtonConf: ButtonConf;

  @Output() loadOffers = new EventEmitter();
  @Output() closePane = new EventEmitter();
  /**
  * A flag that tells the state of the variations panel 1=> variations panel minimized, 
  * 2=> normal, 3 => maximized without variations
  */
  variationPanelState = 2;
  load: boolean = true;

  constructor(
    private translate: TranslateService,
    public selectOffersService: SelectOffersService,
    private offerDataService: OfferDataService
  ) { }

  ngOnInit() {
    this.setConfiguration();
  }

  setConfiguration() {
    this.minimizeVariationsIconButton = {
      color: 'accent',
      buttonType: 'mat',
      value: '',
      borderRadius: 5,
      name: 'iconTextButton',
      styleClass: 'navBtnCls hcl-md-button',
      type: 'reset',
      isIconButton: true,
      icon: 'hcl-icon-left-open'
    };
    this.maxVariationsIconButton = {
      color: 'accent',
      buttonType: 'mat',
      value: '',
      borderRadius: 5,
      name: 'iconTextButton',
      styleClass: 'navBtnCls hcl-md-button',
      type: 'reset',
      isIconButton: true,
      icon: 'hcl-icon-right-open'
    };

    this.cancelActionsConf = {
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      value: this.translate.instant('BUTTONS.CANCEL'),
      styleClass: 'medium-btn',
      name: 'cancel',
      borderRadius: 4
    };
    this.selectVariantButtonConf = {
      color: 'accent',
      buttonType: 'raised',
      type: 'button',
      value: this.translate.instant('BUTTONS.SELECT'),
      styleClass: 'medium-btn',
      name: 'selectVariant',
      borderRadius: 4
    };
  }



  /**
 * this function will minimze the variations pane
 * param event
 */
  minimizeVariationsList(event): void {
    if (this.variationPanelState == 2) {
      this.variationPanelState = 1;
    }
  }

  /**
* this function will normalize both offers and the variations pane
* param event
*/
  maximizeVariationsList(event): void {
    if (this.variationPanelState === 1) {
      this.variationPanelState = 2;
    }
  }

  disableSelect(event) {
    this.selectVariantButtonConf.disabled = event;
  }

  verticalLocale() {
    const userLanguage = this.offerDataService.userConfig.locale;
    return userLanguage === 'zh_TW' || userLanguage === 'zh_CN' || userLanguage === 'ja_JP' || userLanguage === 'ko_KR';
  }

  reload(event) {
    this.load = false;
    setTimeout(() => {
      this.load = true;
    }, 200);
  }

  loadOffersListing(event) {
    if (!this.selectOffersService.directPathAccess) {
      this.loadOffers.emit(this.selectOffersService.viewRoute);
    } else {
      this.closePane.emit();
    }
  }

  selectedVariantOrOffer(event) {
    let selectedVariantData = null;

    if (this.selectOffersService.selectedVariantId) {
      selectedVariantData = this.selectOffersService.getVariantDataForVariantSelection();
    }

    const offerData = this.selectOffersService.getOfferDataForOfferSelection();


    const data = {
      offerId: this.selectOffersService.offerId,
      offerDisplayName: offerData.displayName,
      variantId: selectedVariantData ? this.selectOffersService.selectedVariantId : null,
      variantDisplayName: selectedVariantData ? selectedVariantData.displayName : null,
      offerCode: offerData.offerCode,
      variantAttributes: selectedVariantData ? selectedVariantData.variantAttributes : null,
      offerAttributes: selectedVariantData ? null : offerData.offerAttributes,
      state: this.selectOffersService.offerState
    };

    if (this.selectOffersService.selectedOffersData.length) {
      let hasOffer = false;
      hasOffer = this.selectOffersService.selectedOffersData.some((selectedData, index) => {
        if (selectedData.offerId === this.selectOffersService.offerId) {
          this.selectOffersService.selectedOffersData[index] = data;
          return true;
        }
      });
      if (!hasOffer) {
        this.selectOffersService.selectedOffersData.push(data);
      }
    } else {
      this.selectOffersService.selectedOffersData.push(data);
    }

    this.loadOffers.emit(this.selectOffersService.viewRoute);
  }
}
