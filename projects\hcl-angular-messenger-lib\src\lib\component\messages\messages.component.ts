import {Component, ElementRef, Input, OnInit, Renderer2, ViewChild} from '@angular/core';
import {MessageConfig} from './message-config';

/**
 * This component a single message  inside the message-container
 */
@Component({
  selector: 'hcl-messages',
  templateUrl: './messages.component.html',
  styleUrls: ['./messages.component.scss']
})
export class MessagesComponent implements OnInit {
  /**
   * Based on this configuration the message will be rendered
   */
  @Input() config: MessageConfig;
  /**
   * The DOM instance of the toolbar that is displayed on the
   * message
   */
  @ViewChild('messageToolbar', {static: false})private messageToolbar: ElementRef;
  /**
   * The default constructor
   */
  constructor(private renderer: Renderer2) { }

  /**
   * If required we will do initalization here
   */
  ngOnInit(): void {
  }
  /**
   * This function will be called when the user
   * mouseovers on a message, this function will display the
   * action buttons that are associated to that message
   * param e
   */
  toggleActions(show: boolean): void {
    //this.renderer.setStyle(this.messageToolbar.nativeElement, 'display', show ? 'block' : 'none');
    if (show) {
      this.renderer.addClass(this.messageToolbar.nativeElement, 'show-toolbar');
    } else {
      this.renderer.removeClass(this.messageToolbar.nativeElement, 'show-toolbar');
    }
  }

  /**
   * This function will return the localized status of the message
   */
  getStatusOfMessage(): string {
    switch (this.config.status) {
      case 1:
        return 'Read';
      case 2:
        return 'Sent';
      case 3:
        return 'Sending';
      case 4:
        return 'Error';
    }
  }
}
