  import {ChartSeries} from '../../../config/chart-config';
import {D3GanttDataNodeRenderer} from './D3GanttDataNodeRenderer';
import {D3LinearAxisRenderer} from '../D3LinearAxisRenderer';
import {D3CategoryAxisRenderer} from '../D3CategoryAxisRenderer';
import * as d3 from 'd3';
import {D3ChartRenderer} from '../D3ChartRenderer';

export class D3GanttDependencyRenderer {
  /**
   * This is a map of ID and the renderer
   * type {Map}
   */
  private idDataNodeRendererMap: Map<string, D3GanttDataNodeRenderer>
                        = new Map<string, D3GanttDataNodeRenderer>();
  /**
   * the map of the Id & the "g" object inside which the path recided
   * type {Map}
   */
  private idDependencyMap: Map<string, any[]> = new Map<string, any[]>();
  /**
   * this is the array of dep nodes that have been selected by the user
   * type {any[]}
   */
  private selectedDependency: any[] = [];
  /**
   * the default constructor
   * param {ChartSeries} series
   * param {D3GanttDataNodeRenderer[]} sNodeRenderer
   */
  constructor(private series: ChartSeries,
              private linearAxisRenderer: D3LinearAxisRenderer,
              private categoryAxisRenderer: D3CategoryAxisRenderer,
              private sNodeRenderer: D3GanttDataNodeRenderer[],
              protected chartRenderer: D3ChartRenderer,
              private elementNode: any) {
    this.sNodeRenderer.forEach((nodeRenderer: D3GanttDataNodeRenderer) => {
      if (!nodeRenderer) {}
      this.idDataNodeRendererMap.set(nodeRenderer.getId(), nodeRenderer);
    });
  }
  /**
   * this function will render the dependencies
   */
  public renderDependency(): void {
    this.removeAllDependency();
    // lets iterate through the NodeRenderers
    this.sNodeRenderer.forEach((nodeRenderer: D3GanttDataNodeRenderer) => {
      // now for get te parent of this task
      const pDep: any[] = nodeRenderer.getParentDependency();
      // if we have the pDep array we need to plot them
      pDep.forEach((depId: string) => {
        this.drawDependency(depId, nodeRenderer.getId());
      });
      // if interactive make dep drawable
      if (this.series.interactive && this.series.interactive.drawDependency) {
        const depNode: any = nodeRenderer.getDependencyNode();
        if(depNode) {
          // we need to attach a mouse down listener
          depNode.on('mousedown', this.initTemporaryDependencyLine.bind(this));
        }
      }
    });
  }

  /**
   * draw line for creating dependency which is invoked on mousedown event of taskbars circle
   * param data data of task from which path is to be drawn
   * param index index of task from which path is to be drawn
   */
  private initTemporaryDependencyLine(data: any, index: number): void {
    const line: any = this.elementNode.append('line')
      .attr('class', 'line')
      .attr('stroke', 'blue')
      .attr('stroke-width', '2px')
      .attr('y1', this.categoryAxisRenderer.getPosition(this.series.getCategory(data, -1))
        + (this.categoryAxisRenderer.getBandWidth() / 2))
      .attr('x1', this.linearAxisRenderer.getPosition(this.series.getMaxValue(data, -1)))
      .attr('y2', this.categoryAxisRenderer.getPosition(this.series.getCategory(data, -1))
        + (this.categoryAxisRenderer.getBandWidth() / 2))
      .attr('x2', this.linearAxisRenderer.getPosition(this.series.getMaxValue(data, -1)));
    let svg: any = d3.select(this.elementNode.node().parentNode);
    // moves the line along the cursor
    svg.on('mousemove', () => {
      const coordinates: any = d3.mouse(d3.event.currentTarget);
      line.attr('x2', coordinates[0])
        .attr('y2', coordinates[1]);
    });
    // On mouse up find the element on which we did a mouse up so that we can add the dep
    svg.on('mouseup', () => {
      const coOrdinates: any = d3.mouse(d3.event.currentTarget);
      // check which node falls under the coOrdinates
      const newDependentNode: D3GanttDataNodeRenderer[] = this.sNodeRenderer.filter((nodeRenderer: D3GanttDataNodeRenderer) => {
        // get the element co-ordinates
        const cCoOridinates: {x: number, y: number} = nodeRenderer.getDataNodeCoOrdinates();
        if (!cCoOridinates) {
          return false;
        }
        const cDimensions:  {width: number, height: number}  = nodeRenderer.getDataNodeDimensions();
        // lets chk if the X cordinates atleast fits in this element
        if (coOrdinates[0] > cCoOridinates.x && coOrdinates[0] < (cCoOridinates.x + cDimensions.width)) {
          // now lets chk for Y-coordinate
          if (coOrdinates[1] > cCoOridinates.y && coOrdinates[1] < (cCoOridinates.y + cDimensions.height)) {
            return true;
          }
        }
      });
      // In case there is no new Dep just remove the line
      if (!newDependentNode || newDependentNode.length === 0) {
        line.remove();
        return;
      }
      // of if we have new dep we need to add it
      if (newDependentNode && newDependentNode.length > 0) {
        // make sure the parent & child are not the same
        if (newDependentNode[0].getId() === '' + data.id ) {
          line.remove();
          return;
        }
        // new dependency node, lets verify if this dependency can be added or not
        if (this.series.dependency.validateDependencyAdd) {
          if (!this.series.dependency.validateDependencyAdd(data, newDependentNode[0].getDataObject())) {
            return;
          }
        }
        // we can add the dependency, Emmit event to delete the dependency
        this.chartRenderer.generateAddDependencyEvent(this.series, data, newDependentNode[0].getDataObject());
      }
      // remove the line
      line.remove();
      this.renderDependency();
      // clear all the listeners
      svg.on('mousemove', null);
      svg.on('mouseup', null);
      svg = null;
    });
  }

  /**
   * this function will remove all the dependency
   */
  public removeAllDependency(): void {
    this.idDependencyMap.forEach((value: any[], key: string) => {
      if (value) {
        value.forEach((x) => x.remove());
      }
    });
    this.idDependencyMap = new Map<string, any>();
  }
  /**
   *  this function will draw the dependency from the parent to the child
   * param {string} parentId
   * param {string} childId
   */
  public drawDependency(parentId: string, childId: string): void {
    // lets get the rederer for this object
    const pRenderer: D3GanttDataNodeRenderer = this.idDataNodeRendererMap.get(parentId);
    const cRenderer: D3GanttDataNodeRenderer = this.idDataNodeRendererMap.get(childId);
    // set the coordinates for drawing path
    const line_coordinate: any = {
      x1: this.series.getMaxValue(pRenderer.getDataObject(), -1),
      x2: this.series.getMinValue(cRenderer.getDataObject(), -1),
      y1: this.series.getCategory(pRenderer.getDataObject(), -1),
      y2: this.series.getCategory(cRenderer.getDataObject(), -1)
    };
    const offset: number = (this.categoryAxisRenderer.getBandWidth() / 2);
    // create group for path with parent and dependent id set to group
    // const pathGroup: any = this.elementNode.insert('g', '.taskbarGroup')
    const pathGroup: any = this.elementNode.append('g')
      .attr('class', 'hcl-gantt-dependency-group')
      .attr('from', parentId)
      .attr('to', childId)
      .on('click', () => {});
    // draw a dummy path that will have no storke, to make selection easy
    pathGroup.append('path')
      .on('click', this.dependencySelected.bind(this, pathGroup))
      .attr('class', 'hcl-gantt-dummy-dependency-path')
      .attr('stroke-width', '10px')
      .attr('fill', 'none')
      .attr('d', 'M' + this.linearAxisRenderer.getPosition(line_coordinate.x1) + ','
        + (this.categoryAxisRenderer.getPosition(line_coordinate.y1) + offset) +
        'L' + (this.linearAxisRenderer.getPosition(line_coordinate.x1) - 15) + ','
        + (this.categoryAxisRenderer.getPosition(line_coordinate.y1) + offset) +
        'L' + (this.linearAxisRenderer.getPosition(line_coordinate.x1) - 15) + ','
        + (this.categoryAxisRenderer.getPosition(line_coordinate.y2) + offset) +
        'L' + this.linearAxisRenderer.getPosition(line_coordinate.x2) + ','
        + (this.categoryAxisRenderer.getPosition(line_coordinate.y2) + offset));
    // draw the path with respective coordinates provided
    pathGroup.append('path')
      .on('click', this.dependencySelected.bind(this, pathGroup))
      .attr('class', 'hcl-gantt-dependency-path')
      .attr('stroke-width', '2px')
      .attr('fill', 'none')
      .attr('d', 'M' + this.linearAxisRenderer.getPosition(line_coordinate.x1) + ','
        + (this.categoryAxisRenderer.getPosition(line_coordinate.y1) + offset) +
        'L' + (this.linearAxisRenderer.getPosition(line_coordinate.x1) - 15) + ','
        + (this.categoryAxisRenderer.getPosition(line_coordinate.y1) + offset) +
        'L' + (this.linearAxisRenderer.getPosition(line_coordinate.x1) - 15) + ','
        + (this.categoryAxisRenderer.getPosition(line_coordinate.y2) + offset) +
        'L' + this.linearAxisRenderer.getPosition(line_coordinate.x2) + ','
        + (this.categoryAxisRenderer.getPosition(line_coordinate.y2) + offset));
    // appends the path arrow with respective coordinates provided
    pathGroup.append('path')
      .on('click', this.dependencySelected.bind(this, pathGroup))
      .attr('class', 'hcl-gantt-dependency-end-marker')
      .attr('d', 'M' + (this.linearAxisRenderer.getPosition(line_coordinate.x2) - 7) + ','
        + (this.categoryAxisRenderer.getPosition(line_coordinate.y2) + offset - 4) +
        'L' + this.linearAxisRenderer.getPosition(line_coordinate.x2) + ','
        + (this.categoryAxisRenderer.getPosition(line_coordinate.y2) + offset) +
        'L' + (this.linearAxisRenderer.getPosition(line_coordinate.x2) - 7) + ','
        + (this.categoryAxisRenderer.getPosition(line_coordinate.y2) + offset + 4));
    // add the path group to the map
    let arr: any[] = this.idDependencyMap.get(parentId);
    if (!arr) {
      arr = [];
      this.idDependencyMap.set(parentId, arr);
    }
    arr.push(pathGroup);
  }
  /**
   * This function will highlight the dependency between the parent & the child
   * param {string} parentId
   * param {string} childId
   */
  protected highlightDependency(parentId: string, childId: string): void {
    // get all the dep associated to this parent
    const dep: any[] = this.idDependencyMap.get(parentId);
    // chk which is the dep object
    const depNode: any = dep.filter((d) => d.attr('from') === parentId && d.attr('to') === childId);
    // now we have the mark the group as active
    depNode[0].raise().classed('active', true);
  }
  /**
   * Called when a dependency is selected by the user
   */
  private dependencySelected(g: any): void {
    d3.event.stopPropagation();
    // get the from & to Id of this dep
    const parentId: string = g.attr('from');
    const childId: string = g.attr('to');
    this.unSelectSelectedDependency();
    // highlight the dep
    this.highlightDependency(parentId, childId);
    this.selectedDependency.push(g);
    // add listener for un selection
    d3.select('body')
      .on('click', this.unSelectSelectedDependency.bind(this));
    // add a listener for the delete button hit, so that we can delete the dependency
    d3.select('body')
      .on('keydown', () => {
        const keyCode = d3.event.code;
        if (keyCode === 'Delete') {
          this.deleteSelectedDependency();
        } else if (keyCode === 'Escape') {
          // in case of escape clear all selection
          this.unSelectSelectedDependency();
        }
      });
  }
  /**
   * this function will delete the selected dependency
   */
  private deleteSelectedDependency(): void {
    // the delete will only work if the chart is interactive
    if (this.series.interactive.drawDependency) {
      let objMap: {parent: any, child: any}[] = [];
      this.selectedDependency.forEach((s) => {
        objMap.push({
          parent: this.idDataNodeRendererMap.get(s.attr('from')).getDataObject(),
          child: this.idDataNodeRendererMap.get(s.attr('to')).getDataObject(),
        });
      });
      if (objMap.length <= 0){
        return;
      }
      // check if the dep can be deleted
      if (this.series.dependency.validateDependencyDelete) {
        if (!this.series.dependency.validateDependencyDelete(objMap[0].parent, objMap[0].child)) {
          return;
        }
      }
      //this.deleteDependency(objMap[0]['from'], objMap[0]['to']);
      this.chartRenderer.generateDeleteDependencyEvent(this.series, objMap[0].parent, objMap[0].child);
      // clear the listener
      d3.select('body')
        .on('click', null);
      d3.select('body')
        .on('keydown', null);
      // clear the array
      this.selectedDependency = [];
    }
  }

  /**
   * this function will delete the dependency between after validation
   * param {string} parentId
   * param {string} childId
   */
  public deleteDependency(parentId: string, childId: string): void {
    // lets get the referer for this object
    const pRenderer: D3GanttDataNodeRenderer = this.idDataNodeRendererMap.get(parentId);
    const cRenderer: D3GanttDataNodeRenderer = this.idDataNodeRendererMap.get(childId);
    // get the child object which has dependency
    const childDataObject: any = cRenderer.getDataObject();
    // Emmit event to delete the dependency
    this.chartRenderer.generateDeleteDependencyEvent(this.series, cRenderer.getDataObject(), pRenderer.getDataObject());
    // now we can remove the line
    // / get all the dep associated to this parent
    const dep: any[] = this.idDependencyMap.get(parentId);
    // chk which is the dep object
    const depNode: any = dep.filter((d) => d.attr('from') === parentId && d.attr('to') === childId);
    depNode[0].remove();
  }

  /**
   * this function will clear the selection of the selected dependency
   */
  private unSelectSelectedDependency(): void {
    this.selectedDependency.forEach( (s) => s.raise().classed('active', false));
    // clear the listener
    d3.select('body')
      .on('click', null);
    d3.select('body')
      .on('keydown', null);
    // clear the array
    this.selectedDependency = [];
  }
}
