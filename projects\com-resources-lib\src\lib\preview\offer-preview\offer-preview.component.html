<section class="default-values">
    <div class="metadata">
        <com-offer-metadata [offerData]="offerData" [translations]="translations"></com-offer-metadata>
        <ng-template [ngTemplateOutlet]="pickerUrl"
            [ngTemplateOutletContext]="{attribute: offerThumbnailDetails, offerThumbnail: true}">
        </ng-template>
    </div>
    <hcl-accordion [config]="accordionCustomConfig">
        <ng-template hclTemplate hclTemplateName="headerTemplate_1">
            <span *ngIf="offerData.staticAttributes.length" class="title">{{translations.staticAttributes}}</span>
        </ng-template>

        <ng-template hclTemplate hclTemplateName="contentTemplate_1">
            <div class="default-attributes" *ngIf="offerData.staticAttributes.length">
                <ng-container *ngFor="let attribute of offerData.staticAttributes; let i = index">
                    <ng-container *ngIf="attribute.type.dataType !== 'Object'; else other_dataType">
                        <ng-template [ngTemplateOutlet]="nonComposedField"
                            [ngTemplateOutletContext]="{attribute: attribute, type: 'staticAttributes'}">
                        </ng-template>
                    </ng-container>

                    <ng-template #other_dataType>
                        <ng-container *ngIf="attribute.type.id !== 200">
                            <ng-template [ngTemplateOutlet]="nonComposedField"
                                [ngTemplateOutletContext]="{attribute: attribute , type: 'staticAttributes'}">
                            </ng-template>
                        </ng-container>
                        <ng-container *ngIf="attribute.type.id === 200 && !hiddenAttributesSet.has(+attribute.id) &&
                            !alwaysHiddenAttributesSet.has(+attribute.id)">
                            <div class="attribute">
                                <div class="form-field">
                                    <span class="ro-label" hclTooltip="{{translations.effectiveDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{translations.effectiveDate}}</span>
                                    <span class="ro-value" hclTooltip="{{attribute.value.effectiveDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{attribute.value.effectiveDate}}</span>
                                </div>
                            </div>
                            <div class="attribute">
                                <div class="form-field">
                                    <span class="ro-label" hclTooltip="{{translations.expirationDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{translations.expirationDate}}</span>
                                    <span class="ro-value" hclTooltip="{{attribute.value.expirationDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{attribute.value.expirationDate}}</span>
                                </div>
                            </div>
                        </ng-container>
                    </ng-template>
                </ng-container>
            </div>
        </ng-template>

        <ng-template hclTemplate hclTemplateName="headerTemplate_2">
            <span *ngIf="offerData.parameterizedAttributes.length"
                class="title">{{translations.parameterisedAttributes}}</span>
        </ng-template>

        <ng-template hclTemplate hclTemplateName="contentTemplate_2">
            <div class="default-attributes" *ngIf="offerData.parameterizedAttributes.length">
                <ng-container *ngFor="let attribute of offerData.parameterizedAttributes; let i = index">
                    <ng-container *ngIf="attribute.type.dataType !== 'Object'; else type_object">
                        <ng-template [ngTemplateOutlet]="nonComposedField"
                            [ngTemplateOutletContext]="{attribute: attribute, type: 'parameterisedAttributes'}">
                        </ng-template>
                    </ng-container>

                    <ng-template #type_object>
                        <ng-container *ngIf="attribute.type.id !== 200">
                            <ng-template [ngTemplateOutlet]="nonComposedField"
                                [ngTemplateOutletContext]="{attribute: attribute , type: 'parameterisedAttributes'}">
                            </ng-template>
                        </ng-container>

                        <ng-container *ngIf="attribute.type.id === 200 && !hiddenAttributesSet.has(+attribute.id) &&
                            !alwaysHiddenAttributesSet.has(+attribute.id)">
                            <div class="attribute">
                                <div class="form-field">
                                    <span class="ro-label" hclTooltip="{{translations.effectiveDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{translations.effectiveDate}}</span>
                                    <span class="ro-value" hclTooltip="{{attribute.value.effectiveDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{attribute.value.effectiveDate}}</span>
                                </div>
                            </div>
                            <div class="attribute">
                                <div class="form-field">
                                    <span class="ro-label" hclTooltip="{{translations.expirationDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{translations.expirationDate}}</span>
                                    <span class="ro-value" hclTooltip="{{attribute.value.expirationDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{attribute.value.expirationDate}}</span>
                                </div>
                            </div>
                        </ng-container>

                    </ng-template>
                </ng-container>
            </div>
        </ng-template>

        <ng-template hclTemplate hclTemplateName="headerTemplate_5">
            <span *ngIf="offerData.hiddenAttributes.length" class="title">{{translations.internalAttributes}}</span>
        </ng-template>

        <ng-template hclTemplate hclTemplateName="contentTemplate_5">
            <div class="default-attributes" *ngIf="offerData.hiddenAttributes.length">
                <ng-container *ngFor="let attribute of offerData.hiddenAttributes; let i = index">
                    <ng-container *ngIf="attribute.type.dataType !== 'Object'; else type_object_1">
                        <ng-template [ngTemplateOutlet]="nonComposedField"
                            [ngTemplateOutletContext]="{attribute: attribute, type: 'hiddenAttributes'}">
                        </ng-template>
                    </ng-container>

                    <ng-template #type_object_1>
                        <ng-container *ngIf="attribute.type.id !== 200">
                            <ng-template [ngTemplateOutlet]="nonComposedField"
                                [ngTemplateOutletContext]="{attribute: attribute , type: 'hiddenAttributes'}">
                            </ng-template>
                        </ng-container>

                        <ng-container *ngIf="attribute.type.id === 200 && 
                            !hiddenAttributesSet.has(+attribute.id) &&
                            !alwaysHiddenAttributesSet.has(+attribute.id)">
                            <div class="attribute">
                                <div class="form-field">
                                    <span class="ro-label" hclTooltip="{{translations.effectiveDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{translations.effectiveDate}}</span>
                                    <span class="ro-value" hclTooltip="{{attribute.value.effectiveDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{attribute.value.effectiveDate}}</span>
                                </div>
                            </div>
                            <div class="attribute">
                                <div class="form-field">
                                    <span class="ro-label" hclTooltip="{{translations.expirationDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{translations.expirationDate}}</span>
                                    <span class="ro-value" hclTooltip="{{attribute.value.expirationDate}}"
                                        [attr.data-position]="'bottom-top-start'">{{attribute.value.expirationDate}}</span>
                                </div>
                            </div>
                        </ng-container>

                    </ng-template>
                </ng-container>
            </div>
        </ng-template>

        <ng-template hclTemplate hclTemplateName="headerTemplate_3">
            <span class="title">{{translations.relevantsProducts}}</span>
        </ng-template>

        <ng-template hclTemplate hclTemplateName="descriptionTemplate_3"> </ng-template>

        <ng-template hclTemplate hclTemplateName="contentTemplate_3">
            <ng-container *ngIf="!offerData.productConditions?.length; else productions_length">
                <div class="relevant-products-message">{{translations.noRelevantProductsAdded}} </div>
            </ng-container>
            <ng-template #productions_length>
                <div class="relevant-products-list">
                    <div class="checkbox-label" *ngFor="let condition of offerData.productConditions">
                        {{condition.productCondition}}</div>
                </div>
            </ng-template>
        </ng-template>
    </hcl-accordion>
</section>

<ng-template #nonComposedField let-attribute="attribute" let-type="type">
    <div class="attribute" *ngIf="!hiddenAttributesSet.has(+attribute.id) &&
    !alwaysHiddenAttributesSet.has(+attribute.id)">
        <ng-container
            *ngIf="[2, 6, 4].includes(attribute.type.id) || (attribute.type.id === 1 && attribute.id !== 15) || attribute.type.id === 100">
            <div class="form-field">
                <span class="ro-label" hclTooltip="{{attribute.displayName}}"
                    [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
                <span class="ro-value" hclTooltip="{{attribute.value}}" [attr.data-position]="'bottom-top-start'">
                    <span class="pr-1" *ngIf="attribute.type.id === 4">{{currencySymbol}}</span>
                    {{attribute.value}}
                </span>
            </div>
        </ng-container>
        <ng-container [ngSwitch]="attribute.type.id">
            <ng-container *ngSwitchCase="3">
                <div class="form-field">
                    <span class="ro-label" hclTooltip="{{attribute.displayName}}"
                        [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
                    <span class="ro-value" hclTooltip="{{attribute.value | date: dateFormat}}"
                        [attr.data-position]="'bottom-top-start'">{{attribute.value | date: dateFormat}}</span>
                </div>
            </ng-container>
            <ng-container *ngSwitchCase="10">
                <div class="form-field">
                    <span class="ro-label" hclTooltip="{{attribute.displayName}}"
                        [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
                    <span class="ro-value"
                        hclTooltip="{{attribute.value?.displayColumnValue || attribute.value?.idColumnValue}}"
                        [attr.data-position]="'bottom-top-start'">{{attribute.value?.displayColumnValue ||
                        attribute.value?.idColumnValue}}</span>
                </div>
            </ng-container>
            <ng-container *ngSwitchCase="8">
                <ng-template [ngTemplateOutlet]="pickerUrl"
                    [ngTemplateOutletContext]="{attribute: attribute, offerThumbnail: false}">
                </ng-template>
            </ng-container>
            <ng-container *ngSwitchCase="15">
                <ng-template [ngTemplateOutlet]="pickerUrl"
                    [ngTemplateOutletContext]="{attribute: attribute, offerThumbnail: false}">
                </ng-template>
            </ng-container>
            <ng-container *ngSwitchCase="5">
                <div class="form-field">
                    <span class="ro-label" hclTooltip="{{attribute.displayName}}"
                        [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
                    <span class="ro-value"
                        hclTooltip="{{ attribute.value ? translations.selected : translations.notSelected }}"
                        [attr.data-position]="'bottom-top-start'">{{attribute.value ? translations.selected :
                        translations.notSelected}}</span>
                </div>
            </ng-container>
            <ng-container *ngSwitchCase="11">
                <div #richTextEl class="form-field">
                    <span class="ro-label" hclTooltip="{{attribute.displayName}}"
                        [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
                    <span class="ro-value" hclTooltip="{{translations.richText}}"
                        [attr.data-position]="'bottom-top-start'">{{translations.richText}}

                        <span class="hcl-icon-view" [popoverTriggerFor]="rtPopover" popoverTriggerOn="hover"
                            [popoverCloseOnClick]="true" [originPositionX]="'end'" [originPositionY]="'top'">
                        </span>
                    </span>
                </div>
                <hcl-popover #rtPopover="hclPopover" [popoverPositionX]="apOverlayInputs.popoverPositionX"
                    [popoverPositionY]="apOverlayInputs.popoverPositionY"
                    [originPositionX]="apOverlayInputs.originPositionX"
                    [originPositionY]="apOverlayInputs.originPositionY"
                    [popoverEnterDelay]="apOverlayInputs.popoverEnterDelay"
                    [popoverLeaveDelay]="apOverlayInputs.popoverLeaveDelay" [popoverOffsetX]="0"
                    [popoverOffsetY]="apOverlayInputs.popoverOffsetY">

                    <div class="rt-preview-container">
                        <div class="view" (click)="openRichTextView(attribute)">{{'CREATE_OFFER.LABELS.VIEW' |
                            translate}}
                        </div>
                        <div class="disabled-editor" [froalaEditor]="getRichTextOptions()"
                            [(froalaModel)]="attribute.value"></div>
                    </div>
                </hcl-popover>
            </ng-container>
        </ng-container>
    </div>
</ng-template>

<ng-template #pickerUrl let-attribute="attribute" let-offerThumbnail="offerThumbnail">
    <div class="form-field">
        <span class="ro-label" hclTooltip="{{attribute.displayName}}"
            [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
        <div *ngIf="attribute.value?.url" class="ro-value d-flex align-items-center">
            <a *ngIf="attribute.value.url.substring(0, 4) === 'http' || attribute.value.url.substring(0, 3) === 'ftp' || attribute.value.url.substring(0, 2) === 'gs'; else other_url"
                download href="{{attribute.value.url}}" target="_blank" class="ellipsis mr-5">
                <span [attr.data-position]="'bottom-top-start'"
                    hclTooltip="{{attribute.value.url}}">{{attribute.value.url}}</span>
            </a>
            <ng-template #other_url>
                <span class="mr-3" [attr.data-position]="'bottom-top-start'"
                    hclTooltip="{{attribute.value.url}}">{{attribute.value.url}}</span>
            </ng-template>
            <span class="ap-popover-container">
                <div class="inline-loader">
                    <hcl-progress-spinner [config]="dynamicConfigs[attribute.id + 'loader']">
                    </hcl-progress-spinner>
                </div>
                <span (click)="$event.stopPropagation();" class="ap-popover-trigger" [popoverTriggerFor]="apPopover"
                    popoverTriggerOn="click" [popoverCloseOnClick]="false" [originPositionX]="'end'"
                    [originPositionY]="'top'" (closed)="apItemDetailsClosed()"></span>
                <ng-container *ngIf="isAssetPickerInstalled && attribute.value.url">
                    <span class="hcl-icon-view ap-popover-icon"
                        (click)="viewUrlResource($event, attribute, offerThumbnail)">
                    </span>
                </ng-container>
            </span>
        </div>
    </div>
</ng-template>

<hcl-popover #apPopover="hclPopover" [popoverPositionX]="apOverlayInputs.popoverPositionX"
    [popoverPositionY]="apOverlayInputs.popoverPositionY" [originPositionX]="apOverlayInputs.originPositionX"
    [originPositionY]="apOverlayInputs.originPositionY" [popoverEnterDelay]="apOverlayInputs.popoverEnterDelay"
    [popoverLeaveDelay]="apOverlayInputs.popoverLeaveDelay" [popoverOffsetX]="apOverlayInputs.popoverOffsetX"
    [popoverOffsetY]="apOverlayInputs.popoverOffsetY">
    <com-asset-picker-item-details [apOverlayInputs]="apOverlayInputs" [apItemDetails]="apItemDetails"
        [apItemDetailsState]="apItemDetailsState" [translations]="translations" (closePopover)="closeApItemDetails()">
    </com-asset-picker-item-details>
</hcl-popover>