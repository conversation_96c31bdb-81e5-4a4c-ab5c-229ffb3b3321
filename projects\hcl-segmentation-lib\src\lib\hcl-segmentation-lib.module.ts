import { NgModule } from "@angular/core";
import { CreateSegmentComponent } from "./component/create-segment/create-segment.component";
import { CommonModule } from "@angular/common";
import { HclAngularWidgetsLibModule } from "hcl-angular-widgets-lib";
import { DonutChartComponent } from "./component/donut-chart/donut-chart.component";
import { SegmentMetadataComponent } from "./component/segment-metadata/segment-metadata.component";
import { ReactiveFormsModule } from "@angular/forms";
import { RuleBuilderComponent } from './component/rule-builder/rule-builder.component';
import { HclAngularChartsLibModule } from "hcl-angular-charts-lib";
import { SidePanelComponent } from './component/side-panel/side-panel.component';
import { DataProfileComponent } from './component/data-profile/data-profile.component';
import { DataProfileStatisticsComponent } from './component/data-profile-statistics/data-profile-statistics.component';
import { DataProfileDetailsComponent } from './component/data-profile-details/data-profile-details.component';

@NgModule({
  declarations: [
    CreateSegmentComponent,
    DonutChartComponent,
    SegmentMetadataComponent,
    RuleBuilderComponent,
    SidePanelComponent,
    DataProfileComponent,
    DataProfileStatisticsComponent,
    DataProfileDetailsComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    HclAngularWidgetsLibModule,
    HclAngularChartsLibModule
  ],
  exports: [CreateSegmentComponent, DataProfileComponent,DataProfileStatisticsComponent,DataProfileDetailsComponent],
})
export class HclSegmentationLibModule {}
