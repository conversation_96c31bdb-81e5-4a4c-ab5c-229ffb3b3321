<div class="offers-card-view-container">
    <div class="metadata-container">
        <span class="offer-count">{{'ASSETPICKER.TOTAL' | translate}} -
            {{offerResponseData?.page?.totalElements || 0}}</span>
        <div class="manage-selections">
            <div class="selected-offers" hclTooltip="{{ selectedOffersAndOls() }}">{{ selectedOffersAndOls() }}</div>
            <div class="manage-selections-button">
                <hcl-button class="mr-10px" [config]="manageSelectionsButtonConf" (click)="manageSelections($event)">
                </hcl-button>
            </div>
        </div>
    </div>
    <div class="scroll-container">
        <ng-container *ngIf="!noRecordsFound && !folderWithoutOffers">
            <hcl-offer-card-list [chunkSize]="chunkSize" [offerResponseData]="offerResponseData" [isDataLoading]="isDataLoading"
                (loadNewOffers)="loadNewOffers($event)" (offerSelectionUpdate)="offerSelection($event)">
            </hcl-offer-card-list>
        </ng-container>
        <ng-container *ngIf="noRecordsFound || folderWithoutOffers">
            <div class="no-records">
                <div class="title">{{'MESSAGES.NO_OFFERS_TO_SHOW' | translate}}</div>
            </div>
        </ng-container>
    </div>
</div>