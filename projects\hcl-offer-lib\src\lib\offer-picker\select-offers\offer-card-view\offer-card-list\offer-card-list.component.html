<div class="offers-card-list-container">
    <cdk-virtual-scroll-viewport #scrollViewport class="h-100" itemSize="220">
        <div class="w-100" *cdkVirtualFor="let row of offerResponseData?.content">
            <div *ngFor="let offer of row" class="result-item" [ngClass]="{'selected-item': isSelectedItem(offer)}"
                 (click)="offerClicked(offer)">
                <span class="overlay-container"></span>
                <div class="actions-btn-selector" (click)="$event.stopPropagation();">
                    <div class="select-offer">
                        <hcl-checkbox [config]="setAssetCheckboxConf(offer)"
                            (selectionChanged)="offerCheckboxClicked($event, offer)">
                        </hcl-checkbox>
                    </div>
                </div>
           
             <div class="thumbnail">
                <div class="img-wrapper">
                    <hcl-skeleton-loader
                        [config]="{ contentLoading: offer.thumbnailProperties?.url && !offer.isImageLoaded, animation:'progress', theme:{ 'border-radius': '0', height: '110px', 'margin-bottom':'15px' } }">
                    </hcl-skeleton-loader>
                    <img *ngIf="offer.thumbnailProperties?.url && !offer.brokenThumbnail" #offerThumbnail default=""
                        [src]="offer.thumbnailProperties.url"
                        (imageLoaded)="updateOfferThumbnailState($event, offer, offerThumbnail)" title=""
                        titleText="" />

                    <ng-container *ngIf="offer.brokenThumbnail">
                        <svg width="173" height="117" xmlns="http://www.w3.org/2000/svg">
                            <g fill="none" fill-rule="evenodd">
                                <path fill="#D8D8D8" d="M0 0h173v117H0z" />
                                <path
                                    d="m94.66 66.66-4.493-4.493-2.242-2.242-6.758-6.758-1.175-1.175-.654-.654a.83.83 0 0 0-1.177 1.176l1.339 1.344v10.975c0 .917.75 1.667 1.667 1.667h10.975l1.335 1.335a.837.837 0 0 0 1.181.003.83.83 0 0 0 .002-1.178Zm-13.493-1.827v-9.308l5.7 5.7-.7.875-1.667-2.267-2.5 3.334h6.808l1.667 1.666h-9.308Zm2.358-11.666L81.858 51.5h10.975c.917 0 1.667.75 1.667 1.667v10.975l-1.667-1.667v-9.308h-9.308Z"
                                    fill="#959595" />
                            </g>
                        </svg>
                    </ng-container>
                    <ng-container *ngIf="!offer.thumbnailProperties?.url">
                        <svg width="173" height="117" xmlns="http://www.w3.org/2000/svg">
                            <g fill="none" fill-rule="evenodd">
                                <path fill="#6C7794" d="M0-2h173v119H0z" />
                                <path
                                    d="M84.326 25.347c1.186-1.796 3.821-1.796 5.007 0l2.522 3.818a3 3 0 0 0 3.952.974l4.007-2.209c1.884-1.039 4.218.186 4.433 2.327l.458 4.553a3 3 0 0 0 3.047 2.699l4.575-.094c2.151-.044 3.648 2.124 2.844 4.12l-1.71 4.245a3 3 0 0 0 1.443 3.805L119 51.628c1.925.961 2.243 3.577.603 4.97l-3.486 2.964a3 3 0 0 0-.49 4.04l2.675 3.712c1.259 1.746.324 4.21-1.775 4.681L112.06 73a3 3 0 0 0-2.312 3.35l.645 4.53c.303 2.13-1.67 3.878-3.748 3.32l-4.42-1.186a3 3 0 0 0-3.603 1.892l-1.535 4.31c-.721 2.028-3.28 2.659-4.86 1.199l-3.363-3.104a3 3 0 0 0-4.07 0l-3.362 3.104c-1.581 1.46-4.14.829-4.862-1.198l-1.534-4.311a3 3 0 0 0-3.604-1.892l-4.42 1.186c-2.078.558-4.05-1.19-3.747-3.32l.644-4.53a3 3 0 0 0-2.312-3.35l-4.464-1.004c-2.1-.472-3.034-2.935-1.775-4.68l2.676-3.713a3 3 0 0 0-.49-4.04l-3.488-2.964c-1.64-1.393-1.322-4.009.604-4.97l4.095-2.043a3 3 0 0 0 1.443-3.805l-1.71-4.245c-.805-1.996.692-4.164 2.844-4.12l4.575.094a3 3 0 0 0 3.046-2.7l.459-4.552c.215-2.141 2.548-3.366 4.433-2.327l4.007 2.21a3 3 0 0 0 3.952-.975l2.521-3.818Z"
                                    fill="#FFF" fill-rule="nonzero" />
                                <path d="M104-28H74V2h30z" />
                                <path
                                    d="m74.237 58.475 11.25-11.25a2.484 2.484 0 0 1 1.763-.725H96c1.375 0 2.5 1.125 2.5 2.5v8.75c0 .688-.275 1.313-.737 1.775l-11.25 11.25c-.45.45-1.076.725-1.763.725a2.444 2.444 0 0 1-1.763-.737l-8.75-8.75a2.444 2.444 0 0 1-.737-1.763c0-.688.287-1.325.737-1.775Zm19.888-5.725A1.872 1.872 0 0 0 96 50.875 1.872 1.872 0 0 0 94.125 49a1.872 1.872 0 0 0-1.875 1.875c0 1.038.838 1.875 1.875 1.875Z"
                                    fill="#6C7794" fill-rule="nonzero" />
                            </g>
                        </svg>
                    </ng-container>
                </div>
            </div>

            <div class="metadata">
                <p class="fileName ellipsis" hclTooltip="{{offer.displayName}}">
                    {{offer.displayName}}</p>
                <div class="offer-code">
                    <p class="ellipsis" hclTooltip="{{'CREATE_OFFER.LABELS.OFFER_CODE' | translate}}:">
                        {{'CREATE_OFFER.LABELS.OFFER_CODE' | translate}}:</p>
                    <p class="ellipsis" hclTooltip="{{getOfferCodeWithDelimiter(offer.offerCodes)}}">
                        {{getOfferCodeWithDelimiter(offer.offerCodes)}}</p>
                </div>
            </div>
            <div class="status" [ngClass]="getStatusClass(offer)">
                <span hclTooltip="{{getStatusCopy(offer)}}">
                    {{getStatusCopy(offer)}}
                </span>
            </div>
        </div>
        </div>
        <ng-container *ngIf="isDataLoading">
            <ng-container *ngFor="let row of loaderItems">
                <div>
                    <div class="result-item skeleton-item" *ngFor="let item of row">
                        <hcl-skeleton-loader
                            [config]="{ contentLoading: true, animation:'progress', theme:{ 'border-radius': '0', height: '95px', 'margin-bottom':'15px', display:'block' } }">
                        </hcl-skeleton-loader>
                        <div class="metadata">
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ width: '94px', height: '13px', 'margin-bottom':'11px', display:'block' } }">
                            </hcl-skeleton-loader>
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ width: '105px', 'margin-bottom':'9px', height: '7px', display:'block'  } }">
                            </hcl-skeleton-loader>
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ width: '81px',  height: '7px', display:'block' } }">
                            </hcl-skeleton-loader>
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ width: '54px', 'margin-bottom':'10px', height: '7px', display:'block' } }">
                            </hcl-skeleton-loader>
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ 'border-radius': '6px', width: '31px', height: '13px', display:'block' } }">
                            </hcl-skeleton-loader>
                        </div>
                    </div>
                </div>
            </ng-container>    
        </ng-container>
    </cdk-virtual-scroll-viewport>
</div>