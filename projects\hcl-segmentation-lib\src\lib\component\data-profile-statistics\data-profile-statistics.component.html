<div class="statistics-container">
    <h3>
        {{config.dpConfig.translations.statistics}}
    </h3>
    <div class="loader-sec" *ngIf="dataProfileStatsSpinner.isLoading">
        <hcl-progress-spinner [config]="dataProfileStatsSpinner">
        </hcl-progress-spinner>
    </div>
    <ng-container *ngIf="!dataProfileStatsSpinner.isLoading">
        <div class="row-item">
            <div class="w-60">
                <span class="row-label" data-position="bottom-top-start"
                    hclTooltip="{{config.dpConfig.translations.count}}">
                    {{config.dpConfig.translations.count}} :
                </span>
            </div>
            <div class="w-40">
                <span class="row-value" data-position="bottom-top-start" hclTooltip=" {{statsData.count}}">
                    {{statsData.count}}
                </span>
            </div>
        </div>
        <div class="row-item">
            <div class="w-60">
                <span class="row-label" data-position="bottom-top-start"
                    hclTooltip="{{config.dpConfig.translations.null}}">
                    {{config.dpConfig.translations.null}} :
                </span>
            </div>
            <div class="w-40">
                <span class="row-value" data-position="bottom-top-start" hclTooltip=" {{statsData.nulls}}">
                    {{statsData.nulls}}
                </span>
            </div>
        </div>
        <div class="row-item">
            <div class="w-60">
                <span class="row-label" data-position="bottom-top-start"
                    hclTooltip="{{config.dpConfig.translations.categories}}">
                    {{config.dpConfig.translations.categories}} :
                </span>
            </div>
            <div class="w-40">
                <span class="row-value" data-position="bottom-top-start" hclTooltip=" {{statsData.categories}}">
                    {{statsData.categories}}
                </span>
            </div>
        </div>
        <div class="row-item">
            <div class="w-60">
                <span class="row-label" data-position="bottom-top-start"
                    hclTooltip="{{config.dpConfig.translations.mean}}">
                    {{config.dpConfig.translations.mean}} :
                </span>
            </div>
            <div class="w-40">
                <span class="row-value" data-position="bottom-top-start" hclTooltip=" {{statsData.mean}}">
                   {{statsData.mean}}
                </span>
            </div>
        </div>
        <div class="row-item">
            <div class="w-60">
                <span class="row-label" data-position="bottom-top-start"
                    hclTooltip="{{config.dpConfig.translations.standardDeviation}}">
                    {{config.dpConfig.translations.standardDeviation}} :
                </span>
            </div>
            <div class="w-40">
                <span class="row-value" data-position="bottom-top-start" hclTooltip=" {{statsData.stdev}}">
                   {{statsData.stdev}}
                </span>
            </div>
        </div>
        <div class="row-item">
            <div class="w-60">
                <span class="row-label" data-position="bottom-top-start"
                    hclTooltip="{{config.dpConfig.translations.minimum}}">
                    {{config.dpConfig.translations.minimum}} :
                </span>
            </div>
            <div class="w-40">
                <span class="row-value" data-position="bottom-top-start" hclTooltip=" {{statsData.min}}">
                   {{statsData.min}}
                </span>
            </div>
        </div>
        <div class="row-item">
            <div class="w-60">
                <span class="row-label" data-position="bottom-top-start"
                    hclTooltip="{{config.dpConfig.translations.maximum}}">
                    {{config.dpConfig.translations.maximum}} :
                </span>
            </div>
            <div class="w-40">
                <span class="row-value" data-position="bottom-top-start" hclTooltip=" {{statsData.max}}">
                   {{statsData.max}}
                </span>
            </div>
        </div>
    </ng-container>
</div>