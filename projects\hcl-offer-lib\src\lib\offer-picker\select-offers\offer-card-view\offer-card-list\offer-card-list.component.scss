.offers-card-list-container {
  height: 100%;

  .cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper {
    padding-bottom: 10px;
  }
  .result-item {
    display: inline-block;
    background-color: #f5f5f5;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
    cursor: pointer;
    height: 220px;
    width: 173px;
    position: relative;
    margin: 5px;

    &.selected-item {
      background-color: #fde6d2;
      outline: 2px solid #f5821e;
    }

    &:hover {
      outline: 2px solid #f5821e;
      box-shadow: 6px 6px 10px 0 rgb(0 0 0 / 30%);

      .actions-btn-selector,
       .overlay-container {
        display: flex;
      }
    }
    &.result-item-border {
      outline: 2px solid #f5821e;
    }
    .disable-item-msg {
      display: none;
    }
    .display-block {
      display: flex !important;
    }

    .overlay-container {
      position: absolute;
      height: 35px;
      width: 100%;
      top: 0;
      left: 0;
      background-color: #15161c;
      opacity: 0.8;
      z-index: 1;
      display: none;
    }

    .actions-btn-selector {
      height: 35px;
      position: absolute;
      width: 100%;
      z-index: 1;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      display: none;
      cursor: default;


      .mat-checkbox {
        .mat-checkbox-layout {
          margin: 0;
        }
        .mat-checkbox-frame {
          border: 2px solid #959595 !important;
        }
      }

      // .menu-wrapper {
      //   .hcl-icon-kabab {
      //     color: #959595;
      //     cursor: pointer;
      //   }
      // }
    }

    &.selected {
      outline: 2px solid #f5821e;
      background-color: #fde6d2;
    }

    .thumbnail {
      height: 110px;
      overflow: hidden;
      margin: 0;
      position: relative;
      display: block;
      .img-wrapper {
        height: 100%;

        img {
          max-width: 100%;
          display: block;
          margin: 0 auto;
          position: absolute;
          top: 47%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        .icon-wrapper {
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          i {
            font-size: 50px;
            color: #959595;
          }
        }
      }
    }
    .metadata {
      padding: 7px;
      color: #6d7692;
      font-size: 12px;
      letter-spacing: 0.4px;
      line-height: 14px;
      width: 100%;
      .fileName {
        font-family: "Montserrat";
        font-size: 14px;
        margin-bottom: 10px;
        line-height: 18px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      p {
        margin: 0 0 10px;
      }
      .offer-code {
        margin: 0;
        p {
          margin: 0;
          &:first-child {
            margin-bottom: 03px;
          }
          &:last-child {
            margin-top: 05px;
          }
        }
      }
    }
    .status {
      display: inline-block;
      border-radius: 8px;
      padding: 2px 10px;
      color: #f5f5f5;
      font-family: Roboto;
      font-size: 11px;
      letter-spacing: 0.37px;
      line-height: 13px;
      text-align: center;
      max-width: 90px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      margin: 5px 0;
      position: absolute;
      left: 7px;
      bottom: 5px;

      &.retired {
        background-color: #bcbbbb;
      }

      &.published {
        background-color: #c4d056;
      }

      &.draft {
        background-color: #ffb755;
      }
    }
    // .approval-section {
    //   display: flex;
    //   max-width: 90px;
    //   text-overflow: ellipsis;
    //   overflow: hidden;
    //   white-space: nowrap;
    //   position: absolute;
    //   right: 7px;
    //   bottom: 7px;

    //   .approval-count {
    //     color: #0078d8;
    //     font-family: Roboto;
    //     font-size: 14px;
    //     letter-spacing: 0;
    //     text-align: center;
    //   }

    //   i.hcl-icon-step_linkapproval {
    //     color: rgba(0, 0, 0, 0.5);
    //   }
    // }
    &.skeleton-item {
      background-color: #c0c4d1;
    }
    &.disable-item {
      border: none;
      opacity: 0.3;
      cursor: default;

      .disable-item-msg {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        height: 100%;
        width: 100%;
        justify-content: center;
        align-items: center;
      }

      .thumbnail,
      .metadata {
        pointer-events: none;
      }
      &:hover {
        outline: none;
        box-shadow: none;
      }
    }
  }
}

//   @media only screen and (-webkit-min-device-pixel-ratio: 2),
//     only screen and (min--moz-device-pixel-ratio: 2),
//     only screen and (-o-min-device-pixel-ratio: 2/1),
//     only screen and (min-device-pixel-ratio: 2),
//     only screen and (min-resolution: 192dpi),
//     only screen and (min-resolution: 2dppx) {
//     /* Retina-specific stuff here */
//     .result-area {
//       .cdk-virtual-scroll-viewport {
//         .cdk-virtual-scroll-content-wrapper {
//           display: flex;
//           flex-wrap: wrap;
//         }
//       }
//     }
//   }
