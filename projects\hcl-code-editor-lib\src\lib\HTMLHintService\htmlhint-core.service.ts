import { Injectable } from '@angular/core';
import { HTMLHint } from './HTMLHint/core';
import { Ruleset, Translation } from './HTMLHint/types';

@Injectable({
  providedIn: 'root'
})
export class HTMLHintCoreService {

  constructor() { }
  htmlRuleSets: Ruleset = {};
  errorTranslations: Translation = {};

  setHTMLRuleSets(rulesets: Ruleset) {
    this.htmlRuleSets = rulesets
  }

  setErrorTranslations(translations: Translation) {
    this.errorTranslations = translations
  }

  verifyHTMLCode(htmlCode: string) {
    return HTMLHint.verify(htmlCode, this.htmlRuleSets, this.errorTranslations);
  }
}
