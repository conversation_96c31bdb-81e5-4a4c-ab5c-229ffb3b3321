import {
  Component,
  ViewEncapsulation, Output, EventEmitter, Input, ViewChild, OnInit, ChangeDetectorRef
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { MenuComponent, MenuConfig } from 'hcl-angular-widgets-lib/public_api';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import html2canvas from 'html2canvas';

@Component({
  selector: 'ip-rule-display',
  templateUrl: './rule-display.component.html',
  styleUrls: ['./rule-display.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class RuleDisplayComponent implements OnInit {
  @Input() ruleInfo: any;
  @Input() selectedElement: any;
  // @Input() getDigitalAsset;
  @Output() removeRule = new EventEmitter<any>();
  @ViewChild('actionMenu') actionMenu: MenuComponent;
  isLoading: boolean;
  actionMenuConf: MenuConfig = {
    items: [
      {
        title: this.translate.instant('FOLDERS.BUTTONS.EDIT'),
        label: this.translate.instant('FOLDERS.BUTTONS.EDIT'),
        data: 'edit'
      },
      {
        title: this.translate.instant('FOLDERS.BUTTONS.DELETE'),
        label: this.translate.instant('FOLDERS.BUTTONS.DELETE'),
        data: 'delete'
      }
    ]
  }
  htmlSnippetPreviewSource = '';
  assetsDownloaded = false;

  constructor(public ngb: IpEmailBuilderService,
    public translate: TranslateService,
    private sanitizer: DomSanitizer,
    private chRef: ChangeDetectorRef) {
    this.isLoading = true
  }


  ngOnInit(): void {
    this.ngb.currentEditingField$.subscribe((field) => {
      if (field && field.type === 'html-field') {
        if (this.ruleInfo.htmlSnippetPreviewSrc) {
          this.htmlSnippetPreviewSource = encodeURI(this.ruleInfo.htmlSnippetPreviewSrc);
          this.hideLoader();
          this.chRef.detectChanges();
        }
      }
    });

    this.ngb.currentEditingBlock$.subscribe((block) => {
      if (block && block.type === 'html') {
        if (this.ruleInfo.htmlSnippetPreviewSrc) {
          this.htmlSnippetPreviewSource = encodeURI(this.ruleInfo.htmlSnippetPreviewSrc);
          this.hideLoader();
          this.chRef.detectChanges();
        }
      }
    });

    if (this.ruleInfo.htmlSnippetPreviewSrc) {
      this.htmlSnippetPreviewSource = encodeURI(this.ruleInfo.htmlSnippetPreviewSrc);
      this.hideLoader();
      this.chRef.detectChanges();
    } else if (this.selectedElement.type_display && (this.selectedElement.type === 'html' || this.selectedElement.type === 'html-field') && this.selectedElement.options.innerHtml) {
      const HTMLDOMParser = new DOMParser();
      const htmlStringForUpdate = HTMLDOMParser.parseFromString(this.selectedElement.options.innerHtml, "text/html");
      const preInnerHtml = htmlStringForUpdate.documentElement.querySelector('body').innerHTML;
      htmlStringForUpdate.documentElement.querySelector('body')['innerHTML'] = `<div id="hclConvertToImage" class="_${this.selectedElement.options.id}">${preInnerHtml}</div>`;
      document.body.appendChild(htmlStringForUpdate.documentElement.querySelector('#hclConvertToImage'));
      const tempNode = document.querySelector(`._${this.selectedElement.options.id}`) as HTMLElement;
      html2canvas(tempNode, { allowTaint: true, useCORS: true }).then((canvas) => {
        const data = canvas.toDataURL('image/jpeg', 0.9);
        this.htmlSnippetPreviewSource = encodeURI(data);
        tempNode.parentElement.removeChild(tempNode);
        this.hideLoader();
        this.chRef.detectChanges();
      })
    }
    // else if (this.selectedElement.type === 'html' || this.selectedElement.type === 'html-field') {
    //   this.getDigitalAsset(this.selectedElement.options.id, true).subscribe((resp) => {
    //     const reader = new FileReader();
    //     reader.readAsText(resp);
    //     reader.onload = () => {
    //       const respstr: string = typeof (reader.result) === 'string' ? reader.result : reader.result.toString();
    //       const HTMLDOMParser = new DOMParser();
    //       const htmlStringForUpdate = HTMLDOMParser.parseFromString(respstr, "text/html");
    //       const preInnerHtml = htmlStringForUpdate.documentElement.querySelector('body').innerHTML;
    //       this.selectedElement.options['innerHtml'] = this.sanitizer.sanitize(SecurityContext.HTML, preInnerHtml);

    //       htmlStringForUpdate.documentElement.querySelector('body')['innerHTML'] = this.sanitizer.sanitize(SecurityContext.HTML, `<div id="hclConvertToImage" class="_${this.selectedElement.options.id}">${preInnerHtml}</div>`);
    //       document.body.appendChild(htmlStringForUpdate.documentElement.querySelector('#hclConvertToImage'));
    //       const tempNode = window.document.querySelector(`._${this.selectedElement.options.id}`) as HTMLElement;

    //       html2canvas(tempNode, { allowTaint: true, useCORS: true }).then((canvas) => {
    //         const data = canvas.toDataURL('image/jpeg', 0.9);
    //         this.htmlSnippetPreviewSource = encodeURI(data);
    //         tempNode.parentElement.removeChild(tempNode);
    //         this.hideLoader();
    //         this.chRef.detectChanges();
    //       })
    //     }
    //   })
    // }
  }




  hideLoader() {
    this.isLoading = false;
  }

  actionClicked(event: any) {
    if (event === 'edit') {
      this.ngb.addEditRuleBuilder.next({ ruleInfo: this.ruleInfo, hyperlinkInfo: { id: this.ruleInfo.linkId, startIndex: this.ruleInfo.startIndex, endIndex: this.ruleInfo.endIndex, linkText: this.ruleInfo.linkText } });
    } else if (event === 'delete') {
      this.removeRule.emit(this.ruleInfo);
    }
  }

  openActionMenu(event) {
    this.actionMenu.openMenu(event);
  }

  actionMenuClosed() {

  }

  openLinkInNewTab() {
    if (this.ruleInfo.url) {
      window.open(this.sanitizer.sanitize(4, this.ruleInfo.url), "_blank");
    }
  }
}