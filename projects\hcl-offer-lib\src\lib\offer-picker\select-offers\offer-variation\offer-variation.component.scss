.variant-outer-container {
  overflow: hidden;
  background-color: #ececec;
  display: flex;
  align-items: center;
  .variant-container-heading {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 60px;
    .variant-container-label {
      margin: 0 0 0 20px;
      color: #6d7692;
      font-family: "Montserrat";
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 200px;
    }
  }
  .toggle-selection {
    height: 100%;
    width: 60px;
    background-color: #ececec;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    h3 {
      color: #6d7692;
      font-family: Montserrat;
      font-size: 20px;
      font-weight: bold;
      line-height: 24px;
      writing-mode: vertical-lr;
      transform: rotate(180deg);

      &.reset-transform-rotate {
        transform: rotate(0deg);
      }
    }
  }

  .variant-inner-container {
    box-shadow: -15px 0 10px -2px rgba(0, 0, 0, 0.3);
    flex-grow: 1;
    width: 100%;
    overflow: hidden;
    .variation-container {
      display: flex;
      padding: 0 15px 15px 15px;
      height: calc(100% - 120px);
      .top-panel {
        width: 100%;
        background-color: #f5f5f5;
        height: 54px;
        display: flex;
        align-items: center;
        color: #6d7692;
        font-family: Montserrat;
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 7px;
        box-shadow: 0 2px 2px 0 rgb(0 0 0 / 25%);
        justify-content: space-between;
        .offer-logo {
          margin: 0 0 0 10px;
          font-family: "Montserrat";
          font-size: 16px;
          letter-spacing: 0;
        }
      }
      .variation-listing-container {
        display: inline-block;
        background-color: #f5f5f5;
        width: 22%;
        margin: 0 20px 0 0;
      }
      .variations-inner-container {
        height: calc(100% - 60px);
      }
      .offer-summery-container {
        display: inline-block;
        width: 95%;
      }

      .offer-variations-container {
        display: inline-block;
        width: 95%;
      }

      .offer-edit-container {
        width: 95%;
      }

      .max-min-icon-container {
        display: inline-flex;

        .expand-collapse-icon-container {
          width: 70px;
          display: flex;
          height: 54px;
          align-items: center;
          justify-content: center;

          .max-min-button-container {
            width: 50%;
            height: 56px;
            display: flex;
            padding-left: 5px;

            .max-button-container,
            .min-button-container {
              align-items: center;
              display: flex;
              width: 100%;
              justify-content: center;

              hcl-button > .navBtnCls {
                border-radius: 0px;
                padding: 0 !important;
                min-width: 24px !important;
                min-height: 24px !important;
              }
            }
          }
        }
        .expand-collapse-seperator {
          width: 1px;
          height: 26px;
          margin: 14px 5px 14px 0;
          background-color: #bcbbbb;
        }
      }

      .max-button-offer-container {
        background-color: #f5f5f5;
        height: 54px;
        align-items: center;
        margin-bottom: 7px;
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
        display: flex;
        .hcl-md-button {
          display: flex !important;
        }
        button {
          min-width: 100%;
          width: 100%;
        }
      }
      .max-text-offer-container {
        background-color: #f5f5f5;
        height: calc(100% - 60px);
        margin-bottom: 7px;
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
        padding-left: 5px;
      }

      // The CSS for the max & min buttons on the Folder panel ****ENDS
      // The CSS for animation starts hear ****STARTS
      .variation-panel-minimized {
        -webkit-transition: width 0.5s linear;
        transition: width 0.5s linear;
        width: unset !important;
        min-width: 50px;
        height: 100%;
        .variations-inner-container {
          display: none;
        }
        .top-panel {
          .offer-logo {
            -webkit-transition: width 1.5s linear;
            transition: width 1.5s linear;
            display: none;
          }
          .max-min-icon-container {
            width: 50px;
            .expand-collapse-icon-container {
              width: 50px !important;
              .expand-collapse-seperator {
                display: none !important;
              }
              .expand-collapse-icon-container {
                margin-left: 0px;
              }
            }
          }
        }
      }
      .offer-panel-normal {
        -webkit-transition: width 0.5s linear;
        transition: width 0.5s linear;
        width: calc(78% - 20px) !important;
      }
      .variation-panel-normal {
        -webkit-transition: width 0.5s linear;
        transition: width 0.5s linear;
        .offer-logo {
          -webkit-transition: width 1.5s linear;
          transition: width 1.5s linear;

          .max-min-icon-container {
            width: 40px;
            .expand-collapse-icon-container {
              width: 50px !important;
              .max-button-container {
                display: none;
              }
              .expand-collapse-icon-container {
                margin-left: 0px;
              }
            }
          }
        }
      }
      .offer-panel-maximized {
        -webkit-transition: width 0.5s linear;
        transition: width 0.5s linear;
        width: 96% !important;
        height: 100%;
      }

      .offer-panel-maximized2 {
        -webkit-transition: width 0.5s linear;
        transition: width 0.5s linear;
        width: 100% !important;
        height: 100%;
        .offer-info-container {
          padding: 0 !important;
        }
      }

      // The CSS for animation starts hear ****ENDS

      .vertical-label > span {
        writing-mode: vertical-lr;
        transform: rotate(180deg);
        margin-top: 15px;
        color: #6d7692;
        font-family: Montserrat, sans-serif;
        font-size: 20px;
        margin-left: 7px;
        font-weight: 600;

        &.reset-transform-rotate {
          transform: rotate(0deg);
        }
      }
    }
  }

  .offer-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .action-button {
      margin: 0 10px;
    }
  }
}
