@keyframes slide-in-quick-view-container {
    from {
      width: 100%;
    }
    to {
      width: calc(100% - 390px);
    }
}
@keyframes slide-out-quick-view-container {
    from {
        width: calc(100% - 390px);
    }
    to {
      width: 100%;
    }
}
.quick-view-container {
    width: 100%;
    position: relative;
    height: 100%;
    overflow: auto;
    float: left;
    &.expanded {
        animation: slide-out-quick-view-container 1s forwards;
    }
    &.collapsed {
        animation: slide-in-quick-view-container 1s forwards;
    }
    & > div:first-child {
        position: sticky; 
        top: 0%;
        height: inherit;
        min-height: 40px;
        z-index: 2;
        .code-view-parent {
            box-shadow: 0px 2px 2px 0 rgba(0,0,0,0.25);
            height: inherit;
            display: inline-block;
            border-radius: 0px 0px 5px 5px;
            background: white;
            padding-top: 3px;
            border-top: 1px solid rgba(0, 0, 0, 0.12);
            button {
                border: none;
            }
        }
        .add-html-button {
            margin-left: 20px;
            display: inline-block;
            border-radius: 0px 0px 5px 5px;    
            box-shadow: 0px 2px 2px 0 rgb(0 0 0 / 25%);
            background: #FFF;
            padding: 2px 10px;
            height: 30px;
            color: #0078d8;
            border-top: 1px solid rgba(0, 0, 0, 0.12);
            cursor: pointer;
        }
    }
    .froala-editor {
        margin: auto;
        height: auto;
        // height: 100%;
        .scrollable-container {
            overflow: hidden;
            .fr-toolbar {
                &.fr-desktop {
                    &.fr-inline {
                        height: auto;
                    }
                }
            }
            .fr-popup { 
                &.fr-desktop { 
                    &.fr-inline { 
                        &.fr-active {
                            height: auto;
                        }
                    }
                }
            }
        }
        & > div {
            height: inherit;
            div.fr-wrapper {
                iframe {
                    min-height: 70px !important;
                }
                height: auto;
                width: 100%;
                //font-family: none;
                a {
                    text-decoration: none;
                    color: rgb(0, 0, 238);
                }
            }
        }
        .fr-view table td {
            border: 0px;
        }
        .fr-element.fr-view.fr-element-scroll-visible {
            p:first-of-type {
                display: none;
            }
            img {
                margin-right: 0px;
                margin-top: 0px;
            }
        }
        .fr-element {
            p {
                margin: 0px;
            }
        }
                
        .uaepf-tag-class, .PFTAGCUSTOM.fr-deletable.fr-tribute {
            background-color: #add8e6;
        }
        i.hcl-icon-code-view {
            font-size: 20px;
        }

        .comm-link-wrapper{
            a[style*="text-decoration: underline"] {
                span{
                    text-decoration: underline;
                }
            }
        }
}
@keyframes slide-in-header-footer-manager {
    from {
        right: -390px;
    }
    to {
        right: 0px;
    }
}
@keyframes slide-out-header-footer-manager {
    from {
        right: 0px;
    }
    to {
        right: -390px;
    }
}
.header-footer-manager {
    &.expanded {
        animation: slide-in-header-footer-manager 1s forwards;
    }
    &.collapsed {
        animation: slide-out-header-footer-manager 1s forwards;
    }
    .container {
        width: 350px;
        height: 100%;
        padding: 0;
    }
    right: -390px;
    margin-top: -5px;
    position: absolute;
    float: right;
    padding: 23px 20px 20px 20px;
    color: #6D7692;
    font-family: Montserrat;
    letter-spacing: 0;
    background-color: #F5F5F5;
    box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.25);
    display: flex;
    height: calc(100% + 5px);
    flex-direction: column;
    .content-in-manager {
        height: 88%;
        .manager-text-title {
            font-family: Helvetica;
            font-size: 14px;
            line-height: 17px; 
        }
    }
    hcl-button {
        float: right;
    }
    h6 {
        font-size: 16px;
        line-height: 19px;
        font-weight: bold;
    }
    hcl-checkbox {
        .mat-checkbox-label span {
            padding-bottom: 5px;
        }
    }
}
}
















.fr-popup:not(.color-custom-popup, .link-color-custom-popup) {
    margin-top: 5px !important;
    color: white !important;
    background: #444 !important;
    border-radius: 25px !important;
    .fr-buttons {
        background: transparent;
        display: inline-block;
        margin: 0 17px 0 17px;
        padding: 0px !important;
        .fr-command.fr-btn {
            background: #444 !important;
            margin: 4px 2px !important;
            height: 37px;
            i {
                color: #ccc;
                font-size: 25px;
                margin: 2px 2px;
                width: 30px;
                &.hcl-icon-link-tool {
                    margin: 0px;
                    font-size: 30px;
                    text-align: center;
                    width: 100%;
                }
                &.hcl-icon-delete {
                    font-size: 16px;
                    margin-bottom: 5px;
                }
                &.hcl-icon-italics {
                    font-size: 22px;
                }
                &.hcl-icon-settings {
                    font-size: 17px;
                    margin-bottom: 4px;
                }
                &.hcl-icon-loop {
                    font-size: 22px;
                }
            }
            &.fr-dropdown:after {
                border-top: 4px solid #ccc;
            }
            &.fr-dropdown.fr-active:after {
                border-top: 0;
                border-bottom: 4px solid #ccc;
            }
            &.fr-dropdown i {
                margin-left: 3px;
                margin-right: 10px;
            }
            &:not(.fr-tabs) {
                padding: 0px !important;
            }
        }
        .fr-dropdown-menu {
            color: white !important;
            background: #444 !important;
            li a.fr-active, li a.fr-command:hover, a.fr-active:hover {
                background-color: #292929 !important; 
            }
        }
    }
}

.fr-toolbar {
    color: white !important;
    background: #444 !important;
    border-radius: 25px !important;
    .fr-btn-grp {
        background: transparent;
        .fr-command.fr-btn {
            background: #444 !important;
            i {
                color: #ccc;
                font-size: 25px;
                margin: 2px 2px;
                width: 30px;
                &.hcl-icon-link-tool {
                    font-size: 30px;
                }
                &.hcl-icon-italics {
                    font-size: 22px;
                }
            }
            &.fr-dropdown:after {
                border-top: 4px solid #ccc;
            }
            &.fr-dropdown.fr-active:after {
                border-top: 0;
                border-bottom: 4px solid #ccc;
            }
        }
        .fr-dropdown-menu {
            color: white !important;
            background: #444 !important;
            li a.fr-active, li a.fr-command:hover, a.fr-active:hover {
                background-color: #292929 !important;
            }
        }
    }
}
.fr-toolbar, .fr-popup:not(.color-custom-popup, .link-color-custom-popup) {
    &:after {
        content: "";
        position: absolute;
        bottom: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: transparent transparent #444 transparent;
    }
}
.fr-toolbar, .fr-popup:not(.color-custom-popup, .link-color-custom-popup) {
    &.fr-above {
        &:after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #444 transparent transparent transparent;
        }
    }
    
}
.color-custom-popup, .link-color-custom-popup {
    border-radius: 0px !important;
    div.custom-layer, div.link-custom-layer {
        width: 100%;
        height: 268px;
        .alwan {
            &.alwan--open {
                height: 268px;
                width: 215px;
                .alwan__container {
                    .alwan__inputs {
                        label {
                            &:last-child {
                                margin-right: 0px;
                            }
                            span {
                                top: 15px;
                                position: relative;
                            }
                        }
                    }
                    .alwan__button  {
                        position: absolute;
                        right: 5px;
                        top: 50px;
                    }
                }
            }
        } 
    }
    .custom-layer-buttons, .link-custom-layer-buttons {
        button {
            float: right !important;
            margin-right: 10px !important;
        }
        div.custom-footer-button {
            text-align: center;
            height: 30px;
            max-width: 80px;
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 3px;
            font-family: 'Roboto';
            padding: 5px;
            span {
                font-weight: normal !important;
            }
        }
        div.confirm-button {
            background: #0078d8;
            color: #FFF;
            &:hover {
                background: #f5821e;
            }
        }
        div.cancel-button {
            border: 2px solid #0078d8;
            background: #FFF;
            color: #0078d8;
            font-weight: 500 !important;
            &:hover {
                border: 2px solid #f5821e;
                color: #f5821e;
            }
        }
    }
}
.fr-image-resizer.fr-active {
    z-index: 2;
}

.alwan__button {
    &.alwan__preset-button {
        display: none !important;
    }
}

        body {
          overflow-y: hidden;
        }
        .tribute-container {
            width: 100%;
            z-index: 3;
            background-color: #444;
            max-height: 200px;
            overflow-y: auto;
            max-width: 20%;
            box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
            min-width: 112px;
            overflow: auto;
            padding-top: 0;
            padding-bottom: 0;
            border-radius: 4px;
        }
          .tribute-container ul {
            list-style: none;
            float: left;
            width: 100%;
            padding: 0;
            margin: 0;
        }
          .tribute-container ul li {
            margin: 0;
            height: 42px;
            color: #fff;
            font-size: 14px;
            line-height: 42px;
            width: 100%;
            white-space: nowrap;
            padding: 0 16px;
            text-align: left;
            text-decoration: none;
            position: relative;
            cursor: pointer;
            outline: 0;
            flex-direction: row;
            max-width: 100%;
            box-sizing: border-box;
            align-items: center;
            -webkit-tap-highlight-color: transparent;
            display: inline-block;
            flex-grow: 1;
            font-family: Roboto, "Helvetica Neue", sans-serif;
            overflow: hidden;
            text-overflow: ellipsis;
        }
          .tribute-container ul li.highlight {
            background-color: #0078d8 !important;
        }
        .PFTAGCUSTOM { background-color: #add8e6; } a { text-decoration: none; color: rgb(0, 0, 238); }
        .uaepf-tag-class, .PFTAGCUSTOM.fr-deletable.fr-tribute {
          background-color: #add8e6;
        }