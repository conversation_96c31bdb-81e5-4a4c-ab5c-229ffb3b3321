import { Component, OnInit, Input } from '@angular/core';
import { SpiderChartConfig } from 'projects/hcl-angular-charts-lib/src/public-api';
@Component({
  selector: 'app-spider-chart-demo',
  templateUrl: './spider-chart-demo.component.html',
  styleUrls: ['./spider-chart-demo.component.scss']
})
export class SpiderChartDemoComponent implements OnInit {

  constructor() { }


  MainChartConfig : SpiderChartConfig = {
    chartContainerId: 'abc',
    numofLevels : 5,
    size : 320,
    width: 320,
    drawDataTextLabels: true,
    chartBgColor: '#FFF',
    gridLineColor: '#808080',
    levelsStrokeColor: '#808080',
    gradientColor1: "#F7B500",
    gradientColor2: "#B620E0",
    gradientColor3:  "#32C5FF",
    dataPointCircleColor: 'rgb(96 128 149)',
    tickTextColor: '#000',
    labelsTextColor: '#6D7692',
    radiusForDataPointsCircle: 3,
    gradientOffset1: 0,
    gradientOffset2: 51.26,
    gradientOffset3: 100
  }

  SmallChartConfig : SpiderChartConfig = {
    chartContainerId:'vcy',
    numofLevels : 5,
    size : 35,
    drawDataTextLabels: false,
    chartBgColor: '#FFF',
    gridLineColor: '#808080',
    levelsStrokeColor: '#808080',
    gradientColor1: "#F7B500",
    gradientColor2: "#B620E0",
    gradientColor3:  "#32C5FF",
    dataPointCircleColor: 'rgb(96 128 149)',
    tickTextColor: '#000',
    labelsTextColor: '#6D7692',
    radiusForDataPointsCircle: 0.5,
    gradientOffset1: 40,
    gradientOffset2: 51.26,
    gradientOffset3: 100
  }
  ngOnInit(): void {

  }
}
