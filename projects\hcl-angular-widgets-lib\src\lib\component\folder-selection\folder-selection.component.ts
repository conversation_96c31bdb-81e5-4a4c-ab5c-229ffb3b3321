import {
  Component,
  EventEmitter,
  Input,
  OnD<PERSON>roy,
  OnInit,
  Output,
  ViewEncapsulation,
} from "@angular/core";
import { Subscription } from "rxjs";
import { HclFolderSelectionService } from "../../service/hcl-folder-selection.service";
import { FolderSelectionConf } from "./folder-selection-conf";
import { ButtonConf } from "./../button/button-config";
import { NotificationService } from "../../service/notification.service";

@Component({
  selector: "hcl-folder-selection",
  templateUrl: "./folder-selection.component.html",
  styleUrls: ["./folder-selection.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class FolderSelectionComponent implements OnInit, OnDestroy {
  @Input() config: FolderSelectionConf;
  @Output() cancleAction = new EventEmitter();
  @Output() folderSelected = new EventEmitter();

  subscription: Subscription;

  selectActionConfig: ButtonConf;
  cancelActionConfig: ButtonConf;

  private loginAttempts = 0;
  private MAX_LOGIN_ATTEMPTS = 2;

  itemType: string;
  itemTypeLabel: string;
  // modelTitle: string;
  rootFolderId: number;
  itemsData = [];
  breadCrumbData = [];
  foldersObject = {
    parent: null,
    childrenData: [],
    parentIndex: 0,
  };

  constructor(
    private foldersService: HclFolderSelectionService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.foldersService.applicationBaseURL = this.config.applicationBaseURL;
    this.foldersService.headers = this.config.applicationHeaders;

    if (this.config.showSelectedItemDetails) {
      this.subscription = this.foldersService
        .getItemsFromMoveFolders()
        .subscribe((itemsArray) => {
          if (itemsArray.length > 0) {
            this.itemTypeLabel = this.config.translations.itemTypeLabel;
            // this.modelTitle = this.config.translations.modelTitle;
            this.itemsData = itemsArray;
            this.breadCrumbData = [];
            this.loadFoldersFromApi();
            this.foldersService.clearItemsFromMoveFolders();
          }
        });
    } else {
      this.loadFoldersFromApi();
    }
    this.setConfiguration();
  }

  setConfiguration() {
    this.cancelActionConfig = {
      name: "no",
      value: this.config.translations.cancle,
      color: "accent",
      buttonType: "stroked",
      type: "button",
      styleClass: "hcl-md-button",
      borderRadius: 5,
    };
    this.selectActionConfig = {
      name: "moveHere",
      value: this.config.translations.selectButtonCopy,
      color: "accent",
      buttonType: "flat",
      type: "button",
      styleClass: "hcl-md-button",
      borderRadius: 5,
      disabled: true,
    };
  }

  loadFoldersFromApi() {
    if (this.foldersService.moveFolderApiCallCount === 0) {
      this.rootFolderId = this.config.applicationRootFolder;
      this.foldersService
        .getFolders(this.rootFolderId)
        .subscribe((folderTreeData) => {
          this.foldersService.moveFolderApiCallCount++;
          const treeData = folderTreeData;
          if (
            treeData.displayName &&
            treeData.id === this.config.applicationRootFolder
          ) {
            treeData.displayName = this.config.translations.rootFolderName;
          }
          const { childFolders, ...folderObj } = treeData;
          this.breadCrumbData.push({
            folder: folderObj,
            parent: null,
            index: 0,
            children: treeData.childFolders,
          });
          this.foldersObject.parent = folderObj;
          this.foldersObject.childrenData = treeData.childFolders;
          this.foldersObject.parentIndex = 0;

          if (this.config.selectedFolderBreadcrumb && this.config.selectedFolderBreadcrumb.length > 1) {
            for (let i = 1; i < this.config.selectedFolderBreadcrumb.length; i++) {
              this.onFolderClick(this.config.selectedFolderBreadcrumb[i].folder, this.config.selectedFolderBreadcrumb[i - 1].folder, i - 1);
            }
          }

          if (
            this.itemsData.some(
              (item) => item.folderId && item.folderId === this.rootFolderId
            )
          ) {
            this.selectActionConfig.disabled = true;
          }
        }, this.handleServerError.bind(this, this.loadFoldersFromApi.bind(this)));
    }
  }

  breadCrumbMenuItemClicked(data) {
    this.fetchFolders(data.folder, data.index);
    this.selectActionConfig.disabled = this.checkIfPresentInSelectedItems(
      data.folder.id,
      data.folder.parent
    );
  }

  onFolderClick(folder, parent, parentIndex) {
    this.selectActionConfig.disabled = this.checkIfPresentInSelectedItems(
      folder.id,
      folder
    );

    this.foldersObject.childrenData.some((item) => {
      if (item.id === folder.id) {
        if (item.childFolders) {
          this.breadCrumbData.push({
            folder,
            parent: parent,
            index: parentIndex + 1,
            children: item.childFolders,
          });
          this.foldersObject.parent = folder;
          this.foldersObject.childrenData = item.childFolders;
          this.foldersObject.parentIndex = parentIndex + 1;
        } else {
          this.breadCrumbData.push({
            folder,
            parent: parent,
            index: parentIndex + 1,
            children: null,
          });
          this.foldersObject.parent = folder;
          this.foldersObject.childrenData = [];
          this.foldersObject.parentIndex = parentIndex + 1;
        }

        return true;
      }
    });
  }

  fetchFolders(folder, index) {
    this.breadCrumbData.splice(index + 1);
    this.foldersObject.parent = folder;
    this.foldersObject.childrenData = this.breadCrumbData[index].children
      ? this.breadCrumbData[index].children
      : null;
    this.foldersObject.parentIndex = index;
    this.selectActionConfig.disabled = this.checkIfPresentInSelectedItems(
      folder.id,
      folder
    );
  }

  checkIfPresentInSelectedItems(folderId, parent) {
    if (
      this.config.showSelectedItemDetails &&
      (this.itemsData.length === 0 ||
        this.itemsData.some(
          (item) => item.folderId && item.folderId === parent.id
        ))
    ) {
      // when application items are being moved
      this.selectActionConfig.disabled = true;
      return true;
    }
    // else if (
    //   !this.config.showSelectedItemDetails &&
    //   folderId === this.rootFolderId
    // ) {
    //   this.selectActionConfig.disabled = true;
    //   return true;
    // } 
    else if (
      this.itemType === "folders" &&
      this.itemsData.some((item) => item.id === folderId)
    ) {
      // when folders are being moved
      this.selectActionConfig.disabled = true;
      return true;
    } else {
      return false;
    }
  }

  removeItem(data) {
    this.itemsData.some((item, index) => {
      if (item.id === data.id) {
        this.itemsData.splice(index, 1);
        return true;
      }
    });
    if (this.config.showSelectedItemDetails && this.itemsData.length === 0) {
      this.selectActionConfig.disabled = true;
    }
  }

  cancelAction() {
    this.cancleAction.emit();
  }

  selectAction() {
    this.folderSelected.emit({
      breadCrumbData: this.breadCrumbData,
      selectedItems: this.itemsData,
    });
  }

  /**
   * In case error from server this function will be called
   * param error
   */
  private handleServerError(callback, error) {
    if (error.status === 401 || error.status === 403) {
      if (this.loginAttempts < this.MAX_LOGIN_ATTEMPTS) {
        // we have to a login again
        this.reLogin(callback);
      } else {
        this.notificationService.show({
          message: this.config.translations.unableToFetchData,
          type: "error",
          close: true,
          autoHide: 6000,
        });
        this.cancelAction();
      }
    } else {
      // todo handle other errors
    }
  }

  /**
   * In case there is a un auth error we can do a  relogin to get the new token
   */
  private reLogin(callbackFunction: any): void {
    this.loginAttempts++;
    // check if we have a relogin method
    if (this.config.reLogin) {
      this.config.reLogin(this.reLoginSuccess.bind(this, callbackFunction));
    } else {
      this.notificationService.show({
        message: this.config.translations.unableToFetchData,
        type: "error",
        close: true,
        autoHide: 6000,
      });
    }
  }

  /**
   * called when the relogin is successful from the caller
   */
  public reLoginSuccess(callback): void {
    if (callback) {
      callback();
    }
  }
  ngOnDestroy(): void {
    this.foldersService.resetFolderData();
  }
}
