:host {
  display: block;
  line-height: 0;
}
.content-connector-container {
  padding: 10px 25px;
  box-sizing: border-box;
  font-size: 15px;
  .cc-parent {
    width: 100%;
    .external-content-details {
      width:100%;
      text-align: center;
      line-height: normal;
      display: flex;
      justify-content: center;
      flex-direction: row;
      div:nth-child(1) {
        padding: 10px 5px;
      }
      div:nth-child(2) {
        overflow: hidden;
        text-decoration: none;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 400px;
        padding: 10px 5px;
        background: #f1f1f1;
        border-radius: 3px;
      }
    }
    .external-content-label {
      width:100%;
      height: 50px;
      text-align: center;
      display: block;
      padding-top: 25px;
    }
  }
}