import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { UnsubscribePreferencesComponent } from './unsubscribe-preferences.component';

describe('UnsubscribePreferencesComponent', () => {
  let component: UnsubscribePreferencesComponent;
  let fixture: ComponentFixture<UnsubscribePreferencesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
    declarations: [UnsubscribePreferencesComponent],
    teardown: { destroyAfterEach: false }
})
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UnsubscribePreferencesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
