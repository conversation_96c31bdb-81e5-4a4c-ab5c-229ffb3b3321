const mjml2html = require('mjml');
const express = require('express');
const options = {};
const app = express();
app.use(express.json({limit: '50mb'}));
app.post('/mjml/v1/toHtml', function (req, res) {
  if (req && req.body && req.body.mjml) {
    try {
      const convertedHTML = mjml2html(req.body.mjml, options).html;
      res.status(200).send({ "html": convertedHTML});
    } catch (error) {
      res.status(400).send("Invalid MJML");
    }
  } else {
    res.status(400).send("Empty MJML");
  }
});
app.use(function (req, res) {
  res.sendStatus(404);
});
const commandLineArguments = process.argv.slice(2);
let host = 'localhost';
let port = 3333;
if (commandLineArguments[0]) {
  host = commandLineArguments[0];
  if (commandLineArguments[1]) {
    port = commandLineArguments[1];
  }
}
const server = app.listen(port, host, function () {
  console.log("MJML to HTML Server listening on http://%s:%s", host, port)
})