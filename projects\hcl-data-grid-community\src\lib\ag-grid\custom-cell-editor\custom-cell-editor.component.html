<ng-container [ngSwitch]="columnDef.cellEditorParams.type">
    <hcl-popup-menu *ngSwitchCase="'text'" [config]="configPopUp" popupMenuTemplate="menutemplate" #cellPopupMenu>
        <ng-template hclTemplate hclTemplateName="menutemplate" type="menutemplate">
            <div (click)="$event.stopPropagation();" class="outerPadding" (keyup.enter)="closeMenu($event)">
                <hcl-input [config]="columnDef.cellEditorParams.conf.inputConf" (inputBlured)="inputBlured($event)">
                </hcl-input>
            </div>
        </ng-template>
    </hcl-popup-menu>
    <hcl-drop-down *ngSwitchCase="'select'" [config]="columnDef.cellEditorParams.conf.selectConf"
        (select)="onSelect($event)">
    </hcl-drop-down>
    <hcl-date-picker *ngSwitchCase="'date'" [localeJson]="columnDef.cellEditorParams.conf.localeConfig"
        [config]="columnDef.cellEditorParams.conf.dateConfig" (onSubmitWithoutSelectionMode)="dateSelected($event)"
        (onSubmitWithSelectionMode)="dateSelected($event)">
    </hcl-date-picker>
    <hcl-multi-select *ngSwitchCase="'multiSelect'" [config]="columnDef.cellEditorParams.conf.multiSelectConfig"
        (togglePanel)="togglePanel($event)">
    </hcl-multi-select>
    <hcl-auto-complete *ngSwitchCase="'autoComplete'" class="hcl-auto-complete w-100"
        [config]="columnDef.cellEditorParams.conf.autoCompleteConfig" (optClick)="searchInAutoComplete($event)">
    </hcl-auto-complete>
    <hcl-time-picker *ngSwitchCase="'time'" [config]="columnDef.cellEditorParams.conf.timePickerConfig"
        (closed)="pickerClosed($event)"></hcl-time-picker>
    <hcl-auto-complete-tree *ngSwitchCase="'autoCompleteTree'"
        [config]="columnDef.cellEditorParams.conf.autoCompleteTreeConfig.config"
        [treeConfig]="columnDef.cellEditorParams.conf.autoCompleteTreeConfig.treeConfig"
        [inputConfig]="columnDef.cellEditorParams.conf.autoCompleteTreeConfig.inputConfig"
        (menuClosed)="treeMenuClosed($event)"></hcl-auto-complete-tree>

    <ng-container *ngSwitchCase="'dynamic'">
        <hcl-popup-menu *ngIf="getDynamicType() === 'text' || getDynamicType() === 'number'" [config]="configPopUp"
            popupMenuTemplate="menutemplate" #cellPopupMenu>
            <ng-template hclTemplate hclTemplateName="menutemplate" type="menutemplate">
                <div (click)="$event.stopPropagation();" class="outerPadding" (keyup.enter)="closeMenu($event)">
                    <hcl-input [config]="columnDef.cellEditorParams.conf.inputConf" (inputBlured)="inputBlured($event)">
                    </hcl-input>
                </div>
            </ng-template>
        </hcl-popup-menu>

        <hcl-drop-down *ngIf="getDynamicType() === 'select'" [config]="columnDef.cellEditorParams.conf.selectConf"
            (select)="onSelect($event)">
        </hcl-drop-down>
        <hcl-date-picker *ngIf="getDynamicType() === 'date'" [localeJson]="columnDef.cellEditorParams.conf.localeConfig"
            [config]="columnDef.cellEditorParams.conf.dateConfig" (onSubmitWithoutSelectionMode)="dateSelected($event)"
            (onSubmitWithSelectionMode)="dateSelected($event)">
        </hcl-date-picker>
    </ng-container>
</ng-container>