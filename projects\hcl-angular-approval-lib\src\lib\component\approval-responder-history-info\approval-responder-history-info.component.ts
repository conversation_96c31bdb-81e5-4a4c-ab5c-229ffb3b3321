import { Component, OnInit, OnDestroy, Input, ViewEncapsulation, OnChanges } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { Approval } from '../../models/approval';
import { DataGridConf, DataGridColumnConf } from 'hcl-data-grid-lib';
import { TranslateService } from '@ngx-translate/core';
import { DatePipe } from '@angular/common';
import { UntypedFormControl } from '@angular/forms';
import { ApprovalSharedDataService } from '../../service/approval-shared-data.service';

@Component({
  selector: 'hcl-approval-responder-history-info',
  templateUrl: './approval-responder-history-info.component.html',
  styleUrls: ['./approval-responder-history-info.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ApprovalResponderHistoryInfoComponent implements OnInit, OnDestroy, OnChanges {

  @Input() approval: Approval;
  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  responseStatusColumns: DataGridColumnConf[] = [];
  customizedGridApi: any;
  responseStatusConf: DataGridConf = {
    rowHeight: 34,
    columns: [],
    noDataFlag: false,
    noRowsTemplate: this._translate.instant('APPROVALPICKER.TITLES.NO_ROWS_TO_SHOW'),
    data: []
  };
  requiredConfig = {
    type: 'single',
    formControl: new UntypedFormControl(),
    singleCheckboxData: {
      value: '',
      disabled: false
    }
  };
  historyList: any[] = [];

  constructor(private _translate: TranslateService,
    private _datePipe: DatePipe,
    private _sharedDataService: ApprovalSharedDataService) {
    this.initGridColumns();
  }

  ngOnChanges() {
    if (this.approval.approvalUsers) {
      this.populateResponseStatusData();
    }
  }

  ngOnInit(): void {
    this.subscriptionList.push(this._sharedDataService.getApprovalResponseHistory().subscribe((res: any[]) => this.populateResponseHistory(res)));
  }

  private initGridColumns() {
    this.responseStatusColumns = [
      {
        field: 'approvername',
        header: this._translate.instant('APPROVALPICKER.TITLES.APPROVERNAME'),
        colId: 'approvername',
        rendererTemplateName: 'nameCell',
        suppressColumnReorder: true,
        autoResizeToFit: true,
        minWidth: 210
      },
      {
        field: 'statusCode',
        header: this._translate.instant('APPROVALPICKER.TITLES.STATUS'),
        colId: 'status',
        rendererTemplateName: 'statusCell',
        suppressColumnReorder: true,
        autoResizeToFit: true,
        minWidth: 350
      },
      {
        field: 'required',
        header: this._translate.instant('APPROVALPICKER.TITLES.REQUIRED'),
        colId: 'approverreq',
        rendererTemplateName: 'requiredCell',
        suppressColumnReorder: true,
        autoResizeToFit: true,
        minWidth: 200
      },
      {
        field: 'instructions',
        header: this._translate.instant('APPROVALPICKER.TITLES.INSTRUCTIONS'),
        colId: 'instructions',
        rendererTemplateName: 'instructionCell',
        suppressColumnReorder: true,
        autoResizeToFit: true,
        minWidth: 250
      }
    ];

    this.responseStatusConf.columns = this.responseStatusColumns;
  }

  private populateResponseHistory(data) {
    this.historyList = [];
    data.forEach((element: any) => {
      if (element.curState === 'APPROVED_WCHANGES' ||
        element.curState === 'DENIED' ||
        element.curState === 'APPROVED') {
        this.historyList.push(element);
      }
    });
  }

  private populateResponseStatusData() {
    this.responseStatusConf.data = [...this.approval.approvers];
    if (this.customizedGridApi) {
      this.customizedGridApi.setRowData(this.responseStatusConf.data);
    }
  }

  /**
  * A callback that will be called when the grid is ready and loaded
  * @param data
  */
  onGridReady(data) {
    this.customizedGridApi = data.params.api;
    if (this.customizedGridApi) {
      this.customizedGridApi.setRowData(this.responseStatusConf.data);
    }
  }

  /**
   * This function prints the approvers for current approval
   * @param data
   */
  printApprovers(data): string {
    let approvers = '';
    if (data.user && data.team) {
      approvers += data.team.name + '-->' + data.user.nameWithTimeZone + ', ';
    } else if (data.user) {
      approvers += data.user.nameWithTimeZone + ', ';
    } else if (data.team) {
      approvers += data.team.name + ', ';
    }
    return approvers.slice(0, approvers.length - 2);
  }

  printStatus(data) {
    let responseStatusCode = '';
    this.subscriptionList.push(this._translate.stream('APPROVALPICKER').subscribe(
      (value) => {
        responseStatusCode = value.RESPONSE_STATUS_VALUES[data];
      }
    ));
    return responseStatusCode;
  }

  printResponseDate(data) {
    const localDate = this._datePipe.transform(data, 'M/d/YYYY hh:mm:ss a');
    const dateObj = localDate.toString();
    const date = dateObj.substr(0, dateObj.indexOf(' '));
    const time = dateObj.substr(dateObj.indexOf(' ') + 1);
    let dateString: string = '';
    this.subscriptionList.push(this._translate.stream('APPROVALPICKER').subscribe(
      (value) => {
        dateString = ` ${value.TITLES.ON} ${date} ${value.TITLES.AT} ${time}`;
      }
    ));
    return dateString;
  }

  /**
   * set the config for checkbox in required column
   * @param data current cell data of required column
   */
  setRequiredVal(data): any {
    let checked: boolean = false;
    if (data.required === '1') {
      checked = true;
    }
    this.requiredConfig.formControl.setValue(checked);
    this.requiredConfig.singleCheckboxData.value = data.required;
    return this.requiredConfig;
  }

  /**
  * Returns the css class according to status of history record
  * @param status current status of history record
  */
  getRecordStatusClass(status: string): string {
    switch (status) {
      case 'APPROVED_WCHANGES':
      case 'APPROVED':
        return 'record-status-approved';
      case 'DENIED':
        return 'record-status-denied';
    }
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => {
      sub.unsubscribe();
    });
  }

}
