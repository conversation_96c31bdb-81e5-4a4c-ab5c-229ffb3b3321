<section class="metadata">
    <div class="form-field">
        <span class="ro-label" hclTooltip="{{translations.offerName}}"
            [attr.data-position]="'bottom-top-start'">{{translations.offerName}}</span>
        <span class="ro-value" hclTooltip="{{offerData.displayName}}"
            [attr.data-position]="'bottom-top-start'">{{offerData.displayName}}</span>
    </div>
    <div class="form-field">
        <span class="ro-label" hclTooltip="{{translations.securityPolicy}}"
            [attr.data-position]="'bottom-top-start'">{{translations.securityPolicy}}</span>
        <span class="ro-value" hclTooltip="{{offerData.policyName}}"
            [attr.data-position]="'bottom-top-start'">{{offerData.policyName}}</span>
    </div>
    <div class="form-field">
        <span class="ro-label" hclTooltip="{{translations.offerCode}}"
            [attr.data-position]="'bottom-top-start'">{{translations.offerCode}}</span>
        <span class="ro-value" hclTooltip="{{offerData.offerCodes}}"
            [attr.data-position]="'bottom-top-start'">{{offerData.concatOfferCodes}}</span>
    </div>
    <div class="description">
        <span [ngClass]="{'multi-line' : descValueEl.offsetHeight >= 32}" class="ro-label"
            hclTooltip="{{translations.description}}"
            [attr.data-position]="'bottom-top-start'">{{translations.description}}</span>
        <span #descValueEl [ngClass]="{'desc-ellipsis': descValueEl.offsetHeight >= 33}" class="ro-value"
            hclTooltip="{{offerData.description}}"
            [attr.data-position]="'bottom-top-start'">{{offerData.description}}</span>
    </div>
</section>