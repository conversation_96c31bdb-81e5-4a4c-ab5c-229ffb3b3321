import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ButtonConf } from 'hcl-angular-widgets-lib';
import { SubscriptionLike } from 'rxjs';
import { OfferDataService } from '../../offer-data.service';
import { SelectOffersService } from '../../select-offers.service';
import { SelectOffersConstant } from '../select-offers.constant';
import { OfferList, OfferListGetResponse } from '../select-offers.model';

@Component({
  selector: 'hcl-offerlist-card-view',
  templateUrl: './offerlist-card-view.component.html',
  styleUrls: ['./offerlist-card-view.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class OfferlistCardViewComponent implements OnInit, OnDestroy, OnChanges {

  subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  @Output() offerListSelectionUpdate: EventEmitter<any> = new EventEmitter<any>();
  @Input() selectedOfferlistList: any[] = [];
  @Input() offerlistSortState: string;
  @Input() isSummaryPage: number;
  @Input() folderPanelState: number;


  isDataLoading = false;
  offerlistResponseData: any;
  defaultFolderId: number;

  pageSize: number = 24;
  chunkSize: number;
  lpContainerWidth: number;
  scrollContainer: number;


  offerlistFilterState: string = 'all';
  noRecordsFound: boolean = false;
  folderWithoutOfferlists: boolean = false;
  prevOfferlistResponseDataContent: any[];
  skippedFirstFilterEmit = false;
  manageSelectionsButtonConf: ButtonConf;
  parentContainerWidth: number;

  constructor(public translate: TranslateService,
    private offerDataService: OfferDataService,
    public selectOffersService: SelectOffersService
  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['offerlistSortState']?.previousValue !== undefined) {
      this.loadOfferlists(0, this.selectOffersService.offerListGlobalSearchData);
    }

    if (changes['folderPanelState']?.previousValue !== undefined && changes['folderPanelState']?.currentValue !== 3) {
      this.setUpOfferListRowItemChunkSize();
      this.loadOfferlists(0);
    }
  }


  ngOnInit(): void {
    if (!this.isSummaryPage) {
      this.defaultFolderId = this.selectOffersService.offersFolder;

      this.loadOfferlists(0);

      this.subscriptionList.push(this.selectOffersService.getFolderId().subscribe(obj => {
        if (obj[0] === 'offerlists') {
          this.defaultFolderId = obj[1];
          if (this.selectOffersService.lastFolderClicked === null || this.selectOffersService.lastFolderClicked !== this.defaultFolderId) {
            this.selectOffersService.lastFolderClicked = this.defaultFolderId;
          }
          this.selectOffersService.clearFolderId();
        }
      }));

      this.subscriptionList.push(this.selectOffersService.getOfferListsSearchData().subscribe(obj => {
        if (obj.length > 0) {
          this.loadOfferlists(0, obj[0]);
          this.selectOffersService.clearOfferListsSearchData();
        }

        this.subscriptionList.push(this.selectOffersService.olTypeObservable.subscribe(() => {
          if (this.skippedFirstFilterEmit) {
            this.loadOfferlists(0, this.selectOffersService.offerListGlobalSearchData);
          } else {
            this.skippedFirstFilterEmit = true;
          }
        }));


      }));
      
    } else {
      this.loadOfferlists(0);
    }

    this.manageSelectionsButtonConf = {
      color: 'accent',
      buttonType: 'mat',
      type: 'button',
      value: this.translate.instant('OFFER_PICKER.TITLES.MANAGE_SELECTIONS'),
      name: 'removeMapping',
      disabled: false
    };

    this.lpContainerWidth = document.getElementsByClassName('offers-container')[0]?.clientWidth;
      // this.resultContainerWidth = document.getElementsByClassName('result')[0]?.clientWidth;
    this.setUpOfferListRowItemChunkSize(); 

  }

  setUpOfferListRowItemChunkSize() {  
    if (this.folderPanelState === 2) {
       const folderElementWidth = this.lpContainerWidth * 35 / 100;
       const containerWidth: number = this.lpContainerWidth - (folderElementWidth - 15); // 15 => substraction of result item container left padding 30px and folder panel right margin 15
       this.chunkSize = Math.floor(containerWidth / 276);
       this.pageSize = this.chunkSize * 4;
     } else if (this.folderPanelState === 1) {
       const containerWidth: number = this.lpContainerWidth - 100; // 100 => addition of sidebar 50px, sidebar right margin 15px and result item container left padding 30px and additional 5px for safety
       this.chunkSize = Math.floor(containerWidth / 276);
       this.pageSize = this.chunkSize * 4;
     }    
 }

  loadNewOfferlists(page: number) {
    this.loadOfferlists(page, this.selectOffersService.offerListGlobalSearchData);
  }

  loadOfferlists(page: number, searchParam?: string) {
    this.isDataLoading = true;

    if (this.isSummaryPage) {
      this.isDataLoading = false;
      if (this.offerDataService.offers.length) {
        this.noRecordsFound = false;
        const tempList = this.offerDataService.offers.map((cont: any) => ({ ...cont, isImageLoaded: false }))
        let i: number, j: number, temporary: OfferList[][] = [];
        for (i = 0, j = tempList.length; i < j; i += this.chunkSize) {
          temporary.push(tempList.slice(i, i + this.chunkSize));
        }

        this.offerlistResponseData = {
          page:
            { hasNext: false, hasPrev: false, pageNumber: 0, size: this.pageSize, totalElements: this.offerDataService.offers.length, totalPages: 1 },
          content: temporary
        }
      } else {
          this.noRecordsFound = true;
      }


    } else {
      const filter = this.selectOffersService.offerAndOfferListState;
      searchParam = searchParam ? encodeURI(searchParam) : '';
      this.subscriptionList.push(
        this.selectOffersService.getOfferLists(this.defaultFolderId, page, this.pageSize,
          true, this.offerlistSortState, filter, searchParam).subscribe((offerlistData: OfferListGetResponse) => {
            this.isDataLoading = false;
            if (offerlistData.content.length) {
              this.noRecordsFound = false;
              this.folderWithoutOfferlists = false;

              const tempList = offerlistData.content.map((cont: OfferList) => ({ ...cont, isImageLoaded: false ,
                isSelected: this.selectOffersService.selectedOlData.find(olData => olData.offerListId === cont.id)}));
              let i: number, j: number, temporary: OfferList[][] = [];
              for (i = 0, j = tempList.length; i < j; i += this.chunkSize) {
                temporary.push(tempList.slice(i, i + this.chunkSize));
              }

              if (!page) {
                this.offerlistResponseData = { page: offerlistData.page, content: temporary }
              } else {
                this.prevOfferlistResponseDataContent = [...this.offerlistResponseData.content];
                this.offerlistResponseData.page.pageNumber = page;
                this.offerlistResponseData.content = [...this.offerlistResponseData.content, ...temporary];
              }

              // if (!page) {
              //   this.offerlistResponseData = {
              //     page: offerlistData.page, content: offerlistData.content.map((cont: OfferList) =>
              //     ({
              //       ...cont, isImageLoaded: false,
              //       isSelected: this.selectOffersService.selectedOlData.find(olData => olData.offerListId === cont.id)
              //     }))
              //   };
              // } else {
              //   this.prevOfferlistResponseDataContent = [...this.offerlistResponseData.content];
              //   this.offerlistResponseData.page.pageNumber = page;
              //   this.offerlistResponseData.content = [...this.offerlistResponseData.content,
              //   ...offerlistData.content.map((cont: OfferList) => ({
              //     ...cont, isImageLoaded: false,
              //     isSelected: this.selectOffersService.selectedOlData.find(olData => olData.offerListId === cont.id)
              //   }))];
              // }

              this.subscriptionList.push(this.selectOffersService.getBulkOfferListOffersThumbnail(3,
                offerlistData.content.map(olItem => olItem.id)).subscribe((offerlistThumbnailData: any) => {
                  offerlistData.content.forEach((offerListItem: any) => {
                    offerListItem.count = offerlistThumbnailData[offerListItem.id].count;
                    offerListItem.offers = offerlistThumbnailData[offerListItem.id].offers.map(offer =>
                      ({ thumbnailProperties: offer.thumbnailProperties }));
                  });

                  const tempList = offerlistData.content.map((cont: OfferList) => ({ ...cont, isImageLoaded: false ,
                    isSelected: this.selectOffersService.selectedOlData.find(olData => olData.offerListId === cont.id)}));
                  let i: number, j: number, temporary: OfferList[][] = [];
                  for (i = 0, j = tempList.length; i < j; i += this.chunkSize) {
                    temporary.push(tempList.slice(i, i + this.chunkSize));
                  }
                  if (!page) {
                    this.offerlistResponseData = { page: offerlistData.page, content: temporary }
                  } else {
                    this.prevOfferlistResponseDataContent = [...this.offerlistResponseData.content];
                this.offerlistResponseData.page.pageNumber = page;
                this.offerlistResponseData.content = [...this.offerlistResponseData.content, ...temporary];
                  }
                }));
            } else {
              this.offerlistResponseData = {};
              if (searchParam) {
                this.noRecordsFound = true;
                this.folderWithoutOfferlists = false;
              } else {
                this.folderWithoutOfferlists = true;
                this.noRecordsFound = false;
              }
            }
          })
      );
    }
  }


  offerlistSelected() {
    this.offerListSelectionUpdate.emit(this.selectedOfferlistList);
  }


  selectedOffersAndOls() {
    if (this.selectOffersService.loadOfferOrOfferLists === 'both') {
      return `${this.selectOffersService.selectedOffersData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')} |
      ${this.selectOffersService.selectedOlData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS')}
      ${this.translate.instant('FIELDS.SELECTED')}`;
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offerLists') {
      return `${this.selectOffersService.selectedOlData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS')}
      ${this.translate.instant('FIELDS.SELECTED')}`;
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offers') {
      return `${this.selectOffersService.selectedOffersData.length} ${this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')}
      ${this.translate.instant('FIELDS.SELECTED')}`;
    }
  }

  manageSelections(event) {
    this.selectOffersService.sendloadNext('manageOffers');
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => { sub.unsubscribe(); });
  }

}
