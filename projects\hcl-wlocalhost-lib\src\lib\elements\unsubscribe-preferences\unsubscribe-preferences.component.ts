import {
  Component,
  Input,
  HostBinding,
  ChangeDetectorRef, OnInit
} from '@angular/core';
import { PreferencesUnsubscribeBlock } from '../../classes/Elements';
import {
  createFont,
  createPadding,
  createBorder,
  createLineHeight,
  createWidthHeight,
  createBackground,
} from '../../utils';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'ip-unsubscribe-preferences',
  templateUrl: './unsubscribe-preferences.component.html',
  styleUrls: ['./unsubscribe-preferences.component.css']
})
export class UnsubscribePreferencesComponent implements OnInit {
  @Input() block: PreferencesUnsubscribeBlock = new PreferencesUnsubscribeBlock(false, false, this.translate.instant('labels.Browser'), this.translate.instant('settings.open-as-webpage'));

  constructor(
    private chRef: ChangeDetectorRef,
    public ngb: IpEmailBuilderService,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    // if (this.block.options && this.block.options.link && !this.block.options.link.id) {
    //   // we need to create a Random Id
    //   this.block.options.link.id = this.ngb.generateId(5);
    // }
  }
  getWebpageStyles() {
    const {
      background,
      border,
      color,
      font,
      lineHeight,
      padding,
      fullWidth
    } = this.block.options;

    return {
      color,
      'text-decoration': 'revert',
      width: fullWidth ? '100%' : 'auto',
      display: 'block',
      ...createFont(font),
      ...createBorder(border),
      ...createLineHeight(lineHeight),
      ...createBackground(background)
    };
  }

  getFullWidth() {
    const {
      fullWidth
    } = this.block.options;

    return {
      width: fullWidth ? '100%' : 'auto',
    };
  }

  getParentStyles() {
    const { align } = this.block.options;

    return {
      justifyContent:
        (align === 'center' && 'center') ||
        (align === 'right' && 'flex-end') ||
        'flex-start'
    };
  }

  fetchComponentErrorMessages() {
    const errorMessages = new Set();
    for (let iErrorCounter = 0; iErrorCounter < this.block.errors.length; iErrorCounter++) {
      errorMessages.add(this.translate.instant('messages.'+this.block.errors[iErrorCounter].key+'-collated'))
    }
    return `${Array.from(errorMessages).join(`\n\r`)}`;
  }
}
