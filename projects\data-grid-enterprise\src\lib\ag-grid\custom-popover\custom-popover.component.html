<div  *ngIf="showPopover" class="popoverContainer">
  <div class="triangle"></div>
  <div class="popoverContent">
    <ng-container *ngTemplateOutlet="customPopoverTemplate ? customPopoverTemplate : defaultPopover">
    </ng-container>
    <ng-template #defaultPopover>
      <div class="popoverHeader">
        <ul>
          <li>Filter</li>
        </ul>
      <!--  <mat-tab-group>
          <mat-tab label="First"> Filter </mat-tab>
        </mat-tab-group> -->
      </div>
      <div class="popoverContentDetails">
        <mat-form-field class="searchContact">
          <input type="search" matInput placeholder="Search In Contact List">
        </mat-form-field>
      </div>
      <div class="popoverButton">
        <button mat-button color="primary" class="filterBtn">Clear Filter</button>
        <button mat-button color="primary" class="filterBtn apply">Apply</button>
      </div>
    </ng-template>    
  </div>
</div>
