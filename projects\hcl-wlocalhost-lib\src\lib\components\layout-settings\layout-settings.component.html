<div class="settings-container">
  <mat-accordion>

    <mat-expansion-panel>
      <mat-expansion-panel-header>
        {{ 'settings.background' | translate }}
      </mat-expansion-panel-header>
      <ng-template matExpansionPanelContent>
        <ip-color [model]="(layout | async).options.background" label="{{ 'settings.color' | translate }}"></ip-color>
        <ip-image-upload [block]="(layout | async).options.background" key="url" style="margin-bottom: 1em"
          (uploadImage)="uploadImageFromPicker()"></ip-image-upload>
        <ip-back-repeat [disabled]="!(layout | async).options.background.url" [model]="(layout | async).options.background">
        </ip-back-repeat>
        <ip-width-height [disabled]="!(layout | async).options.background.url"
          [model]="(layout | async).options.background.size" label="{{ 'general.size' | translate }}"></ip-width-height>
      </ng-template>
    </mat-expansion-panel>

    <mat-expansion-panel>
      <mat-expansion-panel-header>
        {{ 'settings.border' | translate }}
      </mat-expansion-panel-header>
      <ng-template matExpansionPanelContent>
        <ip-border [border]="(layout | async).options.border"></ip-border>
      </ng-template>
    </mat-expansion-panel>

    <mat-expansion-panel>
      <mat-expansion-panel-header>
        {{ 'settings.padding' | translate }}
      </mat-expansion-panel-header>
      <ng-template matExpansionPanelContent>
        <ip-padding [padding]="(layout | async).options.padding"></ip-padding>
      </ng-template>
    </mat-expansion-panel>

    <mat-expansion-panel>
      <mat-expansion-panel-header>
        {{ 'settings.margin' | translate }}
      </mat-expansion-panel-header>
      <ng-template matExpansionPanelContent>
        <ip-margin [margin]="(layout | async).options.margin"></ip-margin>
      </ng-template>
    </mat-expansion-panel>

    <mat-expansion-panel>
      <mat-expansion-panel-header>
        {{ 'settings.layout' | translate }}
      </mat-expansion-panel-header>
      <ng-template matExpansionPanelContent>
        <ip-gaps [gaps]="(layout | async).options.gaps"></ip-gaps>
        <!--<mat-slide-toggle [disabled]="(layout | async).type === 'cols_1'" style="margin-top: -24px;"
          [(ngModel)]="disableResponsive">
          {{ 'settings.no-responsive' | translate }}
        </mat-slide-toggle>-->
      </ng-template>
    </mat-expansion-panel>

    <mat-expansion-panel *ngFor="let column of (layout | async).options.columns; let key = index">
      <mat-expansion-panel-header>
        {{ 'settings.column' | translate }} {{ key + 1 }}
      </mat-expansion-panel-header>
      <ng-template matExpansionPanelContent>
        <h6 class="sec-subhead"><span>{{ 'settings.background' | translate }}</span></h6>
        <ip-color [model]="column.background" label="Color"></ip-color>
        <h6 class="sec-subhead"><span>{{ 'settings.border' | translate }}</span></h6>
        <ip-border [border]="column.border"></ip-border>
        <h6 class="sec-subhead"><span>{{ 'settings.vertical-align' | translate }}</span></h6>
        <ip-align [model]="column" mode="vertical"></ip-align>
      </ng-template>
    </mat-expansion-panel>
    <mat-expansion-panel *ngIf="(layout | async).options.columns.length > 1">
      <mat-expansion-panel-header>
        {{ 'settings.stacking' | translate }}
      </mat-expansion-panel-header>
      <ng-template matExpansionPanelContent>
        <hcl-stacking [model]="(layout | async).options"></hcl-stacking>
      </ng-template>
    </mat-expansion-panel>
  </mat-accordion>
  <hcl-hide-on [model]="(layout | async).options"></hcl-hide-on>
</div>