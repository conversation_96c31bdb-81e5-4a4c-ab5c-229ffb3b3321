import * as d3 from 'd3';
import {D3LinearAxisRenderer} from './D3LinearAxisRenderer';
import {D3CategoryAxisRenderer} from './D3CategoryAxisRenderer';

export class D3ReferenceLineRenderer {
  // the actual x line
  private linearAxisGroup: any;
  // in case the group is floatin
  private floatingLinearAxisGroup: any;
  private linearAxisLine: any;
  private linearAxisMarker: any;
  private linearAxisText: any;
  private floatingLinearAxisText: any
  // the actual y line
  private catAxisGroup: any;
  /**
   * the default constructor
   * param referenceNode
   * param {D3LinearAxisRenderer} linearAxisRenderer
   * param {D3CategoryAxisRenderer} categoryAxisRenderer
   */
  constructor(private referenceNode: any,
              private linearAxisRenderer: D3LinearAxisRenderer,
              private categoryAxisRenderer: D3CategoryAxisRenderer,
              private startCoordinates: {x: number, y: number}) {
    // render the linear Axis marker
    this.renderLinearAxisMarker();
    if (this.categoryAxisRenderer != null) {
      this.catAxisGroup = this.referenceNode.append('g').attr('transform', 'translate(0,0)');
      this.catAxisGroup.append('line').attr('class', 'hcl-cat-ref-line');
      // based on the position of the axis we need to append the marker & the text
      if (this.categoryAxisRenderer.getPlacementPosition() === 'left') {
        // position is top, so marker will be at the top
        this.catAxisGroup.append('path')
          .attr('class', 'hcl-cat-ref-marker')
          .attr('d', 'M 10,13 30,13 40,20 30,27 10,27 z');
      }
    }
  }

  /**
   * this function will render the Linear Axis marker
   */
  private renderLinearAxisMarker(): void {
    if (this.linearAxisRenderer != null) {
      const svg: any = d3.select(this.referenceNode.node().parentNode)
      // // based on the position of the axis we need to append the marker & the text
      if (this.linearAxisRenderer.getPlacementPosition() === 'top') {
        const d: {group: any, line: any, marker: any, text: any} = this.createVerticalRefLine(this.referenceNode,
                                                                      parseFloat(svg.attr('height')));
        this.linearAxisGroup = d.group;
        this.linearAxisLine = d.line;
        this.linearAxisMarker = d.marker;
        this.linearAxisText = d.text;
        if (this.linearAxisRenderer.hasFixedAxis()) {
          const f: {group: any, line: any, marker: any, text: any} = this.createVerticalRefLine(this.linearAxisRenderer.floatingAxis,
                                                                  parseFloat(svg.attr('height')));
          this.floatingLinearAxisGroup = f.group;
          this.floatingLinearAxisText = f.text;
        }
      }
    }
  }

  /**
   * this function will draw the vertical ref line
   * param referenceNode
   * returns {{group: any; line: any; marker: any; text: any}}
   */
  private createVerticalRefLine(referenceNode: any, height : number): {group: any, line: any, marker: any, text: any} {
    // lets create the linear & cat line
    const group: any = referenceNode.append('g').attr('transform', 'translate(0,0)');
    const line: any = group.append('line').attr('class', 'hcl-linear-ref-line');
    line.attr('y1', this.linearAxisRenderer.getMargin())
      .attr('x1', this.startCoordinates.x)
      .attr('y2', height)
      .attr('x2', this.startCoordinates.x);
    // position is top, so marker will be at the top
    const marker = group.append('path')
      .attr('transform', 'translate(' + (this.startCoordinates.x) + ',' + (this.linearAxisRenderer.getMargin() - 30) + ')')
      .attr('class', 'hcl-linear-ref-marker')
      .attr('d', 'M -7,0 7,0 7,20 0,30 -7,20 z');
    // need to add the text label
    const text = group.append('text')
      .attr('x', this.startCoordinates.x - 45)
      .attr('class', 'hcl-linear-ref-label')
      .attr('dy', (this.linearAxisRenderer.getMargin() - 35))
      .text(this.linearAxisRenderer.getInvertPosition(this.startCoordinates.x));
    return {
      group : group,
      line: line,
      marker: marker,
      text: text
    };
  }

  /**
   * this function will position the ref line at the coordinates that we have specified
   * param {number} x
   * param {number} y
   */
  public position(x: number, y: number): void {
    // if we do not have the start co-ordinate then this will be our start coordinate
    if (this.linearAxisGroup) {
      if (this.linearAxisRenderer.getPlacementPosition() === 'top') {
        this.linearAxisGroup.attr('transform', 'translate(' + (x - this.startCoordinates.x ) + ', 0)');
        // if we have the floating group we have to update them as wrll
        if(this.floatingLinearAxisGroup) {
          this.floatingLinearAxisGroup.attr('transform', 'translate(' + (x - this.startCoordinates.x ) + ', 0)');
          this.floatingLinearAxisText.text(this.linearAxisRenderer.getInvertPosition(x))
        }
        // get the value associated to this coordinates
        this.linearAxisText.text(this.linearAxisRenderer.getInvertPosition(x));
      }
    }
  }
  /**
   * This function will remove the ref line
   */
  remove(): void {
    if (this.linearAxisGroup) {
      this.linearAxisGroup.remove();
      if(this.floatingLinearAxisGroup) {
        this.floatingLinearAxisGroup.remove();
      }
    }
    if (this.catAxisGroup) {
      this.catAxisGroup.remove();
    }
  }
}
