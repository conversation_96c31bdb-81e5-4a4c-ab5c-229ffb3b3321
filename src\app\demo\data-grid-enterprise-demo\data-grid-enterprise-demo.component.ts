import { Component, OnInit, ViewChild } from '@angular/core';
import {
  DataGridConf, DataGridPagination, HoverIcon, Actions, ColumnSelectionComponent, DataGridV2Component
} from 'hcl-data-grid-lib';
import { ButtonConf } from 'hcl-angular-widgets-lib';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-data-grid-enterprise-demo',
  templateUrl: './data-grid-enterprise-demo.component.html',
  styleUrls: ['./data-grid-enterprise-demo.component.scss']
})
export class DataGridEnterpriseDemoComponent implements OnInit {
  actions?: Actions;
  topRowData?: {}[];
  fullWidthRow: {}[];

  dropObj: {}[];
  conf: DataGridConf;
  selectedRow: any;
  unselectedRow: any;
  selectAllConf: ButtonConf;
  hideRowConf: ButtonConf;
  deSelectAllConf: ButtonConf;
  currentlySelectedRows: any;
  hoverActions: HoverIcon[];
  paginatorConfig: DataGridPagination;
  params: any;
  @ViewChild(DataGridV2Component, { static: true }) ch: DataGridV2Component;
  @ViewChild('addRemoveColumn') addRemoveColumn: ColumnSelectionComponent;

    /**Add new row button config
   */
  addRowBtn: ButtonConf = {
    color: 'accent',
    buttonType: 'stroked',
    value: 'Add Row',
    borderRadius: 5,
    name: 'iconTextButton',
    type: 'button',
    isIconButton: true,
    styleClass: 'refresh-btn',
    icon: 'hcl-icon-add-button'
  };

   /**Add new row button config
   */
  deleteRowBtn: ButtonConf = {
    color: 'accent',
    buttonType: 'stroked',
    value: 'Delete Row',
    borderRadius: 5,
    name: 'iconTextButton',
    type: 'button',
    isIconButton: true,
    styleClass: 'refresh-btn',
    icon: 'hcl-icon-add-button'
  };

  updateRowBtn: ButtonConf = {
    color: 'accent',
    buttonType: 'stroked',
    value: 'Update Row',
    borderRadius: 5,
    name: 'iconTextButton',
    type: 'button',
    isIconButton: true,
    styleClass: 'refresh-btn',
    icon: 'hcl-icon-add-button'
  };


  constructor(private http: HttpClient) { }

  ngOnInit() {
    this.hideRowConf = {
      value: 'Hide  Row',
      buttonType: 'raised',
      color: 'primary',
      borderRadius: 5,
      name: 'hideRow',
      styleClass: 'custom-icon',
      type: 'submit'
    };
    this.selectAllConf = {
      value: 'Select All',
      buttonType: 'raised',
      color: 'primary',
      borderRadius: 5,
      name: 'selectAll',
      styleClass: 'custom-icon',
      type: 'submit'
    };
    this.deSelectAllConf = {
      value: 'Unselect All',
      buttonType: 'raised',
      color: 'primary',
      borderRadius: 5,
      name: 'unSelectAll',
      styleClass: 'custom-icon',
      type: 'submit'
    };
    this.paginatorConfig = {
      rowsPerPage: 10,
      pageSizeArray: [10, 50, 100],
      currentPageIndex: 1,
      rowsPerPageSuffix: 'Files'
    };
    this.hoverActions = [
      {
        tooltip: 'Retire',
        icon: 'hcl-icon-status-off',
        name: 'retire',
        iconClickHandler: this.retireAction
      },
      {
        tooltip: 'Delete',
        icon: 'hcl-icon-delete',
        name: 'delete',
        iconClickHandler: this.deleteAction
      },
      {
        tooltip: 'Edit',
        icon: 'hcl-icon-edit',
        name: 'edit',
        iconClickHandler: this.editAction
      }
    ];
    this.actions = {
      hoverIcons: this.hoverActions
    };

    this.fullWidthRow = [
      {
        name: 'Type or Drag Segment2'
      }
    ];
    this.topRowData = [
      {
        'name': 'Type or Drag Segment1',
        'description': 'Type or Drag Zone1',
        'offerCode': 720001,
        'channels': 'Type or Drag Offer1',
        'expDate': '21-12-20191'
      },
      {
        'name': 'Type or Drag Segment2',
        'description': 'Type or Drag Zone2',
        'offerCode': 350001,
        'channels': 'Type or Drag Offer2',
        'expDate': '21-12-20191',
        'rowTemplateName': 'addrule'
      }
    ];
    this.dropObj = [
      {
        'name': 'Satyam_Name',
        'description': 'Satyam_Desc',
        'offerCode': 35000,
        'channels': 'Red',
        'expDate': '21-12-2019'
      }
    ];

    this.conf = {
      scrollHeight: 400,
      isClientSideRowModel: true,
      data: [],
      suppressRowTransform: true,
      columns: [
        {
          field: 'name',
          header: 'Name',
          colId: 'name',
          disable: true,
          headerRendererTemplateName: 'dadsa',
          // rendererTemplateName: 'entrySourceCellName',
          // popoverTemplateName: 'customPopover',
          // useDefaultRenderer: true,
          // dataFormatter: (attr: any) => {
          //   return this.returnOfferCodes(attr);
          // },
          // tooltip: {
          //   getTooltip: (data) => {
          //     return data.name;
          //   }
          // },
          // sortable: true,
          // checkboxSelection: function (node) {
          //   return node.data ? node.data.name !== 'Toyota' : false;
          // },
          // getQuickFilterText: function (searchText) {
          //   return searchText;
          // }
        },
        {
          field: 'description',
          header: 'Description',
          colId: 'description',
          headerRendererTemplateName: 'dadsa',
          popoverTemplateName: 'dasds',
          rendererTemplateName: 'entrySourceCell',
          sortable: true,
          tooltip: {},
          rowSpan: this.rowSpanFunc.bind(this),
          cellClassRules: { 'show-cell': (params) => !(!params.value) },
          //   rowGroup: true
        },
        {
          field: 'isAssociated',
          header: 'In Use',
          colId: 'isAssociated',
          headerRendererTemplateName: 'dadsa',
          rendererTemplateName: 'inUseCell',
          popoverTemplateName: 'customPopover'
        },
        {
          field: 'createdDate',
          header: 'Created On',
          colId: 'createdDate',
          headerRendererTemplateName: 'dadsa',
          rendererTemplateName: 'createdDateCell',
          popoverTemplateName: 'dasds'
        },
        {
          field: 'lastModifiedDate',
          header: 'Last Modified By',
          colId: 'lastModifiedDate',
          headerRendererTemplateName: 'dadsa',
          rendererTemplateName: 'lastModifiedByCell',
          popoverTemplateName: 'dasds'
        }
      ],
      /*  infiniteScroll: {
          cacheBlockSize: 20,
          rowBuffer: 0
        }, */
      // dataUrl: 'assets/data/cars.json',
      queryParams: { sort: 'name,ASC', test: '123', defaultSort: false, date: new Date(), id: 2 },
      rowSelectMode: 'multiple',
      /*  serverSideScroll: {
          cacheBlockSize: 50,
          rowBuffer: 0
        }, */
      infiniteInitialRowCount: 10,
      pagination: this.paginatorConfig,
      // pagination: null,
      noRowsTemplate: '<span> No data to display</span>',
      actions: this.actions,
      topRowData: this.topRowData,
      fullWidthRow: this.fullWidthRow,
      dropObj: this.dropObj,
      suppressRowClickSelection: true,
      suppressDragLeaveHidesColumns: true,
      dragAndDrop: true,
      rowGroupType: {
        groupUseEntireRow: true,
        groupRemoveSingleChildren: true,
        groupDefaultExpanded: 0,
        groupRowInnerRenderer: true,
        groupRowRendererParams: {
          rowDescription: {
            label: 'Total ## rules under this'
          },
        }
      },
      rowClassRules: {
        'row-disabled': function (node) {
          return node.data && node.data.name === 'Maruti';
        },
        'bck-grd-grey': function (node) {
          return node.data && ['Type or Drag Segment1', 'Type or Drag Segment2'].indexOf(node.data.name) >= 0;
        },
        'bdr-bottom': function (node) {
          return node.data && ['Type or Drag Segment2'].indexOf(node.data.name) >= 0;
        },
      },
      isExternalFilterPresent() {
        return true;
      },
      doesExternalFilterPass(node) {
        if (node.data.name === 'Type or Drag Segment1') {
          return false;
        } else {
          return true;
        }
      },
      isRowSelectable: function (rowNode: any) {
        return rowNode.data ? rowNode.data.description !== 'Coupe' : false;
      }
    };
    this.http.get('assets/data/cars.json').subscribe((res: any) => {
      console.log('cars res', res);
      const nameLinkArr = [];
      const sampleObj = {}
      const mockRes: any = {};
      mockRes.page = res.page;
      mockRes.content = [];
      res.content.forEach(element => {
        mockRes.content.push({
          'name': element.name[0].link,
          'description': element.description,
          'offerCode': element.description,
          'channels': element.description,
          'expDate': element.description,
          'nameLength': element.name.length
        });
        for (let i = 1; i < element.name.length; i++) {
          mockRes.content.push({
            'name': element.name[i].link,
            'description': '',
            'offerCode': '',
            'channels': '',
            'expDate': ''
          });
        }
      });
      this.ch.setData(mockRes);
    });
  }

  rowSpanFunc(params) {
    console.log('in row span', params);

    if (!params.data.description) {
      return 0;
    } else {
      return params.data.nameLength;
    }
  }

  // returnOfferCodes(attr) {
  //   const rowData = attr.data;
  //   const rowNode = attr.node;
  //   let x = '';
  //   let height;
  //   if (rowData && rowData.name && rowData.name.linkClicks && rowData.name.linkClicks.length > 0) {
  //     console.log('height', rowData);
  //     height = rowData.name.linkClicks.length * 48;
  //     rowNode.rowHeight = height;
  //     // console.log('row', row);
  //     this.conf.adjustedHeightArr.push(height);
  //     rowData.name.forEach(element => {
  //       x += `${element.link}<br>${element.uniqueClicks}`;
  //     });
  //     console.log('row', rowData);
  //     return x;
  //   }
  // }

  hideRows(event) {
    this.params.api.onFilterChanged();
  }

  deleteAction(deletedObj: any) {
    console.log('deleted->', deletedObj);
  }

  onFilterTextBoxChanged(searchText) {
    searchText = 'mar';
    this.params.api.setQuickFilter(searchText);
  }

  editAction(editedObj: any) {
    console.log('edited->', editedObj);
  }

  parseDate(date: number) {
    return (new Date());
  }

  retireAction(retiredObj: any) {
    console.log('retired->', retiredObj);
  }
  /**
  * Gets the row which is selected in current selection,
  * and also gets all the rows which are selected till now
  **/
  rowSelected(data: any) {
    this.selectedRow = data.data;
    this.currentlySelectedRows = data.gridApi.getSelectedNodes();
  }
  /**
  * Gets the row which is unselected in current unselection,
  * and also gets all the rows which are still in selected
  **/
  rowUnSelected(data: any) {
    this.unselectedRow = data.data;
    this.currentlySelectedRows = data.gridApi.getSelectedNodes();
  }

  selectAll(event) {
    this.params.api.selectAll();
  }

  deSelectAll(event) {
    this.params.api.deselectAll();
  }

  gridReady(data) {
    this.params = data.params;
    let height;
    if (data && data.name && data.name.linkClicks && data.name.linkClicks.length > 0) {
      console.log('height', data);
      height = data.name.linkClicks.length * 48;
      // rowNode.rowHeight = height;
    }
    // console.log('gridready data', data);
    // let tempArr = [];
    // let height;
    // if (data && data._adapter && data._adapter.data) {
    //   data._adapter.data.forEach(element => {
    //     height = element.name.linkClicks.length * 48;
    //     tempArr.push(height);
    //   });
    //   console.log('tempArr', tempArr);
    //   this.conf.adjustedHeightArr = tempArr;
    // }
    // console.log('adjust height', this.conf.adjustedHeightArr);
    // if (data.params && params.name && params.name.linkClicks && params.name.linkClicks.length > 0) {
    //   height = params.name.linkClicks.length * 48;
    //   // console.log('row', row);
    //   this.conf.adjustedHeightArr.push(height);
    //   return height;
    // }

    // let headers: HttpHeaders = new HttpHeaders();
    // headers = headers.set('m_user_name', 'asm_admin')
    //   .set('content-type', 'application/json');

    // this.http.get<any>('http://localhost:10001/om/attributes/list', {
    //   headers: headers,
    //   observe: 'response'
    // }).subscribe((data: any) => {
    //   this.params.api.setRowData(data.body);
    // });
  }

  onRowClicked(data) {
  }

  onDragOver(event) {
    const types = event.dataTransfer.types;
    const dragSupported = types.length;
    if (dragSupported) {
      event.dataTransfer.dropEffect = 'move';
    }
    event.preventDefault();
  }

  onDrop(event) {
    event.preventDefault();
    const userAgent = window.navigator.userAgent;
    const isIE = userAgent.indexOf('Trident/') >= 0;
    let textData = event.dataTransfer.getData(isIE ? 'text' : 'text/plain');
    textData = textData.split(',');
    let data;
    // Scenario when more than 1 element is dragged
    if (textData.length > 1) {
      textData.forEach(element => {
        data = this.params.api.getRowNode(element);
        const eJsonRow = document.createElement('div');
        eJsonRow.classList.add('json-row');
        eJsonRow.innerText = JSON.stringify(data.data);
        const eJsonDisplay = document.querySelector('#eJsonDisplay');
        eJsonDisplay.appendChild(eJsonRow);
      });
    } else {
      // Scenario when only 1 element is dragged
      data = this.params.api.getRowNode(textData);
      const eJsonRow = document.createElement('div');
      eJsonRow.classList.add('json-row');
      eJsonRow.innerText = JSON.stringify(data.data);
      const eJsonDisplay = document.querySelector('#eJsonDisplay');
      eJsonDisplay.appendChild(eJsonRow);
    }

    this.onFilterTextBoxChanged('');
  }

  applyBtnClick(e) {
    console.log(e);
  }

  resetBtnClick(e) {
    console.log(e);
  }

  columnSorted(column) {
    console.log(column);
  }

  ngAfterViewInit() {
    //this.addRemoveColumn.setDefaultList(['name', 'description']);
  }

  gridDrop(event) {
    const dropObj = this.conf.dropObj && this.conf.dropObj[0];
    this.params.api.updateRowData({
      add: [dropObj],
      addIndex: 1
    });
  }

  createNewRow() {
    const newRowData = {
      'name': 'new name',
      'description': 'new Desc',
      'offerCode': 'new OfferCode',
      'channels': 'new channels',
      'expDate': 'new expDate'
    };
    this.params.api.updateRowData({ add: [newRowData], addIndex: 0 });
  }

  updateRow() {
    const newRowData = {
      'name': 'edited name',
      'description': 'edited Desc',
      'offerCode': 'edited OfferCode',
      'channels': 'edited channels',
      'expDate': 'edited expDate'
    };
    const selectedRows = this.params.api.getSelectedRows();
    const rowNode = this.params.api.getRowNode('0');
    rowNode.setData(newRowData);
  }

  deleteRow() {
    this.params.api.purgeInfiniteCache();
  }
}