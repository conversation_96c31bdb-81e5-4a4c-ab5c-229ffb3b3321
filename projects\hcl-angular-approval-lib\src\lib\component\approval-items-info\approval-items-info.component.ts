import { Component, OnInit, OnDestroy, Input, ViewEncapsulation } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { Approval } from '../../models/approval';
import { ApprovalSharedDataService } from '../../service/approval-shared-data.service';
import { TranslateService } from '@ngx-translate/core';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'hcl-approval-items-info',
  templateUrl: './approval-items-info.component.html',
  styleUrls: ['./approval-items-info.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ApprovalItemsInfoComponent implements OnInit, OnDestroy {

  @Input() approval: Approval;
  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();

  constructor(private _sharedDataService: ApprovalSharedDataService,
    private _translate: TranslateService,
    private _datePipe: DatePipe) { }

  ngOnInit(): void {
  }

  getItemImage(item): string {
    return this._sharedDataService.getTypeIconClass(item.origFileName, item.origFileMime);
  }

  getUserAndDate(item) {
    const addedByOn: string = this._translate.instant('APPROVALPICKER.TITLES.ADDED_BY_ON');
    let userAndDate = '';
    if (item) {
      userAndDate = this.getUserName(item.userId);
      userAndDate = addedByOn.split('{0}').join(userAndDate).split('{1}').join(this._datePipe.transform(item.createDate, 'medium'));
    }
    return userAndDate;
  }

  getCountForItem(el): string {
    const totalApprovedLabel: string = this._translate.instant('APPROVALPICKER.TITLES.TOTAL_APPROVED');
    let count = totalApprovedLabel.split('{0}').join('' + this._sharedDataService.getRespCountByItem(el));
    if (this.approval && this.approval.approvers) {
      count = count.split('{1}').join('' + this.approval.approvers.length);
    } else {
      count = count.split('{1}').join('0');
    }
    return count;
  }

  private getUserName(id: number): string {
    let userName: string = '';
    const user = this.approval.approvalUsers.filter(u => u.userId === id);
    if (user && user.length > 0) {
      userName += user[0].user.nameWithTimeZone;
    }
    return userName;
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => {
      sub.unsubscribe();
    });
  }

}
