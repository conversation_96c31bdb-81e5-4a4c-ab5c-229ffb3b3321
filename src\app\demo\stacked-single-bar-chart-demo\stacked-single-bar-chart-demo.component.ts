import { Component, OnInit } from '@angular/core';
import { StackedSingleBarChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/stacked-single-bar-chart/stacked-single-bar-chart';
import { LinearGaugeChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/linear-gauge-chart/linear-gauge-chart';


@Component({
  selector: 'app-stacked-single-bar-chart-demo',
  templateUrl: './stacked-single-bar-chart-demo.component.html',
  styleUrls: ['./stacked-single-bar-chart-demo.component.scss']
})
export class StackedSingleBarChartDemoComponent implements OnInit {

  constructor() { }

  stackedSingleBarChartdata = [{value: 200000, labelValue: "2,00,000", label: "Problamatic Contacts", description: "Contatcs that do not respond and should not be sent mail to.", color: "black"}, {value: 700000, labelValue: "7,00,000", label: "Good Contacts", description: "Contacts that are responding and can be used to send emails to.", color: "blue"}]

  // stackedSingleBarChartdata = []

  stackedSingleBarChartConfig : StackedSingleBarChartConfig = {
    chartContainerId: `bar_chart1`,
    height : 40,
    //Remove width parameter if you want to set default width i.e. 100%
    //width: 100,
    tooltipHTML: (data) => {
      return `<div class="stacked-single-bar-chart-tooltip-container">
                  <div class="stacked-single-bar-chart-tooltip-row heading">
                      <span class="stacked-single-bar-chart-tooltip-header">${data.label}</span>
                  </div>
                  <div class="stacked-single-bar-chart-tooltip-row value">
                      <span class="stacked-single-bar-chart-tooltip-value">${data.labelValue} (${Math.round(data.percent)}%)</span>
                  </div>
                  <div class="stacked-single-bar-chart-tooltip-row description">
                      <span class="stacked-single-bar-chart-tooltip-description">${data.description}</span>
                  </div>
              </div>`;
    }
  }


  ngOnInit() {
    // window.setTimeout(() => {
    //   this.stackedSingleBarChartdata = this.stackedSingleBarChartdata.map((data) => {
    //     return ({value: (Math.ceil(data.value * Math.random() * 10)), label: data.label, color: data.color, labelValue: data.labelValue, description: data.description})
    //   })
    // }, 3000)
  }

}
