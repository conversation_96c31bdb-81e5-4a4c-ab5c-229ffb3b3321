import { AbstractControl } from '@angular/forms';
export interface AutoCompleteV2Conf {
    // in case you want to use objects as options plz follow {'lable':string, 'value':any} structure
    suggestions?: any[];
    placeholder?: string;
    value?: string;
    autoComplete: 'staticOptions' | 'auto' | 'none';
    suffixIconClass?: string;
    name: string;
    isBlurOrEnter?: boolean;
    disabled?: boolean;
    inputType?: string;
    formControl?: AbstractControl;
    errorList?: Array<{ errorCondition: string, errorMsg: string }>;
    errorInfo?: string;
    canFilter?: boolean;
    showInlineSpinner?: boolean;
    loadingText?: string;
    isLazyLoad?: boolean;
    optionsPrefix?: string;
    iconClickStopPropagation?: boolean;
    selectedOption?: any;
    diabledStaticOptions?: boolean;
    itemSize?: number;
    minBufferPx?: number;
    maxBufferPx?: number;
    iconLoadOnOptionSuffix?: string;
    iconLoadOnOptionPrefix?: string;
    showRecentSearch?: boolean;
    prefixIconClass?: string;
    maxIconClass?: string;
    recentSearch?: any[];
    recentSearchText?: string;
}
