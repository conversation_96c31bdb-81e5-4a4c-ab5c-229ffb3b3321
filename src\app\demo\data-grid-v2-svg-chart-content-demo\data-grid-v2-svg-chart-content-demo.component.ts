

import { Component, OnInit, ViewChild, ViewEncapsulation, AfterViewInit, TemplateRef } from '@angular/core';
import {
  DataGridConf, DataGridPagination, HoverIcon, Actions
} from 'hcl-data-grid-lib';
import { DataGridV2Component } from 'hcl-data-grid-lib';
import { ButtonConf } from 'projects/hcl-angular-widgets-lib/src/public_api';
import { ColumnSelectionComponent } from 'hcl-data-grid-lib';
import { LinearGaugeChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/linear-gauge-chart/linear-gauge-chart';
import { DonutChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/config/chart-config';

@Component({
    selector: 'app-data-grid-v2-svg-chart-content-demo',
    templateUrl: './data-grid-v2-svg-chart-content-demo.component.html',
    styleUrls: ['./data-grid-v2-svg-chart-content-demo.component.scss']
})
export class DataGridV2SVGChartContentDemoComponent implements OnInit, AfterViewInit {
  actions?: Actions;
  donutChartConfig : DonutChartConfig;
  conf: DataGridConf;
  selectedRow: any;
  loadFirstComplete: boolean = false;
  unselectedRow: any;
  selectAllConf: ButtonConf;
  deSelectAllConf: ButtonConf;
  getScrollIndexConf: ButtonConf;
  currentlySelectedRows: any;
  hoverActions: HoverIcon[];
  paginatorConfig: DataGridPagination;
  linearGuageChartData = [0,100];
  params: any;
  @ViewChild(DataGridV2Component, { static: true }) ch: DataGridV2Component;
  @ViewChild('addRemoveColumn', {static: false}) addRemoveColumn: ColumnSelectionComponent;
  linearGuageChartConfig: LinearGaugeChartConfig;

  constructor() { }

  ngOnInit() {
    this.donutChartConfig = {
      chartID: `d3_donut_chart1`,
      height : 200,
      width: 200,
      outerRadius: 15,
      innerRadius: 6,
      data: [ {tooltipValueLabel: "1.1% (10,732)", tooltipHeadingLabel: "Nurturing", color: "blue", value: 10723, tooltipDescriptionLabel: "Keep sending emails and nurturing these newer contacts. They are not included in your Dark Pool."}, 
              {tooltipValueLabel: "42.6% (4,15,287)", tooltipHeadingLabel: "Keep Sending", color: "green", value: 415287, tooltipDescriptionLabel: "Keep sending emails. Contacts are healthy and responding."},
              {tooltipValueLabel: "5.2% (50,692)", tooltipHeadingLabel: "Investigate", color: "orange", value: 50692, tooltipDescriptionLabel: "Investigate. Contacts somewhat fatigued. Good chance they will re-engage."}, 
              {tooltipValueLabel: "23.1% (2,25,191)", tooltipHeadingLabel: "Throttle and Investigate", color: "brown", value: 225191, tooltipDescriptionLabel: "Send fewer emails. Contacts are fatigued. Slight chance they will re-engage."}, 
              {tooltipValueLabel: "23.2% (2,26,166)", tooltipHeadingLabel: "Stop Sending and Shift to a Different Channel", color: "red", value: 226166, tooltipDescriptionLabel: "Stop sending emails. Try another chanel besides email."}, 
              {tooltipValueLabel: "3.0% (29,246)", tooltipHeadingLabel: "Remove", color: "black", value: 29246, tooltipDescriptionLabel: "Contacts are dark. Stop sending emails and remove them from the database."}, 
            ],
      middleText: "",
      middleTextFontSize: "27",
      tooltipHTML: ({data}) => {
        return `<div class="donut-chart-tooltip-container">
                    <div class="donut-chart-tooltip-row heading">
                        <span class="donut-chart-tooltip-header">${data.tooltipHeadingLabel}</span>
                    </div>
                    <div class="donut-chart-tooltip-row value">
                        <span class="donut-chart-tooltip-value">${data.tooltipValueLabel}</span>
                    </div>
                    <div class="donut-chart-tooltip-row description">
                        <span class="donut-chart-tooltip-description">${data.tooltipDescriptionLabel}</span>
                    </div>
                </div>`;
      },
      showTooltip: true
    }
    this.linearGuageChartData = [0,100];
    this.linearGuageChartConfig = {
      chartContainerId: `linearGuage_chart1`,
      height : 200,
      width: 190,
      ticks: 5,
      percentageColorStops: [{percentage: 0, label: "0%",  color: "#245aa5"}, 
                            {percentage: 20, label: "20%", color: "#2b67bf"},
                            {percentage: 40, label: "40%", color: "#377ddb"},
                            {percentage: 60, label: "60%", color: "#6da7e2"},
                            {percentage: 80, label: "80%", color: "#a87a97"},
                            {percentage: 100, label: "100%", color: "#eb4b4b"}],
      title: ""
    }

    this.getScrollIndexConf = {
        value: 'Get Scroll',
        buttonType: 'raised',
        color: 'primary',
        borderRadius: 5,
        name: 'selectAll',
        styleClass: 'custom-icon',
        type: 'submit'
    }
    this.selectAllConf = {
      value: 'Select All',
      buttonType: 'raised',
      color: 'primary',
      borderRadius: 5,
      name: 'selectAll',
      styleClass: 'custom-icon',
      type: 'submit'
    };
    this.deSelectAllConf = {
      value: 'Unselect All',
      buttonType: 'raised',
      color: 'primary',
      borderRadius: 5,
      name: 'unSelectAll',
      styleClass: 'custom-icon',
      type: 'submit'
    };
    this.paginatorConfig = {
      rowsPerPage: 10,
      pageSizeArray: [10, 50, 100],
      currentPageIndex: 1
    };
    this.hoverActions = [
      {
        tooltip: 'Retire',
        icon: 'hcl-icon-status-off',
        name: 'retire',
        iconClickHandler: this.retireAction
      },
      {
        tooltip: 'Delete',
        icon: 'hcl-icon-delete',
        name: 'delete',
        iconClickHandler: this.deleteAction
      },
      {
        tooltip: 'Edit',
        icon: 'hcl-icon-edit',
        name: 'edit',
        iconClickHandler: this.editAction
      }
    ];
    this.actions = {
      hoverIcons: this.hoverActions
    };
    this.conf = {
      scrollHeight: 400,
      columns: [
        {
          field: 'name',
          header: 'Name',
          colId: 'name',
          disable: true,
          headerRendererTemplateName: 'dadsa',
          rendererTemplateName: 'entrySourceCell',
          popoverTemplateName: 'customPopover',
          customLoadingRenderer:{
            defaultRenderer : true,
            infiniteLoadingTemplate: '<div>abc</div>'
          },
          sortable: true,
          checkboxSelection: function (node) {
            return node.data ? node.data.name !== 'Toyota' : false;
          },
          getQuickFilterText: function (searchText) {
            return searchText;
          }
        },
        {
          field: 'description',
          header: 'Description',
          colId: 'description',
          headerRendererTemplateName: 'dadsa',
          rendererTemplateName: 'entrySourceCell',
          popoverTemplateName: 'dasds',
          sortable: false
        },
        {
          field: 'donut',
          header: 'Donut Chart',
          colId: 'donut',
          rendererTemplateName: 'donutChartTemplate'
        },
        {
          field: 'linear',
          header: 'Linear Chart',
          colId: 'linear',
          rendererTemplateName: 'linearChartTemplate'
        }
      ],
      dataUrl: 'assets/data/cars-chart.json',
      rowSelectMode: 'single',
      noRowsTemplate: '<span> No data to display</span>',
    };
  }

  getChartData() {
    return [0, 100]
  }

  deleteAction(deletedObj: any) {
    console.log('deleted->', deletedObj);
  }

  onFilterTextBoxChanged(searchText) {
    searchText = 'mar';
    this.params.api.setQuickFilter(searchText);
  }

  editAction(editedObj: any) {
    console.log('edited->', editedObj);
  }

  parseDate(date: number) {
    return (new Date());
  }

  retireAction(retiredObj: any) {
    console.log('retired->', retiredObj);
  }
  /**
  * Gets the row which is selected in current selection, 
  * and also gets all the rows which are selected till now
  **/
  rowSelected(data: any) {
    this.selectedRow = data.data;
    this.currentlySelectedRows = data.gridApi.getSelectedNodes();
  }
  /**
  * Gets the row which is unselected in current unselection, 
  * and also gets all the rows which are still in selected
  **/
  rowUnSelected(data: any) {
    this.unselectedRow = data.data;
    this.currentlySelectedRows = data.gridApi.getSelectedNodes();
  }

  selectAll(event) {
    this.params.api.selectAll();
  }

  deSelectAll(event) {
    this.params.api.deselectAll();
  }

  gridReady(data) {
    this.params = data.params;

    
  }

  // Drag and Drop of row implementation outside the grid and capture JSON
  // onDragOver(event) {
  //   const dragSupported = event.dataTransfer.types.indexOf('application/json') >= 0;
  //   if (dragSupported) {
  //     event.dataTransfer.dropEffect = 'move';
  //     event.preventDefault();
  //   }
  // }
  // onDrop(event) {
  //   const jsonData = event.dataTransfer.getData('application/json');
  //   const eJsonRow = document.createElement('div');
  //   eJsonRow.classList.add('json-row');
  //   eJsonRow.innerText = jsonData;
  //   const eJsonDisplay = document.querySelector('#eJsonDisplay');
  //   eJsonDisplay.appendChild(eJsonRow);
  // }

  onDragOver(event) {
    const types = event.dataTransfer.types;
    const dragSupported = types.length;
    if (dragSupported) {
      event.dataTransfer.dropEffect = 'move';
    }
    event.preventDefault();
  }

  onDrop(event) {
    event.preventDefault();
    const userAgent = window.navigator.userAgent;
    const isIE = userAgent.indexOf('Trident/') >= 0;
    let textData = event.dataTransfer.getData(isIE ? 'text' : 'text/plain');
    textData = textData.split(',');
    let data;
    // Scenario when more than 1 element is dragged
    if (textData.length > 1) {
      textData.forEach(element => {
        data = this.params.api.getRowNode(element);
        const eJsonRow = document.createElement('div');
        eJsonRow.classList.add('json-row');
        eJsonRow.innerText = JSON.stringify(data.data);
        const eJsonDisplay = document.querySelector('#eJsonDisplay');
        eJsonDisplay.appendChild(eJsonRow);
      });
    } else {
      // Scenario when only 1 element is dragged
      data = this.params.api.getRowNode(textData);
      const eJsonRow = document.createElement('div');
      eJsonRow.classList.add('json-row');
      eJsonRow.innerText = JSON.stringify(data.data);
      const eJsonDisplay = document.querySelector('#eJsonDisplay');
      eJsonDisplay.appendChild(eJsonRow);
    }

    this.onFilterTextBoxChanged('');
  }

  applyBtnClick(e) {
    console.log(e);
  }

  resetBtnClick(e) {
    console.log(e);
  }

  ngAfterViewInit() {
    // this.addRemoveColumn.setDefaultList(['name', 'description']);
  }

  /**
  * On first time row render this method is triggered and 
  * we use ot to jump to the saved row index in session storage
  **/
  initialRowRenderCompleteTrigger(event) {
    if (sessionStorage.getItem("gridScrolled")) {
        // this.ch.params.api.ensureIndexVisible(Number(sessionStorage.getItem("gridScrolled")), "top");
    }
  }

  /**
  * On Body Scroll get the first rendered row index
  **/
  onBodyScrollFirstRow(firstRowIndex) {
    sessionStorage.setItem("gridScrolled", firstRowIndex);
  }

}