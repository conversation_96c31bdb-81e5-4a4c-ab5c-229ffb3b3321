<div class="customGrpBy">
    <div *ngIf="groupHeaderRow && groupHeaderRow.key" class="float-left outerDesc">
        <span title="{{groupHeaderRow.key}}" class="float-left groupTitle">{{groupHeaderRow.key}}</span>
        <span class="float-left offset-1 groupDesc"> {{rowDescription}} </span>
    </div>
    <div *ngIf="groupHeaderRow && !groupHeaderRow.key" class="float-left outerDesc">
        <span class="float-left groupTitle"></span>
        <span class="float-left offset-1 groupDesc"> {{noGroupKeyDesc}} </span>
    </div>
</div>