<div class="pt-2">
    <hcl-button class="pr-2" [config]="saveQueryBtnConf" (onclick)="saveQueryJson()"></hcl-button>

    <hcl-button class="pr-2" [config]="sendDataBtnConf" (onclick)="sendDataToServer()"></hcl-button>

    <hcl-button class="pr-2" [config]="resetBtnConf" (onclick)="resetQuerybuilder()"></hcl-button>
</div>
<div class="mt-2 mb-2" [innerHTML]="queryString"></div>
<hcl-query-builder-v2 #queryComp [config]="qbConfig" (queryBuilderStateStream)="printJson($event)"
    (expressionBuilderValue)="checkExpression($event)" (expressionBuilderPanelClosed)="resetPanelState()">
</hcl-query-builder-v2>

<ng-template #expressionBuilderHeaderTemplate>
    <h5 class="custom-header">Expression Builder</h5>
    <div class="info-msg">Create a custom expression for Decision Split</div>
</ng-template>

<ng-template #expressionBuilderContentTemplate>
    <div class="lower-section-title col-12 p-0">
        <div>Supported Expression / Data Definition fields</div>
    </div>
    <div class="accordion-data-container col-12 p-0">
        <div class="data-child-container">
            <div class="data-child-title d-flex">
                <div style="flex: 1;">Supported Expression</div>
                <!-- <div>Return type</div> -->
            </div>
            <div class="row-data-container">
                <!-- <div class="row-data" *ngFor="let function of functionList; let index = index;"
                    [ngClass]="{'selected' : selectedIndex === index}">
                    <div class="function-name" (click)="selectMacro(function, index)"
                        (dblclick)="addMacro(function.definition)">{{function.definition}}</div>
                </div> -->
                <hcl-accordion [config]="accordionConfig" *ngIf="accordionConfig?.items">
                    <ng-container *ngFor="let function of accordionConfig.items;">
                        <ng-template hclTemplate hclTemplateName="{{function.contentTemplateName}}">
                            <ng-container *ngFor="let macro of function?.data; let index = index;">
                                <div class="row-data" (click)="selectMacro(macro, $event)"
                                    (dblclick)="addMacro(macro.definition)">
                                    <div class="function-name">{{macro.definition}}</div>
                                </div>
                            </ng-container>
                        </ng-template>
                    </ng-container>
                </hcl-accordion>
            </div>
        </div>
        <div class="data-child-container">
            <div class="data-child-title d-flex">
                <div style="flex: 1;">Data Definition Fields</div>
                <div>Type</div>
            </div>
            <div class="row-data-container">
                <div class="row-data">
                    <div class="function-name" (dblclick)="addDD('CustomerId')">CustomerId</div>
                </div>
                <div class="row-data">
                    <div class="function-name">Email</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 p-0 msg-note-container">
        Double click on Supported Expressions / Data Definition fields to add it into input control
    </div>
    <div class="col-12 p-0 supported-expression-info-container" *ngIf="selectedMacro">
        <div class="expression-name">{{selectedMacro?.definition}}</div>
        <div class="expression-description">Lorem Ipsum is simply dummy text of the printing and typesetting industry.
            Lorem
            Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a
            galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but
            also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s
            with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop
            publishing software like Aldus PageMaker including versions of Lorem Ipsum.</div>
    </div>
</ng-template>