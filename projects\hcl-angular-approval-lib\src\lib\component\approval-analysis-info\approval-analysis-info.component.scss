$title-font-color: #6d7692;
$title-font-family: Montserrat;
$value-font-color: #444444;
$value-font-family: <PERSON><PERSON>;
$background-color: #ececec !important;

.analysis-info-container {
    padding-top: 35px;
    .grid-container {
        width: 100%;
        .hcl-grid-container {
            padding: 0px;
            .agGridContainer {
                background: $background-color;
                .ag-status-bar {
                    border: 0px;
                }
            }
            .ag-row {
                &:hover {
                    &:after {
                        z-index: 0;
                    }
                }
            }
            .ag-header {
                height: 34px !important;
                min-height: 34px !important;
                background-color: $background-color;
                border-bottom: 0px;
                .ag-header-row {
                    .ag-header-cell {
                        height: 34px;
                        background-color: #bcbbbb !important;
                        border-right: 1px solid #ffffff !important;
                        .ag-header-cell-resize {
                            border: 0px;
                        }
                        .header-default-container {
                            .column-header-label {
                                color: $value-font-color;
                                font-size: 14px;
                                font-weight: bold;
                            }
                        }
                    }
                }
            }
            .ag-cell {
                line-height: 34px;
            }
            hcl-custom-cell {
                div:first-child {
                    span {
                        line-height: 2.5;
                    }
                }
            }
        }
    }
}
