import { HttpHeaders } from '@angular/common/http';
import { Folders } from 'hcl-angular-widgets-lib';

export interface ExportObjectConf {
    // sourceApplicationAlias: string;
    // sourceCategoryType: string;
    targetCategoryFilter: string;
    sourceSubcategoryId: number;
    cifBaseUrl: string;
    // objectType: string; // required field in case appSchemaEmbeded is true
    // appSchemaEmbeded: boolean; // true in case schema object and target object are not same
    applicationMode?: 'CREATE' | 'EDIT';
    previousData?: any;
    // schemaObjectName?: string;
    cifHeaders: HttpHeaders;
    translations: { [key: string]: any };
    allMappingsList?: any[];

    // reLogin?: (callback) => void;
}



export interface FolderConfForObjectMapping extends Folders {
    folderPanelState?: string;
    folderPanelHeader?: string;
}

export interface ObjectMapping {
    $defs?: any;
    $schema: string;
    properties: {
        [key: string]: PropertiesItem;
    };
    type: string;
}

export interface PropertiesItem {
    $id: string;
    jsonPointer?: string;
    title: string;
    type: string;
    format?: string;
}

export interface AttributesMappingData {
    createDate?: string;
    createdBy?: number;
    updateDate?: string;
    updatedBy?: number;
    mappingId?: number;
    object1: ApplicationObject;
    object2: ApplicationObject;
    attributeMappings: AttributesMapping[];
}

export interface ApplicationObject {
    applicationId: string;
    objectType: string;
    objectId: string;
    schema: string;
}

export interface AttributesMapping {
    createDate?: string;
    createdBy?: number;
    updateDate?: string;
    updatedBy?: number;
    attributeMappingId?: number;
    attribute1Id: string;
    attribute2Id: string;
}



// Repository interfaces

export interface CifRepository {
    identifier: string;
    displayName: string;
    location: string;
    contentLocation: string;
    anonymousContent: boolean;
    // supportedContentTypes: SupportedContentTypes;
    features: Features;
    objectExtensionServices: ObjectExtensionService[];
    objectEventInterpreterServices?: any;
    categories: string[];
    additionalFeatures: AdditionalFeatures;
}

export interface Collaboration {
    postMessageToChannel: boolean;
    addMembersToChannel: boolean;
    createChannel: boolean;
    updateChannel: boolean;
    removeMembersFromChannel: boolean;
    getChannelDetails: boolean;
}

export interface Features {
    createContent: boolean;
    deleteFolder: boolean;
    adhocService: boolean;
    assetSelectionCallback: boolean;
    getCognitiveAnalysis: boolean;
    objectExtension: boolean;
    deleteContent: boolean;
    moveFolder: boolean;
    adoptContent: boolean;
    listContents: boolean;
    updateDynamicContent: boolean;
    getFolder: boolean;
    getSearchQuerySuggestions: boolean;
    adoptDynamicContent: boolean;
    patchContent: boolean;
    createFolder: boolean;
    resourceLoader: boolean;
    moveContent: boolean;
    zipFileUpload: boolean;
    getContentDetails: boolean;
    listCategoryFolders: boolean;
    listFolders: boolean;
    updateFolder: boolean;
    getObjectSchema: boolean;
    createDynamicContent: boolean;
    collaboration: Collaboration;
    listContentByIds: boolean;
    simpleSearch: boolean;
    updateContent: boolean;
    listContentCategories: boolean;
}

export interface AttributeMappingSupport {
    request: boolean;
    response: boolean;
}

export interface ObjectExtensionService {
    serviceId: string;
    displayName: string;
    summary: string;
    attributeMappingSupport: AttributeMappingSupport;
}

export interface Content {
    paginatedSearch: boolean;
    paginatedList: boolean;
    anonymousContent: boolean;
    categorization: string;
    code: string;
    localizedLabel: string;
    categorizationLayout: { levels: CategorizationLayout[] };
}

export interface AdditionalFeatures {
    securityPolicy: boolean;
    content: Content;
}

export interface CifCategory {
    id: string;
    label: string;
    additionalFields?: any;
    disabled?: boolean;
}

export interface CategorizationLayout {
    code: string;
    localizedLabel: string;
    organizationStyle: string;
    parent: any;
}

