export interface Approval {
    approvalId: number;
    description?: string;
    name?: string;
    approvalDate?: number;
    stateCode?: string;
    statusCode?: string;
    reapprovalRule?: number;
    secPolicyId?: number;
    dispositionAllDocs?: string;
    enableCommtsAttchmnt?: string;
    autoComplete?: string;
    approveWithChanges?: string;
    approvalOwners?: any[];
    approvalUsers?: any[];
    approvers?: any[];
    items?: any[];
}