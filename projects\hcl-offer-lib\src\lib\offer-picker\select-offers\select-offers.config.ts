import { DataGridPagination } from 'hcl-data-grid-lib';

export interface SelectOffersConfig {
    // Application URL
    url: string;
    locale?: string;
    // Holds all the folder configuration items
    foldersConfig: OfferFoldersConfig;
    // Holds all the offer configuratons for offers grid
    offersGridConfig?: OfferGridConfig;
    // Offers data to load the offers
    offersData?: OfferData[];
    // Load only offers, only offerlists or load both
    loadOfferOrOfferLists?: 'offers' | 'offerLists' | 'both';
    // TO allow only 1 row selection choose rowSelectMode as single for multiple selection choose multiple
    rowSelectMode?: 'single' | 'multiple';
    // Choose state of offers and offerlists
    offerAndOfferListState?: 'both' | 'draft' | 'published';
    // Disable variants so that user cannot select variants
    disableVariants?: boolean;
    // Load type and also send its data
    loadType?: LoadType;
    // choose true if you don't want last published state for offers
    notLastPublishedState?: boolean;
    userName : string;
    tokenId : string ;
    reLogin?: (callback) => void;    
    tokenValidity?: number;
}


export interface OfferFoldersConfig {
    // Name of the root folder at backend database
    rootFolderName?: string;
    // Id of the root folder at backend databasef
    rootFolderId: number;
    // Title on the top of the folder pane
    foldersPaneTitle?: string;
    // Top left label on sort by pane
    sortByLabel?: string;
    // First sorting item on the left after label which sorts by folder name
    sortByNameLabel?: string;
    // Second sorting item on the left which sorts by folder creatin date
    sortByDateLabel?: string;
    // Label besides/left of the paginator on the offers pane
    selectedFolderLabel?: string;
    // Label on the top offer offers pane in global search scenario
    globalSearchLabel?: string;
    // Key name of the child folders item in the tree object sent by backend API
    childFolderName?: string;
    // Key name of the name of the folder sent in the tree object from API
    displayName?: string;
    // Key name of the timestamp of the folder creation sent in the tree object from API
    createdTimeStamp?: string;
}

export interface OfferGridConfig {
    // Placeholder for global search above the grid in header pane
    globalSearchPlaceholder?: string;
    // Title of the offers pane above data grid
    offersPaneTitle?: string;
    // Refresh button label on the top right in the header above data grid
    refreshButtonLabel?: string;
    // Label on the grid column header for offer name
    offerNameLabel?: string;
    // Label on the grid column header for offer code
    offerCodeLabel?: string;
    // Label for the variation column header on the grid
    variationLabel?: string;
    // Label for description column header on the grid
    descriptionLabel?: string;
    // Label on the grid header for channel
    offerChannelLabel?: string;
    // Label on the grid header for status
    offerStatusLabel?: string;
    // Label on the grid header for effective and expiration date
    effectiveExpiraionDateLabel?: string;
    // Bottom label to show selected offers
    selectedOffersLabel: string;
    // Manage selections label at the bottom
    manageSelectionsLabel: string;
    // Data grid paginator
    paginator?: DataGridPagination,
    offerCodeDelimiter?: any;
    getDatePipeFormatLocale(): void;
}

export interface LoadType {
    // Type of data to be loaded, also send data for the type to be loaded
    type: 'viewOffer' | 'viewOfferVariant' | 'viewOfferList' | 'viewManageSelection';
    // send this if 'viewOfferVariant' is selected as the type
    viewOfferVariantData?: ViewOfferVariant;
    // send this if 'viewOffer' is selected as the type
    viewOfferData?: ViewOffer;
    // send this if 'viewOfferList' is selected as the type
    viewOfferListData?: ViewOfferList;
}

export interface ViewOfferVariant {
    offerId: number;
    variantId: number;
}

export interface ViewOffer {
    offerId: number;
}

export interface ViewOfferList {
    offerListId: number;
}

export interface OfferData {
    offerId?: number;
    offerDisplayName?: string;
    variantId?: number;
    variantDisplayName?: string,
    offerCode?: any,
    variantAttributes?: any,
    state?: boolean,
    offerListId?: number
}
