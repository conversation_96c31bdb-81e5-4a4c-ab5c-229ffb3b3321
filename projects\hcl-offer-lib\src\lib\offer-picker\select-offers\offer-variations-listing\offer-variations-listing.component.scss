.variations-grid-container {
    padding: 20px 20px 0 20px;
    background-color: #f5f5f5;
    height: 100%;
    .master-data {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 20px 20px 20px;
        border-radius: 3px;
        background-color: #cce4f7;
        height: 36px;
        color: #0078d8;
        font-family: Roboto;
        font-size: 14px;
        letter-spacing: 0;
    }
    .variations-search {
        margin: 0 20px;
    }
    .variations-grid {
        height: calc(100% - 65px) !important;
        .hcl-grid-container {
            height: calc(100% - 10px) !important;
            .ag-header {
                display: none;
            }
        }
    }

    .variations-grid-2 {
        height: calc(100% - 65px) !important;
        .hcl-grid-container {
            height: calc(100% - 60px) !important;
            .ag-header {
                display: none;
            }
        }
    }

    .custom-icon {
        height: 30px;
        line-height: 6px;
    }

    .master-selected {
        background-color: #ffe5cf;
        color: #f5821e;
        font-family: Roboto;
        font-size: 14px;
        font-weight: bold;
        letter-spacing: 0;
        text-align: center;
    }

    #hoverActionContainer {
        display: flex;
        align-items: center;
    }
    .ag-cell-focus {
        border: 0 !important;
    }
    .ag-row-hover {
        background-color: #ffe5cf !important;
    }
    .row-selected {
        background-color: #ffe5cf;
    }
}

.disable-row {
    color: #b8b7b7;
    pointer-events: none;
}

.disable-variants-text {
    color: #6d7692;
    font-family: Roboto;
    font-size: 12px;
    letter-spacing: 0.4px;
    line-height: 14px;
    padding: 10px 0;
}
