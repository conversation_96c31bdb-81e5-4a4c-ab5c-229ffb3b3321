<hcl-sankey-diagram #sankeyDiagram *ngIf="isLoad" [sankeyJson]='userJsn'
    [userSankeyConfig]='userSankeyConfig'></hcl-sankey-diagram>
<br>
<button type="button" (click)="exportSVGDataPNG()">SVG-PNG</button>
<br>
<button type="button" (click)="exportSVGDataPDF()">SVG-PDF</button>
<br>
<button type="button" (click)="resetZoomLevel()">ZOOM-RESET</button>
<br>
<button type="button" (click)="switchLabels()">on/off</button>
<button type="button" (click)="changeColorScheme()">changeNodeColor</button>
<br>
<button type="button" (click)="changeLayout()">changeLayout</button>