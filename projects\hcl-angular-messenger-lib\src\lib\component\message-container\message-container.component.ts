import { Component, OnInit } from '@angular/core';
import {MessageConfig} from '../messages/message-config';
import {ButtonConf, InputConfig, MenuConfig} from 'hcl-angular-widgets-lib';
import {FormControl} from '@angular/forms';

/**
 * This is the component that represents the message rectangle inside
 * which all the messages are displayed
 */
@Component({
  selector: 'hcl-message-container',
  templateUrl: './message-container.component.html',
  styleUrls: ['./message-container.component.scss']
})
export class MessageContainerComponent implements OnInit {

  /**
   * Thei list of messages that are being sent
   */
  messagesList: MessageConfig[] = [
    {
      id: 1,
      message: 'This is the message This is the message This is the message This is the message This is the message This is the message',
      userName: 'User 1 (GMT + 5:30)',
      timeSent: new Date(),
      status: 1
    }
  ];

  /**
   * The default constructor
   */
  constructor() { }

  /**
   * If required we will do some initialization
   */
  ngOnInit(): void {
  }

  /**
   * Called when user hits send on a message
   */
  sendMessage(event: {message: string}) : void {
    // we need to add it to the message list
    this.messagesList.push({
      message: event.message,
      timeSent: new Date(),
      userName: 'asm_admin',
      isCurrentUser: true,
      id: -1,
      status: 3
    })
  }
}
