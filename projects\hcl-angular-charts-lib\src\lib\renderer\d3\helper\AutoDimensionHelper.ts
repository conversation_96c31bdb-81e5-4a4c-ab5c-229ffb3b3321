import {ChartConfig, ChartSeries, Dimension} from '../../../config/chart-config';
import * as d3 from 'd3';

export class AutoDimensionHelper {
  private static DEFAULT_ONE_TICK_DISTANCE: number = 225;
  /**
   * this function will do the auto calculation & return the dimension that needs to be set for the chart
   * param {ChartConfig} chartConfig
   * returns {Dimension}
   */
  public static getDimensions(chartConfig: ChartConfig): Dimension {
    const dim: Dimension = {width : 100, height: 100};
    // we need to iterate through all the series
    for (let i = 0; i < chartConfig.series.length; i++) {
      // a temp variable
      let tmpDim: Dimension = {width : 100, height: 100};
      // in case of time series
      if (chartConfig.series[i].timeSeries) {
        tmpDim = this.getDimensionForTimeSeries(chartConfig, chartConfig.series[i]);
      }
      // Now if the dim has increased the update dim
      if (tmpDim.width > dim.width) {
        dim.width = tmpDim.width;
      }
      if (tmpDim.height > dim.height) {
        dim.height = tmpDim.height;
      }
    }
    return dim;
  }

  /**
   * This will calculate the dimension for the time series
   * param {ChartConfig} chartConfig
   * param {ChartSeries} series
   * returns {Dimension}
   */
  private static getDimensionForTimeSeries(chartConfig: ChartConfig, series: ChartSeries): Dimension {
    const dim: Dimension =  {width : 100, height: 100};
    // if we do not have a min value we need to figure it out
    let min: number = series.min;
    let max: number = series.max;
    if (!series.min) {
      min = +d3.min(series.data, (d, i) => {
                                  return series.getMinValue(d, i); });
    }
    if (!series.max) {
      max = +d3.max(series.data, (d, i) => {
                                  return series.getMaxValue(d, i); });
    }
    // lets get the difference
    const diffInMilliSeconds: number = (max - min);
    // lets put a buffer of 1/6 of the diff
    if (series.marginBuffer) {
      const buffer: number = diffInMilliSeconds * series.marginBuffer;
      min -= (buffer);
      max += (buffer);
    }
    // we need to round the
    series.min = min;
    series.max = max;
    const diffInSeconds = (series.max - series.min) / 1000;
    // now based on the displayIn we will set the _timeInterval
    switch (series.timeSeries.displayIn) {
      case 'hour' :
        const numOfHours: number = (diffInSeconds / 3600);
        // default each band is 225 pixel so lets check what is the count of bands if tick is 1
        series.timeSeries._timeInterval = d3.timeHour.every(1);
        // width will be
        dim.width = numOfHours * this.DEFAULT_ONE_TICK_DISTANCE;
        series.tickFormat = d3.timeFormat('%d-%b-%y %I:%M %p');
        break;
      case 'day':
        // check what is the number of days between min & max
        const numOfDays: number = diffInSeconds / 86400;
        // default each band is 225 pixel so lets check what is the count of bands if tick is 1
        series.timeSeries._timeInterval = d3.timeDay.every(1);
        // width will be
        dim.width = numOfDays * this.DEFAULT_ONE_TICK_DISTANCE;
        series.tickFormat = d3.timeFormat('%d-%b-%y');
        break;
      case 'week' :
        const numOfWeeks: number = (diffInSeconds / 86400) / 7;
        // default each band is 225 pixel so lets check what is the count of bands if tick is 1
        series.timeSeries._timeInterval = d3.timeWeek.every(1);
        // width will be
        dim.width = numOfWeeks * this.DEFAULT_ONE_TICK_DISTANCE;
        series.tickFormat = d3.timeFormat('%d-%b-%y');
        break;
      case 'month' :
        const numOfMonths: number = (diffInSeconds / 86400) / 30;
        // default each band is 225 pixel so lets check what is the count of bands if tick is 1
        series.timeSeries._timeInterval = d3.timeMonth.every(1);
        // width will be
        dim.width = numOfMonths * this.DEFAULT_ONE_TICK_DISTANCE;
        series.tickFormat = d3.timeFormat('%d-%b-%y');
        break;
      case 'year' :
        const numOfYears: number = ((diffInSeconds / 86400) / 30) / 12;
        // default each band is 225 pixel so lets check what is the count of bands if tick is 1
        series.timeSeries._timeInterval = d3.timeYear.every(1);
        // width will be
        dim.width = numOfMonths * this.DEFAULT_ONE_TICK_DISTANCE;
        series.tickFormat = d3.timeFormat('%d-%b-%Y');
        break;
    }
    return dim;
  }

  /**
   * this function will round the date to a particular date
   * param {number} milliSeconds
   * returns {Date}
   */
  private static roundDate(milliSeconds: number): number{
    const dt: Date = new Date(milliSeconds);
    dt.setHours(0);
    dt.setMinutes(0)
    dt.setSeconds(0)
    return dt.getTime();
  }
}
