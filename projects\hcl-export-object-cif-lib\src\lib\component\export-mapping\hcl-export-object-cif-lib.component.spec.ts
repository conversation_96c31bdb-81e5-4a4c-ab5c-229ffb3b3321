import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HclExportObjectCifLibComponent } from './hcl-export-object-cif-lib.component';

describe('HclExportObjectCifLibComponent', () => {
  let component: HclExportObjectCifLibComponent;
  let fixture: ComponentFixture<HclExportObjectCifLibComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ HclExportObjectCifLibComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HclExportObjectCifLibComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
