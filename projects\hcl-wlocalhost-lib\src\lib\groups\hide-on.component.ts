import { ChangeDetectionStrategy, Component, Input, OnChanges, SimpleChanges } from '@angular/core';

@Component({
  selector: 'hcl-hide-on',
  template: `
    <div class="hide-on-warapper">
      <div class="hide-on">{{ 'settings.hide-on' | translate}}</div>
      <div class="toggle-group">
      <button mat-icon-button (click)="toggleNone()" class="toggle-none" *ngIf="hideOn">
        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 27" fill="none" stroke="#6d7692" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10" />
          <line x1="15" y1="9" x2="9" y2="15" />
          <line x1="15" y1="15" x2="9" y2="9" />
        </svg>
      </button>
        <mat-button-toggle-group [(ngModel)]="hideOn" class="toggle-buttons">
          <mat-button-toggle value="desktop" (click)="toggleDesktop()" hclTooltip="{{ 'settings.desktop' | translate}}">
            <mat-icon>desktop_windows</mat-icon>
          </mat-button-toggle>
          <mat-button-toggle value="mobile" (click)="toggleMobile()" hclTooltip="{{ 'settings.smartphone' | translate}}">
            <mat-icon>smartphone</mat-icon>
          </mat-button-toggle>
        </mat-button-toggle-group>
      </div>
    </div>
  `,
  styles: [`
    .hide-on-warapper ::ng-deep .mat-button-toggle-label-content {
      line-height: initial !important;
      padding: 3px 6px !important;
    }
    .hide-on-warapper ::ng-deep .mat-button-toggle-checked .mat-button-toggle-label-content {
      background-color: #f5821e!important;
      color: rgba(0, 0, 0, 0.54);
      .mat-icon {
        color: white;
      }
    }
    .hide-on-warapper ::ng-deep .mat-icon {
      height: 14px;
      width: 14px;
      font-size: 14px;
      overflow: hidden;
      color:#6d7692;
    }
    .hide-on-warapper {
      background: #f5f5f5;
      padding: 15px 20px 10px 20px;
      display: flex;
      flex: 2;
      flex-direction: row;
      justify-content: space-between;
      overflow: hidden;
    }
    .hide-on {
      color: #6d7692;
      font-family: "Montserrat";
      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 18px;
    }
    .hide-on-warapper{
      .toggle-group{
        display: inline-flex;
        flex-direction: row;
        max-height:27px;
      }
      .toggle-none{
        margin-top: -5px;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class HideOnComponent implements OnChanges {
  @Input() model: { hideOn: string };
  hideOn: 'mobile' | 'desktop' | undefined = undefined;
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.model && changes.model.currentValue) {
      this.hideOn = changes.model.currentValue.hideOn;
    }
  }

  toggleMobile() {
    this.hideOn = 'mobile';
    this.model.hideOn = this.hideOn;
  }

  toggleDesktop() {
    this.hideOn = 'desktop';
    this.model.hideOn = this.hideOn;
  }

  toggleNone() {
    this.hideOn = undefined;
    this.model.hideOn = this.hideOn;
  }
}
