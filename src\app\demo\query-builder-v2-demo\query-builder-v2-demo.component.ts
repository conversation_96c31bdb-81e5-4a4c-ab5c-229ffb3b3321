import {
  Component,
  OnInit,
  ViewChild,
  TemplateRef,
  ElementRef,
  AfterViewInit,
  ViewEncapsulation,
} from "@angular/core";
import {
  ButtonConf,
  QueryBuilderV2Component,
  QueryBuilderV2Conf,
  Accordion,
  SuggestionDirectiveConfig,
} from "projects/hcl-angular-widgets-lib/src/public_api";
import { DatePipe } from "@angular/common";
import { TranslateService } from "@ngx-translate/core";

@Component({
  selector: "app-query-builder-v2-demo",
  templateUrl: "./query-builder-v2-demo.component.html",
  styleUrls: ["./query-builder-v2-demo.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class QueryBuilderV2DemoComponent implements AfterViewInit {
  // { 'param': 'LastName', 'opt': 'endswith', 'value': 'aa', 'datatype': 'STRING' },
  //     {
  //       'OR': [{ 'param': 'Email', 'opt': 'contains', 'value': '.com', 'datatype': 'STRING', 'dateFormat': '' },
  //       { 'param': 'ID', 'opt': 'gt', 'value': '00', 'datatype': 'NUMBER', 'dateFormat': '' },
  //       {
  //         'AND': [{ 'datatype': "NUMBER", 'dateFormat': "", 'opt': "in", 'param': "ID", 'value': ['12', '14'] },
  //         { 'datatype': "DATE", 'dateFormat': "dd/mm/yyyy", 'opt': "lt", 'param': "date", 'value': '1580495400000' }]
  //       }
  //       ]
  //     }

  qbConfig: QueryBuilderV2Conf = {
    jsonData: null,
    columnTypes: [
      {
        fieldValue: "A1",
        fieldLabel: "A1",
        canReset: false,
      },
      {
        fieldValue: "D1",
        fieldLabel: "D1",
        canReset: false,
      },
      {
        fieldValue: "C1",
        fieldLabel: "C1",
        canReset: true,
      },
      {
        fieldValue: "B1",
        fieldLabel: "B1",
        canReset: false,
      },
    ],
    dynamicColumnFields: {
      A1: [
        {
          label: "AA434A",
          value: "r3",
        },
      ],
      B1: [
        {
          label: "AAA4343",
          value: "f3ff",
        },
      ],
      C1: [
        {
          label: "AAA44343",
          value: "fereff",
        },
      ],
      D1: [
        {
          label: "AAA444",
          value: "fffgrf",
        },
      ],
    },
    columnFields: [
      {
        fieldValue: "dob",
        fieldLabel: "DOB",
        regex: null,
        fieldDataType: "DATE",
        dateFormat: "dd mm yy",
        fieldLength: 1000,
      },
      {
        fieldValue: "dob1",
        fieldLabel: "DOB1",
        regex: null,
        fieldDataType: "DATE",
        dateFormat: "mm-dd-yy",
        fieldLength: 1000,
      },
      {
        fieldValue: "firstName",
        fieldLabel: "FirstName",
        dateFormat: "",
        fieldDataType: "STRING",
        fieldLength: 1000,
        regex: null,
      },
      {
        fieldValue: "lastName",
        fieldLabel: "LastName",
        regex: null,
        fieldLength: 1000,
        fieldDataType: "STRING",
        dateFormat: "",
      },
      {
        fieldValue: "email",
        fieldLabel: "Email",
        regex: "EMAIL_ID",
        fieldDataType: "STRING",
        dateFormat: "",
        fieldLength: 1000,
        isDynamic: true,
        controlType: "hcl-drop-down",
      },
      {
        fieldValue: "id",
        fieldLabel: "ID",
        regex: "",
        fieldDataType: "INTEGER",
        dateFormat: "",
        fieldLength: 1000,
      },
      {
        fieldValue: "salary",
        fieldLabel: "Salary",
        regex: "",
        fieldDataType: "NUMERIC",
        dateFormat: "",
        fieldLength: 1000,
      },
      {
        fieldValue: "expressionBuilder",
        fieldLabel: "Expression Builder",
        fieldDataType: "EXPRESSION",
        expressionField: null,
        isDynamic: true,
        controlType: "hcl-drop-down",
      },
    ],
    timeZone: "Asia/Calcutta",
    conditionConfigExpressionType: [
      { label: "is equal to", value: "eq" },
      { label: "not equal to", value: "neq" },
    ],
    conditionConfigStringType: [
      { label: "is equal to", value: "eq" },
      { label: "not equal to", value: "neq" },
      // { label: 'begins with', value: 'beginswith' },
      // { label: 'ends with', value: 'endswith' },
      // { label: 'contains', value: 'contains' },
      { label: "in", value: "in" },
      { label: "is null", value: "null" },
      { label: "is not null", value: "notnull" },
    ],
    conditionConfigNumberType: [
      { label: "is equal to", value: "eq" },
      { label: "not equal to", value: "neq" },
      // { label: 'is null', value: 'null' },
      // { label: 'is not null', value: 'notnull' },
      { label: "is greater than", value: "gt" },
      { label: "is greater than or equal to", value: "gte" },
      { label: "is less than", value: "lt" },
      { label: "is less than or equal to", value: "lte" },
      { label: "is between", value: "between" },
      // { label: 'is not in between', value: 'notbetween' },
      { label: "in", value: "in" },
      { label: "not in", value: "notin" },
    ],
    conditionConfigDateType: [
      { label: "is equal to", value: "eq" },
      { label: "not equal to", value: "neq" },
      { label: "in", value: "in" },
      { label: "after", value: "gt" },
      { label: "after or equal to", value: "gte" },
      { label: "before", value: "lt" },
      { label: "before or equal to", value: "lte" },
      { label: "is between", value: "between" },
      { label: "is not in between", value: "notbetween" },
    ],
    conditionConfigBooleanType: [
      { label: "is true", value: "true" },
      { label: "is false", value: "false" },
    ],
    emitQueryJsonOnFormChange: true,
    keepOneRulePresent: false,
    enableExpressionBuilder: true,
    disableQueryBuilder: false,
    showDatePicker: false,
    dropdownColumns: [],
    folderColumns: [],
    dynamicValues: [],
    needGroupName: false,
    translations: {
      columntypePlaceHolder: "column type",
      cancelModalBtnLabel: "Cancel",
      deleteGroupModalMsg:
        "After deleting this group, you will not be able to recover it from the system.",
      deleteNestedGroupsModalMsg:
        "After deleting this group, all the nested groups will be deleted. You will not be able to recover it from the system.",
      areYouSure: "Are you sure?",
      addRuleLabel: "Add Rule",
      addGroupLabel: "Add Group",
      deleteGroupLabel: "Delete Group",
      deleteGroupsLabel: "Delete Groups",
      andLabel: "AND",
      orLabel: "OR",
      noResultFoundMsg: "No results found!",
      filterFieldsPlaceholder: "Type to search",
      fieldNamePlaceHolder: "Field",
      conditionPlaceHolder: "Condition",
      conditionValuePlaceHolder: "Value",
      requiredField: "Required field",
      fieldOptionsDidNotLoad: "Field options did not load",
      add: "Add",
      valueAlreadyPresent: "Value already present",
      toLabel: "to",
      more: "more",
      close: "Close",
      invalidDigitsRangeMsg: "From and to digits are out of range",
      invalidDateFormat: "please enter date in correct format",
      save: "Save",
      deleteExpressionLabel: "Delete Expression",
      deleteExpressionModalMsg:
        "You are deleting an expression you have build, you will not be able to recover it if deleted.",
      delete: "Delete",
      expressionInputTitle: "Expression (Write your function)",
      validExpression: "Expression is valid",
      subgroupLabel: "Subgroup(s)",
      ruleLabel: "Rule(s)",
    },
    dateConfigObj: {
      dateConfig: {
        monthNavigator: true,
        yearNavigator: true,
        isUCTDate: false,
        showTime: true,
        selectionMode: "",
        dateFormat: "dd/mm/yy",
        yearRange: "",
        showButtons: false,
        customHeader: "",
        customFooter: "",
        noOfMonth: 1,
        isInline: false,
        appendTo: "body",
        // name: 'datePicker',
        dateInputConfig: {
          placeholder: "value",
          value: "",
          autoComplete: "none",
          suffixIconClass: "hcl-icon-calendar",
          name: "search",
        },
        errorList: [
          {
            errorCondition: "required",
            errorMsg: "Required field",
          },
        ],
      },
      datePipeFormat: "MM/dd/yyyy hh:mm",
      dateLocaleJson: {
        firstDayOfWeek: 0,
        dayNames: [
          "Sunday",
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
        ],
        dayNamesShort: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
        dayNamesMin: ["S", "M", "T", "W", "Th", "F", "St"],
        monthNames: [
          "January",
          "February",
          "March",
          "April",
          "May",
          "June",
          "July",
          "August",
          "September",
          "October",
          "November",
          "December",
        ],
        monthNamesShort: [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sept",
          "Oct",
          "Nov",
          "Dec",
        ],
        today: "Today",
        clear: "Clear",
        reset: "Reset",
        submit: "Submit",
        dateFormat: "Dateformat",
        weekHeader: "Weekheader",
        am: "am",
        pm: "pm",
      },
    },
    expressionBuilderPanelConfig: {
      isChildSidePanel: true,
      parentTitle: "Decision Split",
      suggestionTextareaConfig: {
        tag: "textarea",
        collection: [
          {
            trigger: "#",
            values: [],
            selectTemplate: (item: any) => {
              return "#" + item.string + "()";
            },
            menuItemTemplate: function (item: any) {
              return (
                '<div hclTooltip="' +
                item.string +
                '">' +
                item.string +
                "</div>"
              );
            },
            searchOpts: {
              pre: "",
              post: "",
              skip: false,
            },
            requireLeadingSpace: false,
          },
          {
            trigger: "$",
            values: [],
            selectTemplate: (item: any) => {
              return "$" + item.string;
            },
            menuItemTemplate: function (item: any) {
              return item.string;
            },
            searchOpts: {
              pre: "",
              post: "",
              skip: false,
            },
            requireLeadingSpace: false,
          },
        ],
        keepLastChar: true,
        formControl: null,
      },
    },
  };

  saveQueryBtnConf: ButtonConf = {
    value: "Save Query",
    buttonType: "flat",
    borderRadius: 5,
    color: "accent",
    name: "basicButton",
  };

  sendDataBtnConf: ButtonConf = {
    value: "Send JSON",
    buttonType: "flat",
    borderRadius: 5,
    color: "accent",
    name: "basicButton",
  };

  resetBtnConf: ButtonConf = {
    value: "Reset",
    buttonType: "flat",
    borderRadius: 5,
    color: "accent",
    name: "basicButton",
  };

  isError: any;
  finalQuery: any;

  @ViewChild("queryComp") queryComp: QueryBuilderV2Component;
  @ViewChild("expressionBuilderHeaderTemplate")
  expressionBuilderHeaderTemplate: TemplateRef<ElementRef>;
  @ViewChild("expressionBuilderContentTemplate")
  expressionBuilderContentTemplate: TemplateRef<ElementRef>;

  functionList: any[] = [];
  queryString: any;
  private _dateFormatLocalMap = new Map([
    ["de_DE", "dd.mm.yy"], // german
    ["en_GB", "dd/mm/yy"], // GB
    ["en_US", "mm/dd/yy"], // english
    ["es_ES", "dd/mm/yy"], // spanish
    ["fr_FR", "dd/mm/yy"], // french
    ["it_IT", "dd/mm/yy"], // italian
    ["ja_JP", "yy/mm/dd"], // japnese
    ["ko_KR", "yy/mm/dd"], // korean
    ["pt_BR", "dd/mm/yy"], // portugese
    ["ru_RU", "dd.mm.yy"], // russian
    ["zh_CN", "yy/mm/dd"], // simplified chinses
    ["zh_TW", "yy/mm/dd"], // chinese
  ]);
  private datePipeFormat = this.getDatePipeFormatLocale();

  columnData = [
    {
      label: "Date of Birth",
      value: "dob",
      type: "Date",
    },
    {
      label: "Expression Builder",
      value: "expressionBuilder",
      type: "Expression",
    },
    {
      label: "First name",
      value: "firstName",
      type: "String",
    },
    {
      label: "Last name",
      value: "lastName",
      type: "String",
    },
  ];
  selectedMacro: any = null;
  accordionConfig: Accordion = {
    displayMode: "default",
    hideToggle: false,
    multi: false,
    step: 0,
    panelActions: false,
    items: [],
  };

  constructor(
    private datePipe: DatePipe,
    private translate: TranslateService
  ) {}

  ngOnInit() {
    // setTimeout(() => {
    //   this.qbConfig.columnFields = [
    //     { fieldName: 'FirstName', dateFormat: '', fieldDataType: 'String', fieldLength: 1000, regex: null },
    //     { fieldName: 'LastName', regex: null, fieldLength: 1000, fieldDataType: 'String', dateFormat: '' },
    //     { fieldName: 'Email', regex: 'EMAIL_ID', fieldDataType: 'String', dateFormat: '', fieldLength: 1000 },
    //     { fieldName: 'ID', regex: '[^[0-9]*\.?[0-9]*]', fieldDataType: 'Numeric', dateFormat: '', fieldLength: 1000 },
    //     { fieldName: 'date', regex: null, fieldDataType: 'Date', dateFormat: 'dd/mm/yyyy', fieldLength: 1000 }
    //   ];

    //   this.queryComp.reRender({
    //     'AND': [
    //       { 'param': '', 'opt': '', 'value': '', 'datatype': '', 'dateFormat': '' }
    //     ]
    //   });

    // }, 3000);

    this.qbConfig.dynamicValues = this.columnData;

    setTimeout(() => {
      this.functionList = [
        {
          definition: "DaysDifference",
          returnType: "Number",
          categories: ["Numeric", "Date"],
        },
        {
          definition: "DaysFrom",
          returnType: "Number",
          categories: ["Date"],
        },
        {
          definition: "Substr",
          returnType: "String",
          categories: ["String"],
        },
        {
          definition: "StringLocate",
          returnType: "String",
          categories: ["String"],
        },
      ];
      this.populateAccordionItems(this.functionList);
    }, 100);
  }

  private populateAccordionItems(dataObj: any) {
    this.accordionConfig.items = [];
    dataObj.map((d) => {
      d.categories.forEach((category) => {
        const item = {
          label: category,
          content: "",
          contentTemplateName: category.replace(/\s/g, "") + "_Template",
          isRemovable: false,
          disabled: false,
          expanded: false,
        };
        if (this.accordionConfig.items.length === 0) {
          item.expanded = true;
          this.accordionConfig.items.push(item);
        } else {
          const existingItem = this.accordionConfig.items.find(
            (i) => i.label === category
          );
          if (!existingItem) {
            this.accordionConfig.items.push(item);
          }
        }
      });
    });
    this.accordionConfig.items.map((d) => {
      d.data = dataObj.filter((i) => i.categories.indexOf(d.label) !== -1);
    });
  }

  ngAfterViewInit(): void {
    this.qbConfig.expressionBuilderPanelConfig = {
      ...this.qbConfig.expressionBuilderPanelConfig,
      headerTemplate: this.expressionBuilderHeaderTemplate,
      contentTemplate: this.expressionBuilderContentTemplate,
    };
  }

  saveQueryJson() {
    // // call the widget method'
    const strObj = this.queryComp.getQueryJson();
    //console.log(strObj);
    // if (!this.isError) {
    //   this.finalQuery = this.queryComp.finalQuery;
    //   console.log('final condition demo', this.finalQuery);
    // } else {
    //   this.finalQuery = '';
    // }
    this.queryString = this.generateQueryString(strObj, true, this.datePipe);
    const suggestion = [
      { key: "abc", value: "abc", triggerValue: "#" },
      { key: "def", value: "def", triggerValue: "#" },
      { key: "dd", value: "dd", triggerValue: "$" },
    ];
    this.queryComp.populateSuggestions(suggestion);
  }

  sendDataToServer() {
    const strObj = this.queryComp.getQueryJson(undefined, true);
    // console.log('data to server : ', strObj);
  }

  private generateQueryString(objData: any, rootCall: boolean, datePipe) {
    let finalQueryString;
    if (objData && (objData.AND || objData.OR)) {
      const parentLogicOp = objData.AND ? "AND" : "OR";
      finalQueryString = this.getMultipleRulesQueryString(
        objData[parentLogicOp],
        parentLogicOp,
        rootCall,
        datePipe
      );
    } else if (objData && objData.param) {
      finalQueryString = this.getSingleRulQueryString(objData, datePipe);
    }
    return finalQueryString;
  }

  private getMultipleRulesQueryString(
    multipleRulesQueryData: any,
    logicalOperator: string,
    rootCall: boolean,
    datePipe
  ) {
    let resultString = rootCall ? "" : "(";
    for (let i = 0; i < multipleRulesQueryData.length; i++) {
      if (
        (multipleRulesQueryData[i].AND &&
          multipleRulesQueryData[i].AND.length) ||
        (multipleRulesQueryData[i].OR && multipleRulesQueryData[i].OR.length)
      ) {
        resultString +=
          i === 0
            ? `${this.generateQueryString(
                multipleRulesQueryData[i],
                false,
                datePipe
              )}`
            : ` <span class="qb-logical-operator">${this.translate.instant(
                "QUERY_BUILDER_OPERATORS." + logicalOperator
              )}</span> 
            ${this.generateQueryString(
              multipleRulesQueryData[i],
              false,
              datePipe
            )}`;
      }

      if (multipleRulesQueryData[i].param) {
        resultString +=
          i === 0
            ? this.getSingleRulQueryString(multipleRulesQueryData[i], datePipe)
            : ` <span class="qb-logical-operator">${this.translate.instant(
                "QUERY_BUILDER_OPERATORS." + logicalOperator
              )}</span> ${this.getSingleRulQueryString(
                multipleRulesQueryData[i],
                datePipe
              )}`;
      }
    }
    return resultString + (rootCall ? "" : ")");
  }

  private getSingleRulQueryString(singleQueryData: any, datePipe) {
    const operatorString = singleQueryData.opt
      ? this.translate.instant("QUERY_BUILDER_OPERATORS." + singleQueryData.opt)
      : "";
    let value = "";
    if (
      (singleQueryData.opt === "in" || singleQueryData.opt === "notin") &&
      singleQueryData.value.length
    ) {
      if (singleQueryData.datatype === "Date") {
        value = singleQueryData.value.reduce((accum, val) => {
          return (
            accum +
            (accum ? ", " : "") +
            datePipe.transform(val, this.datePipeFormat)
          );
        }, "");
      } else {
        value = singleQueryData.value.reduce((accum, val) => {
          return accum + (accum ? ", " : "") + val;
        }, "");
      }
    } else if (
      singleQueryData.opt === "between" &&
      singleQueryData.value.length
    ) {
      if (singleQueryData.datatype === "Date") {
        value = `${datePipe.transform(
          singleQueryData.value[0],
          this.datePipeFormat
        )}  ${this.translate.instant(
          "CREATE_SEGMENT.LABELS.TO"
        )}  ${datePipe.transform(
          singleQueryData.value[1],
          this.datePipeFormat
        )}`;
      } else {
        value = `${singleQueryData.value[0]}  ${this.translate.instant(
          "CREATE_SEGMENT.LABELS.TO"
        )}  ${singleQueryData.value[1]}`;
      }
    } else if (
      singleQueryData.opt !== "in" &&
      singleQueryData.opt !== "notin" &&
      singleQueryData.opt !== "between"
    ) {
      value =
        singleQueryData.datatype === "Date"
          ? datePipe.transform(singleQueryData.value, this.datePipeFormat)
          : singleQueryData.dynamicValue
          ? singleQueryData.dynamicValue
          : singleQueryData.value;
    }

    if (singleQueryData.expressionField) {
      return `${singleQueryData.expressionField} ${operatorString} ${value}`;
    } else {
      return `${singleQueryData.paramLabel} ${operatorString} ${value}`;
    }
  }

  // isErrorInQuery(eve) {
  //   this.isError = eve
  //   console.log('is error?', this.isError);
  // }

  printJson(data) {
    //console.log(data);
    this.finalQuery = JSON.stringify(data.jsonData);
  }

  checkExpression(expObj) {
    setTimeout(() => {
      const response: {
        valid: boolean;
        returnType?: string;
        isSaveClicked?: boolean;
      } = {
        returnType: "String",
        valid: true,
        isSaveClicked: expObj.isSaveClicked,
      };
      this.qbConfig.dynamicValues = this.columnData.filter((col) => {
        return response.returnType === col.type || col.type === "Expression";
      });
      this.queryComp && this.queryComp.validateCustomExpression(response);
    }, 1000);
  }

  getDatePipeFormatLocale() {
    return (
      this._dateFormatLocalMap
        .get("en_US")
        .replace("yy", "yyyy")
        .replace("mm", "MM") + " HH:mm"
    );
  }

  resetQuerybuilder() {
    this.queryComp.reset();
  }

  addMacro(fName: string) {
    const value = `#${fName}()`;
    this.queryComp.addMacroToExpressionBuilder(value);
  }

  addDD(ddName: string) {
    this.queryComp.addMacroToExpressionBuilder(`#${ddName}`);
  }

  selectMacro(macro, event) {
    this.selectedMacro = macro;
    document.querySelectorAll(".row-data").forEach((row) => {
      row.classList.remove("selected");
    });
    event.currentTarget.classList.add("selected");
  }

  resetPanelState() {
    this.selectedMacro = null;
  }
}
