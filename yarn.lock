# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@adobe/css-tools@^4.0.1":
  "version" "4.4.3"

"@ag-grid-community/all-modules@~22.0.0":
  "integrity" "sha512-Jw3ZiAoTUHyyC9jsG+L5e9/2fiptxTPVYYBlVEG7DXvI0dfOrQlC8Z42S8iSQKFn26Ua1gZ6qZygyVdU96Oxcw=="
  "resolved" "https://registry.npmjs.org/@ag-grid-community/all-modules/-/all-modules-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/client-side-row-model" "~22.0.0"
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-community/csv-export" "~22.0.0"
    "@ag-grid-community/infinite-row-model" "~22.0.0"
    "gulp-typescript" "^5.0.1"

"@ag-grid-community/client-side-row-model@~22.0.0":
  "integrity" "sha512-qRV9zpdBEd8mgEiOdR17D3YhGGGmcNbkKlKK4xpfR5dSscgdkbC+zJ7RU+bYDITISjoLQ47yvLp5hs1nIywdJw=="
  "resolved" "https://registry.npmjs.org/@ag-grid-community/client-side-row-model/-/client-side-row-model-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"

"@ag-grid-community/core@~22.0.0":
  "integrity" "sha512-hHwUA2vMGcQWKHVwvu9QwCUPakaJ36fMIXfwnq4ZSNo0IkdpXxgWasfij7SI9povTz0CsrrlL4KGsV4mdOutXw=="
  "resolved" "https://registry.npmjs.org/@ag-grid-community/core/-/core-22.0.0.tgz"
  "version" "22.0.0"

"@ag-grid-community/csv-export@~22.0.0":
  "integrity" "sha512-DQXZjvCKIxmZiPaYCfdtCRVVmzkGjSeZ9q5TlcBqxOuhGdftwbsE0WzjspKFtA+5G3BEsyjqy/EdI1m8qXkUYg=="
  "resolved" "https://registry.npmjs.org/@ag-grid-community/csv-export/-/csv-export-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"

"@ag-grid-community/infinite-row-model@~22.0.0":
  "integrity" "sha512-Qqbt8ezcTVvLoe1b5a7ajci2wpojqriL+GE+AzpHZg1TyleRjh5I5KgqjziuSVkINUt8bYXmGq48WwzVCsBhFA=="
  "resolved" "https://registry.npmjs.org/@ag-grid-community/infinite-row-model/-/infinite-row-model-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"

"@ag-grid-enterprise/all-modules@22.0.0":
  "integrity" "sha512-dPNCnLt100zg/Y3osEX5if/9xVwi3lvxY7bu4jm8JTe2tLlYCZMHwUEp/Yyfwl9rJGifh90Gf25X0NweD8XlQw=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/all-modules/-/all-modules-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/all-modules" "~22.0.0"
    "@ag-grid-enterprise/charts" "~22.0.0"
    "@ag-grid-enterprise/clipboard" "~22.0.0"
    "@ag-grid-enterprise/column-tool-panel" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"
    "@ag-grid-enterprise/excel-export" "~22.0.0"
    "@ag-grid-enterprise/filter-tool-panel" "~22.0.0"
    "@ag-grid-enterprise/master-detail" "~22.0.0"
    "@ag-grid-enterprise/menu" "~22.0.0"
    "@ag-grid-enterprise/range-selection" "~22.0.0"
    "@ag-grid-enterprise/rich-select" "~22.0.0"
    "@ag-grid-enterprise/row-grouping" "~22.0.0"
    "@ag-grid-enterprise/server-side-row-model" "~22.0.0"
    "@ag-grid-enterprise/set-filter" "~22.0.0"
    "@ag-grid-enterprise/side-bar" "~22.0.0"
    "@ag-grid-enterprise/status-bar" "~22.0.0"
    "@ag-grid-enterprise/viewport-row-model" "~22.0.0"

"@ag-grid-enterprise/charts@~22.0.0":
  "integrity" "sha512-BqKLZlvhNG9VXSjlOyduIYg0DeYhokYYpoE3IP3GzBDPHWF+Vo17J1RP8doww8dOEyHycSuRPAlB4PF+r1BP4g=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/charts/-/charts-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"
    "@ag-grid-enterprise/range-selection" "~22.0.0"

"@ag-grid-enterprise/clipboard@~22.0.0":
  "integrity" "sha512-vH8miSQp5aZG998F5JLaR0Zp39T3KBXRrbDKqDM0FEhImjokLwyjRW7gRmzwImUJYkyj72/IIDidg9MAEG4qcw=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/clipboard/-/clipboard-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-community/csv-export" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ag-grid-enterprise/column-tool-panel@~22.0.0":
  "integrity" "sha512-IBcDAIAxBM1AwPVsPIwa1W8kU7P520bF1HZx3eMX4icEje/Hm8LQytB8bS/4OqTcMc1GRGz7fw++Ou17tAoAqA=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/column-tool-panel/-/column-tool-panel-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"
    "@ag-grid-enterprise/row-grouping" "~22.0.0"
    "@ag-grid-enterprise/side-bar" "~22.0.0"

"@ag-grid-enterprise/core@~22.0.0", "@ag-grid-enterprise/core@22.0.0":
  "integrity" "sha512-UUqiq8nQqYnpQz6hgniaridVPWxK5Ygg87p7oojrI7B3bE4w2A9lKHedesaqAAb6EIfjKofEAdKSjtPtaypgNw=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/core/-/core-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"

"@ag-grid-enterprise/excel-export@~22.0.0":
  "integrity" "sha512-AY+xqlZIMA5LwoHRALAcwFC+ZnDEt9F860dyXrxiNxdk+QIqz9uWsbe3fiBu3jyFeDfZRI2FFRHtrhtRQm9z8w=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/excel-export/-/excel-export-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-community/csv-export" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ag-grid-enterprise/filter-tool-panel@~22.0.0":
  "integrity" "sha512-f38ePLLULULKckt9wMnAwE1/MUqPPgdmplZ0DeYBKrBTk4nrzPdUoz1rsSVPsxBrLqw4gSTOJekQbORttZMm5g=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/filter-tool-panel/-/filter-tool-panel-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"
    "@ag-grid-enterprise/side-bar" "~22.0.0"

"@ag-grid-enterprise/master-detail@~22.0.0":
  "integrity" "sha512-A9/zTfY309RuvDJC9c0BRPmzLv93GKsS2OcdnElGGf/bndLWFcqGDHb8ElupR4Ow/GPH6LWBgnO7Gatvf7X25g=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/master-detail/-/master-detail-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ag-grid-enterprise/menu@~22.0.0":
  "integrity" "sha512-Ry1l12/NirZ2ZNG946S5NbrTrG3deijAjEoaHPibJasAgDtot0OgczFZMRZbJpov4QpQgUf1V6CGWinU45R0dA=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/menu/-/menu-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ag-grid-enterprise/range-selection@~22.0.0":
  "integrity" "sha512-EDe5BRUK0G/kOJ/ycZ4Ur9BIt/RWvPRUBPzZD36M0F7ZSi0xLN6kIy4bore9E61QIY0+D2F0BjzHnf5yAl+t9Q=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/range-selection/-/range-selection-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ag-grid-enterprise/rich-select@~22.0.0":
  "integrity" "sha512-JGSgguT/srfzuvLXQEcLV6oiOgj6RkDA7Rv43fCmXWoFasBYObkghspg9XdWHL52lh/BUXH44Nx/ZLojmXUPjQ=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/rich-select/-/rich-select-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ag-grid-enterprise/row-grouping@~22.0.0":
  "integrity" "sha512-mzl7Far7r8N1cONq+Z0VheASxAQf5prvCMT3of2rUsZ4hm6JKxNg4POE+EVhgDNajAcY2fwRkXQqGgEN2kOjqQ=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/row-grouping/-/row-grouping-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ag-grid-enterprise/server-side-row-model@~22.0.0":
  "integrity" "sha512-bhfcdCDrRxV7d98zMm67qlgc9zI89r/qaUZq/d/mV3Uv8lbBld75fXy3NwCyswj/HIkkPTWa8C3kX3dLy2skoQ=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/server-side-row-model/-/server-side-row-model-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ag-grid-enterprise/set-filter@~22.0.0":
  "integrity" "sha512-SOqCAT3ir5JOSBQuKPgNmGvab7Iws4WzTgNu+9xm5Y5nbujtAy8XH51ul9nZH6TfNeFtGL+F/F8bQ+QzbRTlMw=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/set-filter/-/set-filter-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ag-grid-enterprise/side-bar@~22.0.0":
  "integrity" "sha512-vhHiZ4V5QzNrdNgnihn/8bA+ex6/GfAByEOQK4IK8iDDAS5q/8a/iA40+1SctxdlI+PcgS4oVLBg2fPZoVeDlQ=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/side-bar/-/side-bar-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ag-grid-enterprise/status-bar@~22.0.0":
  "integrity" "sha512-ATf8RN3v+5GyN+H/gqhL1takC0hzM6jWD8Jxv0hK1mWOF3Ed2Vzn2XUAgAcl/AJfzCTEG5i2xw6vf0Ph1fPPsA=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/status-bar/-/status-bar-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ag-grid-enterprise/viewport-row-model@~22.0.0":
  "integrity" "sha512-3vseZeyTSiniWTC/Qz2lFaP+ERdjDiwlWLhGMUoGzL1qrjTXg4yW/Gnm7cQ05K3l+KVPhmFo5vuhbW4a194cIA=="
  "resolved" "https://registry.npmjs.org/@ag-grid-enterprise/viewport-row-model/-/viewport-row-model-22.0.0.tgz"
  "version" "22.0.0"
  dependencies:
    "@ag-grid-community/core" "~22.0.0"
    "@ag-grid-enterprise/core" "~22.0.0"

"@ampproject/remapping@^2.1.0", "@ampproject/remapping@2.2.0":
  "integrity" "sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w=="
  "resolved" "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.1.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@angular-devkit/architect@0.1402.11":
  "integrity" "sha512-RuSZrBQ+QbipAESZ4aXCyAMQHaEaDyyV/FDS9J2HJWfEFbRD5oxlEt/tBC8XjmJQsktaUOh07GT8MNJjPKVAQw=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/architect/-/architect-0.1402.11.tgz"
  "version" "0.1402.11"
  dependencies:
    "@angular-devkit/core" "14.2.11"
    "rxjs" "6.6.7"

"@angular-devkit/architect@0.1402.13":
  "integrity" "sha512-n0ISBuvkZHoOpAzuAZql1TU9VLHUE9e/a9g4VNOPHewjMzpN02VqeGKvJfOCKtzkCs6gVssIlILm2/SXxkIFxQ=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/architect/-/architect-0.1402.13.tgz"
  "version" "0.1402.13"
  dependencies:
    "@angular-devkit/core" "14.2.13"
    "rxjs" "6.6.7"

"@angular-devkit/build-angular@^14.2.11":
  "integrity" "sha512-FJZKQ3xYFvEJ807sxVy4bCVyGU2NMl3UUPNfLIdIdzwwDEP9tx/cc+c4VtVPEZZfU8jVenu8XOvL6L0vpjt3yg=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/build-angular/-/build-angular-14.2.13.tgz"
  "version" "14.2.13"
  dependencies:
    "@ampproject/remapping" "2.2.0"
    "@angular-devkit/architect" "0.1402.13"
    "@angular-devkit/build-webpack" "0.1402.13"
    "@angular-devkit/core" "14.2.13"
    "@babel/core" "7.18.10"
    "@babel/generator" "7.18.12"
    "@babel/helper-annotate-as-pure" "7.18.6"
    "@babel/plugin-proposal-async-generator-functions" "7.18.10"
    "@babel/plugin-transform-async-to-generator" "7.18.6"
    "@babel/plugin-transform-runtime" "7.18.10"
    "@babel/preset-env" "7.18.10"
    "@babel/runtime" "7.18.9"
    "@babel/template" "7.18.10"
    "@discoveryjs/json-ext" "0.5.7"
    "@ngtools/webpack" "14.2.13"
    "ansi-colors" "4.1.3"
    "babel-loader" "8.2.5"
    "babel-plugin-istanbul" "6.1.1"
    "browserslist" "^4.9.1"
    "cacache" "16.1.2"
    "copy-webpack-plugin" "11.0.0"
    "critters" "0.0.16"
    "css-loader" "6.7.1"
    "esbuild-wasm" "0.15.5"
    "glob" "8.0.3"
    "https-proxy-agent" "5.0.1"
    "inquirer" "8.2.4"
    "jsonc-parser" "3.1.0"
    "karma-source-map-support" "1.4.0"
    "less" "4.1.3"
    "less-loader" "11.0.0"
    "license-webpack-plugin" "4.0.2"
    "loader-utils" "3.2.1"
    "mini-css-extract-plugin" "2.6.1"
    "minimatch" "5.1.0"
    "open" "8.4.0"
    "ora" "5.4.1"
    "parse5-html-rewriting-stream" "6.0.1"
    "piscina" "3.2.0"
    "postcss" "8.4.31"
    "postcss-import" "15.0.0"
    "postcss-loader" "7.0.1"
    "postcss-preset-env" "7.8.0"
    "regenerator-runtime" "0.13.9"
    "resolve-url-loader" "5.0.0"
    "rxjs" "6.6.7"
    "sass" "1.54.4"
    "sass-loader" "13.0.2"
    "semver" "7.5.3"
    "source-map-loader" "4.0.0"
    "source-map-support" "0.5.21"
    "stylus" "0.59.0"
    "stylus-loader" "7.0.0"
    "terser" "5.14.2"
    "text-table" "0.2.0"
    "tree-kill" "1.2.2"
    "tslib" "2.4.0"
    "webpack" "5.76.1"
    "webpack-dev-middleware" "5.3.3"
    "webpack-dev-server" "4.11.0"
    "webpack-merge" "5.8.0"
    "webpack-subresource-integrity" "5.1.0"
  optionalDependencies:
    "esbuild" "0.15.5"

"@angular-devkit/build-webpack@0.1402.13":
  "integrity" "sha512-K27aJmuw86ZOdiu5PoGeGDJ2v7g2ZCK0bGwc8jzkjTLRfvd4FRKIIZumGv3hbQ3vQRLikiU6WMDRTFyCZky/EA=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/build-webpack/-/build-webpack-0.1402.13.tgz"
  "version" "0.1402.13"
  dependencies:
    "@angular-devkit/architect" "0.1402.13"
    "rxjs" "6.6.7"

"@angular-devkit/core@14.2.11":
  "integrity" "sha512-cBIGs6y9rykOQqnuAQOB1DgIRyBFYtvKRJb7QNUfIJ0qUfARKkuV/yikv3lrb95ePGkmoRzmjkFqcFZiYU+r7A=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/core/-/core-14.2.11.tgz"
  "version" "14.2.11"
  dependencies:
    "ajv" "8.11.0"
    "ajv-formats" "2.1.1"
    "jsonc-parser" "3.1.0"
    "rxjs" "6.6.7"
    "source-map" "0.7.4"

"@angular-devkit/core@14.2.13":
  "integrity" "sha512-aIefeZcbjghQg/V6U9CTLtyB5fXDJ63KwYqVYkWP+i0XriS5A9puFgq2u/OVsWxAfYvqpDqp5AdQ0g0bi3CAsA=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/core/-/core-14.2.13.tgz"
  "version" "14.2.13"
  dependencies:
    "ajv" "8.11.0"
    "ajv-formats" "2.1.1"
    "jsonc-parser" "3.1.0"
    "rxjs" "6.6.7"
    "source-map" "0.7.4"

"@angular-devkit/schematics@14.2.11":
  "integrity" "sha512-OTEOu4uf3kZDcSGYkuESxf/IOlJSn/GdLt63Sd1QwJu6pJSeFxkANw/WEWICZyJfRLNW6fdLJLEGPM9Zt5ZqAg=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/schematics/-/schematics-14.2.11.tgz"
  "version" "14.2.11"
  dependencies:
    "@angular-devkit/core" "14.2.11"
    "jsonc-parser" "3.1.0"
    "magic-string" "0.26.2"
    "ora" "5.4.1"
    "rxjs" "6.6.7"

"@angular/animations@14.3.0":
  "integrity" "sha512-QoBcIKy1ZiU+4qJsAh5Ls20BupWiXiZzKb0s6L9/dntPt5Msr4Ao289XR2P6O1L+kTsCprH9Kt41zyGQ/bkRqg=="
  "resolved" "https://registry.npmjs.org/@angular/animations/-/animations-14.3.0.tgz"
  "version" "14.3.0"
  dependencies:
    "tslib" "^2.3.0"

"@angular/cdk@14.2.7":
  "integrity" "sha512-/tEsYaUbDSnfEmKVvAMramIptmhI67O+9STjOV0i+74XR2NospeK0fkbywIANu1n3w6AHGMotvRWJrjmbCElFg=="
  "resolved" "https://registry.npmjs.org/@angular/cdk/-/cdk-14.2.7.tgz"
  "version" "14.2.7"
  dependencies:
    "tslib" "^2.3.0"
  optionalDependencies:
    "parse5" "^5.0.0"

"@angular/cli@14.2.11":
  "integrity" "sha512-k4Epob8Xz+9oyC6Ty9SNntTa2wHAvzxfcCi7itefPMcwEU9pqBcAv4XYfyawb5d7n/S5RBNwdsDpjoh2DPtmow=="
  "resolved" "https://registry.npmjs.org/@angular/cli/-/cli-14.2.11.tgz"
  "version" "14.2.11"
  dependencies:
    "@angular-devkit/architect" "0.1402.11"
    "@angular-devkit/core" "14.2.11"
    "@angular-devkit/schematics" "14.2.11"
    "@schematics/angular" "14.2.11"
    "@yarnpkg/lockfile" "1.1.0"
    "ansi-colors" "4.1.3"
    "debug" "4.3.4"
    "ini" "3.0.0"
    "inquirer" "8.2.4"
    "jsonc-parser" "3.1.0"
    "npm-package-arg" "9.1.0"
    "npm-pick-manifest" "7.0.1"
    "open" "8.4.0"
    "ora" "5.4.1"
    "pacote" "13.6.2"
    "resolve" "1.22.1"
    "semver" "7.3.7"
    "symbol-observable" "4.0.0"
    "uuid" "8.3.2"
    "yargs" "17.5.1"

"@angular/common@14.3.0":
  "integrity" "sha512-pV9oyG3JhGWeQ+TFB0Qub6a1VZWMNZ6/7zEopvYivdqa5yDLLDSBRWb6P80RuONXyGnM1pa7l5nYopX+r/23GQ=="
  "resolved" "https://registry.npmjs.org/@angular/common/-/common-14.3.0.tgz"
  "version" "14.3.0"
  dependencies:
    "tslib" "^2.3.0"

"@angular/compiler-cli@14.3.0":
  "integrity" "sha512-eoKpKdQ2X6axMgzcPUMZVYl3bIlTMzMeTo5V29No4BzgiUB+QoOTYGNJZkGRyqTNpwD9uSBJvmT2vG9+eC4ghQ=="
  "resolved" "https://registry.npmjs.org/@angular/compiler-cli/-/compiler-cli-14.3.0.tgz"
  "version" "14.3.0"
  dependencies:
    "@babel/core" "^7.17.2"
    "chokidar" "^3.0.0"
    "convert-source-map" "^1.5.1"
    "dependency-graph" "^0.11.0"
    "magic-string" "^0.26.0"
    "reflect-metadata" "^0.1.2"
    "semver" "^7.0.0"
    "sourcemap-codec" "^1.4.8"
    "tslib" "^2.3.0"
    "yargs" "^17.2.1"

"@angular/compiler@14.3.0":
  "integrity" "sha512-E15Rh0t3vA+bctbKnBCaDmLvc3ix+ZBt6yFZmhZalReQ+KpOlvOJv+L9oiFEgg+rYVl2QdvN7US1fvT0PqswLw=="
  "resolved" "https://registry.npmjs.org/@angular/compiler/-/compiler-14.3.0.tgz"
  "version" "14.3.0"
  dependencies:
    "tslib" "^2.3.0"

"@angular/core@14.3.0":
  "integrity" "sha512-wYiwItc0Uyn4FWZ/OAx/Ubp2/WrD3EgUJ476y1XI7yATGPF8n9Ld5iCXT08HOvc4eBcYlDfh90kTXR6/MfhzdQ=="
  "resolved" "https://registry.npmjs.org/@angular/core/-/core-14.3.0.tgz"
  "version" "14.3.0"
  dependencies:
    "tslib" "^2.3.0"

"@angular/flex-layout@12.0.0-beta.34":
  "integrity" "sha512-nLwKovXpyG+/U3Lbmfwt+q4ARupxtbPmFTZD0Y8oItFAV6/Oh9l+QQsNQa2VhOHAOrVagyDwcEM+SePtB5EmrQ=="
  "resolved" "https://registry.npmjs.org/@angular/flex-layout/-/flex-layout-12.0.0-beta.34.tgz"
  "version" "12.0.0-beta.34"
  dependencies:
    "tslib" "^2.1.0"

"@angular/forms@14.3.0":
  "integrity" "sha512-fBZZC2UFMom2AZPjGQzROPXFWO6kvCsPDKctjJwClVC8PuMrkm+RRyiYRdBbt2qxWHEqOZM2OCQo73xUyZOYHw=="
  "resolved" "https://registry.npmjs.org/@angular/forms/-/forms-14.3.0.tgz"
  "version" "14.3.0"
  dependencies:
    "tslib" "^2.3.0"

"@angular/language-service@14.3.0":
  "integrity" "sha512-Sij3OQzj1UGs1O8H9PxVAY/o27+oqZwQRnib66rsWvtbIBTjHp4FV3dTs5iVcr62GGv4V4Mff/2I82NP10GPQg=="
  "resolved" "https://registry.npmjs.org/@angular/language-service/-/language-service-14.3.0.tgz"
  "version" "14.3.0"

"@angular/material@14.2.7":
  "integrity" "sha512-WXHh8pEStpgkXZJmYOg2cI8BSHkV82ET4XTJCNPdveumaCn1UYnaNzsXD13kw5z+zmy8CufhFEzdXTrv/yt7KQ=="
  "resolved" "https://registry.npmjs.org/@angular/material/-/material-14.2.7.tgz"
  "version" "14.2.7"
  dependencies:
    "tslib" "^2.3.0"

"@angular/platform-browser-dynamic@14.3.0":
  "integrity" "sha512-rneZiMrIiYRhrkQvdL40E2ErKRn4Zdo6EtjBM9pAmWeyoM8oMnOZb9gz5vhrkNWg06kVMVg0yKqluP5How7j3A=="
  "resolved" "https://registry.npmjs.org/@angular/platform-browser-dynamic/-/platform-browser-dynamic-14.3.0.tgz"
  "version" "14.3.0"
  dependencies:
    "tslib" "^2.3.0"

"@angular/platform-browser@14.3.0":
  "integrity" "sha512-w9Y3740UmTz44T0Egvc+4QV9sEbO61L+aRHbpkLTJdlEGzHByZvxJmJyBYmdqeyTPwc/Zpy7c02frlpfAlyB7A=="
  "resolved" "https://registry.npmjs.org/@angular/platform-browser/-/platform-browser-14.3.0.tgz"
  "version" "14.3.0"
  dependencies:
    "tslib" "^2.3.0"

"@angular/router@14.3.0":
  "integrity" "sha512-uip0V7w7k7xyxxpTPbr7EuMnYLj3FzJrwkLVJSEw3TMMGHt5VU5t4BBa9veGZOta2C205XFrTAHnp8mD+XYY1w=="
  "resolved" "https://registry.npmjs.org/@angular/router/-/router-14.3.0.tgz"
  "version" "14.3.0"
  dependencies:
    "tslib" "^2.3.0"

"@assemblyscript/loader@^0.10.1":
  "integrity" "sha512-H71nDOOL8Y7kWRLqf6Sums+01Q5msqBW2KhDUTemh1tvY04eSkSXrK0uj/4mmY0Xr16/3zyZmsrxN7CKuRbNRg=="
  "resolved" "https://registry.npmjs.org/@assemblyscript/loader/-/loader-0.10.1.tgz"
  "version" "0.10.1"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.18.6", "@babel/code-frame@^7.27.1":
  "integrity" "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg=="
  "resolved" "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    "js-tokens" "^4.0.0"
    "picocolors" "^1.1.1"

"@babel/compat-data@^7.17.7", "@babel/compat-data@^7.18.8", "@babel/compat-data@^7.20.5", "@babel/compat-data@^7.27.2":
  "version" "7.27.3"

"@babel/core@^7.12.3", "@babel/core@^7.17.2", "@babel/core@7.18.10":
  "version" "7.18.10"
  dependencies:
    "@ampproject/remapping" "^2.1.0"
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.18.10"
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-module-transforms" "^7.18.9"
    "@babel/helpers" "^7.18.9"
    "@babel/parser" "^7.18.10"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.18.10"
    "@babel/types" "^7.18.10"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.1"
    "semver" "^6.3.0"

"@babel/generator@^7.18.10", "@babel/generator@7.18.12":
  "integrity" "sha512-dfQ8ebCN98SvyL7IxNMCUtZQSq5R7kxgN+r8qYTGDmmSion1hX2C0zq2yo1bsCDhXixokv1SAWTZUMYbO/V5zg=="
  "resolved" "https://registry.npmjs.org/@babel/generator/-/generator-7.18.12.tgz"
  "version" "7.18.12"
  dependencies:
    "@babel/types" "^7.18.10"
    "@jridgewell/gen-mapping" "^0.3.2"
    "jsesc" "^2.5.1"

"@babel/generator@^7.27.3":
  "version" "7.27.3"
  dependencies:
    "@babel/parser" "^7.27.3"
    "@babel/types" "^7.27.3"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    "jsesc" "^3.0.2"

"@babel/helper-annotate-as-pure@^7.18.6", "@babel/helper-annotate-as-pure@7.18.6":
  "integrity" "sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-annotate-as-pure@^7.27.1":
  "version" "7.27.3"
  dependencies:
    "@babel/types" "^7.27.3"

"@babel/helper-compilation-targets@^7.17.7", "@babel/helper-compilation-targets@^7.18.9", "@babel/helper-compilation-targets@^7.20.7", "@babel/helper-compilation-targets@^7.27.1":
  "version" "7.27.2"
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    "browserslist" "^4.24.0"
    "lru-cache" "^5.1.1"
    "semver" "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.21.0":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    "semver" "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.27.1":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "regexpu-core" "^6.2.0"
    "semver" "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.3.2", "@babel/helper-define-polyfill-provider@^0.3.3":
  "integrity" "sha512-z5aQKU4IzbqCC1XH0nAqfsFLMVSo22SBKUc0BxGrLkolTdPTructy0ToNnlO2zA4j9Q/7pjMZf0DSY+DSTYzww=="
  "resolved" "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@babel/helper-compilation-targets" "^7.17.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"
    "semver" "^6.1.2"

"@babel/helper-environment-visitor@^7.18.9":
  "integrity" "sha512-DoiN84+4Gnd0ncbBOM9AZENV4a5ZiL39HYMyZJGZ/AZEykHYdJw0wW3kdcsh9/Kn+BRXHLkkklZ51ecPKmI1CQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-member-expression-to-functions@^7.27.1":
  "version" "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-imports@^7.18.6", "@babel/helper-module-imports@^7.27.1":
  "version" "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.18.9", "@babel/helper-module-transforms@^7.27.1":
  "version" "7.27.3"
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.3"

"@babel/helper-optimise-call-expression@^7.27.1":
  "version" "7.27.1"
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.18.9", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "version" "7.27.1"

"@babel/helper-remap-async-to-generator@^7.18.6", "@babel/helper-remap-async-to-generator@^7.18.9":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-wrap-function" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-replace-supers@^7.27.1":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-skip-transparent-expression-wrappers@^7.20.0", "@babel/helper-skip-transparent-expression-wrappers@^7.27.1":
  "version" "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-string-parser@^7.27.1":
  "integrity" "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-identifier@^7.27.1":
  "integrity" "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-option@^7.18.6", "@babel/helper-validator-option@^7.27.1":
  "integrity" "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-wrap-function@^7.27.1":
  "version" "7.27.1"
  dependencies:
    "@babel/template" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helpers@^7.18.9":
  "version" "7.27.4"
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.3"

"@babel/parser@^7.14.7", "@babel/parser@^7.18.10", "@babel/parser@^7.27.2", "@babel/parser@^7.27.3", "@babel/parser@^7.27.4":
  "version" "7.27.4"
  dependencies:
    "@babel/types" "^7.27.3"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.18.9":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/plugin-transform-optional-chaining" "^7.27.1"

"@babel/plugin-proposal-async-generator-functions@^7.18.10", "@babel/plugin-proposal-async-generator-functions@7.18.10":
  "integrity" "sha512-1mFuY2TOsR1hxbjCo4QL+qlIjV07p4H4EUYw2J/WCqsvFV6V9X9z9YhXbWndc/4fw+hYGlDT7egYxliMp5O6Ew=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.18.10.tgz"
  "version" "7.18.10"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/helper-remap-async-to-generator" "^7.18.9"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.18.6":
  "integrity" "sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-class-static-block@^7.18.6":
  "integrity" "sha512-XP5G9MWNUskFuP30IfFSEFB0Z6HzLIUcjYM4bYOPHXl7eiJ9HFv8tWj6TXTN5QODiEhDZAeI4hLok2iHFFV4hw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.21.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-dynamic-import@^7.18.6":
  "integrity" "sha512-1auuwmK+Rz13SJj36R+jqFPMJWyKEDd7lLSdOj4oJK0UTgGueSAtkrCvz9ewmgyU/P941Rv2fQwZJN8s6QruXw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.18.9":
  "integrity" "sha512-k1NtHyOMvlDDFeb9G5PhUXuGj8m/wiwojgQVEhJ/fsVsMCpLyOP4h0uGEjYJKrRI+EVPlb5Jk+Gt9P97lOGwtA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.18.6":
  "integrity" "sha512-lr1peyn9kOdbYc0xr0OdHTZ5FMqS6Di+H0Fz2I/JwMzGmzJETNeOFq2pBySw6X/KFL5EWDjlJuMsUGRFb8fQgQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.18.9":
  "integrity" "sha512-y7C7cZgpMIjWlKE5T7eJwp+tnRYM89HmRvWM5EQuB5BoHEONjmQ8lSNmBUwOyy/GFRsohJED51YBF79hE1djug=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.18.6":
  "integrity" "sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.18.6":
  "integrity" "sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.18.9":
  "integrity" "sha512-d2S98yCiLxDVmBmE8UjGcfPvNEUbA1U5q5WxaWFUGRzJSVAZqm5W6MbPct0jxnegUZ0niLeNX+IOzEs7wYg9Dg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/compat-data" "^7.20.5"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.20.7"

"@babel/plugin-proposal-optional-catch-binding@^7.18.6":
  "integrity" "sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.18.9":
  "integrity" "sha512-p4zeefM72gpmEe2fkUr/OnOXpWEf8nAgk7ZYVqqfFiyIG7oFfVZcCrU64hWn5xp4tQ9LkV4bTIa5rD0KANpKNA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.18.6":
  "integrity" "sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-private-property-in-object@^7.18.6":
  "integrity" "sha512-0QZ8qP/***********************************************************+vzwTAg/sMWVNeWeNyaw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.11.tgz"
  "version" "7.21.11"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-create-class-features-plugin" "^7.21.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.18.6", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  "integrity" "sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  "integrity" "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  "integrity" "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  "integrity" "sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  "integrity" "sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-assertions@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  "integrity" "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  "integrity" "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  "integrity" "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  "integrity" "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-arrow-functions@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-async-to-generator@^7.18.6", "@babel/plugin-transform-async-to-generator@7.18.6":
  "integrity" "sha512-ARE5wZLKnTgPW7/1ftQmSi1CmkqqHo2DNmtztFhvgtOWSDfq0Cq9/9L+KnZNYSNrydBekhW3rwShduf59RoXag=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-remap-async-to-generator" "^7.18.6"

"@babel/plugin-transform-block-scoped-functions@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-block-scoping@^7.18.9":
  "version" "7.27.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-classes@^7.18.9":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.18.9":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/template" "^7.27.1"

"@babel/plugin-transform-destructuring@^7.18.9":
  "version" "7.27.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-dotall-regex@^7.18.6", "@babel/plugin-transform-dotall-regex@^7.4.4":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-duplicate-keys@^7.18.9":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-exponentiation-operator@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-for-of@^7.18.8":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-function-name@^7.18.9":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-literals@^7.18.9":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-member-expression-literals@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-amd@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-commonjs@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-systemjs@^7.18.9":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-modules-umd@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-named-capturing-groups-regex@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-new-target@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-object-super@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"

"@babel/plugin-transform-optional-chaining@^7.27.1":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-parameters@^7.18.8", "@babel/plugin-transform-parameters@^7.20.7":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-property-literals@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-regenerator@^7.18.6":
  "version" "7.27.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-reserved-words@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-runtime@7.18.10":
  "integrity" "sha512-q5mMeYAdfEbpBAgzl7tBre/la3LeCxmDO1+wMXRdPWbcoMjR3GiXlCLk7JBZVVye0bqTGNMbt0yYVXX1B1jEWQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.18.10.tgz"
  "version" "7.18.10"
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.9"
    "babel-plugin-polyfill-corejs2" "^0.3.2"
    "babel-plugin-polyfill-corejs3" "^0.5.3"
    "babel-plugin-polyfill-regenerator" "^0.4.0"
    "semver" "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-spread@^7.18.9":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-sticky-regex@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-template-literals@^7.18.9":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-typeof-symbol@^7.18.9":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-escapes@^7.18.10":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-regex@^7.18.6":
  "version" "7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/preset-env@7.18.10":
  "integrity" "sha512-wVxs1yjFdW3Z/XkNfXKoblxoHgbtUF7/l3PvvP4m02Qz9TZ6uZGxRVYjSQeR87oQmHco9zWitW5J82DJ7sCjvA=="
  "resolved" "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.18.10.tgz"
  "version" "7.18.10"
  dependencies:
    "@babel/compat-data" "^7.18.8"
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/helper-validator-option" "^7.18.6"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.18.6"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.18.9"
    "@babel/plugin-proposal-async-generator-functions" "^7.18.10"
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-proposal-class-static-block" "^7.18.6"
    "@babel/plugin-proposal-dynamic-import" "^7.18.6"
    "@babel/plugin-proposal-export-namespace-from" "^7.18.9"
    "@babel/plugin-proposal-json-strings" "^7.18.6"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.18.9"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.18.6"
    "@babel/plugin-proposal-numeric-separator" "^7.18.6"
    "@babel/plugin-proposal-object-rest-spread" "^7.18.9"
    "@babel/plugin-proposal-optional-catch-binding" "^7.18.6"
    "@babel/plugin-proposal-optional-chaining" "^7.18.9"
    "@babel/plugin-proposal-private-methods" "^7.18.6"
    "@babel/plugin-proposal-private-property-in-object" "^7.18.6"
    "@babel/plugin-proposal-unicode-property-regex" "^7.18.6"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.18.6"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.18.6"
    "@babel/plugin-transform-async-to-generator" "^7.18.6"
    "@babel/plugin-transform-block-scoped-functions" "^7.18.6"
    "@babel/plugin-transform-block-scoping" "^7.18.9"
    "@babel/plugin-transform-classes" "^7.18.9"
    "@babel/plugin-transform-computed-properties" "^7.18.9"
    "@babel/plugin-transform-destructuring" "^7.18.9"
    "@babel/plugin-transform-dotall-regex" "^7.18.6"
    "@babel/plugin-transform-duplicate-keys" "^7.18.9"
    "@babel/plugin-transform-exponentiation-operator" "^7.18.6"
    "@babel/plugin-transform-for-of" "^7.18.8"
    "@babel/plugin-transform-function-name" "^7.18.9"
    "@babel/plugin-transform-literals" "^7.18.9"
    "@babel/plugin-transform-member-expression-literals" "^7.18.6"
    "@babel/plugin-transform-modules-amd" "^7.18.6"
    "@babel/plugin-transform-modules-commonjs" "^7.18.6"
    "@babel/plugin-transform-modules-systemjs" "^7.18.9"
    "@babel/plugin-transform-modules-umd" "^7.18.6"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.18.6"
    "@babel/plugin-transform-new-target" "^7.18.6"
    "@babel/plugin-transform-object-super" "^7.18.6"
    "@babel/plugin-transform-parameters" "^7.18.8"
    "@babel/plugin-transform-property-literals" "^7.18.6"
    "@babel/plugin-transform-regenerator" "^7.18.6"
    "@babel/plugin-transform-reserved-words" "^7.18.6"
    "@babel/plugin-transform-shorthand-properties" "^7.18.6"
    "@babel/plugin-transform-spread" "^7.18.9"
    "@babel/plugin-transform-sticky-regex" "^7.18.6"
    "@babel/plugin-transform-template-literals" "^7.18.9"
    "@babel/plugin-transform-typeof-symbol" "^7.18.9"
    "@babel/plugin-transform-unicode-escapes" "^7.18.10"
    "@babel/plugin-transform-unicode-regex" "^7.18.6"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.18.10"
    "babel-plugin-polyfill-corejs2" "^0.3.2"
    "babel-plugin-polyfill-corejs3" "^0.5.3"
    "babel-plugin-polyfill-regenerator" "^0.4.0"
    "core-js-compat" "^3.22.1"
    "semver" "^6.3.0"

"@babel/preset-modules@^0.1.5":
  "integrity" "sha512-ID2yj6K/4lKfhuU3+EX4UvNbIt7eACFbHmNUjzA+ep+B5971CknnA/9DEWKbRokfbbtblxxxXFJJrH47UEAMVg=="
  "resolved" "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/runtime@^7.12.5", "@babel/runtime@7.18.9":
  "version" "7.18.9"
  dependencies:
    "regenerator-runtime" "^0.13.4"

"@babel/runtime@^7.26.7":
  "version" "7.27.4"

"@babel/template@^7.18.10", "@babel/template@7.18.10":
  "integrity" "sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA=="
  "resolved" "https://registry.npmjs.org/@babel/template/-/template-7.18.10.tgz"
  "version" "7.18.10"
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.18.10"
    "@babel/types" "^7.18.10"

"@babel/template@^7.27.1":
  "version" "7.27.2"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/template@^7.27.2":
  "version" "7.27.2"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.18.10", "@babel/traverse@^7.27.1", "@babel/traverse@^7.27.3":
  "version" "7.27.4"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.3"
    "@babel/parser" "^7.27.4"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.3"
    "debug" "^4.3.1"
    "globals" "^11.1.0"

"@babel/types@^7.18.10", "@babel/types@^7.18.6", "@babel/types@^7.24.7", "@babel/types@^7.27.1", "@babel/types@^7.27.3", "@babel/types@^7.4.4":
  "version" "7.27.3"
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@csstools/postcss-cascade-layers@^1.0.5":
  "integrity" "sha512-+KdYrpKC5TgomQr2DlZF4lDEpHcoxnj5IGddYYfBWJAKfj1JtuHUIqMa+E1pJJ+z3kvDViWMqyqPlG4Ja7amQA=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-cascade-layers/-/postcss-cascade-layers-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@csstools/selector-specificity" "^2.0.2"
    "postcss-selector-parser" "^6.0.10"

"@csstools/postcss-color-function@^1.1.1":
  "integrity" "sha512-Bc0f62WmHdtRDjf5f3e2STwRAl89N2CLb+9iAwzrv4L2hncrbDwnQD9PCq0gtAt7pOI2leIV08HIBUd4jxD8cw=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-color-function/-/postcss-color-function-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    "postcss-value-parser" "^4.2.0"

"@csstools/postcss-font-format-keywords@^1.0.1":
  "integrity" "sha512-ZgrlzuUAjXIOc2JueK0X5sZDjCtgimVp/O5CEqTcs5ShWBa6smhWYbS0x5cVc/+rycTDbjjzoP0KTDnUneZGOg=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-font-format-keywords/-/postcss-font-format-keywords-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"@csstools/postcss-hwb-function@^1.0.2":
  "integrity" "sha512-YHdEru4o3Rsbjmu6vHy4UKOXZD+Rn2zmkAmLRfPet6+Jz4Ojw8cbWxe1n42VaXQhD3CQUXXTooIy8OkVbUcL+w=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-hwb-function/-/postcss-hwb-function-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"@csstools/postcss-ic-unit@^1.0.1":
  "integrity" "sha512-Ot1rcwRAaRHNKC9tAqoqNZhjdYBzKk1POgWfhN4uCOE47ebGcLRqXjKkApVDpjifL6u2/55ekkpnFcp+s/OZUw=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-ic-unit/-/postcss-ic-unit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    "postcss-value-parser" "^4.2.0"

"@csstools/postcss-is-pseudo-class@^2.0.7":
  "integrity" "sha512-7JPeVVZHd+jxYdULl87lvjgvWldYu+Bc62s9vD/ED6/QTGjy0jy0US/f6BG53sVMTBJ1lzKZFpYmofBN9eaRiA=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-is-pseudo-class/-/postcss-is-pseudo-class-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "@csstools/selector-specificity" "^2.0.0"
    "postcss-selector-parser" "^6.0.10"

"@csstools/postcss-nested-calc@^1.0.0":
  "integrity" "sha512-JCsQsw1wjYwv1bJmgjKSoZNvf7R6+wuHDAbi5f/7MbFhl2d/+v+TvBTU4BJH3G1X1H87dHl0mh6TfYogbT/dJQ=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-nested-calc/-/postcss-nested-calc-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"@csstools/postcss-normalize-display-values@^1.0.1":
  "integrity" "sha512-jcOanIbv55OFKQ3sYeFD/T0Ti7AMXc9nM1hZWu8m/2722gOTxFg7xYu4RDLJLeZmPUVQlGzo4jhzvTUq3x4ZUw=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-normalize-display-values/-/postcss-normalize-display-values-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"@csstools/postcss-oklab-function@^1.1.1":
  "integrity" "sha512-nJpJgsdA3dA9y5pgyb/UfEzE7W5Ka7u0CX0/HIMVBNWzWemdcTH3XwANECU6anWv/ao4vVNLTMxhiPNZsTK6iA=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-oklab-function/-/postcss-oklab-function-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    "postcss-value-parser" "^4.2.0"

"@csstools/postcss-progressive-custom-properties@^1.1.0", "@csstools/postcss-progressive-custom-properties@^1.3.0":
  "integrity" "sha512-ASA9W1aIy5ygskZYuWams4BzafD12ULvSypmaLJT2jvQ8G0M3I8PRQhC0h7mG0Z3LI05+agZjqSR9+K9yaQQjA=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-progressive-custom-properties/-/postcss-progressive-custom-properties-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"@csstools/postcss-stepped-value-functions@^1.0.1":
  "integrity" "sha512-dz0LNoo3ijpTOQqEJLY8nyaapl6umbmDcgj4AD0lgVQ572b2eqA1iGZYTTWhrcrHztWDDRAX2DGYyw2VBjvCvQ=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-stepped-value-functions/-/postcss-stepped-value-functions-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"@csstools/postcss-text-decoration-shorthand@^1.0.0":
  "integrity" "sha512-c1XwKJ2eMIWrzQenN0XbcfzckOLLJiczqy+YvfGmzoVXd7pT9FfObiSEfzs84bpE/VqfpEuAZ9tCRbZkZxxbdw=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-text-decoration-shorthand/-/postcss-text-decoration-shorthand-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"@csstools/postcss-trigonometric-functions@^1.0.2":
  "integrity" "sha512-woKaLO///4bb+zZC2s80l+7cm07M7268MsyG3M0ActXXEFi6SuhvriQYcb58iiKGbjwwIU7n45iRLEHypB47Og=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-trigonometric-functions/-/postcss-trigonometric-functions-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"@csstools/postcss-unset-value@^1.0.2":
  "integrity" "sha512-c8J4roPBILnelAsdLr4XOAR/GsTm0GJi4XpcfvoWk3U6KiTCqiFYc63KhRMQQX35jYMp4Ao8Ij9+IZRgMfJp1g=="
  "resolved" "https://registry.npmjs.org/@csstools/postcss-unset-value/-/postcss-unset-value-1.0.2.tgz"
  "version" "1.0.2"

"@csstools/selector-specificity@^2.0.0", "@csstools/selector-specificity@^2.0.2":
  "integrity" "sha512-+OJ9konv95ClSTOJCmMZqpd5+YGsB2S+x6w3E1oaM8UuR5j8nTNHYSz8c9BEPGDOCMQYIEEGlVPj/VY64iTbGw=="
  "resolved" "https://registry.npmjs.org/@csstools/selector-specificity/-/selector-specificity-2.2.0.tgz"
  "version" "2.2.0"

"@ctrl/ngx-emoji-mart@^6.0.0":
  "integrity" "sha512-NHvWd7hRmcmS5kz6FmKXO7VTyrhuwCaqOvDjMLLSmDnWwTgmsxEXWdO7/fqOmOeyQUPp7zdkYuRhqPwfc1tKTg=="
  "resolved" "https://registry.npmjs.org/@ctrl/ngx-emoji-mart/-/ngx-emoji-mart-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "tslib" "^2.3.0"

"@ctrl/tinycolor@^3.4.0":
  "integrity" "sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA=="
  "resolved" "https://registry.npmjs.org/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz"
  "version" "3.6.1"

"@discoveryjs/json-ext@0.5.7":
  "integrity" "sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw=="
  "resolved" "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz"
  "version" "0.5.7"

"@gar/promisify@^1.1.3":
  "integrity" "sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw=="
  "resolved" "https://registry.npmjs.org/@gar/promisify/-/promisify-1.1.3.tgz"
  "version" "1.1.3"

"@isaacs/cliui@^8.0.2":
  "version" "8.0.2"
  dependencies:
    "string-width" "^5.1.2"
    "string-width-cjs" "npm:string-width@^4.2.0"
    "strip-ansi" "^7.0.1"
    "strip-ansi-cjs" "npm:strip-ansi@^6.0.1"
    "wrap-ansi" "^8.1.0"
    "wrap-ansi-cjs" "npm:wrap-ansi@^7.0.0"

"@istanbuljs/load-nyc-config@^1.0.0":
  "integrity" "sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ=="
  "resolved" "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "camelcase" "^5.3.1"
    "find-up" "^4.1.0"
    "get-package-type" "^0.1.0"
    "js-yaml" "^3.13.1"
    "resolve-from" "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  "integrity" "sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA=="
  "resolved" "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz"
  "version" "0.1.3"

"@jridgewell/gen-mapping@^0.1.0":
  "integrity" "sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w=="
  "resolved" "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "@jridgewell/set-array" "^1.0.0"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/gen-mapping@^0.3.2":
  "version" "0.3.8"
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/gen-mapping@^0.3.5":
  "version" "0.3.8"
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  "integrity" "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  "version" "3.1.2"

"@jridgewell/set-array@^1.0.0", "@jridgewell/set-array@^1.2.1":
  "integrity" "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A=="
  "resolved" "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  "version" "1.2.1"

"@jridgewell/source-map@^0.3.2", "@jridgewell/source-map@^0.3.3":
  "integrity" "sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  "integrity" "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  "version" "1.5.0"

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25", "@jridgewell/trace-mapping@^0.3.9":
  "integrity" "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  "version" "0.3.25"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@leichtgewicht/ip-codec@^2.0.1":
  "integrity" "sha512-Vo+PSpZG2/fmgmiNzYK9qWRh8h/CHrwD0mo1h1DzL4yzHNSfWYujGTYsWGreD000gcgmZ7K4Ys6Tx9TxtsKdDw=="
  "resolved" "https://registry.npmjs.org/@leichtgewicht/ip-codec/-/ip-codec-2.0.5.tgz"
  "version" "2.0.5"

"@ngtools/webpack@14.2.13":
  "integrity" "sha512-RQx/rGX7K/+R55x1R6Ax1JzyeHi8cW11dEXpzHWipyuSpusQLUN53F02eMB4VTakXsL3mFNWWy4bX3/LSq8/9w=="
  "resolved" "https://registry.npmjs.org/@ngtools/webpack/-/webpack-14.2.13.tgz"
  "version" "14.2.13"

"@ngx-translate/core@11.0.1":
  "integrity" "sha512-nBCa1ZD9fAUY/3eskP3Lql2fNg8OMrYIej1/5GRsfcutx9tG/5fZLCv9m6UCw1aS+u4uK/vXjv1ctG/FdMvaWg=="
  "resolved" "https://registry.npmjs.org/@ngx-translate/core/-/core-11.0.1.tgz"
  "version" "11.0.1"
  dependencies:
    "tslib" "^1.9.0"

"@ngx-translate/http-loader@4.0.0":
  "integrity" "sha512-x8LumqydWD7eX9yQTAVeoCM9gFUIGVTUjZqbxdAUavAA3qVnk9wCQux7iHLPXpydl8vyQmLoPQR+fFU+DUDOMA=="
  "resolved" "https://registry.npmjs.org/@ngx-translate/http-loader/-/http-loader-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "tslib" "^1.9.0"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  "integrity" "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@npmcli/fs@^2.1.0":
  "integrity" "sha512-yOJKRvohFOaLqipNtwYB9WugyZKhC/DZC4VYPmpaCzDBrA8YpK3qHZ8/HGscMnE4GqbkLNuVcCnxkeQEdGt6LQ=="
  "resolved" "https://registry.npmjs.org/@npmcli/fs/-/fs-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "@gar/promisify" "^1.1.3"
    "semver" "^7.3.5"

"@npmcli/git@^3.0.0":
  "integrity" "sha512-CAcd08y3DWBJqJDpfuVL0uijlq5oaXaOJEKHKc4wqrjd00gkvTZB+nFuLn+doOOKddaQS9JfqtNoFCO2LCvA3w=="
  "resolved" "https://registry.npmjs.org/@npmcli/git/-/git-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "@npmcli/promise-spawn" "^3.0.0"
    "lru-cache" "^7.4.4"
    "mkdirp" "^1.0.4"
    "npm-pick-manifest" "^7.0.0"
    "proc-log" "^2.0.0"
    "promise-inflight" "^1.0.1"
    "promise-retry" "^2.0.1"
    "semver" "^7.3.5"
    "which" "^2.0.2"

"@npmcli/installed-package-contents@^1.0.7":
  "integrity" "sha512-9rufe0wnJusCQoLpV9ZPKIVP55itrM5BxOXs10DmdbRfgWtHy1LDyskbwRnBghuB0PrF7pNPOqREVtpz4HqzKw=="
  "resolved" "https://registry.npmjs.org/@npmcli/installed-package-contents/-/installed-package-contents-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "npm-bundled" "^1.1.1"
    "npm-normalize-package-bin" "^1.0.1"

"@npmcli/move-file@^2.0.0":
  "integrity" "sha512-mJd2Z5TjYWq/ttPLLGqArdtnC74J6bOzg4rMDnN+p1xTacZ2yPRCk2y0oSWQtygLR9YVQXgOcONrwtnk3JupxQ=="
  "resolved" "https://registry.npmjs.org/@npmcli/move-file/-/move-file-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mkdirp" "^1.0.4"
    "rimraf" "^3.0.2"

"@npmcli/node-gyp@^2.0.0":
  "integrity" "sha512-doNI35wIe3bBaEgrlPfdJPaCpUR89pJWep4Hq3aRdh6gKazIVWfs0jHttvSSoq47ZXgC7h73kDsUl8AoIQUB+A=="
  "resolved" "https://registry.npmjs.org/@npmcli/node-gyp/-/node-gyp-2.0.0.tgz"
  "version" "2.0.0"

"@npmcli/promise-spawn@^3.0.0":
  "integrity" "sha512-s9SgS+p3a9Eohe68cSI3fi+hpcZUmXq5P7w0kMlAsWVtR7XbK3ptkZqKT2cK1zLDObJ3sR+8P59sJE0w/KTL1g=="
  "resolved" "https://registry.npmjs.org/@npmcli/promise-spawn/-/promise-spawn-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "infer-owner" "^1.0.4"

"@npmcli/run-script@^4.1.0":
  "integrity" "sha512-7dqywvVudPSrRCW5nTHpHgeWnbBtz8cFkOuKrecm6ih+oO9ciydhWt6OF7HlqupRRmB8Q/gECVdB9LMfToJbRg=="
  "resolved" "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "@npmcli/node-gyp" "^2.0.0"
    "@npmcli/promise-spawn" "^3.0.0"
    "node-gyp" "^9.0.0"
    "read-package-json-fast" "^2.0.3"
    "which" "^2.0.2"

"@one-ini/wasm@0.1.1":
  "integrity" "sha512-XuySG1E38YScSJoMlqovLru4KTUNSjgVTIjyh7qMX6aNN5HY5Ct5LhRJdxO79JtTzKfzV/bnWpz+zquYrISsvw=="
  "resolved" "https://registry.npmjs.org/@one-ini/wasm/-/wasm-0.1.1.tgz"
  "version" "0.1.1"

"@pkgjs/parseargs@^0.11.0":
  "integrity" "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg=="
  "resolved" "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  "version" "0.11.0"

"@pnpm/config.env-replace@^1.1.0":
  "integrity" "sha512-htyl8TWnKL7K/ESFa1oW2UB5lVDxuF5DpM7tBi6Hu2LNL3mWkIzNLG6N4zoCUP1lCKNxWy/3iu8mS8MvToGd6w=="
  "resolved" "https://registry.npmjs.org/@pnpm/config.env-replace/-/config.env-replace-1.1.0.tgz"
  "version" "1.1.0"

"@pnpm/network.ca-file@^1.0.1":
  "integrity" "sha512-YcPQ8a0jwYU9bTdJDpXjMi7Brhkr1mXsXrUJvjqM2mQDgkRiz8jFaQGOdaLxgjtUfQgZhKy/O3cG/YwmgKaxLA=="
  "resolved" "https://registry.npmjs.org/@pnpm/network.ca-file/-/network.ca-file-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "graceful-fs" "4.2.10"

"@pnpm/npm-conf@^2.1.0":
  "integrity" "sha512-c83qWb22rNRuB0UaVCI0uRPNRr8Z0FWnEIvT47jiHAmOIUHbBOg5XvV7pM5x+rKn9HRpjxquDbXYSXr3fAKFcw=="
  "resolved" "https://registry.npmjs.org/@pnpm/npm-conf/-/npm-conf-2.3.1.tgz"
  "version" "2.3.1"
  dependencies:
    "@pnpm/config.env-replace" "^1.1.0"
    "@pnpm/network.ca-file" "^1.0.1"
    "config-chain" "^1.1.11"

"@rollup/plugin-json@^4.1.0":
  "integrity" "sha512-yfLbTdNS6amI/2OpmbiBoW12vngr5NW2jCJVZSBEz+H5KfUJZ2M7sDjk0U6GOOdCWFVScShte29o9NezJ53TPw=="
  "resolved" "https://registry.npmjs.org/@rollup/plugin-json/-/plugin-json-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@rollup/pluginutils" "^3.0.8"

"@rollup/plugin-node-resolve@^13.1.3":
  "integrity" "sha512-Lus8rbUo1eEcnS4yTFKLZrVumLPY+YayBdWXgFSHYhTT2iJbMhoaaBL3xl5NCdeRytErGr8tZ0L71BMRmnlwSw=="
  "resolved" "https://registry.npmjs.org/@rollup/plugin-node-resolve/-/plugin-node-resolve-13.3.0.tgz"
  "version" "13.3.0"
  dependencies:
    "@rollup/pluginutils" "^3.1.0"
    "@types/resolve" "1.17.1"
    "deepmerge" "^4.2.2"
    "is-builtin-module" "^3.1.0"
    "is-module" "^1.0.0"
    "resolve" "^1.19.0"

"@rollup/pluginutils@^3.0.8", "@rollup/pluginutils@^3.0.9", "@rollup/pluginutils@^3.1.0":
  "integrity" "sha512-GksZ6pr6TpIjHm8h9lSQ8pi8BE9VeubNT0OMJ3B5uZJ8pz73NPiqOtCog/x2/QzM1ENChPKxMDhiQuRHsqc+lg=="
  "resolved" "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "@types/estree" "0.0.39"
    "estree-walker" "^1.0.1"
    "picomatch" "^2.2.2"

"@schematics/angular@14.2.11":
  "integrity" "sha512-tejU2BOc25bQO34mZmTwmtAfOiFtDE/io/yHqYgUsTn804kyMQbz2QOOXN0epdzRYrkAHvH4KV8c2LDyO6iijA=="
  "resolved" "https://registry.npmjs.org/@schematics/angular/-/angular-14.2.11.tgz"
  "version" "14.2.11"
  dependencies:
    "@angular-devkit/core" "14.2.11"
    "@angular-devkit/schematics" "14.2.11"
    "jsonc-parser" "3.1.0"

"@socket.io/component-emitter@~3.1.0":
  "integrity" "sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA=="
  "resolved" "https://registry.npmjs.org/@socket.io/component-emitter/-/component-emitter-3.1.2.tgz"
  "version" "3.1.2"

"@tootallnate/once@2":
  "integrity" "sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A=="
  "resolved" "https://registry.npmjs.org/@tootallnate/once/-/once-2.0.0.tgz"
  "version" "2.0.0"

"@types/backbone@1.4.1":
  "integrity" "sha512-KYfGuQy4d2vvYXbn0uHFZ6brFLndatTMomxBlljpbWf4kFpA3BG/6LA3ec+J9iredrX6eAVI7sm9SVAvwiIM6g=="
  "resolved" "https://registry.npmjs.org/@types/backbone/-/backbone-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "@types/jquery" "*"
    "@types/underscore" "*"

"@types/body-parser@*":
  "integrity" "sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg=="
  "resolved" "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.5.tgz"
  "version" "1.19.5"
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.9":
  "integrity" "sha512-z9fJ5Im06zvUL548KvYNecEVlA7cVDkGUi6kZusb04mpyEFKCIZJvloCcmpmLaIahDpOQGHaHmG6imtPMmPXGQ=="
  "resolved" "https://registry.npmjs.org/@types/bonjour/-/bonjour-3.5.13.tgz"
  "version" "3.5.13"
  dependencies:
    "@types/node" "*"

"@types/codemirror@^5.60.7":
  "version" "5.60.16"
  dependencies:
    "@types/tern" "*"

"@types/connect-history-api-fallback@^1.3.5":
  "integrity" "sha512-n6Cr2xS1h4uAulPRdlw6Jl6s1oG8KrVilPN2yUITEs+K48EzMJJ3W1xy8K5eWuFvjp3R74AOIGSmp2UfBJ8HFw=="
  "resolved" "https://registry.npmjs.org/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.5.4.tgz"
  "version" "1.5.4"
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  "integrity" "sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug=="
  "resolved" "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz"
  "version" "3.4.38"
  dependencies:
    "@types/node" "*"

"@types/cors@^2.8.12":
  "version" "2.8.18"
  dependencies:
    "@types/node" "*"

"@types/d3-array@^1":
  "version" "1.2.8"

"@types/d3-axis@^1":
  "version" "1.0.14"
  dependencies:
    "@types/d3-selection" "^1"

"@types/d3-brush@^1":
  "version" "1.1.4"
  dependencies:
    "@types/d3-selection" "^1"

"@types/d3-chord@^1":
  "version" "1.0.10"

"@types/d3-collection@*":
  "version" "1.0.10"

"@types/d3-color@^1":
  "version" "1.4.1"

"@types/d3-contour@^1":
  "version" "1.3.1"
  dependencies:
    "@types/d3-array" "^1"
    "@types/geojson" "*"

"@types/d3-dispatch@^1":
  "version" "1.0.9"

"@types/d3-drag@^1":
  "version" "1.2.5"
  dependencies:
    "@types/d3-selection" "^1"

"@types/d3-dsv@^1":
  "version" "1.2.1"

"@types/d3-ease@^1":
  "version" "1.0.10"

"@types/d3-fetch@^1":
  "version" "1.2.2"
  dependencies:
    "@types/d3-dsv" "^1"

"@types/d3-force@^1":
  "version" "1.2.2"

"@types/d3-format@^1":
  "version" "1.4.1"

"@types/d3-geo@^1":
  "version" "1.12.1"
  dependencies:
    "@types/geojson" "*"

"@types/d3-hierarchy@^1":
  "version" "1.1.7"

"@types/d3-interpolate@^1":
  "version" "1.4.2"
  dependencies:
    "@types/d3-color" "^1"

"@types/d3-path@^1":
  "version" "1.0.9"

"@types/d3-polygon@^1":
  "version" "1.0.8"

"@types/d3-quadtree@^1":
  "version" "1.0.8"

"@types/d3-random@^1":
  "version" "1.1.3"

"@types/d3-scale-chromatic@^1":
  "version" "1.5.1"

"@types/d3-scale@^2":
  "version" "2.2.4"
  dependencies:
    "@types/d3-time" "^1"

"@types/d3-selection@^1":
  "version" "1.4.3"

"@types/d3-shape@^1":
  "version" "1.3.5"
  dependencies:
    "@types/d3-path" "^1"

"@types/d3-time-format@^2":
  "version" "2.3.1"

"@types/d3-time@^1":
  "version" "1.1.1"

"@types/d3-timer@^1":
  "version" "1.0.10"

"@types/d3-transition@^1":
  "version" "1.3.1"
  dependencies:
    "@types/d3-selection" "^1"

"@types/d3-voronoi@*":
  "version" "1.1.9"

"@types/d3-zoom@^1":
  "version" "1.8.2"
  dependencies:
    "@types/d3-interpolate" "^1"
    "@types/d3-selection" "^1"

"@types/d3@^5.7.2":
  "version" "5.16.4"
  dependencies:
    "@types/d3-array" "^1"
    "@types/d3-axis" "^1"
    "@types/d3-brush" "^1"
    "@types/d3-chord" "^1"
    "@types/d3-collection" "*"
    "@types/d3-color" "^1"
    "@types/d3-contour" "^1"
    "@types/d3-dispatch" "^1"
    "@types/d3-drag" "^1"
    "@types/d3-dsv" "^1"
    "@types/d3-ease" "^1"
    "@types/d3-fetch" "^1"
    "@types/d3-force" "^1"
    "@types/d3-format" "^1"
    "@types/d3-geo" "^1"
    "@types/d3-hierarchy" "^1"
    "@types/d3-interpolate" "^1"
    "@types/d3-path" "^1"
    "@types/d3-polygon" "^1"
    "@types/d3-quadtree" "^1"
    "@types/d3-random" "^1"
    "@types/d3-scale" "^2"
    "@types/d3-scale-chromatic" "^1"
    "@types/d3-selection" "^1"
    "@types/d3-shape" "^1"
    "@types/d3-time" "^1"
    "@types/d3-time-format" "^2"
    "@types/d3-timer" "^1"
    "@types/d3-transition" "^1"
    "@types/d3-voronoi" "*"
    "@types/d3-zoom" "^1"

"@types/eslint-scope@^3.7.3":
  "version" "3.7.7"
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  "integrity" "sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag=="
  "resolved" "https://registry.npmjs.org/@types/eslint/-/eslint-9.6.1.tgz"
  "version" "9.6.1"
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*":
  "version" "1.0.7"

"@types/estree@^0.0.51":
  "version" "0.0.51"

"@types/estree@0.0.39":
  "integrity" "sha512-EYNwp3bU+98cpU4lAWYYL7Zz+2gryWH1qbdDTidVd6hkiR6weksdbMadyXKXNPEkQFhXM+hVO9ZygomHXp+AIw=="
  "resolved" "https://registry.npmjs.org/@types/estree/-/estree-0.0.39.tgz"
  "version" "0.0.39"

"@types/express-serve-static-core@*":
  "version" "5.0.6"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express-serve-static-core@^4.17.33":
  "integrity" "sha512-N4LZ2xG7DatVqhCZzOGb1Yi5lMbXSZcmdLDe9EzSndPV2HpWYWzRbaerl2n27irrm94EPpprqa8KpskPT085+A=="
  "resolved" "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.19.6.tgz"
  "version" "4.19.6"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*", "@types/express@^4.17.13":
  "version" "4.17.22"
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/geojson@*":
  "version" "7946.0.7"

"@types/http-errors@*":
  "integrity" "sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA=="
  "resolved" "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.4.tgz"
  "version" "2.0.4"

"@types/http-proxy@^1.17.8":
  "version" "1.17.16"
  dependencies:
    "@types/node" "*"

"@types/jasmine@*", "@types/jasmine@3.4.0":
  "integrity" "sha512-6pUnBg6DuSB55xnxJ5+gW9JOkFrPsXkYAuqqEE8oyrpgDiPQ+TZ+1Zt4S+CHcRJcxyNYXeIXG4vHSzdF6y9Uvw=="
  "resolved" "https://registry.npmjs.org/@types/jasmine/-/jasmine-3.4.0.tgz"
  "version" "3.4.0"

"@types/jasminewd2@2.0.6":
  "integrity" "sha512-2ZOKrxb8bKRmP/po5ObYnRDgFE4i+lQiEB27bAMmtMWLgJSqlIDqlLx6S0IRorpOmOPRQ6O80NujTmQAtBkeNw=="
  "resolved" "https://registry.npmjs.org/@types/jasminewd2/-/jasminewd2-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "@types/jasmine" "*"

"@types/jquery@*", "@types/jquery@3.3.31":
  "integrity" "sha512-Lz4BAJihoFw5nRzKvg4nawXPzutkv7wmfQ5121avptaSIXlDNJCUuxZxX/G+9EVidZGuO0UBlk+YjKbwRKJigg=="
  "resolved" "https://registry.npmjs.org/@types/jquery/-/jquery-3.3.31.tgz"
  "version" "3.3.31"
  dependencies:
    "@types/sizzle" "*"

"@types/js-beautify@^1.13.3":
  "integrity" "sha512-FMbQHz+qd9DoGvgLHxeqqVPaNRffpIu5ZjozwV8hf9JAGpIOzuAf4wGbRSo8LNITHqGjmmVjaMggTT5P4v4IHg=="
  "resolved" "https://registry.npmjs.org/@types/js-beautify/-/js-beautify-1.14.3.tgz"
  "version" "1.14.3"

"@types/json-schema@*", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  "integrity" "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA=="
  "resolved" "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz"
  "version" "7.0.15"

"@types/lodash@4.14.137":
  "integrity" "sha512-g4rNK5SRKloO+sUGbuO7aPtwbwzMgjK+bm9BBhLD7jGUiGR7zhwYEhSln/ihgYQBeIJ5j7xjyaYzrWTcu3UotQ=="
  "resolved" "https://registry.npmjs.org/@types/lodash/-/lodash-4.14.137.tgz"
  "version" "4.14.137"

"@types/mime@^1":
  "integrity" "sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w=="
  "resolved" "https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz"
  "version" "1.3.5"

"@types/node-forge@^1.3.0":
  "integrity" "sha512-FQx220y22OKNTqaByeBGqHWYz4cl94tpcxeFdvBo3wjG6XPBuZ0BNgNZRV5J5TFmmcsJ4IzsLkmGRiQbnYsBEQ=="
  "resolved" "https://registry.npmjs.org/@types/node-forge/-/node-forge-1.3.11.tgz"
  "version" "1.3.11"
  dependencies:
    "@types/node" "*"

"@types/node@*", "@types/node@>=10.0.0", "@types/node@~12.7.1":
  "integrity" "sha512-KPYGmfD0/b1eXurQ59fXD1GBzhSQfz6/lKBxkaHX9dKTzjXbK68Zt7yGUxUsCS1jeTy/8aL+d9JEr+S54mpkWQ=="
  "resolved" "https://registry.npmjs.org/@types/node/-/node-12.7.12.tgz"
  "version" "12.7.12"

"@types/parse-json@^4.0.0":
  "integrity" "sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw=="
  "resolved" "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.2.tgz"
  "version" "4.0.2"

"@types/q@^0.0.32":
  "integrity" "sha512-qYi3YV9inU/REEfxwVcGZzbS3KG/Xs90lv0Pr+lDtuVjBPGd1A+eciXzVSaRvLify132BfcvhvEjeVahrUl0Ug=="
  "resolved" "https://registry.npmjs.org/@types/q/-/q-0.0.32.tgz"
  "version" "0.0.32"

"@types/qs@*":
  "version" "6.14.0"

"@types/raf@^3.4.0":
  "integrity" "sha512-c4YAvMedbPZ5tEyxzQdMoOhhJ4RD3rngZIdwC2/qDN3d7JpEhB6fiBRKVY1lg5B7Wk+uPBjn5f39j1/2MY1oOw=="
  "resolved" "https://registry.npmjs.org/@types/raf/-/raf-3.4.3.tgz"
  "version" "3.4.3"

"@types/range-parser@*":
  "integrity" "sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ=="
  "resolved" "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz"
  "version" "1.2.7"

"@types/resolve@1.17.1":
  "integrity" "sha512-yy7HuzQhj0dhGpD8RLXSZWEkLsV9ibvxvi6EiJ3bkqLAO1RGo0WbkWQiwpRlSFymTJRz0d3k5LM3kkx8ArDbLw=="
  "resolved" "https://registry.npmjs.org/@types/resolve/-/resolve-1.17.1.tgz"
  "version" "1.17.1"
  dependencies:
    "@types/node" "*"

"@types/retry@0.12.0":
  "integrity" "sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA=="
  "resolved" "https://registry.npmjs.org/@types/retry/-/retry-0.12.0.tgz"
  "version" "0.12.0"

"@types/selenium-webdriver@^3.0.0":
  "version" "3.0.17"

"@types/send@*":
  "integrity" "sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA=="
  "resolved" "https://registry.npmjs.org/@types/send/-/send-0.17.4.tgz"
  "version" "0.17.4"
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-index@^1.9.1":
  "integrity" "sha512-qLpGZ/c2fhSs5gnYsQxtDEq3Oy8SXPClIXkW5ghvAvsNuVSA8k+gCONcUCS/UjLEYvYps+e8uBtfgXgvhwfNug=="
  "resolved" "https://registry.npmjs.org/@types/serve-index/-/serve-index-1.9.4.tgz"
  "version" "1.9.4"
  dependencies:
    "@types/express" "*"

"@types/serve-static@*", "@types/serve-static@^1.13.10":
  "integrity" "sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw=="
  "resolved" "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.7.tgz"
  "version" "1.15.7"
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/sizzle@*":
  "version" "2.3.2"

"@types/sockjs@^0.3.33":
  "integrity" "sha512-MK9V6NzAS1+Ud7JV9lJLFqW85VbC9dq3LmwZCuBe4wBDgKC0Kj/jd8Xl+nSviU+Qc3+m7umHHyHg//2KSa0a0Q=="
  "resolved" "https://registry.npmjs.org/@types/sockjs/-/sockjs-0.3.36.tgz"
  "version" "0.3.36"
  dependencies:
    "@types/node" "*"

"@types/tern@*":
  "integrity" "sha512-ypzHFE/wBzh+BlH6rrBgS5I/Z7RD21pGhZ2rltb/+ZrVM1awdZwjx7hE5XfuYgHWk9uvV5HLZN3SloevCAp3Bw=="
  "resolved" "https://registry.npmjs.org/@types/tern/-/tern-0.23.9.tgz"
  "version" "0.23.9"
  dependencies:
    "@types/estree" "*"

"@types/trusted-types@^2.0.7":
  "integrity" "sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw=="
  "resolved" "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.7.tgz"
  "version" "2.0.7"

"@types/underscore@*":
  "version" "1.10.24"

"@types/ws@^8.5.1":
  "version" "8.18.1"
  dependencies:
    "@types/node" "*"

"@webassemblyjs/ast@1.11.1":
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"

"@webassemblyjs/floating-point-hex-parser@1.11.1":
  "version" "1.11.1"

"@webassemblyjs/helper-api-error@1.11.1":
  "version" "1.11.1"

"@webassemblyjs/helper-buffer@1.11.1":
  "version" "1.11.1"

"@webassemblyjs/helper-numbers@1.11.1":
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.1":
  "version" "1.11.1"

"@webassemblyjs/helper-wasm-section@1.11.1":
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"

"@webassemblyjs/ieee754@1.11.1":
  "version" "1.11.1"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.1":
  "version" "1.11.1"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.1":
  "version" "1.11.1"

"@webassemblyjs/wasm-edit@1.11.1":
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/helper-wasm-section" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-opt" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "@webassemblyjs/wast-printer" "1.11.1"

"@webassemblyjs/wasm-gen@1.11.1":
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wasm-opt@1.11.1":
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"

"@webassemblyjs/wasm-parser@1.11.1":
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wast-printer@1.11.1":
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA=="
  "resolved" "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ=="
  "resolved" "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz"
  "version" "4.2.2"

"@yarnpkg/lockfile@1.1.0":
  "integrity" "sha512-GpSwvyXOcOOlV70vbnzjj4fW5xW/FdUF6nQEt1ENy7m4ZCczi1+/buVUPAqmGfqznsORNFzUMjctTIp8a9tuCQ=="
  "resolved" "https://registry.npmjs.org/@yarnpkg/lockfile/-/lockfile-1.1.0.tgz"
  "version" "1.1.0"

"abab@^2.0.6":
  "integrity" "sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA=="
  "resolved" "https://registry.npmjs.org/abab/-/abab-2.0.6.tgz"
  "version" "2.0.6"

"abbrev@^1.0.0":
  "integrity" "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q=="
  "resolved" "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz"
  "version" "1.1.1"

"abbrev@^2.0.0":
  "integrity" "sha512-6/mh1E2u2YgEsCHdY0Yx5oW+61gZU+1vXaoiHHrpKeuRNNgFvS+/jrwHiQhB5apAf5oB7UB7E19ol2R2LKH8hQ=="
  "resolved" "https://registry.npmjs.org/abbrev/-/abbrev-2.0.0.tgz"
  "version" "2.0.0"

"accepts@~1.3.4", "accepts@~1.3.8":
  "integrity" "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw=="
  "resolved" "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
  "version" "1.3.8"
  dependencies:
    "mime-types" "~2.1.34"
    "negotiator" "0.6.3"

"acorn-import-assertions@^1.7.6":
  "version" "1.9.0"

"acorn@^8.14.0", "acorn@^8.5.0", "acorn@^8.7.1":
  "integrity" "sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz"
  "version" "8.14.1"

"adjust-sourcemap-loader@^4.0.0":
  "integrity" "sha512-OXwN5b9pCUXNQHJpwwD2qP40byEmSgzj8B4ydSN0uMNYWiFmJ6x6KwUllMmfk8Rwu/HJDFR7U8ubsWBoN0Xp0A=="
  "resolved" "https://registry.npmjs.org/adjust-sourcemap-loader/-/adjust-sourcemap-loader-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "loader-utils" "^2.0.0"
    "regex-parser" "^2.2.11"

"adm-zip@^0.4.9":
  "version" "0.4.16"

"ag-grid-angular@^22.0.0":
  "integrity" "sha512-I1ynYxc+Chb8ecbYkM/+GM+mXCr/rnL+lHTXejBQvDFd61W1nTP2b8BiHwkxaJBDPcFrTyS5P2xqLXLkEzfm6g=="
  "resolved" "https://registry.npmjs.org/ag-grid-angular/-/ag-grid-angular-22.1.1.tgz"
  "version" "22.1.1"

"ag-grid-community@^22.0.0":
  "integrity" "sha512-FNyv9e9JIuuR8NNi/r3NjIjUVy2/K5GgPjwQ63g9/Z4U8EudQZLINGMVKI6OwtZfWyyNSd0hQDCNsdvx0OR1WQ=="
  "resolved" "https://registry.npmjs.org/ag-grid-community/-/ag-grid-community-22.1.1.tgz"
  "version" "22.1.1"

"agent-base@^4.3.0":
  "version" "4.3.0"
  dependencies:
    "es6-promisify" "^5.0.0"

"agent-base@^6.0.2":
  "integrity" "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ=="
  "resolved" "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "debug" "4"

"agent-base@6":
  "integrity" "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ=="
  "resolved" "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "debug" "4"

"agentkeepalive@^4.2.1":
  "version" "4.6.0"
  dependencies:
    "humanize-ms" "^1.2.1"

"aggregate-error@^3.0.0":
  "integrity" "sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA=="
  "resolved" "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "clean-stack" "^2.0.0"
    "indent-string" "^4.0.0"

"ajv-formats@^2.1.1", "ajv-formats@2.1.1":
  "integrity" "sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA=="
  "resolved" "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ajv" "^8.0.0"

"ajv-keywords@^3.5.2":
  "integrity" "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv-keywords@^5.1.0":
  "integrity" "sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw=="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "fast-deep-equal" "^3.1.3"

"ajv@^6.12.3", "ajv@^6.12.4", "ajv@^6.12.5":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.0.0":
  "integrity" "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz"
  "version" "8.17.1"
  dependencies:
    "fast-deep-equal" "^3.1.3"
    "fast-uri" "^3.0.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"

"ajv@^8.10.0":
  "integrity" "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz"
  "version" "8.17.1"
  dependencies:
    "fast-deep-equal" "^3.1.3"
    "fast-uri" "^3.0.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"

"ajv@^8.9.0":
  "integrity" "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz"
  "version" "8.17.1"
  dependencies:
    "fast-deep-equal" "^3.1.3"
    "fast-uri" "^3.0.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"

"ajv@8.11.0":
  "integrity" "sha512-wGgprdCvMalC0BztXvitD2hC04YffAvtsUn93JbGXYLAtCUO4xd17mCCZQxUOItiBwZvJScWo8NIvQMQ71rdpg=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.11.0.tgz"
  "version" "8.11.0"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"alwan@^2.0.2":
  "integrity" "sha512-IxWc+wSY4rDdqmH+t5zBT88q5B+v3Zmw+dBhzG1lMVbJ3Jda65cPfWACvWrUjzeQyblsL406FymLqdVTYnyVzQ=="
  "resolved" "https://registry.npmjs.org/alwan/-/alwan-2.1.1.tgz"
  "version" "2.1.1"

"angular-froala-wysiwyg@4.0.19":
  "integrity" "sha512-vzYaOYgYxGYg9O5XvefXajVcw7/0/c8kdWnKh0vhAvUXCuvLRiuRBfhAjcqWTEgHQxMofEUKUAFp3QoN3bPQGg=="
  "resolved" "https://registry.npmjs.org/angular-froala-wysiwyg/-/angular-froala-wysiwyg-4.0.19.tgz"
  "version" "4.0.19"
  dependencies:
    "froala-editor" "4.0.19"
    "tslib" "^2.0.0"

"angular-gridster2@8.0.0":
  "integrity" "sha512-mSgOvRfhEKAERU63SsunI/NWajU2wXVTToR6sA5JM1sRptK6PLoAIi9VqcNETTmdRssflOFwJJqBqEeRgelUvg=="
  "resolved" "https://registry.npmjs.org/angular-gridster2/-/angular-gridster2-8.0.0.tgz"
  "version" "8.0.0"
  dependencies:
    "tslib" "^1.9.0"

"angular-resizable-element@^3.3.3":
  "version" "3.3.3"
  dependencies:
    "tslib" "^1.9.0"

"ansi-colors@^1.0.1":
  "integrity" "sha512-SFKX67auSNoVR38N3L+nvsPjOE0bybKTYbkf5tRvushrAPQ9V75huw0ZxBkKVeRU9kqH3d6HA4xTckbwZ4ixmA=="
  "resolved" "https://registry.npmjs.org/ansi-colors/-/ansi-colors-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ansi-wrap" "^0.1.0"

"ansi-colors@^3.0.5":
  "integrity" "sha512-hHUXGagefjN2iRrID63xckIvotOXOojhQKWIPUZ4mNUZ9nLZW+7FMNoE1lOkEhNWYsx/7ysGIuJYCiMAA9FnrA=="
  "resolved" "https://registry.npmjs.org/ansi-colors/-/ansi-colors-3.2.4.tgz"
  "version" "3.2.4"

"ansi-colors@^4.1.1":
  "integrity" "sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw=="
  "resolved" "https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.3.tgz"
  "version" "4.1.3"

"ansi-colors@4.1.3":
  "integrity" "sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw=="
  "resolved" "https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.3.tgz"
  "version" "4.1.3"

"ansi-escapes@^4.2.1":
  "integrity" "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ=="
  "resolved" "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-html-community@^0.0.8":
  "integrity" "sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw=="
  "resolved" "https://registry.npmjs.org/ansi-html-community/-/ansi-html-community-0.0.8.tgz"
  "version" "0.0.8"

"ansi-regex@^2.0.0":
  "version" "2.1.1"

"ansi-regex@^5.0.0":
  "version" "5.0.0"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-regex@^6.0.1":
  "version" "6.1.0"

"ansi-styles@^2.2.1":
  "integrity" "sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^6.1.0":
  "version" "6.2.1"

"ansi-wrap@^0.1.0":
  "integrity" "sha512-ZyznvL8k/FZeQHr2T6LzcJ/+vBApDnMNZvfVFy3At0knswWd6rJ3/0Hhmpu8oqa6C92npmozs890sX9Dl6q+Qw=="
  "resolved" "https://registry.npmjs.org/ansi-wrap/-/ansi-wrap-0.1.0.tgz"
  "version" "0.1.0"

"anymatch@^1.3.0":
  "integrity" "sha512-0XNayC8lTHQ2OI8aljNCN3sSx6hsr/1+rlcDAotXJR7C1oZZHCNsfpbKwMjRA3Uqb5tF1Rae2oloTr4xpq+WjA=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "micromatch" "^2.1.5"
    "normalize-path" "^2.0.0"

"anymatch@~3.1.2":
  "integrity" "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"app-root-path@^2.2.1":
  "integrity" "sha512-91IFKeKk7FjfmezPKkwtaRvSpnUc4gDwPAjA1YZ9Gn0q0PPeW+vbeUsZuyDwjI7+QTHhcLen2v25fi/AmhvbJA=="
  "resolved" "https://registry.npmjs.org/app-root-path/-/app-root-path-2.2.1.tgz"
  "version" "2.2.1"

"append-buffer@^1.0.2":
  "integrity" "sha512-WLbYiXzD3y/ATLZFufV/rZvWdZOs+Z/+5v1rBZ463Jn398pa6kcde27cvozYnBoxXblGZTFfoPpsaEw0orU5BA=="
  "resolved" "https://registry.npmjs.org/append-buffer/-/append-buffer-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "buffer-equal" "^1.0.0"

"aproba@^1.0.3 || ^2.0.0":
  "integrity" "sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ=="
  "resolved" "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz"
  "version" "2.0.0"

"are-we-there-yet@^3.0.0":
  "integrity" "sha512-QZW4EDmGwlYur0Yyf/b2uGucHQMa8aFUP7eu9ddR73vvhFyt4V0Vl3QHPcTNJ8l6qYOBdxgXdnBXQrHilfRQBg=="
  "resolved" "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "delegates" "^1.0.0"
    "readable-stream" "^3.6.0"

"arg@^4.1.0":
  "integrity" "sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA=="
  "resolved" "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz"
  "version" "4.1.3"

"argparse@^1.0.7":
  "integrity" "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"aria-query@^3.0.0":
  "integrity" "sha512-majUxHgLehQTeSA+hClx+DY09OVUqG3GtezWkF1krgLGNdlDu9l9V8DaqNMWbq4Eddc8wsyDA0hpDUtnYxQEXw=="
  "resolved" "https://registry.npmjs.org/aria-query/-/aria-query-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ast-types-flow" "0.0.7"
    "commander" "^2.11.0"

"arr-diff@^2.0.0":
  "version" "2.0.0"
  dependencies:
    "arr-flatten" "^1.0.1"

"arr-diff@^4.0.0":
  "integrity" "sha512-YVIQ82gZPGBebQV/a8dar4AitzCQs0jjXwMPZllpXMaGjXPYVUawSxQrRsjhjupyVxEvbHgUmIhKVlND+j02kA=="
  "resolved" "https://registry.npmjs.org/arr-diff/-/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.0.1":
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q=="
  "resolved" "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-flatten@1.1.1":
  "integrity" "sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg=="
  "resolved" "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-union@^1.0.1":
  "integrity" "sha512-Dxr6QJj/RdU/hCaBjOfxW+q6lyuVE6JFWIrAUpuOOhoJJoQ99cUn3igRaHVB5P9WrgFVN0FfArM3x0cueOU8ng=="
  "resolved" "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "array-uniq" "^1.0.1"

"array-uniq@^1.0.1":
  "integrity" "sha512-MNha4BWQ6JbwhFhj03YK552f7cb3AzoE8SzeljgChvL1dl3IcvggXVz1DilzySZkCja+CXuZbdW7yATchWn8/Q=="
  "resolved" "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz"
  "version" "1.0.3"

"array-unique@^0.2.1":
  "version" "0.2.1"

"array-unique@^0.3.2":
  "version" "0.3.2"

"arrify@^1.0.0":
  "integrity" "sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA=="
  "resolved" "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz"
  "version" "1.0.1"

"asn1@~0.2.3":
  "version" "0.2.4"
  dependencies:
    "safer-buffer" "~2.1.0"

"assert-plus@^1.0.0", "assert-plus@1.0.0":
  "integrity" "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw=="
  "resolved" "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"
  "version" "1.0.0"

"assign-symbols@^1.0.0":
  "integrity" "sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw=="
  "resolved" "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"ast-types-flow@0.0.7":
  "integrity" "sha512-eBvWn1lvIApYMhzQMsu9ciLfkBY499mFZlNqG+/9WR7PVlroQw0vG30cOQQbaKz3sCEc44TAOu2ykzqXSNnwag=="
  "resolved" "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.7.tgz"
  "version" "0.0.7"

"async-each@^1.0.0":
  "version" "1.0.3"

"async@3.2.6":
  "version" "3.2.6"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.2":
  "integrity" "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="
  "resolved" "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^10.4.8":
  "version" "10.4.21"
  dependencies:
    "browserslist" "^4.24.4"
    "caniuse-lite" "^1.0.30001702"
    "fraction.js" "^4.3.7"
    "normalize-range" "^0.1.2"
    "picocolors" "^1.1.1"
    "postcss-value-parser" "^4.2.0"

"aws-sign2@~0.7.0":
  "integrity" "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA=="
  "resolved" "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz"
  "version" "0.7.0"

"aws4@^1.8.0":
  "version" "1.11.0"

"axobject-query@^2.0.2":
  "integrity" "sha512-Td525n+iPOOyUQIeBfcASuG6uJsDOITl7Mds5gFyerkWiX7qhUTdYUBlSgNMyVqtSJqwpt1kXGLdUt6SykLMRA=="
  "resolved" "https://registry.npmjs.org/axobject-query/-/axobject-query-2.2.0.tgz"
  "version" "2.2.0"

"babel-loader@8.2.5":
  "integrity" "sha512-OSiFfH89LrEMiWd4pLNqGz4CwJDtbs2ZVc+iGu2HrkRfPxId9F2anQj38IxWpmRfsUY0aBZYi1EFcd3mhtRMLQ=="
  "resolved" "https://registry.npmjs.org/babel-loader/-/babel-loader-8.2.5.tgz"
  "version" "8.2.5"
  dependencies:
    "find-cache-dir" "^3.3.1"
    "loader-utils" "^2.0.0"
    "make-dir" "^3.1.0"
    "schema-utils" "^2.6.5"

"babel-plugin-istanbul@6.1.1":
  "integrity" "sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA=="
  "resolved" "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz"
  "version" "6.1.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-instrument" "^5.0.4"
    "test-exclude" "^6.0.0"

"babel-plugin-polyfill-corejs2@^0.3.2":
  "integrity" "sha512-8hOdmFYFSZhqg2C/JgLUQ+t52o5nirNwaWM2B9LWteozwIvM14VSwdsCAUET10qT+kmySAlseadmfeeSWFCy+Q=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@babel/compat-data" "^7.17.7"
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    "semver" "^6.1.1"

"babel-plugin-polyfill-corejs3@^0.5.3":
  "integrity" "sha512-zKsXDh0XjnrUEW0mxIHLfjBfnXSMr5Q/goMe/fxpQnLm07mcOZiIZHBNWCMx60HmdvjxfXcalac0tfFg0wqxyw=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.2"
    "core-js-compat" "^3.21.0"

"babel-plugin-polyfill-regenerator@^0.4.0":
  "integrity" "sha512-NtQGmyQDXjQqQ+IzRkBVwEOz9lQ4zxAQZgoAYEtU9dJjnl1Oc98qnN7jcp+bE7O7aYzVpavXE3/VKXNzUbh7aw=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"

"babel-runtime@^6.9.2":
  "integrity" "sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g=="
  "resolved" "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"backbone@1.4.0":
  "integrity" "sha512-RLmDrRXkVdouTg38jcgHhyQ/2zjg7a8E6sz2zxfz21Hh17xDJYUHBZimVIt5fUyS8vbfpeSmTL3gUjTEvUV3qQ=="
  "resolved" "https://registry.npmjs.org/backbone/-/backbone-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "underscore" ">=1.8.3"

"balanced-match@^1.0.0":
  "version" "1.0.0"

"base@^0.11.1":
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-arraybuffer@^1.0.2":
  "integrity" "sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ=="
  "resolved" "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz"
  "version" "1.0.2"

"base64-js@^1.2.0", "base64-js@^1.3.1":
  "integrity" "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="
  "resolved" "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"base64id@~2.0.0", "base64id@2.0.0":
  "integrity" "sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog=="
  "resolved" "https://registry.npmjs.org/base64id/-/base64id-2.0.0.tgz"
  "version" "2.0.0"

"batch@0.6.1":
  "integrity" "sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw=="
  "resolved" "https://registry.npmjs.org/batch/-/batch-0.6.1.tgz"
  "version" "0.6.1"

"bcrypt-pbkdf@^1.0.0":
  "integrity" "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w=="
  "resolved" "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tweetnacl" "^0.14.3"

"big.js@^5.2.2":
  "integrity" "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="
  "resolved" "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^1.0.0":
  "integrity" "sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw=="
  "resolved" "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.13.1.tgz"
  "version" "1.13.1"

"binary-extensions@^2.0.0":
  "version" "2.1.0"

"bl@^4.1.0":
  "integrity" "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w=="
  "resolved" "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer" "^5.5.0"
    "inherits" "^2.0.4"
    "readable-stream" "^3.4.0"

"blocking-proxy@^1.0.0":
  "integrity" "sha512-KE8NFMZr3mN2E0HcvCgRtX7DjhiIQrwle+nSVJVC/yqFb9+xznHl2ZcoBp2L9qzkI4t4cBFJ1efXF8Dwi132RA=="
  "resolved" "https://registry.npmjs.org/blocking-proxy/-/blocking-proxy-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "^1.2.0"

"body-parser@^1.19.0", "body-parser@1.20.3":
  "integrity" "sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g=="
  "resolved" "https://registry.npmjs.org/body-parser/-/body-parser-1.20.3.tgz"
  "version" "1.20.3"
  dependencies:
    "bytes" "3.1.2"
    "content-type" "~1.0.5"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "on-finished" "2.4.1"
    "qs" "6.13.0"
    "raw-body" "2.5.2"
    "type-is" "~1.6.18"
    "unpipe" "1.0.0"

"bonjour-service@^1.0.11":
  "integrity" "sha512-3YuAUiSkWykd+2Azjgyxei8OWf8thdn8AITIog2M4UICzoqfjlqr64WIjEXZllf/W6vK1goqleSR6brGomxQqA=="
  "resolved" "https://registry.npmjs.org/bonjour-service/-/bonjour-service-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "fast-deep-equal" "^3.1.3"
    "multicast-dns" "^7.2.5"

"boolbase@^1.0.0":
  "integrity" "sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww=="
  "resolved" "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"bootstrap@4.3.1":
  "integrity" "sha512-rXqOmH1VilAt2DyPzluTi2blhk17bO7ef+zLLPlWvG494pDxcM234pJ8wTc/6R40UWizAIIMgxjvxZg5kmsbag=="
  "resolved" "https://registry.npmjs.org/bootstrap/-/bootstrap-4.3.1.tgz"
  "version" "4.3.1"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"brace-expansion@^2.0.1":
  "version" "2.0.1"
  dependencies:
    "balanced-match" "^1.0.0"

"braces@^1.8.2":
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"braces@^2.3.1":
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"braces@^3.0.2", "braces@^3.0.3", "braces@~3.0.2":
  "integrity" "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "fill-range" "^7.1.1"

"browserslist@^4.14.5", "browserslist@^4.20.0", "browserslist@^4.21.3", "browserslist@^4.24.0", "browserslist@^4.24.4", "browserslist@^4.9.1":
  "version" "4.25.0"
  dependencies:
    "caniuse-lite" "^1.0.30001718"
    "electron-to-chromium" "^1.5.160"
    "node-releases" "^2.0.19"
    "update-browserslist-db" "^1.1.3"

"browserstack@^1.5.1":
  "version" "1.6.0"
  dependencies:
    "https-proxy-agent" "^2.2.1"

"btoa@^1.2.1":
  "integrity" "sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g=="
  "resolved" "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz"
  "version" "1.2.1"

"buffer-equal@^1.0.0":
  "version" "1.0.0"

"buffer-from@^1.0.0":
  "version" "1.1.1"

"buffer@^5.5.0":
  "integrity" "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ=="
  "resolved" "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz"
  "version" "5.7.1"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.1.13"

"builtin-modules@^1.1.1":
  "integrity" "sha512-wxXCdllwGhI2kCC0MnvTGYTMvnVZTvqgypkiTI8Pa5tcz2i6VqsqwYGgqwXji+4RgCzms6EajE4IxiUH6HH8nQ=="
  "resolved" "https://registry.npmjs.org/builtin-modules/-/builtin-modules-1.1.1.tgz"
  "version" "1.1.1"

"builtin-modules@^3.3.0":
  "integrity" "sha512-zhaCDicdLuWN5UbN5IMnFqNMhNfo919sH85y2/ea+5Yg9TsTkeZxpL+JLbp6cgYFS4sRLp3YV4S6yDuqVWHYOw=="
  "resolved" "https://registry.npmjs.org/builtin-modules/-/builtin-modules-3.3.0.tgz"
  "version" "3.3.0"

"builtins@^5.0.0":
  "integrity" "sha512-SW9lzGTLvWTP1AY8xeAMZimqDrIaSdLQUcVr9DMef51niJ022Ri87SwRRKYm4A6iHfkPaiVUu/Duw2Wc4J7kKg=="
  "resolved" "https://registry.npmjs.org/builtins/-/builtins-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "semver" "^7.0.0"

"bytes@3.1.2":
  "integrity" "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg=="
  "resolved" "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
  "version" "3.1.2"

"cacache@^16.0.0", "cacache@^16.1.0", "cacache@16.1.2":
  "integrity" "sha512-Xx+xPlfCZIUHagysjjOAje9nRo8pRDczQCcXb4J2O0BLtH+xeVue6ba4y1kfJfQMAnM2mkcoMIAyOctlaRGWYA=="
  "resolved" "https://registry.npmjs.org/cacache/-/cacache-16.1.2.tgz"
  "version" "16.1.2"
  dependencies:
    "@npmcli/fs" "^2.1.0"
    "@npmcli/move-file" "^2.0.0"
    "chownr" "^2.0.0"
    "fs-minipass" "^2.1.0"
    "glob" "^8.0.1"
    "infer-owner" "^1.0.4"
    "lru-cache" "^7.7.1"
    "minipass" "^3.1.6"
    "minipass-collect" "^1.0.2"
    "minipass-flush" "^1.0.5"
    "minipass-pipeline" "^1.2.4"
    "mkdirp" "^1.0.4"
    "p-map" "^4.0.0"
    "promise-inflight" "^1.0.1"
    "rimraf" "^3.0.2"
    "ssri" "^9.0.0"
    "tar" "^6.1.11"
    "unique-filename" "^1.1.1"

"cache-base@^1.0.1":
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"call-bind-apply-helpers@^1.0.0", "call-bind-apply-helpers@^1.0.1", "call-bind-apply-helpers@^1.0.2":
  "version" "1.0.2"
  dependencies:
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"

"call-bind@^1.0.0", "call-bind@^1.0.7", "call-bind@^1.0.8":
  "integrity" "sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww=="
  "resolved" "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "call-bind-apply-helpers" "^1.0.0"
    "es-define-property" "^1.0.0"
    "get-intrinsic" "^1.2.4"
    "set-function-length" "^1.2.2"

"call-bound@^1.0.2":
  "version" "1.0.4"
  dependencies:
    "call-bind-apply-helpers" "^1.0.2"
    "get-intrinsic" "^1.3.0"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camelcase@^5.0.0", "camelcase@^5.3.1":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"caniuse-lite@^1.0.30001702", "caniuse-lite@^1.0.30001718":
  "version" "1.0.30001720"

"canvg@^3.0.11":
  "version" "3.0.11"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@types/raf" "^3.4.0"
    "core-js" "^3.8.3"
    "raf" "^3.4.1"
    "regenerator-runtime" "^0.13.7"
    "rgbcolor" "^1.0.1"
    "stackblur-canvas" "^2.0.0"
    "svg-pathdata" "^6.0.3"

"caseless@~0.12.0":
  "integrity" "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw=="
  "resolved" "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz"
  "version" "0.12.0"

"chalk@^1.1.1", "chalk@^1.1.3":
  "integrity" "sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.3.0":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^4.1.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.1.1":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@4.1.2":
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chardet@^0.7.0":
  "integrity" "sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA=="
  "resolved" "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz"
  "version" "0.7.0"

"chokidar@^1.6.0":
  "integrity" "sha512-mk8fAWcRUOxY7btlLtitj3A45jOwSAxH4tOFOoEGbVsl6cL6pPMWUy7dwZ/canfj3QEdP6FHSnf/l1c6/WkzVg=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "anymatch" "^1.3.0"
    "async-each" "^1.0.0"
    "glob-parent" "^2.0.0"
    "inherits" "^2.0.1"
    "is-binary-path" "^1.0.0"
    "is-glob" "^2.0.0"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.0.0"
  optionalDependencies:
    "fsevents" "^1.0.0"

"chokidar@^3.0.0", "chokidar@^3.5.1", "chokidar@^3.5.3", "chokidar@>=3.0.0 <4.0.0":
  "integrity" "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chownr@^2.0.0":
  "integrity" "sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ=="
  "resolved" "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz"
  "version" "2.0.0"

"chrome-trace-event@^1.0.2":
  "integrity" "sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ=="
  "resolved" "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz"
  "version" "1.0.4"

"class-utils@^0.3.5":
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"clean-stack@^2.0.0":
  "integrity" "sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A=="
  "resolved" "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz"
  "version" "2.2.0"

"cli-cursor@^3.1.0":
  "integrity" "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw=="
  "resolved" "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "restore-cursor" "^3.1.0"

"cli-spinners@^2.5.0":
  "integrity" "sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg=="
  "resolved" "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz"
  "version" "2.9.2"

"cli-width@^3.0.0":
  "integrity" "sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw=="
  "resolved" "https://registry.npmjs.org/cli-width/-/cli-width-3.0.0.tgz"
  "version" "3.0.0"

"cliui@^6.0.0":
  "integrity" "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"cliui@^7.0.2":
  "integrity" "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^7.0.0"

"clone-buffer@^1.0.0":
  "integrity" "sha512-KLLTJWrvwIP+OPfMn0x2PheDEP20RPUcGXj/ERegTgdmPEZylALQldygiqrPPu8P45uNuPs7ckmReLY6v/iA5g=="
  "resolved" "https://registry.npmjs.org/clone-buffer/-/clone-buffer-1.0.0.tgz"
  "version" "1.0.0"

"clone-deep@^4.0.1":
  "integrity" "sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ=="
  "resolved" "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"
    "kind-of" "^6.0.2"
    "shallow-clone" "^3.0.0"

"clone-stats@^1.0.0":
  "integrity" "sha512-au6ydSpg6nsrigcZ4m8Bc9hxjeW+GJ8xh5G3BJCMt4WXe1H10UNaVOamqQTmrx1kjVuxAHIQSNU6hY4Nsn9/ag=="
  "resolved" "https://registry.npmjs.org/clone-stats/-/clone-stats-1.0.0.tgz"
  "version" "1.0.0"

"clone@^1.0.2":
  "integrity" "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg=="
  "resolved" "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@^2.1.1":
  "integrity" "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w=="
  "resolved" "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  "version" "2.1.2"

"cloneable-readable@^1.0.0":
  "integrity" "sha512-2EF8zTQOxYq70Y4XKtorQupqF0m49MBz2/yf5Bj+MHjvpG3Hy7sImifnqD6UA+TKYxeSV+u6qqQPawN5UvnpKQ=="
  "resolved" "https://registry.npmjs.org/cloneable-readable/-/cloneable-readable-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "inherits" "^2.0.1"
    "process-nextick-args" "^2.0.0"
    "readable-stream" "^2.3.5"

"codelyzer@~5.1.0":
  "integrity" "sha512-1z7mtpwxcz5uUqq0HLO0ifj/tz2dWEmeaK+8c5TEZXAwwVxrjjg0118ODCOCCOcpfYaaEHxStNCaWVYo9FUPXw=="
  "resolved" "https://registry.npmjs.org/codelyzer/-/codelyzer-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "app-root-path" "^2.2.1"
    "aria-query" "^3.0.0"
    "axobject-query" "^2.0.2"
    "css-selector-tokenizer" "^0.7.1"
    "cssauron" "^1.4.0"
    "damerau-levenshtein" "^1.0.4"
    "semver-dsl" "^1.0.1"
    "source-map" "^0.5.7"
    "sprintf-js" "^1.1.2"

"codemirror@^5.65.12":
  "version" "5.65.19"

"collection-visit@^1.0.0":
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-support@^1.1.3":
  "integrity" "sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg=="
  "resolved" "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz"
  "version" "1.1.3"

"colorette@^2.0.10":
  "integrity" "sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w=="
  "resolved" "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz"
  "version" "2.0.20"

"colors@1.4.0":
  "integrity" "sha512-a+UqTh4kgZg/SlGvfbzDHpgRu7AAQOmmqRHJnxhRZICKFUT91brVhNNt58CMWU9PsBbv3PDCZUHbVxuDiH2mtA=="
  "resolved" "https://registry.npmjs.org/colors/-/colors-1.4.0.tgz"
  "version" "1.4.0"

"combined-stream@^1.0.6", "combined-stream@~1.0.6":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^10.0.0":
  "integrity" "sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-10.0.1.tgz"
  "version" "10.0.1"

"commander@^2.11.0", "commander@^2.12.1", "commander@^2.20.0", "commander@2":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^9.0.0":
  "integrity" "sha512-KRs7WVDKg86PWiuAqhDrAQnTXZKraVcCc6vFdL14qrZ/DcWwuRo7VoiYXalXO7S5GKpqYiVEwCbgFDfxNHKJBQ=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-9.5.0.tgz"
  "version" "9.5.0"

"commander@11.1.0":
  "version" "11.1.0"

"commondir@^1.0.1":
  "integrity" "sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg=="
  "resolved" "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-emitter@^1.2.1":
  "version" "1.3.0"

"compressible@~2.0.18":
  "integrity" "sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg=="
  "resolved" "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression@^1.7.4":
  "version" "1.8.0"
  dependencies:
    "bytes" "3.1.2"
    "compressible" "~2.0.18"
    "debug" "2.6.9"
    "negotiator" "~0.6.4"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.2.1"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"config-chain@^1.1.11", "config-chain@^1.1.13":
  "integrity" "sha512-qj+f8APARXHrM0hraqXYb2/bOVSV4PvJQlNZ/DVj0QrmNM2q2euizkeuVckQ57J+W0mRH6Hvi+k50M4Jul2VRQ=="
  "resolved" "https://registry.npmjs.org/config-chain/-/config-chain-1.1.13.tgz"
  "version" "1.1.13"
  dependencies:
    "ini" "^1.3.4"
    "proto-list" "~1.2.1"

"connect-history-api-fallback@^2.0.0":
  "integrity" "sha512-U73+6lQFmfiNPrYbXqr6kZ1i1wiRqXnp2nhMsINseWXO8lDau0LGEffJ8kQi4EjLZympVgRdvqjAgiZ1tgzDDA=="
  "resolved" "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz"
  "version" "2.0.0"

"connect@^3.7.0":
  "integrity" "sha512-ZqRXc+tZukToSNmh5C2iWMSoV3X1YUcPbqEM4DkEG5tNQXrQUZCNVGGv3IuicnkMtPfGf3Xtp8WCXs295iQ1pQ=="
  "resolved" "https://registry.npmjs.org/connect/-/connect-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "debug" "2.6.9"
    "finalhandler" "1.1.2"
    "parseurl" "~1.3.3"
    "utils-merge" "1.0.1"

"console-control-strings@^1.1.0":
  "integrity" "sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ=="
  "resolved" "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz"
  "version" "1.1.0"

"content-disposition@0.5.4":
  "integrity" "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ=="
  "resolved" "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz"
  "version" "0.5.4"
  dependencies:
    "safe-buffer" "5.2.1"

"content-type@~1.0.4", "content-type@~1.0.5":
  "integrity" "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA=="
  "resolved" "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz"
  "version" "1.0.5"

"convert-source-map@^1.5.0", "convert-source-map@^1.5.1", "convert-source-map@^1.7.0":
  "version" "1.7.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"cookie-signature@1.0.6":
  "integrity" "sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ=="
  "resolved" "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@~0.7.2":
  "version" "0.7.2"

"cookie@0.7.1":
  "version" "0.7.1"

"copy-anything@^2.0.1":
  "integrity" "sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw=="
  "resolved" "https://registry.npmjs.org/copy-anything/-/copy-anything-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "is-what" "^3.14.1"

"copy-descriptor@^0.1.0":
  "version" "0.1.1"

"copy-webpack-plugin@11.0.0":
  "integrity" "sha512-fX2MWpamkW0hZxMEg0+mYnA40LTosOSa5TqZ9GYIBzyJa9C3QUaMPSE2xAi/buNr8u89SfD9wHSQVBzrRa/SOQ=="
  "resolved" "https://registry.npmjs.org/copy-webpack-plugin/-/copy-webpack-plugin-11.0.0.tgz"
  "version" "11.0.0"
  dependencies:
    "fast-glob" "^3.2.11"
    "glob-parent" "^6.0.1"
    "globby" "^13.1.1"
    "normalize-path" "^3.0.0"
    "schema-utils" "^4.0.0"
    "serialize-javascript" "^6.0.0"

"core-js-compat@^3.21.0", "core-js-compat@^3.22.1":
  "version" "3.42.0"
  dependencies:
    "browserslist" "^4.24.4"

"core-js@^2.4.0", "core-js@^2.5.7":
  "version" "2.6.11"

"core-js@^3.6.0":
  "version" "3.42.0"

"core-js@^3.8.3":
  "version" "3.42.0"

"core-util-is@~1.0.0", "core-util-is@1.0.2":
  "integrity" "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ=="
  "resolved" "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz"
  "version" "1.0.2"

"cors@~2.8.5":
  "integrity" "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g=="
  "resolved" "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz"
  "version" "2.8.5"
  dependencies:
    "object-assign" "^4"
    "vary" "^1"

"cosmiconfig@^7.0.0":
  "integrity" "sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA=="
  "resolved" "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "@types/parse-json" "^4.0.0"
    "import-fresh" "^3.2.1"
    "parse-json" "^5.0.0"
    "path-type" "^4.0.0"
    "yaml" "^1.10.0"

"cpx@^1.5.0":
  "integrity" "sha512-jHTjZhsbg9xWgsP2vuNW2jnnzBX+p4T+vNI9Lbjzs1n4KhOfa22bQppiFYLsWQKd8TzmL5aSP/Me3yfsCwXbDA=="
  "resolved" "https://registry.npmjs.org/cpx/-/cpx-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "babel-runtime" "^6.9.2"
    "chokidar" "^1.6.0"
    "duplexer" "^0.1.1"
    "glob" "^7.0.5"
    "glob2base" "^0.0.12"
    "minimatch" "^3.0.2"
    "mkdirp" "^0.5.1"
    "resolve" "^1.1.7"
    "safe-buffer" "^5.0.1"
    "shell-quote" "^1.6.1"
    "subarg" "^1.0.0"

"critters@0.0.16":
  "integrity" "sha512-JwjgmO6i3y6RWtLYmXwO5jMd+maZt8Tnfu7VVISmEWyQqfLpB8soBswf8/2bu6SBXxtKA68Al3c+qIG1ApT68A=="
  "resolved" "https://registry.npmjs.org/critters/-/critters-0.0.16.tgz"
  "version" "0.0.16"
  dependencies:
    "chalk" "^4.1.0"
    "css-select" "^4.2.0"
    "parse5" "^6.0.1"
    "parse5-htmlparser2-tree-adapter" "^6.0.1"
    "postcss" "^8.3.7"
    "pretty-bytes" "^5.3.0"

"cross-spawn@^7.0.3", "cross-spawn@^7.0.6":
  "version" "7.0.6"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"css-blank-pseudo@^3.0.3":
  "integrity" "sha512-VS90XWtsHGqoM0t4KpH053c4ehxZ2E6HtGI7x68YFV0pTo/QmkV/YFA+NnlvK8guxZVNWGQhVNJGC39Q8XF4OQ=="
  "resolved" "https://registry.npmjs.org/css-blank-pseudo/-/css-blank-pseudo-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "postcss-selector-parser" "^6.0.9"

"css-has-pseudo@^3.0.4":
  "integrity" "sha512-Vse0xpR1K9MNlp2j5w1pgWIJtm1a8qS0JwS9goFYcImjlHEmywP9VUF05aGBXzGpDJF86QXk4L0ypBmwPhGArw=="
  "resolved" "https://registry.npmjs.org/css-has-pseudo/-/css-has-pseudo-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "postcss-selector-parser" "^6.0.9"

"css-line-break@^2.1.0":
  "integrity" "sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w=="
  "resolved" "https://registry.npmjs.org/css-line-break/-/css-line-break-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "utrie" "^1.0.2"

"css-loader@6.7.1":
  "integrity" "sha512-yB5CNFa14MbPJcomwNh3wLThtkZgcNyI2bNMRt8iE5Z8Vwl7f8vQXFAzn2HDOJvtDq2NTZBUGMSUNNyrv3/+cw=="
  "resolved" "https://registry.npmjs.org/css-loader/-/css-loader-6.7.1.tgz"
  "version" "6.7.1"
  dependencies:
    "icss-utils" "^5.1.0"
    "postcss" "^8.4.7"
    "postcss-modules-extract-imports" "^3.0.0"
    "postcss-modules-local-by-default" "^4.0.0"
    "postcss-modules-scope" "^3.0.0"
    "postcss-modules-values" "^4.0.0"
    "postcss-value-parser" "^4.2.0"
    "semver" "^7.3.5"

"css-prefers-color-scheme@^6.0.3":
  "integrity" "sha512-4BqMbZksRkJQx2zAjrokiGMd07RqOa2IxIrrN10lyBe9xhn9DEvjUK79J6jkeiv9D9hQFXKb6g1jwU62jziJZA=="
  "resolved" "https://registry.npmjs.org/css-prefers-color-scheme/-/css-prefers-color-scheme-6.0.3.tgz"
  "version" "6.0.3"

"css-select@^4.2.0":
  "integrity" "sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ=="
  "resolved" "https://registry.npmjs.org/css-select/-/css-select-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^6.0.1"
    "domhandler" "^4.3.1"
    "domutils" "^2.8.0"
    "nth-check" "^2.0.1"

"css-selector-tokenizer@^0.7.1":
  "integrity" "sha512-jWQv3oCEL5kMErj4wRnK/OPoBi0D+P1FR2cDCKYPaMeD2eW3/mttav8HT4hT1CKopiJI/psEULjkClhvJo4Lvg=="
  "resolved" "https://registry.npmjs.org/css-selector-tokenizer/-/css-selector-tokenizer-0.7.3.tgz"
  "version" "0.7.3"
  dependencies:
    "cssesc" "^3.0.0"
    "fastparse" "^1.1.2"

"css-what@^6.0.1":
  "integrity" "sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw=="
  "resolved" "https://registry.npmjs.org/css-what/-/css-what-6.1.0.tgz"
  "version" "6.1.0"

"cssauron@^1.4.0":
  "integrity" "sha512-Ht70DcFBh+/ekjVrYS2PlDMdSQEl3OFNmjK6lcn49HptBgilXf/Zwg4uFh9Xn0pX3Q8YOkSjIFOfK2osvdqpBw=="
  "resolved" "https://registry.npmjs.org/cssauron/-/cssauron-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "through" "X.X.X"

"cssdb@^7.0.0":
  "integrity" "sha512-lhQ32TFkc1X4eTefGfYPvgovRSzIMofHkigfH8nWtyRL4XJLsRhJFreRvEgKzept7x1rjBuy3J/MurXLaFxW/A=="
  "resolved" "https://registry.npmjs.org/cssdb/-/cssdb-7.11.2.tgz"
  "version" "7.11.2"

"cssesc@^3.0.0":
  "integrity" "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="
  "resolved" "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cuint@^0.2.2":
  "integrity" "sha512-d4ZVpCW31eWwCMe1YT3ur7mUDnTXbgwyzaL320DrcRT45rfjYxkt5QWLrmOJ+/UEAI2+fQgKe/fCjR8l4TpRgw=="
  "resolved" "https://registry.npmjs.org/cuint/-/cuint-0.2.2.tgz"
  "version" "0.2.2"

"custom-event@~1.0.0":
  "integrity" "sha512-GAj5FOq0Hd+RsCGVJxZuKaIDXDf3h6GQoNEjFgbLLI/trgtavwUbSnZ5pVfg27DVCaWjIohryS0JFwIJyT2cMg=="
  "resolved" "https://registry.npmjs.org/custom-event/-/custom-event-1.0.1.tgz"
  "version" "1.0.1"

"d3-array@^1.1.1", "d3-array@^1.2.0", "d3-array@1", "d3-array@1 - 2":
  "integrity" "sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw=="
  "resolved" "https://registry.npmjs.org/d3-array/-/d3-array-1.2.4.tgz"
  "version" "1.2.4"

"d3-axis@1":
  "integrity" "sha512-ejINPfPSNdGFKEOAtnBtdkpr24c4d4jsei6Lg98mxf424ivoDP2956/5HDpIAtmHo85lqT4pruy+zEgvRUBqaQ=="
  "resolved" "https://registry.npmjs.org/d3-axis/-/d3-axis-1.0.12.tgz"
  "version" "1.0.12"

"d3-brush@1":
  "integrity" "sha512-7RW+w7HfMCPyZLifTz/UnJmI5kdkXtpCbombUSs8xniAyo0vIbrDzDwUJB6eJOgl9u5DQOt2TQlYumxzD1SvYA=="
  "resolved" "https://registry.npmjs.org/d3-brush/-/d3-brush-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "d3-dispatch" "1"
    "d3-drag" "1"
    "d3-interpolate" "1"
    "d3-selection" "1"
    "d3-transition" "1"

"d3-chord@1":
  "integrity" "sha512-JXA2Dro1Fxw9rJe33Uv+Ckr5IrAa74TlfDEhE/jfLOaXegMQFQTAgAw9WnZL8+HxVBRXaRGCkrNU7pJeylRIuA=="
  "resolved" "https://registry.npmjs.org/d3-chord/-/d3-chord-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "d3-array" "1"
    "d3-path" "1"

"d3-collection@1":
  "integrity" "sha512-ii0/r5f4sjKNTfh84Di+DpztYwqKhEyUlKoPrzUFfeSkWxjW49xU2QzO9qrPrNkpdI0XJkfzvmTu8V2Zylln6A=="
  "resolved" "https://registry.npmjs.org/d3-collection/-/d3-collection-1.0.7.tgz"
  "version" "1.0.7"

"d3-color@1":
  "version" "1.4.1"

"d3-contour@1":
  "integrity" "sha512-hoPp4K/rJCu0ladiH6zmJUEz6+u3lgR+GSm/QdM2BBvDraU39Vr7YdDCicJcxP1z8i9B/2dJLgDC1NcvlF8WCg=="
  "resolved" "https://registry.npmjs.org/d3-contour/-/d3-contour-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "d3-array" "^1.1.1"

"d3-dispatch@1":
  "integrity" "sha512-fVjoElzjhCEy+Hbn8KygnmMS7Or0a9sI2UzGwoB7cCtvI1XpVN9GpoYlnb3xt2YV66oXYb1fLJ8GMvP4hdU1RA=="
  "resolved" "https://registry.npmjs.org/d3-dispatch/-/d3-dispatch-1.0.6.tgz"
  "version" "1.0.6"

"d3-drag@1":
  "integrity" "sha512-rD1ohlkKQwMZYkQlYVCrSFxsWPzI97+W+PaEIBNTMxRuxz9RF0Hi5nJWHGVJ3Om9d2fRTe1yOBINJyy/ahV95w=="
  "resolved" "https://registry.npmjs.org/d3-drag/-/d3-drag-1.2.5.tgz"
  "version" "1.2.5"
  dependencies:
    "d3-dispatch" "1"
    "d3-selection" "1"

"d3-dsv@1":
  "integrity" "sha512-9yVlqvZcSOMhCYzniHE7EVUws7Fa1zgw+/EAV2BxJoG3ME19V6BQFBwI855XQDsxyOuG7NibqRMTtiF/Qup46g=="
  "resolved" "https://registry.npmjs.org/d3-dsv/-/d3-dsv-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "commander" "2"
    "iconv-lite" "0.4"
    "rw" "1"

"d3-ease@1":
  "integrity" "sha512-lx14ZPYkhNx0s/2HX5sLFUI3mbasHjSSpwO/KaaNACweVwxUruKyWVcb293wMv1RqTPZyZ8kSZ2NogUZNcLOFQ=="
  "resolved" "https://registry.npmjs.org/d3-ease/-/d3-ease-1.0.7.tgz"
  "version" "1.0.7"

"d3-fetch@1":
  "integrity" "sha512-yC78NBVcd2zFAyR/HnUiBS7Lf6inSCoWcSxFfw8FYL7ydiqe80SazNwoffcqOfs95XaLo7yebsmQqDKSsXUtvA=="
  "resolved" "https://registry.npmjs.org/d3-fetch/-/d3-fetch-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "d3-dsv" "1"

"d3-force@1":
  "integrity" "sha512-HHvehyaiUlVo5CxBJ0yF/xny4xoaxFxDnBXNvNcfW9adORGZfyNF1dj6DGLKyk4Yh3brP/1h3rnDzdIAwL08zg=="
  "resolved" "https://registry.npmjs.org/d3-force/-/d3-force-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "d3-collection" "1"
    "d3-dispatch" "1"
    "d3-quadtree" "1"
    "d3-timer" "1"

"d3-format@1":
  "integrity" "sha512-J0piedu6Z8iB6TbIGfZgDzfXxUFN3qQRMofy2oPdXzQibYGqPB/9iMcxr/TGalU+2RsyDO+U4f33id8tbnSRMQ=="
  "resolved" "https://registry.npmjs.org/d3-format/-/d3-format-1.4.5.tgz"
  "version" "1.4.5"

"d3-geo@1":
  "integrity" "sha512-XG4d1c/UJSEX9NfU02KwBL6BYPj8YKHxgBEw5om2ZnTRSbIcego6dhHwcxuSR3clxh0EpE38os1DVPOmnYtTPg=="
  "resolved" "https://registry.npmjs.org/d3-geo/-/d3-geo-1.12.1.tgz"
  "version" "1.12.1"
  dependencies:
    "d3-array" "1"

"d3-hierarchy@1":
  "integrity" "sha512-j8tPxlqh1srJHAtxfvOUwKNYJkQuBFdM1+JAUfq6xqH5eAqf93L7oG1NVqDa4CpFZNvnNKtCYEUC8KY9yEn9lQ=="
  "resolved" "https://registry.npmjs.org/d3-hierarchy/-/d3-hierarchy-1.1.9.tgz"
  "version" "1.1.9"

"d3-interpolate@1":
  "version" "1.4.0"
  dependencies:
    "d3-color" "1"

"d3-path@1":
  "integrity" "sha512-VLaYcn81dtHVTjEHd8B+pbe9yHWpXKZUC87PzoFmsFrJqgFwDe/qxfp5MlfsfM1V5E/iVt0MmEbWQ7FVIXh/bg=="
  "resolved" "https://registry.npmjs.org/d3-path/-/d3-path-1.0.9.tgz"
  "version" "1.0.9"

"d3-polygon@1":
  "integrity" "sha512-k+RF7WvI08PC8reEoXa/w2nSg5AUMTi+peBD9cmFc+0ixHfbs4QmxxkarVal1IkVkgxVuk9JSHhJURHiyHKAuQ=="
  "resolved" "https://registry.npmjs.org/d3-polygon/-/d3-polygon-1.0.6.tgz"
  "version" "1.0.6"

"d3-quadtree@1":
  "integrity" "sha512-RKPAeXnkC59IDGD0Wu5mANy0Q2V28L+fNe65pOCXVdVuTJS3WPKaJlFHer32Rbh9gIo9qMuJXio8ra4+YmIymA=="
  "resolved" "https://registry.npmjs.org/d3-quadtree/-/d3-quadtree-1.0.7.tgz"
  "version" "1.0.7"

"d3-random@1":
  "integrity" "sha512-6AK5BNpIFqP+cx/sreKzNjWbwZQCSUatxq+pPRmFIQaWuoD+NrbVWw7YWpHiXpCQ/NanKdtGDuB+VQcZDaEmYQ=="
  "resolved" "https://registry.npmjs.org/d3-random/-/d3-random-1.1.2.tgz"
  "version" "1.1.2"

"d3-sankey@^0.12.3":
  "integrity" "sha512-nQhsBRmM19Ax5xEIPLMY9ZmJ/cDvd1BG3UVvt5h3WRxKg5zGRbvnteTyWAbzeSvlh3tW7ZEmq4VwR5mB3tutmQ=="
  "resolved" "https://registry.npmjs.org/d3-sankey/-/d3-sankey-0.12.3.tgz"
  "version" "0.12.3"
  dependencies:
    "d3-array" "1 - 2"
    "d3-shape" "^1.2.0"

"d3-scale-chromatic@1":
  "integrity" "sha512-ACcL46DYImpRFMBcpk9HhtIyC7bTBR4fNOPxwVSl0LfulDAwyiHyPOTqcDG1+t5d4P9W7t/2NAuWu59aKko/cg=="
  "resolved" "https://registry.npmjs.org/d3-scale-chromatic/-/d3-scale-chromatic-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "d3-color" "1"
    "d3-interpolate" "1"

"d3-scale@2":
  "integrity" "sha512-LbeEvGgIb8UMcAa0EATLNX0lelKWGYDQiPdHj+gLblGVhGLyNbaCn3EvrJf0A3Y/uOOU5aD6MTh5ZFCdEwGiCw=="
  "resolved" "https://registry.npmjs.org/d3-scale/-/d3-scale-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "d3-array" "^1.2.0"
    "d3-collection" "1"
    "d3-format" "1"
    "d3-interpolate" "1"
    "d3-time" "1"
    "d3-time-format" "2"

"d3-selection@^1.1.0", "d3-selection@1":
  "integrity" "sha512-SJ0BqYihzOjDnnlfyeHT0e30k0K1+5sR3d5fNueCNeuhZTnGw4M4o8mqJchSwgKMXCNFo+e2VTChiSJ0vYtXkg=="
  "resolved" "https://registry.npmjs.org/d3-selection/-/d3-selection-1.4.2.tgz"
  "version" "1.4.2"

"d3-shape@^1.2.0", "d3-shape@1":
  "integrity" "sha512-EUkvKjqPFUAZyOlhY5gzCxCeI0Aep04LwIRpsZ/mLFelJiUfnK56jo5JMDSE7yyP2kLSb6LtF+S5chMk7uqPqw=="
  "resolved" "https://registry.npmjs.org/d3-shape/-/d3-shape-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "d3-path" "1"

"d3-time-format@2":
  "integrity" "sha512-guv6b2H37s2Uq/GefleCDtbe0XZAuy7Wa49VGkPVPMfLL9qObgBST3lEHJBMUp8S7NdLQAGIvr2KXk8Hc98iKQ=="
  "resolved" "https://registry.npmjs.org/d3-time-format/-/d3-time-format-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "d3-time" "1"

"d3-time@1":
  "integrity" "sha512-Xh0isrZ5rPYYdqhAVk8VLnMEidhz5aP7htAADH6MfzgmmicPkTo8LhkLxci61/lCB7n7UmE3bN0leRt+qvkLxA=="
  "resolved" "https://registry.npmjs.org/d3-time/-/d3-time-1.1.0.tgz"
  "version" "1.1.0"

"d3-timer@1":
  "integrity" "sha512-B1JDm0XDaQC+uvo4DT79H0XmBskgS3l6Ve+1SBCfxgmtIb1AVrPIoqd+nPSv+loMX8szQ0sVUhGngL7D5QPiXw=="
  "resolved" "https://registry.npmjs.org/d3-timer/-/d3-timer-1.0.10.tgz"
  "version" "1.0.10"

"d3-transition@1":
  "integrity" "sha512-sc0gRU4PFqZ47lPVHloMn9tlPcv8jxgOQg+0zjhfZXMQuvppjG6YuwdMBE0TuqCZjeJkLecku/l9R0JPcRhaDA=="
  "resolved" "https://registry.npmjs.org/d3-transition/-/d3-transition-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "d3-color" "1"
    "d3-dispatch" "1"
    "d3-ease" "1"
    "d3-interpolate" "1"
    "d3-selection" "^1.1.0"
    "d3-timer" "1"

"d3-voronoi@1":
  "integrity" "sha512-dArJ32hchFsrQ8uMiTBLq256MpnZjeuBtdHpaDlYuQyjU0CVzCJl/BVW+SkszaAeH95D/8gxqAhgx0ouAWAfRg=="
  "resolved" "https://registry.npmjs.org/d3-voronoi/-/d3-voronoi-1.1.4.tgz"
  "version" "1.1.4"

"d3-zoom@1":
  "integrity" "sha512-VoLXTK4wvy1a0JpH2Il+F2CiOhVu7VRXWF5M/LroMIh3/zBAC3WAt7QoIvPibOavVo20hN6/37vwAsdBejLyKQ=="
  "resolved" "https://registry.npmjs.org/d3-zoom/-/d3-zoom-1.8.3.tgz"
  "version" "1.8.3"
  dependencies:
    "d3-dispatch" "1"
    "d3-drag" "1"
    "d3-interpolate" "1"
    "d3-selection" "1"
    "d3-transition" "1"

"d3@^5.12.0":
  "integrity" "sha512-4PL5hHaHwX4m7Zr1UapXW23apo6pexCgdetdJ5kTmADpG/7T9Gkxw0M0tf/pjoB63ezCCm0u5UaFYy2aMt0Mcw=="
  "resolved" "https://registry.npmjs.org/d3/-/d3-5.16.0.tgz"
  "version" "5.16.0"
  dependencies:
    "d3-array" "1"
    "d3-axis" "1"
    "d3-brush" "1"
    "d3-chord" "1"
    "d3-collection" "1"
    "d3-color" "1"
    "d3-contour" "1"
    "d3-dispatch" "1"
    "d3-drag" "1"
    "d3-dsv" "1"
    "d3-ease" "1"
    "d3-fetch" "1"
    "d3-force" "1"
    "d3-format" "1"
    "d3-geo" "1"
    "d3-hierarchy" "1"
    "d3-interpolate" "1"
    "d3-path" "1"
    "d3-polygon" "1"
    "d3-quadtree" "1"
    "d3-random" "1"
    "d3-scale" "2"
    "d3-scale-chromatic" "1"
    "d3-selection" "1"
    "d3-shape" "1"
    "d3-time" "1"
    "d3-time-format" "2"
    "d3-timer" "1"
    "d3-transition" "1"
    "d3-voronoi" "1"
    "d3-zoom" "1"

"dagre@0.8.4":
  "integrity" "sha512-Dj0csFDrWYKdavwROb9FccHfTC4fJbyF/oJdL9LNZJ8WUvl968P6PAKEriGqfbdArVJEmmfA+UyumgWEwcHU6A=="
  "resolved" "https://registry.npmjs.org/dagre/-/dagre-0.8.4.tgz"
  "version" "0.8.4"
  dependencies:
    "graphlib" "^2.1.7"
    "lodash" "^4.17.4"

"damerau-levenshtein@^1.0.4":
  "version" "1.0.6"

"dashdash@^1.12.0":
  "integrity" "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g=="
  "resolved" "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "assert-plus" "^1.0.0"

"date-format@^4.0.14":
  "integrity" "sha512-39BOQLs9ZjKh0/patS9nrT8wc3ioX3/eA/zgbKNopnF2wCqJEoxywwwElATYvRsXdnOxA/OQeQoFZ3rFjVajhg=="
  "resolved" "https://registry.npmjs.org/date-format/-/date-format-4.0.14.tgz"
  "version" "4.0.14"

"debug@^2.2.0", "debug@^2.3.3", "debug@2.6.9":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.1.0":
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.1.0":
  "version" "4.4.1"
  dependencies:
    "ms" "^2.1.3"

"debug@^4.1.1":
  "version" "4.4.1"
  dependencies:
    "ms" "^2.1.3"

"debug@^4.3.1":
  "version" "4.4.1"
  dependencies:
    "ms" "^2.1.3"

"debug@^4.3.2":
  "version" "4.4.1"
  dependencies:
    "ms" "^2.1.3"

"debug@^4.3.3", "debug@4":
  "version" "4.4.1"
  dependencies:
    "ms" "^2.1.3"

"debug@^4.3.4":
  "version" "4.4.1"
  dependencies:
    "ms" "^2.1.3"

"debug@~4.3.1":
  "integrity" "sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  "version" "4.3.7"
  dependencies:
    "ms" "^2.1.3"

"debug@~4.3.2":
  "integrity" "sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  "version" "4.3.7"
  dependencies:
    "ms" "^2.1.3"

"debug@~4.3.4":
  "integrity" "sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  "version" "4.3.7"
  dependencies:
    "ms" "^2.1.3"

"debug@4.3.4":
  "integrity" "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"decamelize@^1.2.0":
  "integrity" "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA=="
  "resolved" "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "version" "0.2.0"

"deep-equal@^1.0.1":
  "integrity" "sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg=="
  "resolved" "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-arguments" "^1.1.1"
    "is-date-object" "^1.0.5"
    "is-regex" "^1.1.4"
    "object-is" "^1.1.5"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.5.1"

"deep-extend@^0.6.0":
  "integrity" "sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA=="
  "resolved" "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz"
  "version" "0.6.0"

"deepmerge@^4.2.2":
  "integrity" "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A=="
  "resolved" "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  "version" "4.3.1"

"default-gateway@^6.0.3":
  "integrity" "sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg=="
  "resolved" "https://registry.npmjs.org/default-gateway/-/default-gateway-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "execa" "^5.0.0"

"defaults@^1.0.3":
  "integrity" "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A=="
  "resolved" "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "clone" "^1.0.2"

"define-data-property@^1.0.1", "define-data-property@^1.1.4":
  "integrity" "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A=="
  "resolved" "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "es-define-property" "^1.0.0"
    "es-errors" "^1.3.0"
    "gopd" "^1.0.1"

"define-lazy-prop@^2.0.0":
  "integrity" "sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og=="
  "resolved" "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz"
  "version" "2.0.0"

"define-properties@^1.1.3", "define-properties@^1.2.1":
  "integrity" "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg=="
  "resolved" "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "define-data-property" "^1.0.1"
    "has-property-descriptors" "^1.0.0"
    "object-keys" "^1.1.1"

"define-property@^0.2.5":
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"del@^2.2.0":
  "integrity" "sha512-Z4fzpbIRjOu7lO5jCETSWoqUDVe0IPOlfugBsF6suen2LKDlVb4QZpKEM9P+buNJ4KI1eN7I083w/pbKUpsrWQ=="
  "resolved" "https://registry.npmjs.org/del/-/del-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "globby" "^5.0.0"
    "is-path-cwd" "^1.0.0"
    "is-path-in-cwd" "^1.0.0"
    "object-assign" "^4.0.1"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"
    "rimraf" "^2.2.8"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"delegates@^1.0.0":
  "integrity" "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ=="
  "resolved" "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz"
  "version" "1.0.0"

"depd@~1.1.2":
  "integrity" "sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ=="
  "resolved" "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  "version" "1.1.2"

"depd@2.0.0":
  "integrity" "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw=="
  "resolved" "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  "version" "2.0.0"

"dependency-graph@^0.11.0":
  "integrity" "sha512-JeMq7fEshyepOWDfcfHK06N3MhyPhz++vtqWhMT5O9A3K42rdsEDpfdVqjaqaAhsw6a+ZqeDvQVtD0hFHQWrzg=="
  "resolved" "https://registry.npmjs.org/dependency-graph/-/dependency-graph-0.11.0.tgz"
  "version" "0.11.0"

"destroy@1.2.0":
  "integrity" "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg=="
  "resolved" "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz"
  "version" "1.2.0"

"detect-node@^2.0.4":
  "integrity" "sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g=="
  "resolved" "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz"
  "version" "2.1.0"

"di@^0.0.1":
  "integrity" "sha512-uJaamHkagcZtHPqCIHZxnFrXlunQXgBOsZSUOWwFw31QJCAbyTBoHMW75YOTur5ZNx8pIeAKgf6GWIgaqqiLhA=="
  "resolved" "https://registry.npmjs.org/di/-/di-0.0.1.tgz"
  "version" "0.0.1"

"diff@^4.0.1":
  "integrity" "sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A=="
  "resolved" "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz"
  "version" "4.0.2"

"dir-glob@^3.0.1":
  "integrity" "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA=="
  "resolved" "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "path-type" "^4.0.0"

"dns-packet@^5.2.2":
  "integrity" "sha512-l4gcSouhcgIKRvyy99RNVOgxXiicE+2jZoNmaNmZ6JXiGajBOJAesk1OBlJuM5k2c+eudGdLxDqXuPCKIj6kpw=="
  "resolved" "https://registry.npmjs.org/dns-packet/-/dns-packet-5.6.1.tgz"
  "version" "5.6.1"
  dependencies:
    "@leichtgewicht/ip-codec" "^2.0.1"

"dom-serialize@^2.2.1":
  "integrity" "sha512-Yra4DbvoW7/Z6LBN560ZwXMjoNOSAN2wRsKFGc4iBeso+mpIA6qj1vfdf9HpMaKAqG6wXTy+1SYEzmNpKXOSsQ=="
  "resolved" "https://registry.npmjs.org/dom-serialize/-/dom-serialize-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "custom-event" "~1.0.0"
    "ent" "~2.2.0"
    "extend" "^3.0.0"
    "void-elements" "^2.0.0"

"dom-serializer@^1.0.1":
  "integrity" "sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag=="
  "resolved" "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.2.0"
    "entities" "^2.0.0"

"domelementtype@^2.0.1", "domelementtype@^2.2.0":
  "integrity" "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw=="
  "resolved" "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz"
  "version" "2.3.0"

"domhandler@^4.2.0", "domhandler@^4.3.1":
  "integrity" "sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ=="
  "resolved" "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "domelementtype" "^2.2.0"

"dompurify@^3.2.4":
  "version" "3.2.6"
  optionalDependencies:
    "@types/trusted-types" "^2.0.7"

"domutils@^2.8.0":
  "integrity" "sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A=="
  "resolved" "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "dom-serializer" "^1.0.1"
    "domelementtype" "^2.2.0"
    "domhandler" "^4.2.0"

"dunder-proto@^1.0.1":
  "version" "1.0.1"
  dependencies:
    "call-bind-apply-helpers" "^1.0.1"
    "es-errors" "^1.3.0"
    "gopd" "^1.2.0"

"duplexer@^0.1.1":
  "integrity" "sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg=="
  "resolved" "https://registry.npmjs.org/duplexer/-/duplexer-0.1.2.tgz"
  "version" "0.1.2"

"duplexify@^3.6.0":
  "integrity" "sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g=="
  "resolved" "https://registry.npmjs.org/duplexify/-/duplexify-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "end-of-stream" "^1.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"
    "stream-shift" "^1.0.0"

"eastasianwidth@^0.2.0":
  "version" "0.2.0"

"ecc-jsbn@~0.1.1":
  "integrity" "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw=="
  "resolved" "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.1.0"

"echarts@^5.5.0":
  "version" "5.6.0"
  dependencies:
    "tslib" "2.3.0"
    "zrender" "5.6.1"

"editorconfig@^1.0.4":
  "integrity" "sha512-L9Qe08KWTlqYMVvMcTIvMAdl1cDUubzRNYL+WfA4bLDMHe4nemKkpmYzkznE1FwLKu0EEmy6obgQKzMJrg4x9Q=="
  "resolved" "https://registry.npmjs.org/editorconfig/-/editorconfig-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "@one-ini/wasm" "0.1.1"
    "commander" "^10.0.0"
    "minimatch" "9.0.1"
    "semver" "^7.5.3"

"ee-first@1.1.1":
  "integrity" "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow=="
  "resolved" "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"electron-to-chromium@^1.5.160":
  "version" "1.5.162"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emoji-regex@^9.2.2":
  "version" "9.2.2"

"emojis-list@^3.0.0":
  "integrity" "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="
  "resolved" "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encodeurl@~1.0.2":
  "integrity" "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w=="
  "resolved" "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"encodeurl@~2.0.0":
  "integrity" "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg=="
  "resolved" "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz"
  "version" "2.0.0"

"encoding@^0.1.13":
  "integrity" "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A=="
  "resolved" "https://registry.npmjs.org/encoding/-/encoding-0.1.13.tgz"
  "version" "0.1.13"
  dependencies:
    "iconv-lite" "^0.6.2"

"end-of-stream@^1.0.0", "end-of-stream@^1.1.0":
  "integrity" "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="
  "resolved" "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"engine.io-parser@~5.2.1":
  "version" "5.2.3"

"engine.io@~6.6.0":
  "version" "6.6.4"
  dependencies:
    "@types/cors" "^2.8.12"
    "@types/node" ">=10.0.0"
    "accepts" "~1.3.4"
    "base64id" "2.0.0"
    "cookie" "~0.7.2"
    "cors" "~2.8.5"
    "debug" "~4.3.1"
    "engine.io-parser" "~5.2.1"
    "ws" "~8.17.1"

"enhanced-resolve@^5.10.0":
  "version" "5.18.1"
  dependencies:
    "graceful-fs" "^4.2.4"
    "tapable" "^2.2.0"

"ent@~2.2.0":
  "version" "2.2.0"

"entities@^2.0.0":
  "integrity" "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A=="
  "resolved" "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz"
  "version" "2.2.0"

"env-paths@^2.2.0":
  "integrity" "sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A=="
  "resolved" "https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz"
  "version" "2.2.1"

"err-code@^2.0.2":
  "integrity" "sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA=="
  "resolved" "https://registry.npmjs.org/err-code/-/err-code-2.0.3.tgz"
  "version" "2.0.3"

"errno@^0.1.1":
  "integrity" "sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A=="
  "resolved" "https://registry.npmjs.org/errno/-/errno-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.3.1":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"es-define-property@^1.0.0", "es-define-property@^1.0.1":
  "version" "1.0.1"

"es-errors@^1.3.0":
  "integrity" "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="
  "resolved" "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  "version" "1.3.0"

"es-module-lexer@^0.9.0":
  "version" "0.9.3"

"es-object-atoms@^1.0.0", "es-object-atoms@^1.1.1":
  "version" "1.1.1"
  dependencies:
    "es-errors" "^1.3.0"

"es6-promise@^4.0.3":
  "version" "4.2.8"

"es6-promisify@^5.0.0":
  "version" "5.0.0"
  dependencies:
    "es6-promise" "^4.0.3"

"esbuild-wasm@^0.15.0", "esbuild-wasm@0.15.5":
  "integrity" "sha512-lTJOEKekN/4JI/eOEq0wLcx53co2N6vaT/XjBz46D1tvIVoUEyM0o2K6txW6gEotf31szFD/J1PbxmnbkGlK9A=="
  "resolved" "https://registry.npmjs.org/esbuild-wasm/-/esbuild-wasm-0.15.5.tgz"
  "version" "0.15.5"

"esbuild-windows-64@0.15.5":
  "integrity" "sha512-v+PjvNtSASHOjPDMIai9Yi+aP+Vwox+3WVdg2JB8N9aivJ7lyhp4NVU+J0MV2OkWFPnVO8AE/7xH+72ibUUEnw=="
  "resolved" "https://registry.npmjs.org/esbuild-windows-64/-/esbuild-windows-64-0.15.5.tgz"
  "version" "0.15.5"

"esbuild@^0.15.0", "esbuild@0.15.5":
  "integrity" "sha512-VSf6S1QVqvxfIsSKb3UKr3VhUCis7wgDbtF4Vd9z84UJr05/Sp2fRKmzC+CSPG/dNAPPJZ0BTBLTT1Fhd6N9Gg=="
  "resolved" "https://registry.npmjs.org/esbuild/-/esbuild-0.15.5.tgz"
  "version" "0.15.5"
  optionalDependencies:
    "@esbuild/linux-loong64" "0.15.5"
    "esbuild-android-64" "0.15.5"
    "esbuild-android-arm64" "0.15.5"
    "esbuild-darwin-64" "0.15.5"
    "esbuild-darwin-arm64" "0.15.5"
    "esbuild-freebsd-64" "0.15.5"
    "esbuild-freebsd-arm64" "0.15.5"
    "esbuild-linux-32" "0.15.5"
    "esbuild-linux-64" "0.15.5"
    "esbuild-linux-arm" "0.15.5"
    "esbuild-linux-arm64" "0.15.5"
    "esbuild-linux-mips64le" "0.15.5"
    "esbuild-linux-ppc64le" "0.15.5"
    "esbuild-linux-riscv64" "0.15.5"
    "esbuild-linux-s390x" "0.15.5"
    "esbuild-netbsd-64" "0.15.5"
    "esbuild-openbsd-64" "0.15.5"
    "esbuild-sunos-64" "0.15.5"
    "esbuild-windows-32" "0.15.5"
    "esbuild-windows-64" "0.15.5"
    "esbuild-windows-arm64" "0.15.5"

"escalade@^3.1.1", "escalade@^3.2.0":
  "integrity" "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA=="
  "resolved" "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  "version" "3.2.0"

"escape-html@~1.0.3":
  "integrity" "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow=="
  "resolved" "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.5":
  "integrity" "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"eslint-scope@5.1.1":
  "integrity" "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"esprima@^4.0.0":
  "integrity" "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="
  "resolved" "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.2.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"estree-walker@^1.0.1":
  "integrity" "sha512-1fMXF3YP4pZZVozF8j/ZLfvnR8NSIljt56UhbZ5PeeDmmGHpgpdwQt7ITlGvYaQukCvuBRMLEiKiYC+oeIg4cg=="
  "resolved" "https://registry.npmjs.org/estree-walker/-/estree-walker-1.0.1.tgz"
  "version" "1.0.1"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg=="
  "resolved" "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"eventemitter-asyncresource@^1.0.0":
  "integrity" "sha512-39F7TBIV0G7gTelxwbEqnwhp90eqCPON1k0NwNfwhgKn4Co4ybUbj2pECcXT0B3ztRKZ7Pw1JujUUgmQJHcVAQ=="
  "resolved" "https://registry.npmjs.org/eventemitter-asyncresource/-/eventemitter-asyncresource-1.0.0.tgz"
  "version" "1.0.0"

"eventemitter3@^2.0.3":
  "integrity" "sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg=="
  "resolved" "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.3.tgz"
  "version" "2.0.3"

"eventemitter3@^4.0.0":
  "integrity" "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw=="
  "resolved" "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz"
  "version" "4.0.7"

"events@^3.2.0":
  "integrity" "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="
  "resolved" "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  "version" "3.3.0"

"execa@^5.0.0":
  "integrity" "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cross-spawn" "^7.0.3"
    "get-stream" "^6.0.0"
    "human-signals" "^2.1.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.1"
    "onetime" "^5.1.2"
    "signal-exit" "^3.0.3"
    "strip-final-newline" "^2.0.0"

"exit@^0.1.2":
  "integrity" "sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ=="
  "resolved" "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz"
  "version" "0.1.2"

"expand-brackets@^0.1.4":
  "version" "0.1.5"
  dependencies:
    "is-posix-bracket" "^0.1.0"

"expand-brackets@^2.1.4":
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"exponential-backoff@^3.1.1":
  "version" "3.1.2"

"express@^4.17.3":
  "version" "4.21.2"
  dependencies:
    "accepts" "~1.3.8"
    "array-flatten" "1.1.1"
    "body-parser" "1.20.3"
    "content-disposition" "0.5.4"
    "content-type" "~1.0.4"
    "cookie" "0.7.1"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "encodeurl" "~2.0.0"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "1.3.1"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "merge-descriptors" "1.0.3"
    "methods" "~1.1.2"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.12"
    "proxy-addr" "~2.0.7"
    "qs" "6.13.0"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.2.1"
    "send" "0.19.0"
    "serve-static" "1.16.2"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"extend-shallow@^2.0.1":
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0", "extend-shallow@^3.0.2":
  "integrity" "sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q=="
  "resolved" "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend@^3.0.0", "extend@^3.0.2", "extend@~3.0.2":
  "integrity" "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
  "resolved" "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"external-editor@^3.0.3":
  "integrity" "sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew=="
  "resolved" "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "chardet" "^0.7.0"
    "iconv-lite" "^0.4.24"
    "tmp" "^0.0.33"

"extglob@^0.3.1":
  "version" "0.3.2"
  dependencies:
    "is-extglob" "^1.0.0"

"extglob@^2.0.4":
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"extsprintf@^1.2.0", "extsprintf@1.3.0":
  "integrity" "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g=="
  "resolved" "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz"
  "version" "1.3.0"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-diff@1.1.2":
  "integrity" "sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig=="
  "resolved" "https://registry.npmjs.org/fast-diff/-/fast-diff-1.1.2.tgz"
  "version" "1.1.2"

"fast-glob@^3.2.11", "fast-glob@^3.3.0":
  "version" "3.3.3"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.8"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-uri@^3.0.1":
  "version" "3.0.6"

"fastparse@^1.1.2":
  "integrity" "sha512-483XLLxTVIwWK3QTrMGRqUfUpoOs/0hbQrl2oz4J0pAcm3A3bu84wxTFqGqkJzewCLdME38xJLJAxBABfQT8sQ=="
  "resolved" "https://registry.npmjs.org/fastparse/-/fastparse-1.1.2.tgz"
  "version" "1.1.2"

"fastq@^1.6.0":
  "version" "1.19.1"
  dependencies:
    "reusify" "^1.0.4"

"faye-websocket@^0.11.3":
  "integrity" "sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g=="
  "resolved" "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz"
  "version" "0.11.4"
  dependencies:
    "websocket-driver" ">=0.5.1"

"fflate@^0.8.1":
  "integrity" "sha512-cPJU47OaAoCbg0pBvzsgpTPhmhqI5eJjh/JIu8tPj5q+T7iLvW/JAYUqmE7KOB4R1ZyEhzBaIQpQpardBF5z8A=="
  "resolved" "https://registry.npmjs.org/fflate/-/fflate-0.8.2.tgz"
  "version" "0.8.2"

"figures@^3.0.0":
  "integrity" "sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg=="
  "resolved" "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"filename-regex@^2.0.0":
  "version" "2.0.1"

"fill-range@^7.0.1", "fill-range@^7.1.1":
  "integrity" "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@1.1.2":
  "integrity" "sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA=="
  "resolved" "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "statuses" "~1.5.0"
    "unpipe" "~1.0.0"

"finalhandler@1.3.1":
  "integrity" "sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ=="
  "resolved" "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~2.0.0"
    "escape-html" "~1.0.3"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "statuses" "2.0.1"
    "unpipe" "~1.0.0"

"find-cache-dir@^3.3.1", "find-cache-dir@^3.3.2":
  "integrity" "sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig=="
  "resolved" "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^3.0.2"
    "pkg-dir" "^4.1.0"

"find-index@^0.1.1":
  "integrity" "sha512-uJ5vWrfBKMcE6y2Z8834dwEZj9mNGxYa3t3I53OwFeuZ8D9oc2E5zcsrkuhX6h4iYrjhiv0T3szQmxlAV9uxDg=="
  "resolved" "https://registry.npmjs.org/find-index/-/find-index-0.1.1.tgz"
  "version" "0.1.1"

"find-up@^4.0.0", "find-up@^4.1.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"flatted@^3.2.7":
  "version" "3.3.3"

"flush-write-stream@^1.0.2":
  "integrity" "sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w=="
  "resolved" "https://registry.npmjs.org/flush-write-stream/-/flush-write-stream-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "inherits" "^2.0.3"
    "readable-stream" "^2.3.6"

"follow-redirects@^1.0.0":
  "version" "1.13.0"

"for-in@^1.0.1", "for-in@^1.0.2":
  "version" "1.0.2"

"for-own@^0.1.4":
  "version" "0.1.5"
  dependencies:
    "for-in" "^1.0.1"

"foreground-child@^3.1.0":
  "version" "3.3.1"
  dependencies:
    "cross-spawn" "^7.0.6"
    "signal-exit" "^4.0.1"

"forever-agent@~0.6.1":
  "integrity" "sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw=="
  "resolved" "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz"
  "version" "0.6.1"

"form-data@~2.3.2":
  "integrity" "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.6"
    "mime-types" "^2.1.12"

"forwarded@0.2.0":
  "integrity" "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow=="
  "resolved" "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz"
  "version" "0.2.0"

"fraction.js@^4.3.7":
  "integrity" "sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew=="
  "resolved" "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz"
  "version" "4.3.7"

"fragment-cache@^0.2.1":
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q=="
  "resolved" "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  "version" "0.5.2"

"froala-editor@4.0.19":
  "integrity" "sha512-rvrCiYWx39pApqfFfBQ9p3METidmR9iK4HjW2kRxqW92/bceXtmU+VXRy78BU55+dtv8bctTpr/W2ogCOVR67A=="
  "resolved" "https://registry.npmjs.org/froala-editor/-/froala-editor-4.0.19.tgz"
  "version" "4.0.19"

"fs-extra@^8.1.0":
  "integrity" "sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g=="
  "resolved" "https://registry.npmjs.org/fs-extra/-/fs-extra-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "graceful-fs" "^4.2.0"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs-minipass@^2.0.0", "fs-minipass@^2.1.0":
  "integrity" "sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg=="
  "resolved" "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "minipass" "^3.0.0"

"fs-mkdirp-stream@^1.0.0":
  "integrity" "sha512-+vSd9frUnapVC2RZYfL3FCB2p3g4TBhaUmrsWlSudsGdnxIuUvBB2QM1VZeBtc49QFwrp+wQLrDs3+xxDgI5gQ=="
  "resolved" "https://registry.npmjs.org/fs-mkdirp-stream/-/fs-mkdirp-stream-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "graceful-fs" "^4.1.11"
    "through2" "^2.0.3"

"fs-monkey@^1.0.4":
  "integrity" "sha512-b1FMfwetIKymC0eioW7mTywihSQE4oLzQn1dB6rZB5fx/3NpNEdAWeCSMB+60/AeT0TCXsxzAlcYVEFCTAksWg=="
  "resolved" "https://registry.npmjs.org/fs-monkey/-/fs-monkey-1.0.6.tgz"
  "version" "1.0.6"

"fs.realpath@^1.0.0":
  "integrity" "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="
  "resolved" "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"function-bind@^1.1.2":
  "integrity" "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="
  "resolved" "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  "version" "1.1.2"

"functions-have-names@^1.2.3":
  "integrity" "sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ=="
  "resolved" "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
  "version" "1.2.3"

"gauge@^4.0.3":
  "integrity" "sha512-f9m+BEN5jkg6a0fZjleidjN51VE1X+mPFQ2DJ0uv1V39oCLCbsGe6yjbBnp7eK7z/+GAon99a3nHuqbuuthyPg=="
  "resolved" "https://registry.npmjs.org/gauge/-/gauge-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "aproba" "^1.0.3 || ^2.0.0"
    "color-support" "^1.1.3"
    "console-control-strings" "^1.1.0"
    "has-unicode" "^2.0.1"
    "signal-exit" "^3.0.7"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"
    "wide-align" "^1.1.5"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.1", "get-caller-file@^2.0.5":
  "integrity" "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="
  "resolved" "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.2.4", "get-intrinsic@^1.2.5", "get-intrinsic@^1.3.0":
  "version" "1.3.0"
  dependencies:
    "call-bind-apply-helpers" "^1.0.2"
    "es-define-property" "^1.0.1"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.1.1"
    "function-bind" "^1.1.2"
    "get-proto" "^1.0.1"
    "gopd" "^1.2.0"
    "has-symbols" "^1.1.0"
    "hasown" "^2.0.2"
    "math-intrinsics" "^1.1.0"

"get-package-type@^0.1.0":
  "integrity" "sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q=="
  "resolved" "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz"
  "version" "0.1.0"

"get-proto@^1.0.1":
  "version" "1.0.1"
  dependencies:
    "dunder-proto" "^1.0.1"
    "es-object-atoms" "^1.0.0"

"get-stream@^6.0.0":
  "integrity" "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg=="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  "version" "6.0.1"

"get-value@^2.0.3", "get-value@^2.0.6":
  "version" "2.0.6"

"getpass@^0.1.1":
  "integrity" "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng=="
  "resolved" "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "assert-plus" "^1.0.0"

"glob-base@^0.3.0":
  "version" "0.3.0"
  dependencies:
    "glob-parent" "^2.0.0"
    "is-glob" "^2.0.0"

"glob-parent@^2.0.0":
  "version" "2.0.0"
  dependencies:
    "is-glob" "^2.0.0"

"glob-parent@^3.1.0":
  "version" "3.1.0"
  dependencies:
    "is-glob" "^3.1.0"
    "path-dirname" "^1.0.0"

"glob-parent@^5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@^6.0.1":
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-stream@^6.1.0":
  "integrity" "sha512-uMbLGAP3S2aDOHUDfdoYcdIePUCfysbAd0IAoWVZbeGU/oNQ8asHVSshLDJUPWxfzj8zsCG7/XeHPHTtow0nsw=="
  "resolved" "https://registry.npmjs.org/glob-stream/-/glob-stream-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "extend" "^3.0.0"
    "glob" "^7.1.1"
    "glob-parent" "^3.1.0"
    "is-negated-glob" "^1.0.0"
    "ordered-read-streams" "^1.0.0"
    "pumpify" "^1.3.5"
    "readable-stream" "^2.1.5"
    "remove-trailing-separator" "^1.0.1"
    "to-absolute-glob" "^2.0.0"
    "unique-stream" "^2.0.2"

"glob-to-regexp@^0.4.1":
  "integrity" "sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw=="
  "resolved" "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  "version" "0.4.1"

"glob@^10.4.2":
  "version" "10.4.5"
  dependencies:
    "foreground-child" "^3.1.0"
    "jackspeak" "^3.1.2"
    "minimatch" "^9.0.4"
    "minipass" "^7.1.2"
    "package-json-from-dist" "^1.0.0"
    "path-scurry" "^1.11.1"

"glob@^7.0.3", "glob@^7.0.5", "glob@^7.0.6", "glob@^7.1.1", "glob@^7.1.3", "glob@^7.1.4", "glob@^7.1.6":
  "version" "7.1.6"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@^7.1.7":
  "integrity" "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@^8.0.0":
  "integrity" "sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^5.0.1"
    "once" "^1.3.0"

"glob@^8.0.1":
  "integrity" "sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^5.0.1"
    "once" "^1.3.0"

"glob@^8.1.0":
  "version" "8.1.0"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^5.0.1"
    "once" "^1.3.0"

"glob@8.0.3":
  "integrity" "sha512-ull455NHSHI/Y1FqGaaYFaLGkNMMJbavMrEGFXG/PGrg6y7sutWHUHrz6gy6WEBH6akM1M414dWKCNs+IhKdiQ=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-8.0.3.tgz"
  "version" "8.0.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^5.0.1"
    "once" "^1.3.0"

"glob2base@^0.0.12":
  "integrity" "sha512-ZyqlgowMbfj2NPjxaZZ/EtsXlOch28FRXgMd64vqZWk1bT9+wvSRLYD1om9M7QfQru51zJPAT17qXm4/zd+9QA=="
  "resolved" "https://registry.npmjs.org/glob2base/-/glob2base-0.0.12.tgz"
  "version" "0.0.12"
  dependencies:
    "find-index" "^0.1.1"

"globals@^11.1.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globby@^13.1.1":
  "integrity" "sha512-Y1zNGV+pzQdh7H39l9zgB4PJqjRNqydvdYCDG4HFXM4XuvSaQQlEc91IU1yALL8gUTDomgBAfz3XJdmUS+oo0w=="
  "resolved" "https://registry.npmjs.org/globby/-/globby-13.2.2.tgz"
  "version" "13.2.2"
  dependencies:
    "dir-glob" "^3.0.1"
    "fast-glob" "^3.3.0"
    "ignore" "^5.2.4"
    "merge2" "^1.4.1"
    "slash" "^4.0.0"

"globby@^5.0.0":
  "integrity" "sha512-HJRTIH2EeH44ka+LWig+EqT2ONSYpVlNfx6pyd592/VF1TbfljJ7elwie7oSwcViLGqOdWocSdu2txwBF9bjmQ=="
  "resolved" "https://registry.npmjs.org/globby/-/globby-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "array-union" "^1.0.1"
    "arrify" "^1.0.0"
    "glob" "^7.0.3"
    "object-assign" "^4.0.1"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"gopd@^1.0.1", "gopd@^1.2.0":
  "integrity" "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="
  "resolved" "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  "version" "1.2.0"

"graceful-fs@^4.0.0", "graceful-fs@^4.1.11", "graceful-fs@^4.1.2", "graceful-fs@^4.1.6", "graceful-fs@^4.2.0", "graceful-fs@^4.2.4", "graceful-fs@^4.2.6", "graceful-fs@^4.2.9":
  "integrity" "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="
  "resolved" "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  "version" "4.2.11"

"graceful-fs@4.2.10":
  "integrity" "sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA=="
  "resolved" "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.10.tgz"
  "version" "4.2.10"

"graphlib@^2.1.7", "graphlib@2.1.7":
  "integrity" "sha512-TyI9jIy2J4j0qgPmOOrHTCtpPqJGN/aurBwc6ZT+bRii+di1I+Wv3obRhVrmBEXet+qkMaEX67dXrwsd3QQM6w=="
  "resolved" "https://registry.npmjs.org/graphlib/-/graphlib-2.1.7.tgz"
  "version" "2.1.7"
  dependencies:
    "lodash" "^4.17.5"

"gulp-typescript@^5.0.1":
  "integrity" "sha512-YuMMlylyJtUSHG1/wuSVTrZp60k1dMEFKYOvDf7OvbAJWrDtxxD4oZon4ancdWwzjj30ztiidhe4VXJniF0pIQ=="
  "resolved" "https://registry.npmjs.org/gulp-typescript/-/gulp-typescript-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "ansi-colors" "^3.0.5"
    "plugin-error" "^1.0.1"
    "source-map" "^0.7.3"
    "through2" "^3.0.0"
    "vinyl" "^2.1.0"
    "vinyl-fs" "^3.0.3"

"handle-thing@^2.0.0":
  "integrity" "sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg=="
  "resolved" "https://registry.npmjs.org/handle-thing/-/handle-thing-2.0.1.tgz"
  "version" "2.0.1"

"har-schema@^2.0.0":
  "integrity" "sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q=="
  "resolved" "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz"
  "version" "2.0.0"

"har-validator@~5.1.3":
  "integrity" "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w=="
  "resolved" "https://registry.npmjs.org/har-validator/-/har-validator-5.1.5.tgz"
  "version" "5.1.5"
  dependencies:
    "ajv" "^6.12.3"
    "har-schema" "^2.0.0"

"has-ansi@^2.0.0":
  "integrity" "sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg=="
  "resolved" "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-flag@^3.0.0":
  "integrity" "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-property-descriptors@^1.0.0", "has-property-descriptors@^1.0.2":
  "integrity" "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg=="
  "resolved" "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "es-define-property" "^1.0.0"

"has-symbols@^1.0.1", "has-symbols@^1.0.3", "has-symbols@^1.1.0":
  "integrity" "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ=="
  "resolved" "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  "version" "1.1.0"

"has-tostringtag@^1.0.2":
  "integrity" "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw=="
  "resolved" "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "has-symbols" "^1.0.3"

"has-unicode@^2.0.1":
  "integrity" "sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ=="
  "resolved" "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz"
  "version" "2.0.1"

"has-value@^0.3.1":
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "version" "0.1.4"

"has-values@^1.0.0":
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"hasown@^2.0.2":
  "integrity" "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ=="
  "resolved" "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "function-bind" "^1.1.2"

"hdr-histogram-js@^2.0.1":
  "integrity" "sha512-Hkn78wwzWHNCp2uarhzQ2SGFLU3JY8SBDDd3TAABK4fc30wm+MuPOrg5QVFVfkKOQd6Bfz3ukJEI+q9sXEkK1g=="
  "resolved" "https://registry.npmjs.org/hdr-histogram-js/-/hdr-histogram-js-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "@assemblyscript/loader" "^0.10.1"
    "base64-js" "^1.2.0"
    "pako" "^1.0.3"

"hdr-histogram-percentiles-obj@^3.0.0":
  "integrity" "sha512-7kIufnBqdsBGcSZLPJwqHT3yhk1QTsSlFsVD3kx5ixH/AlgBs9yM1q6DPhXZ8f8gtdqgh7N7/5btRLpQsS2gHw=="
  "resolved" "https://registry.npmjs.org/hdr-histogram-percentiles-obj/-/hdr-histogram-percentiles-obj-3.0.0.tgz"
  "version" "3.0.0"

"hosted-git-info@^5.0.0":
  "integrity" "sha512-xIcQYMnhcx2Nr4JTjsFmwwnr9vldugPy9uVm0o87bjqqWMv9GaqsTeT+i99wTl0mk1uLxJtHxLb8kymqTENQsw=="
  "resolved" "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "lru-cache" "^7.5.1"

"hpack.js@^2.1.6":
  "integrity" "sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ=="
  "resolved" "https://registry.npmjs.org/hpack.js/-/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "^2.0.1"
    "obuf" "^1.0.0"
    "readable-stream" "^2.0.1"
    "wbuf" "^1.1.0"

"html-entities@^2.3.2":
  "version" "2.6.0"

"html-escaper@^2.0.0":
  "integrity" "sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg=="
  "resolved" "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz"
  "version" "2.0.2"

"html2canvas@^1.0.0-rc.5", "html2canvas@1.4.1":
  "integrity" "sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA=="
  "resolved" "https://registry.npmjs.org/html2canvas/-/html2canvas-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "css-line-break" "^2.1.0"
    "text-segmentation" "^1.0.3"

"htmlhint@^1.1.4":
  "version" "1.4.0"
  dependencies:
    "async" "3.2.6"
    "chalk" "4.1.2"
    "commander" "11.1.0"
    "glob" "^8.1.0"
    "is-glob" "^4.0.3"
    "node-fetch" "^2.7.0"
    "strip-json-comments" "3.1.1"
    "xml" "1.0.1"

"http-cache-semantics@^4.1.0":
  "version" "4.2.0"

"http-deceiver@^1.2.7":
  "integrity" "sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw=="
  "resolved" "https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@~1.6.2":
  "integrity" "sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A=="
  "resolved" "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@2.0.0":
  "integrity" "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ=="
  "resolved" "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "depd" "2.0.0"
    "inherits" "2.0.4"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "toidentifier" "1.0.1"

"http-parser-js@>=0.5.1":
  "version" "0.5.2"

"http-proxy-agent@^5.0.0":
  "integrity" "sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w=="
  "resolved" "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@tootallnate/once" "2"
    "agent-base" "6"
    "debug" "4"

"http-proxy-middleware@^2.0.3":
  "version" "2.0.9"
  dependencies:
    "@types/http-proxy" "^1.17.8"
    "http-proxy" "^1.18.1"
    "is-glob" "^4.0.1"
    "is-plain-obj" "^3.0.0"
    "micromatch" "^4.0.2"

"http-proxy@^1.18.1":
  "integrity" "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ=="
  "resolved" "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"http-signature@~1.2.0":
  "integrity" "sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ=="
  "resolved" "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "jsprim" "^1.2.2"
    "sshpk" "^1.7.0"

"https-proxy-agent@^2.2.1":
  "version" "2.2.4"
  dependencies:
    "agent-base" "^4.3.0"
    "debug" "^3.1.0"

"https-proxy-agent@^5.0.0":
  "integrity" "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA=="
  "resolved" "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "agent-base" "6"
    "debug" "4"

"https-proxy-agent@5.0.1":
  "integrity" "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA=="
  "resolved" "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "agent-base" "6"
    "debug" "4"

"human-signals@^2.1.0":
  "integrity" "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw=="
  "resolved" "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz"
  "version" "2.1.0"

"humanize-ms@^1.2.1":
  "integrity" "sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ=="
  "resolved" "https://registry.npmjs.org/humanize-ms/-/humanize-ms-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "ms" "^2.0.0"

"iconv-lite@^0.4.24", "iconv-lite@0.4", "iconv-lite@0.4.24":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"iconv-lite@^0.6.2":
  "integrity" "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"iconv-lite@^0.6.3":
  "integrity" "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"icss-utils@^5.0.0", "icss-utils@^5.1.0":
  "integrity" "sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA=="
  "resolved" "https://registry.npmjs.org/icss-utils/-/icss-utils-5.1.0.tgz"
  "version" "5.1.0"

"ieee754@^1.1.13":
  "integrity" "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="
  "resolved" "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"ignore-walk@^5.0.1":
  "integrity" "sha512-yemi4pMf51WKT7khInJqAvsIGzoqYXblnsz0ql8tM+yi1EKYTY1evX4NAbJrLL/Aanr2HyZeluqU+Oi7MGHokw=="
  "resolved" "https://registry.npmjs.org/ignore-walk/-/ignore-walk-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "minimatch" "^5.0.1"

"ignore@^5.2.4":
  "integrity" "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz"
  "version" "5.3.2"

"image-size@~0.5.0":
  "integrity" "sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ=="
  "resolved" "https://registry.npmjs.org/image-size/-/image-size-0.5.5.tgz"
  "version" "0.5.5"

"immediate@~3.0.5":
  "integrity" "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ=="
  "resolved" "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz"
  "version" "3.0.6"

"immutable@^4.0.0":
  "integrity" "sha512-1hqclzwYwjRDFLjcFxOM5AYkkG0rpFPpr1RLPMEuGczoS7YA8gLhy8SWXYRAA/XwfEHpfo3cw5JGioS32fnMRw=="
  "resolved" "https://registry.npmjs.org/immutable/-/immutable-4.3.7.tgz"
  "version" "4.3.7"

"import-fresh@^3.2.1":
  "version" "3.3.1"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="
  "resolved" "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^4.0.0":
  "integrity" "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg=="
  "resolved" "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"infer-owner@^1.0.4":
  "integrity" "sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A=="
  "resolved" "https://registry.npmjs.org/infer-owner/-/infer-owner-1.0.4.tgz"
  "version" "1.0.4"

"inflight@^1.0.4":
  "integrity" "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA=="
  "resolved" "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.3":
  "integrity" "sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"
  "version" "2.0.3"

"ini@^1.3.4", "ini@~1.3.0":
  "version" "1.3.5"

"ini@3.0.0":
  "integrity" "sha512-TxYQaeNW/N8ymDvwAxPyRbhMBtnEwuvaTYpOQkFx1nSeusgezHniEc/l35Vo4iCq/mMiTJbpD7oYxN98hFlfmw=="
  "resolved" "https://registry.npmjs.org/ini/-/ini-3.0.0.tgz"
  "version" "3.0.0"

"injection-js@^2.4.0":
  "integrity" "sha512-6jiJt0tCAo9zjHbcwLiPL+IuNe9SQ6a9g0PEzafThW3fOQi0mrmiJGBJvDD6tmhPh8cQHIQtCOrJuBfQME4kPA=="
  "resolved" "https://registry.npmjs.org/injection-js/-/injection-js-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "tslib" "^2.0.0"

"inquirer@8.2.4":
  "integrity" "sha512-nn4F01dxU8VeKfq192IjLsxu0/OmMZ4Lg3xKAns148rCaXP6ntAoEkVYZThWjwON8AlzdZZi6oqnhNbxUG9hVg=="
  "resolved" "https://registry.npmjs.org/inquirer/-/inquirer-8.2.4.tgz"
  "version" "8.2.4"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.1.1"
    "cli-cursor" "^3.1.0"
    "cli-width" "^3.0.0"
    "external-editor" "^3.0.3"
    "figures" "^3.0.0"
    "lodash" "^4.17.21"
    "mute-stream" "0.0.8"
    "ora" "^5.4.1"
    "run-async" "^2.4.0"
    "rxjs" "^7.5.5"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "through" "^2.3.6"
    "wrap-ansi" "^7.0.0"

"ip-address@^9.0.5":
  "integrity" "sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g=="
  "resolved" "https://registry.npmjs.org/ip-address/-/ip-address-9.0.5.tgz"
  "version" "9.0.5"
  dependencies:
    "jsbn" "1.1.0"
    "sprintf-js" "^1.1.3"

"ipaddr.js@^2.0.1":
  "integrity" "sha512-Ag3wB2o37wslZS19hZqorUnrnzSkpOVy+IiiDEiTqNubEYpYuHWIf6K4psgN2ZWKExS4xhVCrRVfb/wfW8fWJA=="
  "resolved" "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.2.0.tgz"
  "version" "2.2.0"

"ipaddr.js@1.9.1":
  "integrity" "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="
  "resolved" "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-absolute@^1.0.0":
  "integrity" "sha512-dOWoqflvcydARa360Gvv18DZ/gRuHKi2NU/wU5X1ZFzdYfH29nkiNZsF3mp4OJ3H4yo9Mx8A/uAGNzpzPN3yBA=="
  "resolved" "https://registry.npmjs.org/is-absolute/-/is-absolute-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-relative" "^1.0.0"
    "is-windows" "^1.0.1"

"is-accessor-descriptor@^0.1.6":
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arguments@^1.1.1":
  "integrity" "sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA=="
  "resolved" "https://registry.npmjs.org/is-arguments/-/is-arguments-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "call-bound" "^1.0.2"
    "has-tostringtag" "^1.0.2"

"is-arrayish@^0.2.1":
  "integrity" "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg=="
  "resolved" "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-binary-path@^1.0.0":
  "integrity" "sha512-9fRVlXc0uCxEDj1nQzaWONSpbTfx0FmJfzHF7pwlI8DkWGoHBBea4Pg5Ky0ojwwxQmnSifgbKkI06Qv0Ljgj+Q=="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "binary-extensions" "^1.0.0"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="
  "resolved" "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-builtin-module@^3.1.0":
  "integrity" "sha512-BSLE3HnV2syZ0FK0iMA/yUGplUeMmNz4AW5fnTunbCIqZi4vG3WjJT9FHMy5D69xmAYBHXQhJdALdpwVxV501A=="
  "resolved" "https://registry.npmjs.org/is-builtin-module/-/is-builtin-module-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "builtin-modules" "^3.3.0"

"is-core-module@^2.8.1", "is-core-module@^2.9.0":
  "version" "2.16.1"
  dependencies:
    "hasown" "^2.0.2"

"is-data-descriptor@^0.1.4":
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-date-object@^1.0.5":
  "integrity" "sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg=="
  "resolved" "https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "call-bound" "^1.0.2"
    "has-tostringtag" "^1.0.2"

"is-descriptor@^0.1.0":
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0":
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-descriptor@^1.0.2":
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-docker@^2.0.0", "is-docker@^2.1.1":
  "integrity" "sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ=="
  "resolved" "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz"
  "version" "2.2.1"

"is-dotfile@^1.0.0":
  "version" "1.0.3"

"is-equal-shallow@^0.1.3":
  "version" "0.1.3"
  dependencies:
    "is-primitive" "^2.0.0"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA=="
  "resolved" "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^1.0.0":
  "integrity" "sha512-7Q+VbVafe6x2T+Tu6NcOf6sRklazEPmBoB3IWk3WdGZM2iGUwU/Oe3Wtq5lSEkDTTlpp8yx+5t4pzO/i9Ty1ww=="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz"
  "version" "1.0.0"

"is-extglob@^2.1.0", "is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-glob@^2.0.0", "is-glob@^2.0.1":
  "integrity" "sha512-a1dBeB19NXsf/E0+FHqkagizel/LQw2DjSQpvQrj3zT+jYPpaUCryPnrQajXKFLCMuf4I6FhRpaGtw4lPrG6Eg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extglob" "^1.0.0"

"is-glob@^3.1.0":
  "version" "3.1.0"
  dependencies:
    "is-extglob" "^2.1.0"

"is-glob@^4.0.1", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-glob@^4.0.3":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-interactive@^1.0.0":
  "integrity" "sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w=="
  "resolved" "https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz"
  "version" "1.0.0"

"is-lambda@^1.0.1":
  "integrity" "sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ=="
  "resolved" "https://registry.npmjs.org/is-lambda/-/is-lambda-1.0.1.tgz"
  "version" "1.0.1"

"is-module@^1.0.0":
  "integrity" "sha512-51ypPSPCoTEIN9dy5Oy+h4pShgJmPCygKfyRCISBI+JoWT/2oJvK8QPxmwv7b/p239jXrm9M1mlQbyKJ5A152g=="
  "resolved" "https://registry.npmjs.org/is-module/-/is-module-1.0.0.tgz"
  "version" "1.0.0"

"is-negated-glob@^1.0.0":
  "integrity" "sha512-czXVVn/QEmgvej1f50BZ648vUI+em0xqMq2Sn+QncCLN4zj1UAxlT+kw/6ggQTOaZPd1HqKQGEqbpQVtJucWug=="
  "resolved" "https://registry.npmjs.org/is-negated-glob/-/is-negated-glob-1.0.0.tgz"
  "version" "1.0.0"

"is-number@^3.0.0":
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-path-cwd@^1.0.0":
  "integrity" "sha512-cnS56eR9SPAscL77ik76ATVqoPARTqPIVkMDVxRaWH06zT+6+CzIroYRJ0VVvm0Z1zfAvxvz9i/D3Ppjaqt5Nw=="
  "resolved" "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-1.0.0.tgz"
  "version" "1.0.0"

"is-path-in-cwd@^1.0.0":
  "integrity" "sha512-FjV1RTW48E7CWM7eE/J2NJvAEEVektecDBVBE5Hh3nM1Jd0kvhHtX68Pr3xsDf857xt3Y4AkwVULK1Vku62aaQ=="
  "resolved" "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-path-inside" "^1.0.0"

"is-path-inside@^1.0.0":
  "integrity" "sha512-qhsCR/Esx4U4hg/9I19OVUAJkGWtjRYHMRgUMZE2TDdj+Ag+kttZanLupfddNyglzz50cUlmWzUaI37GDfNx/g=="
  "resolved" "https://registry.npmjs.org/is-path-inside/-/is-path-inside-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "path-is-inside" "^1.0.1"

"is-plain-obj@^3.0.0":
  "integrity" "sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA=="
  "resolved" "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-3.0.0.tgz"
  "version" "3.0.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og=="
  "resolved" "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-posix-bracket@^0.1.0":
  "version" "0.1.1"

"is-primitive@^2.0.0":
  "version" "2.0.0"

"is-regex@^1.1.4":
  "integrity" "sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g=="
  "resolved" "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "call-bound" "^1.0.2"
    "gopd" "^1.2.0"
    "has-tostringtag" "^1.0.2"
    "hasown" "^2.0.2"

"is-relative@^1.0.0":
  "integrity" "sha512-Kw/ReK0iqwKeu0MITLFuj0jbPAmEiOsIwyIXvvbfa6QfmN9pkD1M+8pdk7Rl/dTKbH34/XBFMbgD4iMJhLQbGA=="
  "resolved" "https://registry.npmjs.org/is-relative/-/is-relative-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-unc-path" "^1.0.0"

"is-stream@^2.0.0":
  "integrity" "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg=="
  "resolved" "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-typedarray@~1.0.0":
  "integrity" "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA=="
  "resolved" "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-unc-path@^1.0.0":
  "integrity" "sha512-mrGpVd0fs7WWLfVsStvgF6iEJnbjDFZh9/emhRDcGWTduTfNHd9CHeUwH3gYIjdbwo4On6hunkztwOaAw0yllQ=="
  "resolved" "https://registry.npmjs.org/is-unc-path/-/is-unc-path-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "unc-path-regex" "^0.1.2"

"is-unicode-supported@^0.1.0":
  "integrity" "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw=="
  "resolved" "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  "version" "0.1.0"

"is-utf8@^0.2.1":
  "integrity" "sha512-rMYPYvCzsXywIsldgLaSoPlw5PfoB/ssr7hY4pLfcodrA5M/eArza1a9VmTiNIBNMjOGr1Ow9mTyU2o69U6U9Q=="
  "resolved" "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz"
  "version" "0.2.1"

"is-valid-glob@^1.0.0":
  "integrity" "sha512-AhiROmoEFDSsjx8hW+5sGwgKVIORcXnrlAx/R0ZSeaPw70Vw0CqkGBBhHGL58Uox2eXnU1AnvXJl1XlyedO5bA=="
  "resolved" "https://registry.npmjs.org/is-valid-glob/-/is-valid-glob-1.0.0.tgz"
  "version" "1.0.0"

"is-what@^3.14.1":
  "integrity" "sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA=="
  "resolved" "https://registry.npmjs.org/is-what/-/is-what-3.14.1.tgz"
  "version" "3.14.1"

"is-windows@^1.0.1", "is-windows@^1.0.2":
  "integrity" "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="
  "resolved" "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^2.2.0":
  "integrity" "sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww=="
  "resolved" "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-docker" "^2.0.0"

"isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isbinaryfile@^4.0.8":
  "integrity" "sha512-iHrqe5shvBUcFbmZq9zOQHBoeOhZJu6RQGrDpBgenUm/Am+F3JM2MgQj+rK3Z601fzrL5gLZWtAPH2OBaSVcyw=="
  "resolved" "https://registry.npmjs.org/isbinaryfile/-/isbinaryfile-4.0.10.tgz"
  "version" "4.0.10"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg=="
  "resolved" "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isstream@~0.1.2":
  "integrity" "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g=="
  "resolved" "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz"
  "version" "0.1.2"

"istanbul-lib-coverage@^2.0.5":
  "integrity" "sha512-8aXznuEPCJvGnMSRft4udDRDtb1V3pkQkMMI5LI+6HuQz5oQ4J2UFn1H82raA3qJtyOLkkwVqICBQkjnGtn5mA=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.5.tgz"
  "version" "2.0.5"

"istanbul-lib-coverage@^3.0.0", "istanbul-lib-coverage@^3.2.0":
  "integrity" "sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz"
  "version" "3.2.2"

"istanbul-lib-instrument@^5.0.4":
  "integrity" "sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-coverage" "^3.2.0"
    "semver" "^6.3.0"

"istanbul-lib-report@^3.0.0":
  "integrity" "sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "istanbul-lib-coverage" "^3.0.0"
    "make-dir" "^4.0.0"
    "supports-color" "^7.1.0"

"istanbul-lib-source-maps@^3.0.6":
  "integrity" "sha512-R47KzMtDJH6X4/YW9XTx+jrLnZnscW4VpNN+1PViSYTejLVPWv7oov+Duf8YQSPyVRUvueQqz1TcsC6mooZTXw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-3.0.6.tgz"
  "version" "3.0.6"
  dependencies:
    "debug" "^4.1.1"
    "istanbul-lib-coverage" "^2.0.5"
    "make-dir" "^2.1.0"
    "rimraf" "^2.6.3"
    "source-map" "^0.6.1"

"istanbul-reports@^3.0.2":
  "integrity" "sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g=="
  "resolved" "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.7.tgz"
  "version" "3.1.7"
  dependencies:
    "html-escaper" "^2.0.0"
    "istanbul-lib-report" "^3.0.0"

"jackspeak@^3.1.2":
  "version" "3.4.3"
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

"jasmine-core@^3.6.0":
  "integrity" "sha512-Hu1dmuoGcZ7AfyynN3LsfruwMbxMALMka+YtZeGoLuDEySVmVAPaonkNoBRIw/ectu8b9tVQCJNgp4a4knp+tg=="
  "resolved" "https://registry.npmjs.org/jasmine-core/-/jasmine-core-3.99.1.tgz"
  "version" "3.99.1"

"jasmine-core@~2.8.0":
  "integrity" "sha512-SNkOkS+/jMZvLhuSx1fjhcNWUC/KG6oVyFUGkSBEr9n1axSNduWU8GlI7suaHXr4yxjet6KjrUZxUTE5WzzWwQ=="
  "resolved" "https://registry.npmjs.org/jasmine-core/-/jasmine-core-2.8.0.tgz"
  "version" "2.8.0"

"jasmine-core@~3.5.0":
  "integrity" "sha512-nCeAiw37MIMA9w9IXso7bRaLl+c/ef3wnxsoSAlYrzS+Ot0zTG6nU8G/cIfGkqpkjX2wNaIW9RFG0TwIFnG6bA=="
  "resolved" "https://registry.npmjs.org/jasmine-core/-/jasmine-core-3.5.0.tgz"
  "version" "3.5.0"

"jasmine-spec-reporter@~5.0.0":
  "integrity" "sha512-6gP1LbVgJ+d7PKksQBc2H0oDGNRQI3gKUsWlswKaQ2fif9X5gzhQcgM5+kiJGCQVurOG09jqNhk7payggyp5+g=="
  "resolved" "https://registry.npmjs.org/jasmine-spec-reporter/-/jasmine-spec-reporter-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "colors" "1.4.0"

"jasmine@2.8.0":
  "integrity" "sha512-KbdGQTf5jbZgltoHs31XGiChAPumMSY64OZMWLNYnEnMfG5uwGBhffePwuskexjT+/Jea/gU3qAU8344hNohSw=="
  "resolved" "https://registry.npmjs.org/jasmine/-/jasmine-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "exit" "^0.1.2"
    "glob" "^7.0.6"
    "jasmine-core" "~2.8.0"

"jasminewd2@^2.1.0":
  "integrity" "sha512-Rn0nZe4rfDhzA63Al3ZGh0E+JTmM6ESZYXJGKuqKGZObsAB9fwXPD03GjtIEvJBDOhN94T5MzbwZSqzFHSQPzg=="
  "resolved" "https://registry.npmjs.org/jasminewd2/-/jasminewd2-2.2.0.tgz"
  "version" "2.2.0"

"jest-worker@^27.4.5":
  "integrity" "sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg=="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz"
  "version" "27.5.1"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^8.0.0"

"jquery@3.5.1":
  "integrity" "sha512-XwIBPqcMn57FxfT+Go5pzySnm4KWkT1Tv7gjrpT1srtf8Weynl6R273VJ5GjkRb51IzMp5nbaPjJXMWeju2MKg=="
  "resolved" "https://registry.npmjs.org/jquery/-/jquery-3.5.1.tgz"
  "version" "3.5.1"

"js-beautify@^1.14.7":
  "version" "1.15.4"
  dependencies:
    "config-chain" "^1.1.13"
    "editorconfig" "^1.0.4"
    "glob" "^10.4.2"
    "js-cookie" "^3.0.5"
    "nopt" "^7.2.1"

"js-cookie@^3.0.5":
  "integrity" "sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw=="
  "resolved" "https://registry.npmjs.org/js-cookie/-/js-cookie-3.0.5.tgz"
  "version" "3.0.5"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "version" "3.14.0"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsbn@~0.1.0":
  "integrity" "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg=="
  "resolved" "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz"
  "version" "0.1.1"

"jsbn@1.1.0":
  "integrity" "sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A=="
  "resolved" "https://registry.npmjs.org/jsbn/-/jsbn-1.1.0.tgz"
  "version" "1.1.0"

"jsesc@^2.5.1":
  "integrity" "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@^3.0.2":
  "version" "3.1.0"

"jsesc@~3.0.2":
  "integrity" "sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-3.0.2.tgz"
  "version" "3.0.2"

"json-parse-even-better-errors@^2.3.0", "json-parse-even-better-errors@^2.3.1":
  "integrity" "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="
  "resolved" "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-schema@0.2.3":
  "version" "0.2.3"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw=="
  "resolved" "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json-stringify-safe@~5.0.1":
  "integrity" "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA=="
  "resolved" "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
  "version" "5.0.1"

"json5@^2.1.2", "json5@^2.2.1":
  "version" "2.2.3"

"jsonc-parser@^3.0.0", "jsonc-parser@3.1.0":
  "integrity" "sha512-DRf0QjnNeCUds3xTjKlQQ3DpJD51GvDjJfnxUVWg6PZTo2otSm+slzNAxU/35hF8/oJIKoG9slq30JYOsF2azg=="
  "resolved" "https://registry.npmjs.org/jsonc-parser/-/jsonc-parser-3.1.0.tgz"
  "version" "3.1.0"

"jsonfile@^4.0.0":
  "integrity" "sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg=="
  "resolved" "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz"
  "version" "4.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonparse@^1.3.1":
  "integrity" "sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg=="
  "resolved" "https://registry.npmjs.org/jsonparse/-/jsonparse-1.3.1.tgz"
  "version" "1.3.1"

"jspdf@3.0.1":
  "integrity" "sha512-qaGIxqxetdoNnFQQXxTKUD9/Z7AloLaw94fFsOiJMxbfYdBbrBuhWmbzI8TVjrw7s3jBY1PFHofBKMV/wZPapg=="
  "resolved" "https://registry.npmjs.org/jspdf/-/jspdf-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@babel/runtime" "^7.26.7"
    "atob" "^2.1.2"
    "btoa" "^1.2.1"
    "fflate" "^0.8.1"
  optionalDependencies:
    "canvg" "^3.0.11"
    "core-js" "^3.6.0"
    "dompurify" "^3.2.4"
    "html2canvas" "^1.0.0-rc.5"

"jsprim@^1.2.2":
  "version" "1.4.1"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.2.3"
    "verror" "1.10.0"

"jszip@^3.1.3":
  "version" "3.5.0"
  dependencies:
    "lie" "~3.3.0"
    "pako" "~1.0.2"
    "readable-stream" "~2.3.6"
    "set-immediate-shim" "~1.0.1"

"karma-chrome-launcher@~3.1.0":
  "integrity" "sha512-hsIglcq1vtboGPAN+DGCISCFOxW+ZVnIqhDQcCMqqCp+4dmJ0Qpq5QAjkbA0X2L9Mi6OBkHi2Srrbmm7pUKkzQ=="
  "resolved" "https://registry.npmjs.org/karma-chrome-launcher/-/karma-chrome-launcher-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "which" "^1.2.1"

"karma-coverage-istanbul-reporter@~3.0.2":
  "integrity" "sha512-wE4VFhG/QZv2Y4CdAYWDbMmcAHeS926ZIji4z+FkB2aF/EposRb6DP6G5ncT/wXhqUfAb/d7kZrNKPonbvsATw=="
  "resolved" "https://registry.npmjs.org/karma-coverage-istanbul-reporter/-/karma-coverage-istanbul-reporter-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "istanbul-lib-coverage" "^3.0.0"
    "istanbul-lib-report" "^3.0.0"
    "istanbul-lib-source-maps" "^3.0.6"
    "istanbul-reports" "^3.0.2"
    "minimatch" "^3.0.4"

"karma-jasmine-html-reporter@^1.5.0":
  "integrity" "sha512-pzum1TL7j90DTE86eFt48/s12hqwQuiD+e5aXx2Dc9wDEn2LfGq6RoAxEZZjFiN0RDSCOnosEKRZWxbQ+iMpQQ=="
  "resolved" "https://registry.npmjs.org/karma-jasmine-html-reporter/-/karma-jasmine-html-reporter-1.7.0.tgz"
  "version" "1.7.0"

"karma-jasmine@~4.0.0":
  "integrity" "sha512-ggi84RMNQffSDmWSyyt4zxzh2CQGwsxvYYsprgyR1j8ikzIduEdOlcLvXjZGwXG/0j41KUXOWsUCBfbEHPWP9g=="
  "resolved" "https://registry.npmjs.org/karma-jasmine/-/karma-jasmine-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "jasmine-core" "^3.6.0"

"karma-source-map-support@1.4.0":
  "integrity" "sha512-RsBECncGO17KAoJCYXjv+ckIz+Ii9NCi+9enk+rq6XC81ezYkb4/RHE6CTXdA7IOJqoF3wcaLfVG0CPmE5ca6A=="
  "resolved" "https://registry.npmjs.org/karma-source-map-support/-/karma-source-map-support-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "source-map-support" "^0.5.5"

"karma@6.3.16":
  "integrity" "sha512-nEU50jLvDe5yvXqkEJRf8IuvddUkOY2x5Xc4WXHz6dxINgGDrgD2uqQWeVrJs4hbfNaotn+HQ1LZJ4yOXrL7xQ=="
  "resolved" "https://registry.npmjs.org/karma/-/karma-6.3.16.tgz"
  "version" "6.3.16"
  dependencies:
    "body-parser" "^1.19.0"
    "braces" "^3.0.2"
    "chokidar" "^3.5.1"
    "colors" "1.4.0"
    "connect" "^3.7.0"
    "di" "^0.0.1"
    "dom-serialize" "^2.2.1"
    "glob" "^7.1.7"
    "graceful-fs" "^4.2.6"
    "http-proxy" "^1.18.1"
    "isbinaryfile" "^4.0.8"
    "lodash" "^4.17.21"
    "log4js" "^6.4.1"
    "mime" "^2.5.2"
    "minimatch" "^3.0.4"
    "mkdirp" "^0.5.5"
    "qjobs" "^1.2.0"
    "range-parser" "^1.2.1"
    "rimraf" "^3.0.2"
    "socket.io" "^4.2.0"
    "source-map" "^0.6.1"
    "tmp" "^0.2.1"
    "ua-parser-js" "^0.7.30"
    "yargs" "^16.1.1"

"kind-of@^3.0.2":
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.0.3":
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"klona@^2.0.4", "klona@^2.0.5":
  "integrity" "sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA=="
  "resolved" "https://registry.npmjs.org/klona/-/klona-2.0.6.tgz"
  "version" "2.0.6"

"ky@^1.2.0":
  "integrity" "sha512-7Bp3TpsE+L+TARSnnDpk3xg8Idi8RwSLdj6CMbNWoOARIrGrbuLGusV0dYwbZOm4bB3jHNxSw8Wk/ByDqJEnDw=="
  "resolved" "https://registry.npmjs.org/ky/-/ky-1.8.1.tgz"
  "version" "1.8.1"

"lazystream@^1.0.0":
  "version" "1.0.0"
  dependencies:
    "readable-stream" "^2.0.5"

"lead@^1.0.0":
  "integrity" "sha512-IpSVCk9AYvLHo5ctcIXxOBpMWUe+4TKN3VPWAKUbJikkmsGp0VrSM8IttVc32D6J4WUsiPE6aEFRNmIoF/gdow=="
  "resolved" "https://registry.npmjs.org/lead/-/lead-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "flush-write-stream" "^1.0.2"

"less-loader@11.0.0":
  "integrity" "sha512-9+LOWWjuoectIEx3zrfN83NAGxSUB5pWEabbbidVQVgZhN+wN68pOvuyirVlH1IK4VT1f3TmlyvAnCXh8O5KEw=="
  "resolved" "https://registry.npmjs.org/less-loader/-/less-loader-11.0.0.tgz"
  "version" "11.0.0"
  dependencies:
    "klona" "^2.0.4"

"less@^4.1.2", "less@4.1.3":
  "integrity" "sha512-w16Xk/Ta9Hhyei0Gpz9m7VS8F28nieJaL/VyShID7cYvP6IL5oHeL6p4TXSDJqZE/lNv0oJ2pGVjJsRkfwm5FA=="
  "resolved" "https://registry.npmjs.org/less/-/less-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "copy-anything" "^2.0.1"
    "parse-node-version" "^1.0.1"
    "tslib" "^2.3.0"
  optionalDependencies:
    "errno" "^0.1.1"
    "graceful-fs" "^4.1.2"
    "image-size" "~0.5.0"
    "make-dir" "^2.1.0"
    "mime" "^1.4.1"
    "needle" "^3.1.0"
    "source-map" "~0.6.0"

"license-webpack-plugin@4.0.2":
  "integrity" "sha512-771TFWFD70G1wLTC4oU2Cw4qvtmNrIw+wRvBtn+okgHl7slJVi7zfNcdmqDL72BojM30VNJ2UHylr1o77U37Jw=="
  "resolved" "https://registry.npmjs.org/license-webpack-plugin/-/license-webpack-plugin-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "webpack-sources" "^3.0.0"

"lie@~3.3.0":
  "integrity" "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ=="
  "resolved" "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "immediate" "~3.0.5"

"lines-and-columns@^1.1.6":
  "integrity" "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
  "resolved" "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"loader-runner@^4.2.0":
  "integrity" "sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg=="
  "resolved" "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz"
  "version" "4.3.0"

"loader-utils@^2.0.0":
  "integrity" "sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw=="
  "resolved" "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^2.1.2"

"loader-utils@3.2.1":
  "version" "3.2.1"

"locate-path@^5.0.0":
  "integrity" "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash.debounce@^4.0.8":
  "integrity" "sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow=="
  "resolved" "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash@^4.17.21", "lodash@^4.17.4", "lodash@^4.17.5", "lodash@4.17.21":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"log-symbols@^4.1.0":
  "integrity" "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg=="
  "resolved" "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "chalk" "^4.1.0"
    "is-unicode-supported" "^0.1.0"

"log4js@^6.4.1":
  "version" "6.9.1"
  dependencies:
    "date-format" "^4.0.14"
    "debug" "^4.3.4"
    "flatted" "^3.2.7"
    "rfdc" "^1.3.0"
    "streamroller" "^3.1.5"

"loose-envify@^1.4.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lru-cache@^10.2.0":
  "integrity" "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz"
  "version" "10.4.3"

"lru-cache@^5.1.1":
  "integrity" "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"lru-cache@^6.0.0":
  "integrity" "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"lru-cache@^7.4.4":
  "integrity" "sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz"
  "version" "7.18.3"

"lru-cache@^7.5.1":
  "integrity" "sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz"
  "version" "7.18.3"

"lru-cache@^7.7.1":
  "integrity" "sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz"
  "version" "7.18.3"

"magic-string@^0.26.0":
  "integrity" "sha512-hX9XH3ziStPoPhJxLq1syWuZMxbDvGNbVchfrdCtanC7D13888bMFow61x8axrx+GfHLtVeAx2kxL7tTGRl+Ow=="
  "resolved" "https://registry.npmjs.org/magic-string/-/magic-string-0.26.7.tgz"
  "version" "0.26.7"
  dependencies:
    "sourcemap-codec" "^1.4.8"

"magic-string@0.26.2":
  "integrity" "sha512-NzzlXpclt5zAbmo6h6jNc8zl2gNRGHvmsZW4IvZhTC4W7k4OlLP+S5YLussa/r3ixNT66KOQfNORlXHSOy/X4A=="
  "resolved" "https://registry.npmjs.org/magic-string/-/magic-string-0.26.2.tgz"
  "version" "0.26.2"
  dependencies:
    "sourcemap-codec" "^1.4.8"

"make-dir@^2.1.0":
  "integrity" "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^3.0.2", "make-dir@^3.1.0", "make-dir@~3.1.0":
  "integrity" "sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "semver" "^6.0.0"

"make-dir@^4.0.0":
  "integrity" "sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "semver" "^7.5.3"

"make-error@^1.1.1":
  "integrity" "sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw=="
  "resolved" "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz"
  "version" "1.3.6"

"make-fetch-happen@^10.0.3", "make-fetch-happen@^10.0.6":
  "integrity" "sha512-NgOPbRiaQM10DYXvN3/hhGVI2M5MtITFryzBGxHM5p4wnFxsVCbxkrBrDsk+EZ5OB4jEOT7AjDxtdF+KVEFT7w=="
  "resolved" "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.2.1.tgz"
  "version" "10.2.1"
  dependencies:
    "agentkeepalive" "^4.2.1"
    "cacache" "^16.1.0"
    "http-cache-semantics" "^4.1.0"
    "http-proxy-agent" "^5.0.0"
    "https-proxy-agent" "^5.0.0"
    "is-lambda" "^1.0.1"
    "lru-cache" "^7.7.1"
    "minipass" "^3.1.6"
    "minipass-collect" "^1.0.2"
    "minipass-fetch" "^2.0.3"
    "minipass-flush" "^1.0.5"
    "minipass-pipeline" "^1.2.4"
    "negotiator" "^0.6.3"
    "promise-retry" "^2.0.1"
    "socks-proxy-agent" "^7.0.0"
    "ssri" "^9.0.0"

"map-cache@^0.2.2":
  "version" "0.2.2"

"map-visit@^1.0.0":
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"material-colors@^1.2.6":
  "integrity" "sha512-6qE4B9deFBIa9YSpOc9O0Sgc43zTeVYbgDT5veRKSlB2+ZuHNoVVxA1L/ckMUayV9Ay9y7Z/SZCLcGteW9i7bg=="
  "resolved" "https://registry.npmjs.org/material-colors/-/material-colors-1.2.6.tgz"
  "version" "1.2.6"

"math-intrinsics@^1.1.0":
  "version" "1.1.0"

"media-typer@0.3.0":
  "integrity" "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ=="
  "resolved" "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"memfs@^3.4.3":
  "version" "3.5.3"
  dependencies:
    "fs-monkey" "^1.0.4"

"merge-descriptors@1.0.3":
  "integrity" "sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ=="
  "resolved" "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.3.tgz"
  "version" "1.0.3"

"merge-stream@^2.0.0":
  "integrity" "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
  "resolved" "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge2@^1.3.0", "merge2@^1.4.1":
  "integrity" "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="
  "resolved" "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"methods@~1.1.2":
  "integrity" "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w=="
  "resolved" "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^2.1.5":
  "version" "2.3.11"
  dependencies:
    "arr-diff" "^2.0.0"
    "array-unique" "^0.2.1"
    "braces" "^1.8.2"
    "expand-brackets" "^0.1.4"
    "extglob" "^0.3.1"
    "filename-regex" "^2.0.0"
    "is-extglob" "^1.0.0"
    "is-glob" "^2.0.1"
    "kind-of" "^3.0.2"
    "normalize-path" "^2.0.1"
    "object.omit" "^2.0.0"
    "parse-glob" "^3.0.4"
    "regex-cache" "^0.4.2"

"micromatch@^3.1.10":
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"micromatch@^4.0.2", "micromatch@^4.0.8":
  "integrity" "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "braces" "^3.0.3"
    "picomatch" "^2.3.1"

"mime-db@>= 1.43.0 < 2", "mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.12", "mime-types@^2.1.27", "mime-types@^2.1.31", "mime-types@~2.1.17", "mime-types@~2.1.19", "mime-types@~2.1.24", "mime-types@~2.1.34":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime@^1.4.1":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mime@^2.5.2":
  "integrity" "sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz"
  "version" "2.6.0"

"mime@~2.5.2":
  "integrity" "sha512-tqkh47FzKeCPD2PUiPB6pkbMzsCasjxAfC62/Wap5qrUWcb+sFasXUC5I3gYM5iBM8v/Qpn4UK0x+j0iHyFPDg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-2.5.2.tgz"
  "version" "2.5.2"

"mime@1.6.0":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mimic-fn@^2.1.0":
  "integrity" "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="
  "resolved" "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"mini-css-extract-plugin@2.6.1":
  "integrity" "sha512-wd+SD57/K6DiV7jIR34P+s3uckTRuQvx0tKPcvjFlrEylk6P4mQ2KSWk1hblj1Kxaqok7LogKOieygXqBczNlg=="
  "resolved" "https://registry.npmjs.org/mini-css-extract-plugin/-/mini-css-extract-plugin-2.6.1.tgz"
  "version" "2.6.1"
  dependencies:
    "schema-utils" "^4.0.0"

"minimalistic-assert@^1.0.0":
  "integrity" "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A=="
  "resolved" "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.2", "minimatch@^3.0.4", "minimatch@~3.0.4":
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimatch@^3.1.1":
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimatch@^5.0.1", "minimatch@5.1.0":
  "version" "5.1.0"
  dependencies:
    "brace-expansion" "^2.0.1"

"minimatch@^9.0.4":
  "version" "9.0.5"
  dependencies:
    "brace-expansion" "^2.0.1"

"minimatch@9.0.1":
  "version" "9.0.1"
  dependencies:
    "brace-expansion" "^2.0.1"

"minimist@^1.1.0", "minimist@^1.2.0", "minimist@^1.2.5", "minimist@^1.2.6":
  "version" "1.2.8"

"minipass-collect@^1.0.2":
  "integrity" "sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA=="
  "resolved" "https://registry.npmjs.org/minipass-collect/-/minipass-collect-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "minipass" "^3.0.0"

"minipass-fetch@^2.0.3":
  "integrity" "sha512-LT49Zi2/WMROHYoqGgdlQIZh8mLPZmOrN2NdJjMXxYe4nkN6FUyuPuOAOedNJDrx0IRGg9+4guZewtp8hE6TxA=="
  "resolved" "https://registry.npmjs.org/minipass-fetch/-/minipass-fetch-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "minipass" "^3.1.6"
    "minipass-sized" "^1.0.3"
    "minizlib" "^2.1.2"
  optionalDependencies:
    "encoding" "^0.1.13"

"minipass-flush@^1.0.5":
  "integrity" "sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw=="
  "resolved" "https://registry.npmjs.org/minipass-flush/-/minipass-flush-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "minipass" "^3.0.0"

"minipass-json-stream@^1.0.1":
  "integrity" "sha512-myxeeTm57lYs8pH2nxPzmEEg8DGIgW+9mv6D4JZD2pa81I/OBjeU7PtICXV6c9eRGTA5JMDsuIPUZRCyBMYNhg=="
  "resolved" "https://registry.npmjs.org/minipass-json-stream/-/minipass-json-stream-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "jsonparse" "^1.3.1"
    "minipass" "^3.0.0"

"minipass-pipeline@^1.2.4":
  "integrity" "sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A=="
  "resolved" "https://registry.npmjs.org/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "minipass" "^3.0.0"

"minipass-sized@^1.0.3":
  "integrity" "sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g=="
  "resolved" "https://registry.npmjs.org/minipass-sized/-/minipass-sized-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "minipass" "^3.0.0"

"minipass@^3.0.0", "minipass@^3.1.1", "minipass@^3.1.6":
  "integrity" "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw=="
  "resolved" "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz"
  "version" "3.3.6"
  dependencies:
    "yallist" "^4.0.0"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0":
  "integrity" "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw=="
  "resolved" "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  "version" "7.1.2"

"minipass@^5.0.0":
  "integrity" "sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ=="
  "resolved" "https://registry.npmjs.org/minipass/-/minipass-5.0.0.tgz"
  "version" "5.0.0"

"minipass@^7.1.2":
  "integrity" "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw=="
  "resolved" "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  "version" "7.1.2"

"minizlib@^2.1.1", "minizlib@^2.1.2":
  "integrity" "sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg=="
  "resolved" "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "minipass" "^3.0.0"
    "yallist" "^4.0.0"

"mixin-deep@^1.2.0":
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@^0.5.1":
  "version" "0.5.5"
  dependencies:
    "minimist" "^1.2.5"

"mkdirp@^0.5.3":
  "version" "0.5.5"
  dependencies:
    "minimist" "^1.2.5"

"mkdirp@^0.5.5":
  "integrity" "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw=="
  "resolved" "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "minimist" "^1.2.6"

"mkdirp@^1.0.3", "mkdirp@^1.0.4":
  "integrity" "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw=="
  "resolved" "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz"
  "version" "1.0.4"

"moment-timezone@^0.5.34":
  "version" "0.5.48"
  dependencies:
    "moment" "^2.29.4"

"moment@^2.29.1", "moment@^2.29.4":
  "integrity" "sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how=="
  "resolved" "https://registry.npmjs.org/moment/-/moment-2.30.1.tgz"
  "version" "2.30.1"

"ms@^2.0.0", "ms@2.0.0":
  "integrity" "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@^2.1.1":
  "version" "2.1.2"

"ms@^2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"ms@2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"multicast-dns@^7.2.5":
  "integrity" "sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg=="
  "resolved" "https://registry.npmjs.org/multicast-dns/-/multicast-dns-7.2.5.tgz"
  "version" "7.2.5"
  dependencies:
    "dns-packet" "^5.2.2"
    "thunky" "^1.0.2"

"mute-stream@0.0.8":
  "integrity" "sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA=="
  "resolved" "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz"
  "version" "0.0.8"

"nanoid@^3.3.6":
  "version" "3.3.11"

"nanomatch@^1.2.9":
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"needle@^3.1.0":
  "integrity" "sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q=="
  "resolved" "https://registry.npmjs.org/needle/-/needle-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "iconv-lite" "^0.6.3"
    "sax" "^1.2.4"

"negotiator@^0.6.3", "negotiator@~0.6.4":
  "integrity" "sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w=="
  "resolved" "https://registry.npmjs.org/negotiator/-/negotiator-0.6.4.tgz"
  "version" "0.6.4"

"negotiator@0.6.3":
  "integrity" "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg=="
  "resolved" "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
  "version" "0.6.3"

"neo-async@^2.6.2":
  "integrity" "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw=="
  "resolved" "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"ng-packagr@^14.2.2":
  "version" "14.2.2"
  dependencies:
    "@rollup/plugin-json" "^4.1.0"
    "@rollup/plugin-node-resolve" "^13.1.3"
    "ajv" "^8.10.0"
    "ansi-colors" "^4.1.1"
    "browserslist" "^4.20.0"
    "cacache" "^16.0.0"
    "chokidar" "^3.5.3"
    "commander" "^9.0.0"
    "dependency-graph" "^0.11.0"
    "esbuild-wasm" "^0.15.0"
    "find-cache-dir" "^3.3.2"
    "glob" "^8.0.0"
    "injection-js" "^2.4.0"
    "jsonc-parser" "^3.0.0"
    "less" "^4.1.2"
    "ora" "^5.1.0"
    "postcss" "^8.4.8"
    "postcss-preset-env" "^7.4.2"
    "postcss-url" "^10.1.3"
    "rollup" "^2.70.0"
    "rollup-plugin-sourcemaps" "^0.6.3"
    "rxjs" "^7.5.5"
    "sass" "^1.49.9"
    "stylus" "^0.59.0"
  optionalDependencies:
    "esbuild" "^0.15.0"

"ngx-color@7.2.0":
  "integrity" "sha512-bX7xelfMDTMtr8oL5eUrz+j70CwPhzoqthIg2aHYwaAHBUZk+T/xGB1RXlRfUSV3oZua8OZ7qUtQ1Wo2Zc+suQ=="
  "resolved" "https://registry.npmjs.org/ngx-color/-/ngx-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "@ctrl/tinycolor" "^3.4.0"
    "material-colors" "^1.2.6"
    "tslib" "^2.1.0"

"ngx-quill@14.2.0":
  "integrity" "sha512-UMglVpbagvO+Loy6AK852Vpzmg7I2211MsP23I0IRvbXO9alsSanc2oIzhD1K48po8SLEruRgKqAkdBubk8pvw=="
  "resolved" "https://registry.npmjs.org/ngx-quill/-/ngx-quill-14.2.0.tgz"
  "version" "14.2.0"
  dependencies:
    "tslib" "^2.2.0"

"ngx-skeleton-loader@4.0.0":
  "integrity" "sha512-fbCWZrBVJF1jVVK056z3zPcAk0EXUtImdRkrjbc4xiWNkmhGskGfNh6UxKrkheMS9iOJoaLLo8KscLeUa9yggQ=="
  "resolved" "https://registry.npmjs.org/ngx-skeleton-loader/-/ngx-skeleton-loader-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "perf-marks" "^1.13.4"
    "tslib" "^1.10.0"

"ngx-tribute@^1.5.0":
  "integrity" "sha512-UyBwFfg4x8OyxjCb+F8xmlTEMU787xeXiHV3Q9/pWs+LcOetX6XXDi1dc1SWoLuT3+sgM+TM6p8SvfWyaGc4/Q=="
  "resolved" "https://registry.npmjs.org/ngx-tribute/-/ngx-tribute-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "tslib" "^1.9.0"

"node-fetch@^2.7.0":
  "version" "2.7.0"
  dependencies:
    "whatwg-url" "^5.0.0"

"node-forge@^1":
  "version" "1.3.1"

"node-gyp@^9.0.0":
  "integrity" "sha512-OQkWKbjQKbGkMf/xqI1jjy3oCTgMKJac58G2+bjZb3fza6gW2YrCSdMQYaoTb70crvE//Gngr4f0AgVHmqHvBQ=="
  "resolved" "https://registry.npmjs.org/node-gyp/-/node-gyp-9.4.1.tgz"
  "version" "9.4.1"
  dependencies:
    "env-paths" "^2.2.0"
    "exponential-backoff" "^3.1.1"
    "glob" "^7.1.4"
    "graceful-fs" "^4.2.6"
    "make-fetch-happen" "^10.0.3"
    "nopt" "^6.0.0"
    "npmlog" "^6.0.0"
    "rimraf" "^3.0.2"
    "semver" "^7.3.5"
    "tar" "^6.1.2"
    "which" "^2.0.2"

"node-releases@^2.0.19":
  "version" "2.0.19"

"nopt@^6.0.0":
  "integrity" "sha512-ZwLpbTgdhuZUnZzjd7nb1ZV+4DoiC6/sfiVKok72ym/4Tlf+DFdlHYmT2JPmcNNWV6Pi3SDf1kT+A4r9RTuT9g=="
  "resolved" "https://registry.npmjs.org/nopt/-/nopt-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "abbrev" "^1.0.0"

"nopt@^7.2.1":
  "version" "7.2.1"
  dependencies:
    "abbrev" "^2.0.0"

"normalize-package-data@^4.0.0":
  "integrity" "sha512-EBk5QKKuocMJhB3BILuKhmaPjI8vNRSpIfO9woLC6NyHVkKKdVEdAO1mrT0ZfxNR1lKwCcTkuZfmGIFdizZ8Pg=="
  "resolved" "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "hosted-git-info" "^5.0.0"
    "is-core-module" "^2.8.1"
    "semver" "^7.3.5"
    "validate-npm-package-license" "^3.0.4"

"normalize-path@^2.0.0", "normalize-path@^2.0.1", "normalize-path@^2.1.1":
  "integrity" "sha512-3pKJwH184Xo/lnH6oyP1q2pMd7HcypqqmRs91/6/i2CGtWwIKGCkOOMTm/zXbgTEWHw1uNpNi/igc3ePOYHb6w=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA=="
  "resolved" "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"now-and-later@^2.0.0":
  "integrity" "sha512-KGvQ0cB70AQfg107Xvs/Fbu+dGmZoTRJp2TaPwcwQm3/7PteUyN2BCgk8KBMPGBUXZdVwyWS8fDCGFygBm19UQ=="
  "resolved" "https://registry.npmjs.org/now-and-later/-/now-and-later-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "once" "^1.3.2"

"npm-bundled@^1.1.1":
  "integrity" "sha512-x5DHup0SuyQcmL3s7Rx/YQ8sbw/Hzg0rj48eN0dV7hf5cmQq5PXIeioroH3raV1QC1yh3uTYuMThvEQF3iKgGQ=="
  "resolved" "https://registry.npmjs.org/npm-bundled/-/npm-bundled-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "npm-normalize-package-bin" "^1.0.1"

"npm-bundled@^2.0.0":
  "integrity" "sha512-gZLxXdjEzE/+mOstGDqR6b0EkhJ+kM6fxM6vUuckuctuVPh80Q6pw/rSZj9s4Gex9GxWtIicO1pc8DB9KZWudw=="
  "resolved" "https://registry.npmjs.org/npm-bundled/-/npm-bundled-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "npm-normalize-package-bin" "^2.0.0"

"npm-install-checks@^5.0.0":
  "integrity" "sha512-65lUsMI8ztHCxFz5ckCEC44DRvEGdZX5usQFriauxHEwt7upv1FKaQEmAtU0YnOAdwuNWCmk64xYiQABNrEyLA=="
  "resolved" "https://registry.npmjs.org/npm-install-checks/-/npm-install-checks-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "semver" "^7.1.1"

"npm-normalize-package-bin@^1.0.1":
  "integrity" "sha512-EPfafl6JL5/rU+ot6P3gRSCpPDW5VmIzX959Ob1+ySFUuuYHWHekXpwdUZcKP5C+DS4GEtdJluwBjnsNDl+fSA=="
  "resolved" "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-1.0.1.tgz"
  "version" "1.0.1"

"npm-normalize-package-bin@^2.0.0":
  "integrity" "sha512-awzfKUO7v0FscrSpRoogyNm0sajikhBWpU0QMrW09AMi9n1PoKU6WaIqUzuJSQnpciZZmJ/jMZ2Egfmb/9LiWQ=="
  "resolved" "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-2.0.0.tgz"
  "version" "2.0.0"

"npm-package-arg@^9.0.0", "npm-package-arg@^9.0.1", "npm-package-arg@9.1.0":
  "integrity" "sha512-4J0GL+u2Nh6OnhvUKXRr2ZMG4lR8qtLp+kv7UiV00Y+nGiSxtttCyIRHCt5L5BNkXQld/RceYItau3MDOoGiBw=="
  "resolved" "https://registry.npmjs.org/npm-package-arg/-/npm-package-arg-9.1.0.tgz"
  "version" "9.1.0"
  dependencies:
    "hosted-git-info" "^5.0.0"
    "proc-log" "^2.0.1"
    "semver" "^7.3.5"
    "validate-npm-package-name" "^4.0.0"

"npm-packlist@^5.1.0":
  "integrity" "sha512-263/0NGrn32YFYi4J533qzrQ/krmmrWwhKkzwTuM4f/07ug51odoaNjUexxO4vxlzURHcmYMH1QjvHjsNDKLVg=="
  "resolved" "https://registry.npmjs.org/npm-packlist/-/npm-packlist-5.1.3.tgz"
  "version" "5.1.3"
  dependencies:
    "glob" "^8.0.1"
    "ignore-walk" "^5.0.1"
    "npm-bundled" "^2.0.0"
    "npm-normalize-package-bin" "^2.0.0"

"npm-pick-manifest@^7.0.0", "npm-pick-manifest@7.0.1":
  "integrity" "sha512-IA8+tuv8KujbsbLQvselW2XQgmXWS47t3CB0ZrzsRZ82DbDfkcFunOaPm4X7qNuhMfq+FmV7hQT4iFVpHqV7mg=="
  "resolved" "https://registry.npmjs.org/npm-pick-manifest/-/npm-pick-manifest-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "npm-install-checks" "^5.0.0"
    "npm-normalize-package-bin" "^1.0.1"
    "npm-package-arg" "^9.0.0"
    "semver" "^7.3.5"

"npm-registry-fetch@^13.0.1":
  "integrity" "sha512-eukJPi++DKRTjSBRcDZSDDsGqRK3ehbxfFUcgaRd0Yp6kRwOwh2WVn0r+8rMB4nnuzvAk6rQVzl6K5CkYOmnvw=="
  "resolved" "https://registry.npmjs.org/npm-registry-fetch/-/npm-registry-fetch-13.3.1.tgz"
  "version" "13.3.1"
  dependencies:
    "make-fetch-happen" "^10.0.6"
    "minipass" "^3.1.6"
    "minipass-fetch" "^2.0.3"
    "minipass-json-stream" "^1.0.1"
    "minizlib" "^2.1.2"
    "npm-package-arg" "^9.0.1"
    "proc-log" "^2.0.0"

"npm-run-path@^4.0.1":
  "integrity" "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw=="
  "resolved" "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"npmlog@^6.0.0":
  "integrity" "sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg=="
  "resolved" "https://registry.npmjs.org/npmlog/-/npmlog-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "are-we-there-yet" "^3.0.0"
    "console-control-strings" "^1.1.0"
    "gauge" "^4.0.3"
    "set-blocking" "^2.0.0"

"nth-check@^2.0.1":
  "version" "2.1.1"
  dependencies:
    "boolbase" "^1.0.0"

"oauth-sign@~0.9.0":
  "integrity" "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ=="
  "resolved" "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz"
  "version" "0.9.0"

"object-assign@^4", "object-assign@^4.0.1", "object-assign@^4.1.1":
  "integrity" "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-inspect@^1.13.3":
  "version" "1.13.4"

"object-is@^1.1.5":
  "integrity" "sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q=="
  "resolved" "https://registry.npmjs.org/object-is/-/object-is-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"

"object-keys@^1.1.1":
  "integrity" "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="
  "resolved" "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@^1.0.0":
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.assign@^4.0.4":
  "version" "4.1.2"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "has-symbols" "^1.0.1"
    "object-keys" "^1.1.1"

"object.omit@^2.0.0":
  "version" "2.0.1"
  dependencies:
    "for-own" "^0.1.4"
    "is-extendable" "^0.1.1"

"object.pick@^1.3.0":
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"obuf@^1.0.0", "obuf@^1.1.2":
  "integrity" "sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg=="
  "resolved" "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz"
  "version" "1.1.2"

"on-finished@~2.3.0":
  "integrity" "sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww=="
  "resolved" "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"on-finished@2.4.1":
  "integrity" "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg=="
  "resolved" "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA=="
  "resolved" "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.3.2", "once@^1.4.0":
  "integrity" "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="
  "resolved" "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^5.1.0", "onetime@^5.1.2":
  "integrity" "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg=="
  "resolved" "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"open@^8.0.9", "open@8.4.0":
  "integrity" "sha512-XgFPPM+B28FtCCgSb9I+s9szOC1vZRSwgWsRUA5ylIxRTgKozqjOCrVOqGsYABPYK5qnfqClxZTFBa8PKt2v6Q=="
  "resolved" "https://registry.npmjs.org/open/-/open-8.4.0.tgz"
  "version" "8.4.0"
  dependencies:
    "define-lazy-prop" "^2.0.0"
    "is-docker" "^2.1.1"
    "is-wsl" "^2.2.0"

"ora@^5.1.0", "ora@^5.4.1", "ora@5.4.1":
  "integrity" "sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ=="
  "resolved" "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bl" "^4.1.0"
    "chalk" "^4.1.0"
    "cli-cursor" "^3.1.0"
    "cli-spinners" "^2.5.0"
    "is-interactive" "^1.0.0"
    "is-unicode-supported" "^0.1.0"
    "log-symbols" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "wcwidth" "^1.0.1"

"ordered-read-streams@^1.0.0":
  "integrity" "sha512-Z87aSjx3r5c0ZB7bcJqIgIRX5bxR7A4aSzvIbaxd0oTkWBCOoKfuGHiKj60CHVUgg1Phm5yMZzBdt8XqRs73Mw=="
  "resolved" "https://registry.npmjs.org/ordered-read-streams/-/ordered-read-streams-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "readable-stream" "^2.0.1"

"os-tmpdir@~1.0.1", "os-tmpdir@~1.0.2":
  "integrity" "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g=="
  "resolved" "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"p-limit@^2.2.0":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-map@^4.0.0":
  "integrity" "sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ=="
  "resolved" "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-retry@^4.5.0":
  "integrity" "sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ=="
  "resolved" "https://registry.npmjs.org/p-retry/-/p-retry-4.6.2.tgz"
  "version" "4.6.2"
  dependencies:
    "@types/retry" "0.12.0"
    "retry" "^0.13.1"

"p-try@^2.0.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"package-json-from-dist@^1.0.0":
  "integrity" "sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw=="
  "resolved" "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz"
  "version" "1.0.1"

"package-json@^10.0.1":
  "integrity" "sha512-ua1L4OgXSBdsu1FPb7F3tYH0F48a6kxvod4pLUlGY9COeJAJQNX/sNH2IiEmsxw7lqYiAwrdHMjz1FctOsyDQg=="
  "resolved" "https://registry.npmjs.org/package-json/-/package-json-10.0.1.tgz"
  "version" "10.0.1"
  dependencies:
    "ky" "^1.2.0"
    "registry-auth-token" "^5.0.2"
    "registry-url" "^6.0.1"
    "semver" "^7.6.0"

"pacote@13.6.2":
  "integrity" "sha512-Gu8fU3GsvOPkak2CkbojR7vjs3k3P9cA6uazKTHdsdV0gpCEQq2opelnEv30KRQWgVzP5Vd/5umjcedma3MKtg=="
  "resolved" "https://registry.npmjs.org/pacote/-/pacote-13.6.2.tgz"
  "version" "13.6.2"
  dependencies:
    "@npmcli/git" "^3.0.0"
    "@npmcli/installed-package-contents" "^1.0.7"
    "@npmcli/promise-spawn" "^3.0.0"
    "@npmcli/run-script" "^4.1.0"
    "cacache" "^16.0.0"
    "chownr" "^2.0.0"
    "fs-minipass" "^2.1.0"
    "infer-owner" "^1.0.4"
    "minipass" "^3.1.6"
    "mkdirp" "^1.0.4"
    "npm-package-arg" "^9.0.0"
    "npm-packlist" "^5.1.0"
    "npm-pick-manifest" "^7.0.0"
    "npm-registry-fetch" "^13.0.1"
    "proc-log" "^2.0.0"
    "promise-retry" "^2.0.1"
    "read-package-json" "^5.0.0"
    "read-package-json-fast" "^2.0.3"
    "rimraf" "^3.0.2"
    "ssri" "^9.0.0"
    "tar" "^6.1.11"

"pako@^1.0.3", "pako@~1.0.2":
  "integrity" "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="
  "resolved" "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  "version" "1.0.11"

"parchment@^1.1.4":
  "integrity" "sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg=="
  "resolved" "https://registry.npmjs.org/parchment/-/parchment-1.1.4.tgz"
  "version" "1.1.4"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-glob@^3.0.4":
  "version" "3.0.4"
  dependencies:
    "glob-base" "^0.3.0"
    "is-dotfile" "^1.0.0"
    "is-extglob" "^1.0.0"
    "is-glob" "^2.0.0"

"parse-json@^5.0.0":
  "integrity" "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg=="
  "resolved" "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parse-node-version@^1.0.1":
  "integrity" "sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA=="
  "resolved" "https://registry.npmjs.org/parse-node-version/-/parse-node-version-1.0.1.tgz"
  "version" "1.0.1"

"parse5-html-rewriting-stream@6.0.1":
  "integrity" "sha512-vwLQzynJVEfUlURxgnf51yAJDQTtVpNyGD8tKi2Za7m+akukNHxCcUQMAa/mUGLhCeicFdpy7Tlvj8ZNKadprg=="
  "resolved" "https://registry.npmjs.org/parse5-html-rewriting-stream/-/parse5-html-rewriting-stream-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "parse5" "^6.0.1"
    "parse5-sax-parser" "^6.0.1"

"parse5-htmlparser2-tree-adapter@^6.0.1":
  "integrity" "sha512-qPuWvbLgvDGilKc5BoicRovlT4MtYT6JfJyBOMDsKoiT+GiuP5qyrPCnR9HcPECIJJmZh5jRndyNThnhhb/vlA=="
  "resolved" "https://registry.npmjs.org/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "parse5" "^6.0.1"

"parse5-sax-parser@^6.0.1":
  "integrity" "sha512-kXX+5S81lgESA0LsDuGjAlBybImAChYRMT+/uKCEXFBFOeEhS52qUCydGhU3qLRD8D9DVjaUo821WK7DM4iCeg=="
  "resolved" "https://registry.npmjs.org/parse5-sax-parser/-/parse5-sax-parser-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "parse5" "^6.0.1"

"parse5@^5.0.0":
  "integrity" "sha512-ugq4DFI0Ptb+WWjAdOK16+u/nHfiIrcE+sh8kZMaM0WllQKLI9rOUq6c2b7cwPkXdzfQESqvoqK6ug7U/Yyzug=="
  "resolved" "https://registry.npmjs.org/parse5/-/parse5-5.1.1.tgz"
  "version" "5.1.1"

"parse5@^6.0.1":
  "integrity" "sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw=="
  "resolved" "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz"
  "version" "6.0.1"

"parseurl@~1.3.2", "parseurl@~1.3.3":
  "integrity" "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="
  "resolved" "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascalcase@^0.1.1":
  "version" "0.1.1"

"path-dirname@^1.0.0":
  "version" "1.0.2"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg=="
  "resolved" "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-is-inside@^1.0.1":
  "integrity" "sha512-DUWJr3+ULp4zXmol/SZkFf3JGsS9/SIv+Y3Rt93/UjPpDpklB5f1er4O3POIbUuUJ3FXgqte2Q7SrU6zAqwk8w=="
  "resolved" "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz"
  "version" "1.0.2"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.7":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-scurry@^1.11.1":
  "integrity" "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA=="
  "resolved" "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "lru-cache" "^10.2.0"
    "minipass" "^5.0.0 || ^6.0.2 || ^7.0.0"

"path-to-regexp@0.1.12":
  "version" "0.1.12"

"path-type@^4.0.0":
  "integrity" "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw=="
  "resolved" "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  "version" "4.0.0"

"perf-marks@^1.13.4":
  "version" "1.14.1"
  dependencies:
    "tslib" "^2.1.0"

"performance-now@^2.1.0":
  "integrity" "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="
  "resolved" "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picocolors@^1.0.0", "picocolors@^1.1.1":
  "integrity" "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  "version" "1.1.1"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.2.2", "picomatch@^2.3.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pify@^2.0.0", "pify@^2.3.0":
  "integrity" "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog=="
  "resolved" "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^4.0.1":
  "integrity" "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="
  "resolved" "https://registry.npmjs.org/pify/-/pify-4.0.1.tgz"
  "version" "4.0.1"

"pinkie-promise@^2.0.0":
  "integrity" "sha512-0Gni6D4UcLTbv9c57DfxDGdr41XfgUjqWZu492f0cIGr16zDU06BWP/RAEvOuo7CQ0CNjHaLlM59YJJFm3NWlw=="
  "resolved" "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "^2.0.0"

"pinkie@^2.0.0":
  "integrity" "sha512-MnUuEycAemtSaeFSjXKW/aroV7akBbY+Sv+RkyqFjgAe73F+MR0TBWKBRDkmfWq/HiFmdavfZ1G7h4SPZXaCSg=="
  "resolved" "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"piscina@3.2.0":
  "integrity" "sha512-yn/jMdHRw+q2ZJhFhyqsmANcbF6V2QwmD84c6xRau+QpQOmtrBCoRGdvTfeuFDYXB5W2m6MfLkjkvQa9lUSmIA=="
  "resolved" "https://registry.npmjs.org/piscina/-/piscina-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "eventemitter-asyncresource" "^1.0.0"
    "hdr-histogram-js" "^2.0.1"
    "hdr-histogram-percentiles-obj" "^3.0.0"
  optionalDependencies:
    "nice-napi" "^1.0.2"

"pkg-dir@^4.1.0":
  "integrity" "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ=="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"plugin-error@^1.0.1":
  "integrity" "sha512-L1zP0dk7vGweZME2i+EeakvUNqSrdiI3F91TwEoYiGrAfUXmVv6fJIq4g82PAXxNsWOp0J7ZqQy/3Szz0ajTxA=="
  "resolved" "https://registry.npmjs.org/plugin-error/-/plugin-error-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "ansi-colors" "^1.0.1"
    "arr-diff" "^4.0.0"
    "arr-union" "^3.1.0"
    "extend-shallow" "^3.0.2"

"posix-character-classes@^0.1.0":
  "version" "0.1.1"

"postcss-attribute-case-insensitive@^5.0.2":
  "integrity" "sha512-XIidXV8fDr0kKt28vqki84fRK8VW8eTuIa4PChv2MqKuT6C9UjmSKzen6KaWhWEoYvwxFCa7n/tC1SZ3tyq4SQ=="
  "resolved" "https://registry.npmjs.org/postcss-attribute-case-insensitive/-/postcss-attribute-case-insensitive-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "postcss-selector-parser" "^6.0.10"

"postcss-clamp@^4.1.0":
  "integrity" "sha512-ry4b1Llo/9zz+PKC+030KUnPITTJAHeOwjfAyyB60eT0AorGLdzp52s31OsPRHRf8NchkgFoG2y6fCfn1IV1Ow=="
  "resolved" "https://registry.npmjs.org/postcss-clamp/-/postcss-clamp-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-color-functional-notation@^4.2.4":
  "integrity" "sha512-2yrTAUZUab9s6CpxkxC4rVgFEVaR6/2Pipvi6qcgvnYiVqZcbDHEoBDhrXzyb7Efh2CCfHQNtcqWcIruDTIUeg=="
  "resolved" "https://registry.npmjs.org/postcss-color-functional-notation/-/postcss-color-functional-notation-4.2.4.tgz"
  "version" "4.2.4"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-color-hex-alpha@^8.0.4":
  "integrity" "sha512-nLo2DCRC9eE4w2JmuKgVA3fGL3d01kGq752pVALF68qpGLmx2Qrk91QTKkdUqqp45T1K1XV8IhQpcu1hoAQflQ=="
  "resolved" "https://registry.npmjs.org/postcss-color-hex-alpha/-/postcss-color-hex-alpha-8.0.4.tgz"
  "version" "8.0.4"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-color-rebeccapurple@^7.1.1":
  "integrity" "sha512-pGxkuVEInwLHgkNxUc4sdg4g3py7zUeCQ9sMfwyHAT+Ezk8a4OaaVZ8lIY5+oNqA/BXXgLyXv0+5wHP68R79hg=="
  "resolved" "https://registry.npmjs.org/postcss-color-rebeccapurple/-/postcss-color-rebeccapurple-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-custom-media@^8.0.2":
  "integrity" "sha512-7yi25vDAoHAkbhAzX9dHx2yc6ntS4jQvejrNcC+csQJAXjj15e7VcWfMgLqBNAbOvqi5uIa9huOVwdHbf+sKqg=="
  "resolved" "https://registry.npmjs.org/postcss-custom-media/-/postcss-custom-media-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-custom-properties@^12.1.8":
  "integrity" "sha512-0IDJYhgU8xDv1KY6+VgUwuQkVtmYzRwu+dMjnmdMafXYv86SWqfxkc7qdDvWS38vsjaEtv8e0vGOUQrAiMBLpQ=="
  "resolved" "https://registry.npmjs.org/postcss-custom-properties/-/postcss-custom-properties-12.1.11.tgz"
  "version" "12.1.11"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-custom-selectors@^6.0.3":
  "integrity" "sha512-fgVkmyiWDwmD3JbpCmB45SvvlCD6z9CG6Ie6Iere22W5aHea6oWa7EM2bpnv2Fj3I94L3VbtvX9KqwSi5aFzSg=="
  "resolved" "https://registry.npmjs.org/postcss-custom-selectors/-/postcss-custom-selectors-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "postcss-selector-parser" "^6.0.4"

"postcss-dir-pseudo-class@^6.0.5":
  "integrity" "sha512-eqn4m70P031PF7ZQIvSgy9RSJ5uI2171O/OO/zcRNYpJbvaeKFUlar1aJ7rmgiQtbm0FSPsRewjpdS0Oew7MPA=="
  "resolved" "https://registry.npmjs.org/postcss-dir-pseudo-class/-/postcss-dir-pseudo-class-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "postcss-selector-parser" "^6.0.10"

"postcss-double-position-gradients@^3.1.2":
  "integrity" "sha512-GX+FuE/uBR6eskOK+4vkXgT6pDkexLokPaz/AbJna9s5Kzp/yl488pKPjhy0obB475ovfT1Wv8ho7U/cHNaRgQ=="
  "resolved" "https://registry.npmjs.org/postcss-double-position-gradients/-/postcss-double-position-gradients-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    "postcss-value-parser" "^4.2.0"

"postcss-env-function@^4.0.6":
  "integrity" "sha512-kpA6FsLra+NqcFnL81TnsU+Z7orGtDTxcOhl6pwXeEq1yFPpRMkCDpHhrz8CFQDr/Wfm0jLiNQ1OsGGPjlqPwA=="
  "resolved" "https://registry.npmjs.org/postcss-env-function/-/postcss-env-function-4.0.6.tgz"
  "version" "4.0.6"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-focus-visible@^6.0.4":
  "integrity" "sha512-QcKuUU/dgNsstIK6HELFRT5Y3lbrMLEOwG+A4s5cA+fx3A3y/JTq3X9LaOj3OC3ALH0XqyrgQIgey/MIZ8Wczw=="
  "resolved" "https://registry.npmjs.org/postcss-focus-visible/-/postcss-focus-visible-6.0.4.tgz"
  "version" "6.0.4"
  dependencies:
    "postcss-selector-parser" "^6.0.9"

"postcss-focus-within@^5.0.4":
  "integrity" "sha512-vvjDN++C0mu8jz4af5d52CB184ogg/sSxAFS+oUJQq2SuCe7T5U2iIsVJtsCp2d6R4j0jr5+q3rPkBVZkXD9fQ=="
  "resolved" "https://registry.npmjs.org/postcss-focus-within/-/postcss-focus-within-5.0.4.tgz"
  "version" "5.0.4"
  dependencies:
    "postcss-selector-parser" "^6.0.9"

"postcss-font-variant@^5.0.0":
  "integrity" "sha512-1fmkBaCALD72CK2a9i468mA/+tr9/1cBxRRMXOUaZqO43oWPR5imcyPjXwuv7PXbCid4ndlP5zWhidQVVa3hmA=="
  "resolved" "https://registry.npmjs.org/postcss-font-variant/-/postcss-font-variant-5.0.0.tgz"
  "version" "5.0.0"

"postcss-gap-properties@^3.0.5":
  "integrity" "sha512-IuE6gKSdoUNcvkGIqdtjtcMtZIFyXZhmFd5RUlg97iVEvp1BZKV5ngsAjCjrVy+14uhGBQl9tzmi1Qwq4kqVOg=="
  "resolved" "https://registry.npmjs.org/postcss-gap-properties/-/postcss-gap-properties-3.0.5.tgz"
  "version" "3.0.5"

"postcss-image-set-function@^4.0.7":
  "integrity" "sha512-9T2r9rsvYzm5ndsBE8WgtrMlIT7VbtTfE7b3BQnudUqnBcBo7L758oc+o+pdj/dUV0l5wjwSdjeOH2DZtfv8qw=="
  "resolved" "https://registry.npmjs.org/postcss-image-set-function/-/postcss-image-set-function-4.0.7.tgz"
  "version" "4.0.7"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-import@15.0.0":
  "integrity" "sha512-Y20shPQ07RitgBGv2zvkEAu9bqvrD77C9axhj/aA1BQj4czape2MdClCExvB27EwYEJdGgKZBpKanb0t1rK2Kg=="
  "resolved" "https://registry.npmjs.org/postcss-import/-/postcss-import-15.0.0.tgz"
  "version" "15.0.0"
  dependencies:
    "postcss-value-parser" "^4.0.0"
    "read-cache" "^1.0.0"
    "resolve" "^1.1.7"

"postcss-initial@^4.0.1":
  "integrity" "sha512-0ueD7rPqX8Pn1xJIjay0AZeIuDoF+V+VvMt/uOnn+4ezUKhZM/NokDeP6DwMNyIoYByuN/94IQnt5FEkaN59xQ=="
  "resolved" "https://registry.npmjs.org/postcss-initial/-/postcss-initial-4.0.1.tgz"
  "version" "4.0.1"

"postcss-lab-function@^4.2.1":
  "integrity" "sha512-xuXll4isR03CrQsmxyz92LJB2xX9n+pZJ5jE9JgcnmsCammLyKdlzrBin+25dy6wIjfhJpKBAN80gsTlCgRk2w=="
  "resolved" "https://registry.npmjs.org/postcss-lab-function/-/postcss-lab-function-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    "postcss-value-parser" "^4.2.0"

"postcss-loader@7.0.1":
  "integrity" "sha512-VRviFEyYlLjctSM93gAZtcJJ/iSkPZ79zWbN/1fSH+NisBByEiVLqpdVDrPLVSi8DX0oJo12kL/GppTBdKVXiQ=="
  "resolved" "https://registry.npmjs.org/postcss-loader/-/postcss-loader-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "cosmiconfig" "^7.0.0"
    "klona" "^2.0.5"
    "semver" "^7.3.7"

"postcss-logical@^5.0.4":
  "integrity" "sha512-RHXxplCeLh9VjinvMrZONq7im4wjWGlRJAqmAVLXyZaXwfDWP73/oq4NdIp+OZwhQUMj0zjqDfM5Fj7qby+B4g=="
  "resolved" "https://registry.npmjs.org/postcss-logical/-/postcss-logical-5.0.4.tgz"
  "version" "5.0.4"

"postcss-media-minmax@^5.0.0":
  "integrity" "sha512-yDUvFf9QdFZTuCUg0g0uNSHVlJ5X1lSzDZjPSFaiCWvjgsvu8vEVxtahPrLMinIDEEGnx6cBe6iqdx5YWz08wQ=="
  "resolved" "https://registry.npmjs.org/postcss-media-minmax/-/postcss-media-minmax-5.0.0.tgz"
  "version" "5.0.0"

"postcss-modules-extract-imports@^3.0.0":
  "integrity" "sha512-k3kNe0aNFQDAZGbin48pL2VNidTF0w4/eASDsxlyspobzU3wZQLOGj7L9gfRe0Jo9/4uud09DsjFNH7winGv8Q=="
  "resolved" "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.1.0.tgz"
  "version" "3.1.0"

"postcss-modules-local-by-default@^4.0.0":
  "version" "4.2.0"
  dependencies:
    "icss-utils" "^5.0.0"
    "postcss-selector-parser" "^7.0.0"
    "postcss-value-parser" "^4.1.0"

"postcss-modules-scope@^3.0.0":
  "integrity" "sha512-m9jZstCVaqGjTAuny8MdgE88scJnCiQSlSrOWcTQgM2t32UBe+MUmFSO5t7VMSfAf/FJKImAxBav8ooCHJXCJA=="
  "resolved" "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "postcss-selector-parser" "^7.0.0"

"postcss-modules-values@^4.0.0":
  "integrity" "sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ=="
  "resolved" "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "icss-utils" "^5.0.0"

"postcss-nesting@^10.1.10":
  "integrity" "sha512-EwMkYchxiDiKUhlJGzWsD9b2zvq/r2SSubcRrgP+jujMXFzqvANLt16lJANC+5uZ6hjI7lpRmI6O8JIl+8l1KA=="
  "resolved" "https://registry.npmjs.org/postcss-nesting/-/postcss-nesting-10.2.0.tgz"
  "version" "10.2.0"
  dependencies:
    "@csstools/selector-specificity" "^2.0.0"
    "postcss-selector-parser" "^6.0.10"

"postcss-opacity-percentage@^1.1.2":
  "integrity" "sha512-An6Ba4pHBiDtyVpSLymUUERMo2cU7s+Obz6BTrS+gxkbnSBNKSuD0AVUc+CpBMrpVPKKfoVz0WQCX+Tnst0i4A=="
  "resolved" "https://registry.npmjs.org/postcss-opacity-percentage/-/postcss-opacity-percentage-1.1.3.tgz"
  "version" "1.1.3"

"postcss-overflow-shorthand@^3.0.4":
  "integrity" "sha512-otYl/ylHK8Y9bcBnPLo3foYFLL6a6Ak+3EQBPOTR7luMYCOsiVTUk1iLvNf6tVPNGXcoL9Hoz37kpfriRIFb4A=="
  "resolved" "https://registry.npmjs.org/postcss-overflow-shorthand/-/postcss-overflow-shorthand-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-page-break@^3.0.4":
  "integrity" "sha512-1JGu8oCjVXLa9q9rFTo4MbeeA5FMe00/9C7lN4va606Rdb+HkxXtXsmEDrIraQ11fGz/WvKWa8gMuCKkrXpTsQ=="
  "resolved" "https://registry.npmjs.org/postcss-page-break/-/postcss-page-break-3.0.4.tgz"
  "version" "3.0.4"

"postcss-place@^7.0.5":
  "integrity" "sha512-wR8igaZROA6Z4pv0d+bvVrvGY4GVHihBCBQieXFY3kuSuMyOmEnnfFzHl/tQuqHZkfkIVBEbDvYcFfHmpSet9g=="
  "resolved" "https://registry.npmjs.org/postcss-place/-/postcss-place-7.0.5.tgz"
  "version" "7.0.5"
  dependencies:
    "postcss-value-parser" "^4.2.0"

"postcss-preset-env@^7.4.2", "postcss-preset-env@7.8.0":
  "integrity" "sha512-leqiqLOellpLKfbHkD06E04P6d9ZQ24mat6hu4NSqun7WG0UhspHR5Myiv/510qouCjoo4+YJtNOqg5xHaFnCA=="
  "resolved" "https://registry.npmjs.org/postcss-preset-env/-/postcss-preset-env-7.8.0.tgz"
  "version" "7.8.0"
  dependencies:
    "@csstools/postcss-cascade-layers" "^1.0.5"
    "@csstools/postcss-color-function" "^1.1.1"
    "@csstools/postcss-font-format-keywords" "^1.0.1"
    "@csstools/postcss-hwb-function" "^1.0.2"
    "@csstools/postcss-ic-unit" "^1.0.1"
    "@csstools/postcss-is-pseudo-class" "^2.0.7"
    "@csstools/postcss-nested-calc" "^1.0.0"
    "@csstools/postcss-normalize-display-values" "^1.0.1"
    "@csstools/postcss-oklab-function" "^1.1.1"
    "@csstools/postcss-progressive-custom-properties" "^1.3.0"
    "@csstools/postcss-stepped-value-functions" "^1.0.1"
    "@csstools/postcss-text-decoration-shorthand" "^1.0.0"
    "@csstools/postcss-trigonometric-functions" "^1.0.2"
    "@csstools/postcss-unset-value" "^1.0.2"
    "autoprefixer" "^10.4.8"
    "browserslist" "^4.21.3"
    "css-blank-pseudo" "^3.0.3"
    "css-has-pseudo" "^3.0.4"
    "css-prefers-color-scheme" "^6.0.3"
    "cssdb" "^7.0.0"
    "postcss-attribute-case-insensitive" "^5.0.2"
    "postcss-clamp" "^4.1.0"
    "postcss-color-functional-notation" "^4.2.4"
    "postcss-color-hex-alpha" "^8.0.4"
    "postcss-color-rebeccapurple" "^7.1.1"
    "postcss-custom-media" "^8.0.2"
    "postcss-custom-properties" "^12.1.8"
    "postcss-custom-selectors" "^6.0.3"
    "postcss-dir-pseudo-class" "^6.0.5"
    "postcss-double-position-gradients" "^3.1.2"
    "postcss-env-function" "^4.0.6"
    "postcss-focus-visible" "^6.0.4"
    "postcss-focus-within" "^5.0.4"
    "postcss-font-variant" "^5.0.0"
    "postcss-gap-properties" "^3.0.5"
    "postcss-image-set-function" "^4.0.7"
    "postcss-initial" "^4.0.1"
    "postcss-lab-function" "^4.2.1"
    "postcss-logical" "^5.0.4"
    "postcss-media-minmax" "^5.0.0"
    "postcss-nesting" "^10.1.10"
    "postcss-opacity-percentage" "^1.1.2"
    "postcss-overflow-shorthand" "^3.0.4"
    "postcss-page-break" "^3.0.4"
    "postcss-place" "^7.0.5"
    "postcss-pseudo-class-any-link" "^7.1.6"
    "postcss-replace-overflow-wrap" "^4.0.0"
    "postcss-selector-not" "^6.0.1"
    "postcss-value-parser" "^4.2.0"

"postcss-pseudo-class-any-link@^7.1.6":
  "integrity" "sha512-9sCtZkO6f/5ML9WcTLcIyV1yz9D1rf0tWc+ulKcvV30s0iZKS/ONyETvoWsr6vnrmW+X+KmuK3gV/w5EWnT37w=="
  "resolved" "https://registry.npmjs.org/postcss-pseudo-class-any-link/-/postcss-pseudo-class-any-link-7.1.6.tgz"
  "version" "7.1.6"
  dependencies:
    "postcss-selector-parser" "^6.0.10"

"postcss-replace-overflow-wrap@^4.0.0":
  "integrity" "sha512-KmF7SBPphT4gPPcKZc7aDkweHiKEEO8cla/GjcBK+ckKxiZslIu3C4GCRW3DNfL0o7yW7kMQu9xlZ1kXRXLXtw=="
  "resolved" "https://registry.npmjs.org/postcss-replace-overflow-wrap/-/postcss-replace-overflow-wrap-4.0.0.tgz"
  "version" "4.0.0"

"postcss-selector-not@^6.0.1":
  "integrity" "sha512-1i9affjAe9xu/y9uqWH+tD4r6/hDaXJruk8xn2x1vzxC2U3J3LKO3zJW4CyxlNhA56pADJ/djpEwpH1RClI2rQ=="
  "resolved" "https://registry.npmjs.org/postcss-selector-not/-/postcss-selector-not-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "postcss-selector-parser" "^6.0.10"

"postcss-selector-parser@^6.0.10":
  "integrity" "sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
  "version" "6.1.2"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-selector-parser@^6.0.4":
  "integrity" "sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
  "version" "6.1.2"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-selector-parser@^6.0.9":
  "integrity" "sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
  "version" "6.1.2"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-selector-parser@^7.0.0":
  "version" "7.1.0"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-url@^10.1.3":
  "integrity" "sha512-FUzyxfI5l2tKmXdYc6VTu3TWZsInayEKPbiyW+P6vmmIrrb4I6CGX0BFoewgYHLK+oIL5FECEK02REYRpBvUCw=="
  "resolved" "https://registry.npmjs.org/postcss-url/-/postcss-url-10.1.3.tgz"
  "version" "10.1.3"
  dependencies:
    "make-dir" "~3.1.0"
    "mime" "~2.5.2"
    "minimatch" "~3.0.4"
    "xxhashjs" "~0.2.2"

"postcss-value-parser@^4.0.0", "postcss-value-parser@^4.1.0", "postcss-value-parser@^4.2.0":
  "integrity" "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="
  "resolved" "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss@^8.2.14", "postcss@^8.3.7", "postcss@^8.4.7", "postcss@^8.4.8", "postcss@8.4.31":
  "integrity" "sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-8.4.31.tgz"
  "version" "8.4.31"
  dependencies:
    "nanoid" "^3.3.6"
    "picocolors" "^1.0.0"
    "source-map-js" "^1.0.2"

"pretty-bytes@^5.3.0":
  "integrity" "sha512-FFw039TmrBqFK8ma/7OL3sDz/VytdtJr044/QUJtH0wK9lb9jLq9tJyIxUwtQJHwar2BqtiA4iCWSwo9JLkzFg=="
  "resolved" "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-5.6.0.tgz"
  "version" "5.6.0"

"proc-log@^2.0.0", "proc-log@^2.0.1":
  "integrity" "sha512-Kcmo2FhfDTXdcbfDH76N7uBYHINxc/8GW7UAVuVP9I+Va3uHSerrnKV6dLooga/gh7GlgzuCCr/eoldnL1muGw=="
  "resolved" "https://registry.npmjs.org/proc-log/-/proc-log-2.0.1.tgz"
  "version" "2.0.1"

"process-nextick-args@^2.0.0", "process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"promise-inflight@^1.0.1":
  "integrity" "sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g=="
  "resolved" "https://registry.npmjs.org/promise-inflight/-/promise-inflight-1.0.1.tgz"
  "version" "1.0.1"

"promise-retry@^2.0.1":
  "integrity" "sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g=="
  "resolved" "https://registry.npmjs.org/promise-retry/-/promise-retry-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "err-code" "^2.0.2"
    "retry" "^0.12.0"

"prop-types@^15.8.1":
  "integrity" "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg=="
  "resolved" "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  "version" "15.8.1"
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"
    "react-is" "^16.13.1"

"proto-list@~1.2.1":
  "integrity" "sha512-vtK/94akxsTMhe0/cbfpR+syPuszcuwhqVjJq26CuNDgFGj682oRBXOP5MJpv2r7JtE8MsiepGIqvvOTBwn2vA=="
  "resolved" "https://registry.npmjs.org/proto-list/-/proto-list-1.2.4.tgz"
  "version" "1.2.4"

"protractor@~7.0.0":
  "integrity" "sha512-UqkFjivi4GcvUQYzqGYNe0mLzfn5jiLmO8w9nMhQoJRLhy2grJonpga2IWhI6yJO30LibWXJJtA4MOIZD2GgZw=="
  "resolved" "https://registry.npmjs.org/protractor/-/protractor-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "@types/q" "^0.0.32"
    "@types/selenium-webdriver" "^3.0.0"
    "blocking-proxy" "^1.0.0"
    "browserstack" "^1.5.1"
    "chalk" "^1.1.3"
    "glob" "^7.0.3"
    "jasmine" "2.8.0"
    "jasminewd2" "^2.1.0"
    "q" "1.4.1"
    "saucelabs" "^1.5.0"
    "selenium-webdriver" "3.6.0"
    "source-map-support" "~0.4.0"
    "webdriver-js-extender" "2.1.0"
    "webdriver-manager" "^12.1.7"
    "yargs" "^15.3.1"

"proxy-addr@~2.0.7":
  "integrity" "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg=="
  "resolved" "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "forwarded" "0.2.0"
    "ipaddr.js" "1.9.1"

"prr@~1.0.1":
  "integrity" "sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw=="
  "resolved" "https://registry.npmjs.org/prr/-/prr-1.0.1.tgz"
  "version" "1.0.1"

"psl@^1.1.28":
  "version" "1.8.0"

"pump@^2.0.0":
  "integrity" "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA=="
  "resolved" "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pumpify@^1.3.5":
  "integrity" "sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ=="
  "resolved" "https://registry.npmjs.org/pumpify/-/pumpify-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "duplexify" "^3.6.0"
    "inherits" "^2.0.3"
    "pump" "^2.0.0"

"punycode@^2.1.0", "punycode@^2.1.1":
  "version" "2.1.1"

"q@^1.4.1", "q@1.4.1":
  "integrity" "sha512-/CdEdaw49VZVmyIDGUQKDDT53c7qBkO6g5CefWz91Ae+l4+cRtcDYwMTXh6me4O8TMldeGHG3N2Bl84V78Ywbg=="
  "resolved" "https://registry.npmjs.org/q/-/q-1.4.1.tgz"
  "version" "1.4.1"

"qjobs@^1.2.0":
  "integrity" "sha512-8YOJEHtxpySA3fFDyCRxA+UUV+fA+rTWnuWvylOK/NCjhY+b4ocCtmu8TtsWb+mYeU+GCHf/S66KZF/AsteKHg=="
  "resolved" "https://registry.npmjs.org/qjobs/-/qjobs-1.2.0.tgz"
  "version" "1.2.0"

"qs@~6.5.2":
  "version" "6.5.2"

"qs@6.13.0":
  "integrity" "sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg=="
  "resolved" "https://registry.npmjs.org/qs/-/qs-6.13.0.tgz"
  "version" "6.13.0"
  dependencies:
    "side-channel" "^1.0.6"

"queue-microtask@^1.2.2":
  "integrity" "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="
  "resolved" "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"quill-delta@^3.6.2":
  "integrity" "sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg=="
  "resolved" "https://registry.npmjs.org/quill-delta/-/quill-delta-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "deep-equal" "^1.0.1"
    "extend" "^3.0.2"
    "fast-diff" "1.1.2"

"quill@1.3.7":
  "integrity" "sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g=="
  "resolved" "https://registry.npmjs.org/quill/-/quill-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "clone" "^2.1.1"
    "deep-equal" "^1.0.1"
    "eventemitter3" "^2.0.3"
    "extend" "^3.0.2"
    "parchment" "^1.1.4"
    "quill-delta" "^3.6.2"

"raf@^3.4.1":
  "integrity" "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA=="
  "resolved" "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "performance-now" "^2.1.0"

"randombytes@^2.1.0":
  "integrity" "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ=="
  "resolved" "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"range-parser@^1.2.1", "range-parser@~1.2.1":
  "integrity" "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="
  "resolved" "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.5.2":
  "integrity" "sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA=="
  "resolved" "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz"
  "version" "2.5.2"
  dependencies:
    "bytes" "3.1.2"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"rc@1.2.8":
  "integrity" "sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw=="
  "resolved" "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "deep-extend" "^0.6.0"
    "ini" "~1.3.0"
    "minimist" "^1.2.0"
    "strip-json-comments" "~2.0.1"

"react-is@^16.13.1":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"read-cache@^1.0.0":
  "integrity" "sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA=="
  "resolved" "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "pify" "^2.3.0"

"read-package-json-fast@^2.0.3":
  "integrity" "sha512-W/BKtbL+dUjTuRL2vziuYhp76s5HZ9qQhd/dKfWIZveD0O40453QNyZhC0e63lqZrAQ4jiOapVoeJ7JrszenQQ=="
  "resolved" "https://registry.npmjs.org/read-package-json-fast/-/read-package-json-fast-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "json-parse-even-better-errors" "^2.3.0"
    "npm-normalize-package-bin" "^1.0.1"

"read-package-json@^5.0.0":
  "integrity" "sha512-BSzugrt4kQ/Z0krro8zhTwV1Kd79ue25IhNN/VtHFy1mG/6Tluyi+msc0UpwaoQzxSHa28mntAjIZY6kEgfR9Q=="
  "resolved" "https://registry.npmjs.org/read-package-json/-/read-package-json-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "glob" "^8.0.1"
    "json-parse-even-better-errors" "^2.3.1"
    "normalize-package-data" "^4.0.0"
    "npm-normalize-package-bin" "^2.0.0"

"readable-stream@^2.0.0":
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.1":
  "integrity" "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.2":
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.5":
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.1.5":
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.3.3", "readable-stream@~2.3.6":
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.3.5":
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.3.6":
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6", "readable-stream@^3.4.0", "readable-stream@^3.6.0", "readable-stream@2 || 3":
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdirp@^2.0.0":
  "integrity" "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "graceful-fs" "^4.1.11"
    "micromatch" "^3.1.10"
    "readable-stream" "^2.0.2"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"reflect-metadata@^0.1.2":
  "version" "0.1.13"

"regenerate-unicode-properties@^10.2.0":
  "integrity" "sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA=="
  "resolved" "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz"
  "version" "10.2.0"
  dependencies:
    "regenerate" "^1.4.2"

"regenerate@^1.4.2":
  "integrity" "sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A=="
  "resolved" "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.11.0":
  "integrity" "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-runtime@^0.13.4":
  "version" "0.13.11"

"regenerator-runtime@^0.13.7":
  "version" "0.13.11"

"regenerator-runtime@0.13.9":
  "integrity" "sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz"
  "version" "0.13.9"

"regex-cache@^0.4.2":
  "version" "0.4.4"
  dependencies:
    "is-equal-shallow" "^0.1.3"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regex-parser@^2.2.11":
  "version" "2.3.1"

"regexp.prototype.flags@^1.5.1":
  "integrity" "sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA=="
  "resolved" "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz"
  "version" "1.5.4"
  dependencies:
    "call-bind" "^1.0.8"
    "define-properties" "^1.2.1"
    "es-errors" "^1.3.0"
    "get-proto" "^1.0.1"
    "gopd" "^1.2.0"
    "set-function-name" "^2.0.2"

"regexpu-core@^6.2.0":
  "integrity" "sha512-H66BPQMrv+V16t8xtmq+UC0CBpiTBA60V8ibS1QVReIp8T1z8hwFxqcGzm9K6lgsN7sB5edVH8a+ze6Fqm4weA=="
  "resolved" "https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "regenerate" "^1.4.2"
    "regenerate-unicode-properties" "^10.2.0"
    "regjsgen" "^0.8.0"
    "regjsparser" "^0.12.0"
    "unicode-match-property-ecmascript" "^2.0.0"
    "unicode-match-property-value-ecmascript" "^2.1.0"

"registry-auth-token@^5.0.2":
  "integrity" "sha512-GdekYuwLXLxMuFTwAPg5UKGLW/UXzQrZvH/Zj791BQif5T05T0RsaLfHc9q3ZOKi7n+BoprPD9mJ0O0k4xzUlw=="
  "resolved" "https://registry.npmjs.org/registry-auth-token/-/registry-auth-token-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "@pnpm/npm-conf" "^2.1.0"

"registry-url@^6.0.1":
  "integrity" "sha512-+crtS5QjFRqFCoQmvGduwYWEBng99ZvmFvF+cUJkGYF1L1BfU8C6Zp9T7f5vPAwyLkUExpvK+ANVZmGU49qi4Q=="
  "resolved" "https://registry.npmjs.org/registry-url/-/registry-url-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "rc" "1.2.8"

"regjsgen@^0.8.0":
  "integrity" "sha512-RvwtGe3d7LvWiDQXeQw8p5asZUmfU1G/l6WbUXeHta7Y2PEIvBTwH6E2EfmYUK8pxcxEdEmaomqyp0vZZ7C+3Q=="
  "resolved" "https://registry.npmjs.org/regjsgen/-/regjsgen-0.8.0.tgz"
  "version" "0.8.0"

"regjsparser@^0.12.0":
  "integrity" "sha512-cnE+y8bz4NhMjISKbgeVJtqNbtf5QpjZP+Bslo+UqkIt9QPnX9q095eiRRASJG1/tz6dlNr6Z5NsBiWYokp6EQ=="
  "resolved" "https://registry.npmjs.org/regjsparser/-/regjsparser-0.12.0.tgz"
  "version" "0.12.0"
  dependencies:
    "jsesc" "~3.0.2"

"remove-bom-buffer@^3.0.0":
  "integrity" "sha512-8v2rWhaakv18qcvNeli2mZ/TMTL2nEyAKRvzo1WtnZBl15SHyEhrCu2/xKlJyUFKHiHgfXIyuY6g2dObJJycXQ=="
  "resolved" "https://registry.npmjs.org/remove-bom-buffer/-/remove-bom-buffer-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "is-buffer" "^1.1.5"
    "is-utf8" "^0.2.1"

"remove-bom-stream@^1.2.0":
  "integrity" "sha512-wigO8/O08XHb8YPzpDDT+QmRANfW6vLqxfaXm1YXhnFf3AkSLyjfG3GEFg4McZkmgL7KvCj5u2KczkvSP6NfHA=="
  "resolved" "https://registry.npmjs.org/remove-bom-stream/-/remove-bom-stream-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "remove-bom-buffer" "^3.0.0"
    "safe-buffer" "^5.1.0"
    "through2" "^2.0.3"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha512-/hS+Y0u3aOfIETiaiirUFwDBDzmXPvO+jAfKTitUngIPzdKc6Z0LoFjM/CK5PL4C+eKwHohlHAb6H0VFfmmUsw=="
  "resolved" "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"replace-ext@^1.0.0":
  "integrity" "sha512-yD5BHCe7quCgBph4rMQ+0KkIRKwWCrHDOX1p1Gp6HwjPM5kVoCdKGNhN7ydqqsX6lJEnQDKZ/tFMiEdQ1dvPEw=="
  "resolved" "https://registry.npmjs.org/replace-ext/-/replace-ext-1.0.1.tgz"
  "version" "1.0.1"

"request@^2.87.0":
  "integrity" "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw=="
  "resolved" "https://registry.npmjs.org/request/-/request-2.88.2.tgz"
  "version" "2.88.2"
  dependencies:
    "aws-sign2" "~0.7.0"
    "aws4" "^1.8.0"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.6"
    "extend" "~3.0.2"
    "forever-agent" "~0.6.1"
    "form-data" "~2.3.2"
    "har-validator" "~5.1.3"
    "http-signature" "~1.2.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.19"
    "oauth-sign" "~0.9.0"
    "performance-now" "^2.1.0"
    "qs" "~6.5.2"
    "safe-buffer" "^5.1.2"
    "tough-cookie" "~2.5.0"
    "tunnel-agent" "^0.6.0"
    "uuid" "^3.3.2"

"require-directory@^2.1.1":
  "integrity" "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="
  "resolved" "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="
  "resolved" "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"require-main-filename@^2.0.0":
  "integrity" "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg=="
  "resolved" "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"requires-port@^1.0.0":
  "integrity" "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ=="
  "resolved" "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-from@^5.0.0":
  "integrity" "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
  "version" "5.0.0"

"resolve-options@^1.1.0":
  "integrity" "sha512-NYDgziiroVeDC29xq7bp/CacZERYsA9bXYd1ZmcJlF3BcrZv5pTb4NG7SjdyKDnXZ84aC4vo2u6sNKIA1LCu/A=="
  "resolved" "https://registry.npmjs.org/resolve-options/-/resolve-options-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "value-or-function" "^3.0.0"

"resolve-url-loader@5.0.0":
  "integrity" "sha512-uZtduh8/8srhBoMx//5bwqjQ+rfYOUq8zC9NrMUGtjBiGTtFJM42s58/36+hTqeqINcnYe08Nj3LkK9lW4N8Xg=="
  "resolved" "https://registry.npmjs.org/resolve-url-loader/-/resolve-url-loader-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "adjust-sourcemap-loader" "^4.0.0"
    "convert-source-map" "^1.7.0"
    "loader-utils" "^2.0.0"
    "postcss" "^8.2.14"
    "source-map" "0.6.1"

"resolve-url@^0.2.1":
  "version" "0.2.1"

"resolve@^1.1.7", "resolve@^1.14.2", "resolve@^1.19.0", "resolve@^1.3.2", "resolve@1.22.1":
  "integrity" "sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.22.1.tgz"
  "version" "1.22.1"
  dependencies:
    "is-core-module" "^2.9.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"restore-cursor@^3.1.0":
  "integrity" "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA=="
  "resolved" "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "version" "0.1.15"

"retry@^0.12.0":
  "integrity" "sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow=="
  "resolved" "https://registry.npmjs.org/retry/-/retry-0.12.0.tgz"
  "version" "0.12.0"

"retry@^0.13.1":
  "integrity" "sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg=="
  "resolved" "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz"
  "version" "0.13.1"

"reusify@^1.0.4":
  "version" "1.1.0"

"rfdc@^1.3.0":
  "integrity" "sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA=="
  "resolved" "https://registry.npmjs.org/rfdc/-/rfdc-1.4.1.tgz"
  "version" "1.4.1"

"rgbcolor@^1.0.1":
  "integrity" "sha512-9aZLIrhRaD97sgVhtJOW6ckOEh6/GnvQtdVNfdZ6s67+3/XwLS9lBcQYzEEhYVeUowN7pRzMLsyGhK2i/xvWbw=="
  "resolved" "https://registry.npmjs.org/rgbcolor/-/rgbcolor-1.0.1.tgz"
  "version" "1.0.1"

"rimraf@^2.2.8", "rimraf@^2.5.2", "rimraf@^2.5.4", "rimraf@^2.6.3":
  "integrity" "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w=="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^3.0.0", "rimraf@^3.0.2":
  "integrity" "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA=="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rollup-plugin-sourcemaps@^0.6.3":
  "integrity" "sha512-paFu+nT1xvuO1tPFYXGe+XnQvg4Hjqv/eIhG8i5EspfYYPBKL57X7iVbfv55aNVASg3dzWvES9dmWsL2KhfByw=="
  "resolved" "https://registry.npmjs.org/rollup-plugin-sourcemaps/-/rollup-plugin-sourcemaps-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "@rollup/pluginutils" "^3.0.9"
    "source-map-resolve" "^0.6.0"

"rollup@^2.70.0":
  "integrity" "sha512-fS6iqSPZDs3dr/y7Od6y5nha8dW1YnbgtsyotCVvoFGKbERG++CVRFv1meyGDE1SNItQA8BrnCw7ScdAhRJ3XQ=="
  "resolved" "https://registry.npmjs.org/rollup/-/rollup-2.79.2.tgz"
  "version" "2.79.2"
  optionalDependencies:
    "fsevents" "~2.3.2"

"run-async@^2.4.0":
  "integrity" "sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ=="
  "resolved" "https://registry.npmjs.org/run-async/-/run-async-2.4.1.tgz"
  "version" "2.4.1"

"run-parallel@^1.1.9":
  "integrity" "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  "resolved" "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"rw@1":
  "integrity" "sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ=="
  "resolved" "https://registry.npmjs.org/rw/-/rw-1.3.3.tgz"
  "version" "1.3.3"

"rxjs@^7.5.5":
  "version" "7.8.2"
  dependencies:
    "tslib" "^2.1.0"

"rxjs@6.6.7":
  "integrity" "sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ=="
  "resolved" "https://registry.npmjs.org/rxjs/-/rxjs-6.6.7.tgz"
  "version" "6.6.7"
  dependencies:
    "tslib" "^1.9.0"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.1.2", "safe-buffer@>=5.1.0", "safe-buffer@~5.2.0", "safe-buffer@5.2.1":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-buffer@~5.1.0", "safe-buffer@~5.1.1":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-regex@^1.1.0":
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@^2.0.2", "safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", "safer-buffer@~2.1.0":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sass-loader@13.0.2":
  "integrity" "sha512-BbiqbVmbfJaWVeOOAu2o7DhYWtcNmTfvroVgFXa6k2hHheMxNAeDHLNoDy/Q5aoaVlz0LH+MbMktKwm9vN/j8Q=="
  "resolved" "https://registry.npmjs.org/sass-loader/-/sass-loader-13.0.2.tgz"
  "version" "13.0.2"
  dependencies:
    "klona" "^2.0.4"
    "neo-async" "^2.6.2"

"sass@^1.49.9", "sass@1.54.4":
  "integrity" "sha512-3tmF16yvnBwtlPrNBHw/H907j8MlOX8aTBnlNX1yrKx24RKcJGPyLhFUwkoKBKesR3unP93/2z14Ll8NicwQUA=="
  "resolved" "https://registry.npmjs.org/sass/-/sass-1.54.4.tgz"
  "version" "1.54.4"
  dependencies:
    "chokidar" ">=3.0.0 <4.0.0"
    "immutable" "^4.0.0"
    "source-map-js" ">=0.6.2 <2.0.0"

"saucelabs@^1.5.0":
  "integrity" "sha512-jlX3FGdWvYf4Q3LFfFWS1QvPg3IGCGWxIc8QBFdPTbpTJnt/v17FHXYVAn7C8sHf1yUXo2c7yIM0isDryfYtHQ=="
  "resolved" "https://registry.npmjs.org/saucelabs/-/saucelabs-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "https-proxy-agent" "^2.2.1"

"sax@^1.2.4", "sax@>=0.6.0":
  "integrity" "sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg=="
  "resolved" "https://registry.npmjs.org/sax/-/sax-1.4.1.tgz"
  "version" "1.4.1"

"sax@~1.2.4":
  "integrity" "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="
  "resolved" "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz"
  "version" "1.2.4"

"schema-utils@^2.6.5":
  "integrity" "sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"schema-utils@^3.1.0":
  "version" "3.3.0"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"schema-utils@^4.0.0":
  "integrity" "sha512-Gn/JaSk/Mt9gYubxTtSn/QCV4em9mpAPiR1rqy/Ocu19u/G9J5WWdNoUT4SiV6mFC3y6cxyFcFwdzPM3FgxGAQ=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "ajv" "^8.9.0"
    "ajv-formats" "^2.1.1"
    "ajv-keywords" "^5.1.0"

"schema-utils@^4.3.0":
  "integrity" "sha512-Gn/JaSk/Mt9gYubxTtSn/QCV4em9mpAPiR1rqy/Ocu19u/G9J5WWdNoUT4SiV6mFC3y6cxyFcFwdzPM3FgxGAQ=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "ajv" "^8.9.0"
    "ajv-formats" "^2.1.1"
    "ajv-keywords" "^5.1.0"

"select-hose@^2.0.0":
  "integrity" "sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg=="
  "resolved" "https://registry.npmjs.org/select-hose/-/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"selenium-webdriver@^3.0.1", "selenium-webdriver@3.6.0":
  "version" "3.6.0"
  dependencies:
    "jszip" "^3.1.3"
    "rimraf" "^2.5.4"
    "tmp" "0.0.30"
    "xml2js" "^0.4.17"

"selfsigned@^2.0.1":
  "integrity" "sha512-th5B4L2U+eGLq1TVh7zNRGBapioSORUeymIydxgFpwww9d2qyKvtuPU2jJuHvYAwwqi2Y596QBL3eEqcPEYL8Q=="
  "resolved" "https://registry.npmjs.org/selfsigned/-/selfsigned-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "@types/node-forge" "^1.3.0"
    "node-forge" "^1"

"semver-dsl@^1.0.1":
  "integrity" "sha512-e8BOaTo007E3dMuQQTnPdalbKTABKNS7UxoBIDnwOqRa+QwMrCPjynB8zAlPF6xlqUfdLPPLIJ13hJNmhtq8Ng=="
  "resolved" "https://registry.npmjs.org/semver-dsl/-/semver-dsl-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "semver" "^5.3.0"

"semver@^5.3.0", "semver@^5.6.0":
  "version" "5.7.1"

"semver@^6.0.0":
  "version" "6.3.1"

"semver@^6.1.1":
  "version" "6.3.1"

"semver@^6.1.2":
  "version" "6.3.1"

"semver@^6.3.0":
  "version" "6.3.1"

"semver@^6.3.1":
  "version" "6.3.1"

"semver@^7.0.0":
  "version" "7.7.2"

"semver@^7.1.1":
  "version" "7.7.2"

"semver@^7.3.5":
  "version" "7.7.2"

"semver@^7.3.7":
  "version" "7.7.2"

"semver@^7.5.3":
  "version" "7.7.2"

"semver@^7.6.0":
  "version" "7.7.2"

"semver@7.3.7":
  "version" "7.3.7"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@7.5.3":
  "version" "7.5.3"
  dependencies:
    "lru-cache" "^6.0.0"

"send@0.19.0":
  "integrity" "sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw=="
  "resolved" "https://registry.npmjs.org/send/-/send-0.19.0.tgz"
  "version" "0.19.0"
  dependencies:
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "mime" "1.6.0"
    "ms" "2.1.3"
    "on-finished" "2.4.1"
    "range-parser" "~1.2.1"
    "statuses" "2.0.1"

"serialize-javascript@^6.0.0", "serialize-javascript@^6.0.2":
  "integrity" "sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g=="
  "resolved" "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "randombytes" "^2.1.0"

"serve-index@^1.9.1":
  "integrity" "sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw=="
  "resolved" "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.16.2":
  "integrity" "sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw=="
  "resolved" "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz"
  "version" "1.16.2"
  dependencies:
    "encodeurl" "~2.0.0"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.19.0"

"set-blocking@^2.0.0":
  "integrity" "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw=="
  "resolved" "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-function-length@^1.2.2":
  "integrity" "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg=="
  "resolved" "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "define-data-property" "^1.1.4"
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.4"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.2"

"set-function-name@^2.0.2":
  "integrity" "sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ=="
  "resolved" "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "define-data-property" "^1.1.4"
    "es-errors" "^1.3.0"
    "functions-have-names" "^1.2.3"
    "has-property-descriptors" "^1.0.2"

"set-immediate-shim@~1.0.1":
  "version" "1.0.1"

"set-value@^2.0.0", "set-value@^2.0.1":
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setprototypeof@1.1.0":
  "integrity" "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.2.0":
  "integrity" "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  "version" "1.2.0"

"shallow-clone@^3.0.0":
  "integrity" "sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA=="
  "resolved" "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^6.0.2"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shell-quote@^1.6.1":
  "version" "1.7.2"

"side-channel-list@^1.0.0":
  "version" "1.0.0"
  dependencies:
    "es-errors" "^1.3.0"
    "object-inspect" "^1.13.3"

"side-channel-map@^1.0.1":
  "version" "1.0.1"
  dependencies:
    "call-bound" "^1.0.2"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.5"
    "object-inspect" "^1.13.3"

"side-channel-weakmap@^1.0.2":
  "version" "1.0.2"
  dependencies:
    "call-bound" "^1.0.2"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.5"
    "object-inspect" "^1.13.3"
    "side-channel-map" "^1.0.1"

"side-channel@^1.0.6":
  "version" "1.1.0"
  dependencies:
    "es-errors" "^1.3.0"
    "object-inspect" "^1.13.3"
    "side-channel-list" "^1.0.0"
    "side-channel-map" "^1.0.1"
    "side-channel-weakmap" "^1.0.2"

"signal-exit@^3.0.2", "signal-exit@^3.0.3", "signal-exit@^3.0.7":
  "integrity" "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  "version" "3.0.7"

"signal-exit@^4.0.1":
  "integrity" "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw=="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  "version" "4.1.0"

"slash@^4.0.0":
  "integrity" "sha512-3dOsAHXXUkQTpOYcoAxLIorMTp4gIQr5IW3iVb7A7lFIp0VHhnynm9izx6TssdrIcVIESAlVjtnO2K8bg+Coew=="
  "resolved" "https://registry.npmjs.org/slash/-/slash-4.0.0.tgz"
  "version" "4.0.0"

"smart-buffer@^4.2.0":
  "integrity" "sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg=="
  "resolved" "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz"
  "version" "4.2.0"

"snapdragon@^0.8.1":
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"socket.io-adapter@~2.5.2":
  "integrity" "sha512-eLDQas5dzPgOWCk9GuuJC2lBqItuhKI4uxGgo9aIV7MYbk2h9Q6uULEh8WBzThoI7l+qU9Ast9fVUmkqPP9wYg=="
  "resolved" "https://registry.npmjs.org/socket.io-adapter/-/socket.io-adapter-2.5.5.tgz"
  "version" "2.5.5"
  dependencies:
    "debug" "~4.3.4"
    "ws" "~8.17.1"

"socket.io-parser@~4.2.4":
  "integrity" "sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew=="
  "resolved" "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-4.2.4.tgz"
  "version" "4.2.4"
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    "debug" "~4.3.1"

"socket.io@^4.2.0":
  "integrity" "sha512-oZ7iUCxph8WYRHHcjBEc9unw3adt5CmSNlppj/5Q4k2RIrhl8Z5yY2Xr4j9zj0+wzVZ0bxmYoGSzKJnRl6A4yg=="
  "resolved" "https://registry.npmjs.org/socket.io/-/socket.io-4.8.1.tgz"
  "version" "4.8.1"
  dependencies:
    "accepts" "~1.3.4"
    "base64id" "~2.0.0"
    "cors" "~2.8.5"
    "debug" "~4.3.2"
    "engine.io" "~6.6.0"
    "socket.io-adapter" "~2.5.2"
    "socket.io-parser" "~4.2.4"

"sockjs@^0.3.24":
  "integrity" "sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ=="
  "resolved" "https://registry.npmjs.org/sockjs/-/sockjs-0.3.24.tgz"
  "version" "0.3.24"
  dependencies:
    "faye-websocket" "^0.11.3"
    "uuid" "^8.3.2"
    "websocket-driver" "^0.7.4"

"socks-proxy-agent@^7.0.0":
  "integrity" "sha512-Fgl0YPZ902wEsAyiQ+idGd1A7rSFx/ayC1CQVMw5P+EQx2V0SgpGtf6OKFhVjPflPUl9YMmEOnmfjCdMUsygww=="
  "resolved" "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "agent-base" "^6.0.2"
    "debug" "^4.3.3"
    "socks" "^2.6.2"

"socks@^2.6.2":
  "version" "2.8.4"
  dependencies:
    "ip-address" "^9.0.5"
    "smart-buffer" "^4.2.0"

"source-map-js@^1.0.2", "source-map-js@>=0.6.2 <2.0.0":
  "integrity" "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA=="
  "resolved" "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  "version" "1.2.1"

"source-map-loader@4.0.0":
  "integrity" "sha512-i3KVgM3+QPAHNbGavK+VBq03YoJl24m9JWNbLgsjTj8aJzXG9M61bantBTNBt7CNwY2FYf+RJRYJ3pzalKjIrw=="
  "resolved" "https://registry.npmjs.org/source-map-loader/-/source-map-loader-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "abab" "^2.0.6"
    "iconv-lite" "^0.6.3"
    "source-map-js" "^1.0.2"

"source-map-resolve@^0.5.0":
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-resolve@^0.6.0":
  "integrity" "sha512-KXBr9d/fO/bWo97NXsPIAW1bFSBOuCnjbNTBMO7N59hsv5i9yzRDfcYwwt0l04+VqnKC+EwzvJZIP/qkuMgR/w=="
  "resolved" "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"

"source-map-support@^0.5.5", "source-map-support@^0.5.6", "source-map-support@~0.5.20", "source-map-support@0.5.21":
  "integrity" "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz"
  "version" "0.5.21"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-support@~0.4.0":
  "integrity" "sha512-try0/JqxPLF9nOjvSta7tVondkP5dwgyLDjVoyMDlmjugT2lRZ1OfsrYTkCd2hkDnJTKRbO/Rl3orm8vlsUzbA=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.4.18.tgz"
  "version" "0.4.18"
  dependencies:
    "source-map" "^0.5.6"

"source-map-url@^0.4.0":
  "version" "0.4.0"

"source-map@^0.5.6":
  "integrity" "sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.5.7":
  "integrity" "sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "version" "0.7.3"

"source-map@~0.6.0":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@0.7.4":
  "integrity" "sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.7.4.tgz"
  "version" "0.7.4"

"sourcemap-codec@^1.4.8":
  "integrity" "sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA=="
  "resolved" "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz"
  "version" "1.4.8"

"spdx-correct@^3.0.0":
  "integrity" "sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA=="
  "resolved" "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w=="
  "resolved" "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz"
  "version" "2.5.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q=="
  "resolved" "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "version" "3.0.21"

"spdy-transport@^3.0.0":
  "integrity" "sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw=="
  "resolved" "https://registry.npmjs.org/spdy-transport/-/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "^4.1.0"
    "detect-node" "^2.0.4"
    "hpack.js" "^2.1.6"
    "obuf" "^1.1.2"
    "readable-stream" "^3.0.6"
    "wbuf" "^1.7.3"

"spdy@^4.0.2":
  "integrity" "sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA=="
  "resolved" "https://registry.npmjs.org/spdy/-/spdy-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "debug" "^4.1.0"
    "handle-thing" "^2.0.0"
    "http-deceiver" "^1.2.7"
    "select-hose" "^2.0.0"
    "spdy-transport" "^3.0.0"

"split-string@^3.0.1":
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@^1.1.2":
  "version" "1.1.2"

"sprintf-js@^1.1.3":
  "integrity" "sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA=="
  "resolved" "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.3.tgz"
  "version" "1.1.3"

"sprintf-js@~1.0.2":
  "integrity" "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g=="
  "resolved" "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"sshpk@^1.7.0":
  "version" "1.16.1"
  dependencies:
    "asn1" "~0.2.3"
    "assert-plus" "^1.0.0"
    "bcrypt-pbkdf" "^1.0.0"
    "dashdash" "^1.12.0"
    "ecc-jsbn" "~0.1.1"
    "getpass" "^0.1.1"
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.0.2"
    "tweetnacl" "~0.14.0"

"ssri@^9.0.0":
  "integrity" "sha512-o57Wcn66jMQvfHG1FlYbWeZWW/dHZhJXjpIcTfXldXEk5nz5lStPo3mK0OJQfGR3RbZUlbISexbljkJzuEj/8Q=="
  "resolved" "https://registry.npmjs.org/ssri/-/ssri-9.0.1.tgz"
  "version" "9.0.1"
  dependencies:
    "minipass" "^3.1.1"

"stackblur-canvas@^2.0.0":
  "integrity" "sha512-yf7OENo23AGJhBriGx0QivY5JP6Y1HbrrDI6WLt6C5auYZXlQrheoY8hD4ibekFKz1HOfE48Ww8kMWMnJD/zcQ=="
  "resolved" "https://registry.npmjs.org/stackblur-canvas/-/stackblur-canvas-2.7.0.tgz"
  "version" "2.7.0"

"static-extend@^0.1.1":
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@~1.5.0":
  "integrity" "sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA=="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz"
  "version" "1.5.0"

"statuses@2.0.1":
  "integrity" "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ=="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
  "version" "2.0.1"

"stream-shift@^1.0.0":
  "version" "1.0.1"

"streamroller@^3.1.5":
  "version" "3.1.5"
  dependencies:
    "date-format" "^4.0.14"
    "debug" "^4.3.4"
    "fs-extra" "^8.1.0"

"string_decoder@^1.1.1":
  "integrity" "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "safe-buffer" "~5.2.0"

"string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-width-cjs@npm:string-width@^4.2.0":
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string-width@^1.0.2 || 2 || 3 || 4", "string-width@^4.1.0", "string-width@^4.2.0", "string-width@^4.2.3":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string-width@^5.0.1", "string-width@^5.1.2":
  "version" "5.1.2"
  dependencies:
    "eastasianwidth" "^0.2.0"
    "emoji-regex" "^9.2.2"
    "strip-ansi" "^7.0.1"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-ansi@^3.0.0":
  "integrity" "sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^6.0.0":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-ansi@^7.0.1":
  "version" "7.1.0"
  dependencies:
    "ansi-regex" "^6.0.1"

"strip-final-newline@^2.0.0":
  "integrity" "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA=="
  "resolved" "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-json-comments@~2.0.1":
  "integrity" "sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ=="
  "resolved" "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
  "version" "2.0.1"

"strip-json-comments@3.1.1":
  "version" "3.1.1"

"stylus-loader@7.0.0":
  "integrity" "sha512-WTbtLrNfOfLgzTaR9Lj/BPhQroKk/LC1hfTXSUbrxmxgfUo3Y3LpmKRVA2R1XbjvTAvOfaian9vOyfv1z99E+A=="
  "resolved" "https://registry.npmjs.org/stylus-loader/-/stylus-loader-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "fast-glob" "^3.2.11"
    "klona" "^2.0.5"
    "normalize-path" "^3.0.0"

"stylus@^0.59.0", "stylus@0.59.0":
  "integrity" "sha512-lQ9w/XIOH5ZHVNuNbWW8D822r+/wBSO/d6XvtyHLF7LW4KaCIDeVbvn5DF8fGCJAUCwVhVi/h6J0NUcnylUEjg=="
  "resolved" "https://registry.npmjs.org/stylus/-/stylus-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "@adobe/css-tools" "^4.0.1"
    "debug" "^4.3.2"
    "glob" "^7.1.6"
    "sax" "~1.2.4"
    "source-map" "^0.7.3"

"subarg@^1.0.0":
  "integrity" "sha512-RIrIdRY0X1xojthNcVtgT9sjpOGagEUKpZdgBUi054OEPFo282yg+zE+t1Rj3+RqKq2xStL7uUHhY+AjbC4BXg=="
  "resolved" "https://registry.npmjs.org/subarg/-/subarg-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "minimist" "^1.1.0"

"supports-color@^2.0.0":
  "integrity" "sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^5.3.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@^8.0.0":
  "integrity" "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  "version" "8.1.1"
  dependencies:
    "has-flag" "^4.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svg-pathdata@^6.0.3":
  "integrity" "sha512-qsjeeq5YjBZ5eMdFuUa4ZosMLxgr5RZ+F+Y1OrDhuOCEInRMA3x74XdBtggJcj9kOeInz0WE+LgCPDkZFlBYJw=="
  "resolved" "https://registry.npmjs.org/svg-pathdata/-/svg-pathdata-6.0.3.tgz"
  "version" "6.0.3"

"symbol-observable@4.0.0":
  "integrity" "sha512-b19dMThMV4HVFynSAM1++gBHAbk2Tc/osgLIBZMKsyqh34jb2e8Os7T6ZW/Bt3pJFdBTd2JwAnAAEQV7rSNvcQ=="
  "resolved" "https://registry.npmjs.org/symbol-observable/-/symbol-observable-4.0.0.tgz"
  "version" "4.0.0"

"tapable@^2.1.1", "tapable@^2.2.0":
  "version" "2.2.2"

"tar@^6.1.11", "tar@^6.1.2":
  "integrity" "sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A=="
  "resolved" "https://registry.npmjs.org/tar/-/tar-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "chownr" "^2.0.0"
    "fs-minipass" "^2.0.0"
    "minipass" "^5.0.0"
    "minizlib" "^2.1.1"
    "mkdirp" "^1.0.3"
    "yallist" "^4.0.0"

"terser-webpack-plugin@^5.1.3":
  "version" "5.3.14"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    "jest-worker" "^27.4.5"
    "schema-utils" "^4.3.0"
    "serialize-javascript" "^6.0.2"
    "terser" "^5.31.1"

"terser@^5.31.1":
  "version" "5.40.0"
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    "acorn" "^8.14.0"
    "commander" "^2.20.0"
    "source-map-support" "~0.5.20"

"terser@5.14.2":
  "integrity" "sha512-oL0rGeM/WFQCUd0y2QrWxYnq7tfSuKBiqTjRPWrRgB46WD/kiwHwF8T23z78H6Q6kGCuuHcPB+KULHRdxvVGQA=="
  "resolved" "https://registry.npmjs.org/terser/-/terser-5.14.2.tgz"
  "version" "5.14.2"
  dependencies:
    "@jridgewell/source-map" "^0.3.2"
    "acorn" "^8.5.0"
    "commander" "^2.20.0"
    "source-map-support" "~0.5.20"

"test-exclude@^6.0.0":
  "integrity" "sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w=="
  "resolved" "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    "glob" "^7.1.4"
    "minimatch" "^3.0.4"

"text-segmentation@^1.0.3":
  "integrity" "sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw=="
  "resolved" "https://registry.npmjs.org/text-segmentation/-/text-segmentation-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "utrie" "^1.0.2"

"text-table@0.2.0":
  "integrity" "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw=="
  "resolved" "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"through@^2.3.6", "through@X.X.X":
  "integrity" "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg=="
  "resolved" "https://registry.npmjs.org/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"through2-filter@^3.0.0":
  "version" "3.0.0"
  dependencies:
    "through2" "~2.0.0"
    "xtend" "~4.0.0"

"through2@^2.0.0":
  "integrity" "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ=="
  "resolved" "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"through2@^2.0.3":
  "integrity" "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ=="
  "resolved" "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"through2@^3.0.0":
  "integrity" "sha512-enaDQ4MUyP2W6ZyT6EsMzqBPZaM/avg8iuo+l2d3QCs0J+6RaqkHV/2/lOwDTueBHeJ/2LG9lrLW3d5rWPucuQ=="
  "resolved" "https://registry.npmjs.org/through2/-/through2-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "inherits" "^2.0.4"
    "readable-stream" "2 || 3"

"through2@~2.0.0":
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"thunky@^1.0.2":
  "integrity" "sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA=="
  "resolved" "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz"
  "version" "1.1.0"

"tmp@^0.0.33":
  "integrity" "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw=="
  "resolved" "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz"
  "version" "0.0.33"
  dependencies:
    "os-tmpdir" "~1.0.2"

"tmp@^0.2.1":
  "version" "0.2.1"
  dependencies:
    "rimraf" "^3.0.0"

"tmp@0.0.30":
  "version" "0.0.30"
  dependencies:
    "os-tmpdir" "~1.0.1"

"to-absolute-glob@^2.0.0":
  "integrity" "sha512-rtwLUQEwT8ZeKQbyFJyomBRYXyE16U5VKuy0ftxLMK/PZb2fkOsg5r9kHdauuVDbsNdIBoC/HCthpidamQFXYA=="
  "resolved" "https://registry.npmjs.org/to-absolute-glob/-/to-absolute-glob-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-absolute" "^1.0.0"
    "is-negated-glob" "^1.0.0"

"to-object-path@^0.3.0":
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"to-through@^2.0.0":
  "integrity" "sha512-+QIz37Ly7acM4EMdw2PRN389OneM5+d844tirkGp4dPKzI5OE72V9OsbFp+CIYJDahZ41ZV05hNtcPAQUAm9/Q=="
  "resolved" "https://registry.npmjs.org/to-through/-/to-through-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "through2" "^2.0.3"

"toidentifier@1.0.1":
  "integrity" "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA=="
  "resolved" "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  "version" "1.0.1"

"tough-cookie@~2.5.0":
  "version" "2.5.0"
  dependencies:
    "psl" "^1.1.28"
    "punycode" "^2.1.1"

"tr46@~0.0.3":
  "integrity" "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw=="
  "resolved" "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  "version" "0.0.3"

"tree-kill@1.2.2":
  "integrity" "sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A=="
  "resolved" "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz"
  "version" "1.2.2"

"tributejs@^4.0.0":
  "integrity" "sha512-+VUqyi8p7tCdaqCINCWHf95E2hJFMIML180BhplTpXNooz3E2r96AONXI9qO2Ru6Ugp7MsMPJjB+rnBq+hAmzA=="
  "resolved" "https://registry.npmjs.org/tributejs/-/tributejs-4.1.3.tgz"
  "version" "4.1.3"

"ts-node@~8.3.0":
  "integrity" "sha512-dyNS/RqyVTDcmNM4NIBAeDMpsAdaQ+ojdf0GOLqE6nwJOgzEkdRNzJywhDfwnuvB10oa6NLVG1rUJQCpRN7qoQ=="
  "resolved" "https://registry.npmjs.org/ts-node/-/ts-node-8.3.0.tgz"
  "version" "8.3.0"
  dependencies:
    "arg" "^4.1.0"
    "diff" "^4.0.1"
    "make-error" "^1.1.1"
    "source-map-support" "^0.5.6"
    "yn" "^3.0.0"

"tsickle@0.39.1":
  "integrity" "sha512-CCc9cZhZbKoNizVM+K3Uqgit/go8GacjpqTv1cpwG/n2P0gB9GMoWZbxrUULDE9Wz26Lh86CGf6QyIPUVV1lnQ=="
  "resolved" "https://registry.npmjs.org/tsickle/-/tsickle-0.39.1.tgz"
  "version" "0.39.1"

"tslib@^1.10.0":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^1.13.0":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^1.8.1":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^1.9.0":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^2.0.0", "tslib@^2.1.0", "tslib@^2.2.0", "tslib@^2.3.0":
  "integrity" "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  "version" "2.8.1"

"tslib@2.3.0":
  "integrity" "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.3.0.tgz"
  "version" "2.3.0"

"tslib@2.4.0":
  "integrity" "sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz"
  "version" "2.4.0"

"tslint@~6.1.0":
  "integrity" "sha512-IbR4nkT96EQOvKE2PW/djGz8iGNeJ4rF2mBfiYaR/nvUWYKJhLwimoJKgjIFEIDibBtOevj7BqCRL4oHeWWUCg=="
  "resolved" "https://registry.npmjs.org/tslint/-/tslint-6.1.3.tgz"
  "version" "6.1.3"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "builtin-modules" "^1.1.1"
    "chalk" "^2.3.0"
    "commander" "^2.12.1"
    "diff" "^4.0.1"
    "glob" "^7.1.1"
    "js-yaml" "^3.13.1"
    "minimatch" "^3.0.4"
    "mkdirp" "^0.5.3"
    "resolve" "^1.3.2"
    "semver" "^5.3.0"
    "tslib" "^1.13.0"
    "tsutils" "^2.29.0"

"tsutils@^2.29.0":
  "integrity" "sha512-g5JVHCIJwzfISaXpXE1qvNalca5Jwob6FjI4AoPlqMusJ6ftFE7IkkFoMhVLRgK+4Kx3gkzb8UZK5t5yTTvEmA=="
  "resolved" "https://registry.npmjs.org/tsutils/-/tsutils-2.29.0.tgz"
  "version" "2.29.0"
  dependencies:
    "tslib" "^1.8.1"

"tunnel-agent@^0.6.0":
  "integrity" "sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w=="
  "resolved" "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "safe-buffer" "^5.0.1"

"tweetnacl@^0.14.3", "tweetnacl@~0.14.0":
  "integrity" "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA=="
  "resolved" "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz"
  "version" "0.14.5"

"type-fest@^0.21.3":
  "integrity" "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-is@~1.6.18":
  "integrity" "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g=="
  "resolved" "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"typed-assert@^1.0.8":
  "integrity" "sha512-KNNZtayBCtmnNmbo5mG47p1XsCyrx6iVqomjcZnec/1Y5GGARaxPs6r49RnSPeUP3YjNYiU9sQHAtY4BBvnZwg=="
  "resolved" "https://registry.npmjs.org/typed-assert/-/typed-assert-1.0.9.tgz"
  "version" "1.0.9"

"typescript@~4.6.4":
  "integrity" "sha512-9ia/jWHIEbo49HfjrLGfKbZSuWo9iTMwXO+Ca3pRsSpbsMbc7/IU8NKdCZVRRBafVPGnoJeFL76ZOAA84I9fEg=="
  "resolved" "https://registry.npmjs.org/typescript/-/typescript-4.6.4.tgz"
  "version" "4.6.4"

"typewriter-effect@^2.21.0":
  "version" "2.22.0"
  dependencies:
    "prop-types" "^15.8.1"
    "raf" "^3.4.1"

"ua-parser-js@^0.7.30":
  "version" "0.7.40"

"unc-path-regex@^0.1.2":
  "integrity" "sha512-eXL4nmJT7oCpkZsHZUOJo8hcX3GbsiDOa0Qu9F646fi8dT3XuSVopVqAcEiVzSKKH7UoDti23wNX3qGFxcW5Qg=="
  "resolved" "https://registry.npmjs.org/unc-path-regex/-/unc-path-regex-0.1.2.tgz"
  "version" "0.1.2"

"underscore@>=1.8.3":
  "version" "1.11.0"

"unicode-canonical-property-names-ecmascript@^2.0.0":
  "integrity" "sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg=="
  "resolved" "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz"
  "version" "2.0.1"

"unicode-match-property-ecmascript@^2.0.0":
  "integrity" "sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^2.0.0"
    "unicode-property-aliases-ecmascript" "^2.0.0"

"unicode-match-property-value-ecmascript@^2.1.0":
  "integrity" "sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz"
  "version" "2.2.0"

"unicode-property-aliases-ecmascript@^2.0.0":
  "integrity" "sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w=="
  "resolved" "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"
  "version" "2.1.0"

"union-value@^1.0.0":
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"unique-filename@^1.1.1":
  "integrity" "sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ=="
  "resolved" "https://registry.npmjs.org/unique-filename/-/unique-filename-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "unique-slug" "^2.0.0"

"unique-slug@^2.0.0":
  "integrity" "sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w=="
  "resolved" "https://registry.npmjs.org/unique-slug/-/unique-slug-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "imurmurhash" "^0.1.4"

"unique-stream@^2.0.2":
  "integrity" "sha512-2nY4TnBE70yoxHkDli7DMazpWiP7xMdCYqU2nBRO0UB+ZpEkGsSija7MvmvnZFUeC+mrgiUfcHSr3LmRFIg4+A=="
  "resolved" "https://registry.npmjs.org/unique-stream/-/unique-stream-2.3.1.tgz"
  "version" "2.3.1"
  dependencies:
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "through2-filter" "^3.0.0"

"universalify@^0.1.0":
  "integrity" "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg=="
  "resolved" "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz"
  "version" "0.1.2"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ=="
  "resolved" "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unset-value@^1.0.0":
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"update-browserslist-db@^1.1.3":
  "version" "1.1.3"
  dependencies:
    "escalade" "^3.2.0"
    "picocolors" "^1.1.1"

"uri-js@^4.2.2":
  "version" "4.4.0"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "version" "0.1.0"

"use@^3.1.0":
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@^1.0.2", "util-deprecate@~1.0.1":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"utils-merge@1.0.1":
  "integrity" "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA=="
  "resolved" "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"utrie@^1.0.2":
  "integrity" "sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw=="
  "resolved" "https://registry.npmjs.org/utrie/-/utrie-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "base64-arraybuffer" "^1.0.2"

"uuid@^3.3.2":
  "integrity" "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz"
  "version" "3.4.0"

"uuid@^8.3.2":
  "integrity" "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  "version" "8.3.2"

"uuid@8.3.2":
  "integrity" "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  "version" "8.3.2"

"validate-npm-package-license@^3.0.4":
  "integrity" "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew=="
  "resolved" "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"validate-npm-package-name@^4.0.0":
  "integrity" "sha512-mzR0L8ZDktZjpX4OB46KT+56MAhl4EIazWP/+G/HPGuvfdaqg4YsCdtOm6U9+LOFyYDoh4dpnpxZRB9MQQns5Q=="
  "resolved" "https://registry.npmjs.org/validate-npm-package-name/-/validate-npm-package-name-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "builtins" "^5.0.0"

"value-or-function@^3.0.0":
  "integrity" "sha512-jdBB2FrWvQC/pnPtIqcLsMaQgjhdb6B7tk1MMyTKapox+tQZbdRP4uLxu/JY0t7fbfDCUMnuelzEYv5GsxHhdg=="
  "resolved" "https://registry.npmjs.org/value-or-function/-/value-or-function-3.0.0.tgz"
  "version" "3.0.0"

"vary@^1", "vary@~1.1.2":
  "integrity" "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg=="
  "resolved" "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"verror@1.10.0":
  "integrity" "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw=="
  "resolved" "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "core-util-is" "1.0.2"
    "extsprintf" "^1.2.0"

"vinyl-fs@^3.0.3":
  "integrity" "sha512-vIu34EkyNyJxmP0jscNzWBSygh7VWhqun6RmqVfXePrOwi9lhvRs//dOaGOTRUQr4tx7/zd26Tk5WeSVZitgng=="
  "resolved" "https://registry.npmjs.org/vinyl-fs/-/vinyl-fs-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "fs-mkdirp-stream" "^1.0.0"
    "glob-stream" "^6.1.0"
    "graceful-fs" "^4.0.0"
    "is-valid-glob" "^1.0.0"
    "lazystream" "^1.0.0"
    "lead" "^1.0.0"
    "object.assign" "^4.0.4"
    "pumpify" "^1.3.5"
    "readable-stream" "^2.3.3"
    "remove-bom-buffer" "^3.0.0"
    "remove-bom-stream" "^1.2.0"
    "resolve-options" "^1.1.0"
    "through2" "^2.0.0"
    "to-through" "^2.0.0"
    "value-or-function" "^3.0.0"
    "vinyl" "^2.0.0"
    "vinyl-sourcemap" "^1.1.0"

"vinyl-sourcemap@^1.1.0":
  "integrity" "sha512-NiibMgt6VJGJmyw7vtzhctDcfKch4e4n9TBeoWlirb7FMg9/1Ov9k+A5ZRAtywBpRPiyECvQRQllYM8dECegVA=="
  "resolved" "https://registry.npmjs.org/vinyl-sourcemap/-/vinyl-sourcemap-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "append-buffer" "^1.0.2"
    "convert-source-map" "^1.5.0"
    "graceful-fs" "^4.1.6"
    "normalize-path" "^2.1.1"
    "now-and-later" "^2.0.0"
    "remove-bom-buffer" "^3.0.0"
    "vinyl" "^2.0.0"

"vinyl@^2.0.0", "vinyl@^2.1.0":
  "integrity" "sha512-LII3bXRFBZLlezoG5FfZVcXflZgWP/4dCwKtxd5ky9+LOtM4CS3bIRQsmR1KMnMW07jpE8fqR2lcxPZ+8sJIcw=="
  "resolved" "https://registry.npmjs.org/vinyl/-/vinyl-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "clone" "^2.1.1"
    "clone-buffer" "^1.0.0"
    "clone-stats" "^1.0.0"
    "cloneable-readable" "^1.0.0"
    "remove-trailing-separator" "^1.0.1"
    "replace-ext" "^1.0.0"

"void-elements@^2.0.0":
  "integrity" "sha512-qZKX4RnBzH2ugr8Lxa7x+0V6XD9Sb/ouARtiasEQCHB1EVU4NXtmHsDDrx1dO4ne5fc3J6EW05BP1Dl0z0iung=="
  "resolved" "https://registry.npmjs.org/void-elements/-/void-elements-2.0.1.tgz"
  "version" "2.0.1"

"watchpack@^2.4.0":
  "version" "2.4.4"
  dependencies:
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.1.2"

"wbuf@^1.1.0", "wbuf@^1.7.3":
  "integrity" "sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA=="
  "resolved" "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "^1.0.0"

"wcwidth@^1.0.1":
  "integrity" "sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg=="
  "resolved" "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webdriver-js-extender@2.1.0":
  "integrity" "sha512-lcUKrjbBfCK6MNsh7xaY2UAUmZwe+/ib03AjVOpFobX4O7+83BUveSrLfU0Qsyb1DaKJdQRbuU+kM9aZ6QUhiQ=="
  "resolved" "https://registry.npmjs.org/webdriver-js-extender/-/webdriver-js-extender-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "@types/selenium-webdriver" "^3.0.0"
    "selenium-webdriver" "^3.0.1"

"webdriver-manager@^12.1.7":
  "version" "12.1.7"
  dependencies:
    "adm-zip" "^0.4.9"
    "chalk" "^1.1.1"
    "del" "^2.2.0"
    "glob" "^7.0.3"
    "ini" "^1.3.4"
    "minimist" "^1.2.0"
    "q" "^1.4.1"
    "request" "^2.87.0"
    "rimraf" "^2.5.2"
    "semver" "^5.3.0"
    "xml2js" "^0.4.17"

"webidl-conversions@^3.0.0":
  "integrity" "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  "version" "3.0.1"

"webpack-dev-middleware@^5.3.1", "webpack-dev-middleware@5.3.3":
  "version" "5.3.3"
  dependencies:
    "colorette" "^2.0.10"
    "memfs" "^3.4.3"
    "mime-types" "^2.1.31"
    "range-parser" "^1.2.1"
    "schema-utils" "^4.0.0"

"webpack-dev-server@4.11.0":
  "integrity" "sha512-L5S4Q2zT57SK7tazgzjMiSMBdsw+rGYIX27MgPgx7LDhWO0lViPrHKoLS7jo5In06PWYAhlYu3PbyoC6yAThbw=="
  "resolved" "https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-4.11.0.tgz"
  "version" "4.11.0"
  dependencies:
    "@types/bonjour" "^3.5.9"
    "@types/connect-history-api-fallback" "^1.3.5"
    "@types/express" "^4.17.13"
    "@types/serve-index" "^1.9.1"
    "@types/serve-static" "^1.13.10"
    "@types/sockjs" "^0.3.33"
    "@types/ws" "^8.5.1"
    "ansi-html-community" "^0.0.8"
    "bonjour-service" "^1.0.11"
    "chokidar" "^3.5.3"
    "colorette" "^2.0.10"
    "compression" "^1.7.4"
    "connect-history-api-fallback" "^2.0.0"
    "default-gateway" "^6.0.3"
    "express" "^4.17.3"
    "graceful-fs" "^4.2.6"
    "html-entities" "^2.3.2"
    "http-proxy-middleware" "^2.0.3"
    "ipaddr.js" "^2.0.1"
    "open" "^8.0.9"
    "p-retry" "^4.5.0"
    "rimraf" "^3.0.2"
    "schema-utils" "^4.0.0"
    "selfsigned" "^2.0.1"
    "serve-index" "^1.9.1"
    "sockjs" "^0.3.24"
    "spdy" "^4.0.2"
    "webpack-dev-middleware" "^5.3.1"
    "ws" "^8.4.2"

"webpack-merge@5.8.0":
  "integrity" "sha512-/SaI7xY0831XwP6kzuwhKWVKDP9t1QY1h65lAFLbZqMPIuYcD9QAW4u9STIbU9kaJbPBB/geU/gLr1wDjOhQ+Q=="
  "resolved" "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.8.0.tgz"
  "version" "5.8.0"
  dependencies:
    "clone-deep" "^4.0.1"
    "wildcard" "^2.0.0"

"webpack-sources@^3.0.0", "webpack-sources@^3.2.3":
  "version" "3.3.2"

"webpack-subresource-integrity@5.1.0":
  "integrity" "sha512-sacXoX+xd8r4WKsy9MvH/q/vBtEHr86cpImXwyg74pFIpERKt6FmB8cXpeuh0ZLgclOlHI4Wcll7+R5L02xk9Q=="
  "resolved" "https://registry.npmjs.org/webpack-subresource-integrity/-/webpack-subresource-integrity-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "typed-assert" "^1.0.8"

"webpack@5.76.1":
  "version" "5.76.1"
  dependencies:
    "@types/eslint-scope" "^3.7.3"
    "@types/estree" "^0.0.51"
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/wasm-edit" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "acorn" "^8.7.1"
    "acorn-import-assertions" "^1.7.6"
    "browserslist" "^4.14.5"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^5.10.0"
    "es-module-lexer" "^0.9.0"
    "eslint-scope" "5.1.1"
    "events" "^3.2.0"
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.2.9"
    "json-parse-even-better-errors" "^2.3.1"
    "loader-runner" "^4.2.0"
    "mime-types" "^2.1.27"
    "neo-async" "^2.6.2"
    "schema-utils" "^3.1.0"
    "tapable" "^2.1.1"
    "terser-webpack-plugin" "^5.1.3"
    "watchpack" "^2.4.0"
    "webpack-sources" "^3.2.3"

"websocket-driver@^0.7.4", "websocket-driver@>=0.5.1":
  "integrity" "sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg=="
  "resolved" "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz"
  "version" "0.7.4"
  dependencies:
    "http-parser-js" ">=0.5.1"
    "safe-buffer" ">=5.1.0"
    "websocket-extensions" ">=0.1.1"

"websocket-extensions@>=0.1.1":
  "integrity" "sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg=="
  "resolved" "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  "version" "0.1.4"

"whatwg-url@^5.0.0":
  "integrity" "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw=="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "tr46" "~0.0.3"
    "webidl-conversions" "^3.0.0"

"which-module@^2.0.0":
  "version" "2.0.0"

"which@^1.2.1":
  "integrity" "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ=="
  "resolved" "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.2":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wide-align@^1.1.5":
  "integrity" "sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg=="
  "resolved" "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "string-width" "^1.0.2 || 2 || 3 || 4"

"wildcard@^2.0.0":
  "integrity" "sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ=="
  "resolved" "https://registry.npmjs.org/wildcard/-/wildcard-2.0.1.tgz"
  "version" "2.0.1"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^6.2.0":
  "integrity" "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^8.1.0":
  "version" "8.1.0"
  dependencies:
    "ansi-styles" "^6.1.0"
    "string-width" "^5.0.1"
    "strip-ansi" "^7.0.1"

"wrappy@1":
  "integrity" "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="
  "resolved" "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"ws@^8.4.2", "ws@~8.17.1":
  "integrity" "sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-8.17.1.tgz"
  "version" "8.17.1"

"xml@1.0.1":
  "integrity" "sha512-huCv9IH9Tcf95zuYCsQraZtWnJvBtLVE0QHMOs8bWyZAFZNDcYjsPq1nEx8jKA9y+Beo9v+7OBPRisQTjinQMw=="
  "resolved" "https://registry.npmjs.org/xml/-/xml-1.0.1.tgz"
  "version" "1.0.1"

"xml2js@^0.4.17":
  "version" "0.4.23"
  dependencies:
    "sax" ">=0.6.0"
    "xmlbuilder" "~11.0.0"

"xmlbuilder@~11.0.0":
  "integrity" "sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA=="
  "resolved" "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz"
  "version" "11.0.1"

"xtend@~4.0.0", "xtend@~4.0.1":
  "integrity" "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="
  "resolved" "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"

"xxhashjs@~0.2.2":
  "integrity" "sha512-AkTuIuVTET12tpsVIQo+ZU6f/qDmKuRUcjaqR+OIvm+aCBsZ95i7UVY5WJ9TMsSaZ0DA2WxoZ4acu0sPH+OKAw=="
  "resolved" "https://registry.npmjs.org/xxhashjs/-/xxhashjs-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "cuint" "^0.2.2"

"y18n@^4.0.0":
  "version" "5.0.5"

"y18n@^5.0.5":
  "integrity" "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^3.0.2":
  "integrity" "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^4.0.0":
  "integrity" "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yaml@^1.10.0":
  "integrity" "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg=="
  "resolved" "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
  "version" "1.10.2"

"yargs-parser@^18.1.2":
  "integrity" "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz"
  "version" "18.1.3"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^20.2.2":
  "integrity" "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs-parser@^21.0.0":
  "integrity" "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz"
  "version" "21.1.1"

"yargs@^15.3.1":
  "integrity" "sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz"
  "version" "15.4.1"
  dependencies:
    "cliui" "^6.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^4.1.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^4.2.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^18.1.2"

"yargs@^16.1.1":
  "integrity" "sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz"
  "version" "16.2.0"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"

"yargs@^17.2.1", "yargs@17.5.1":
  "integrity" "sha512-t6YAJcxDkNX7NFYiVtKvWUz8l+PaKTLiL63mJYWR2GnHq2gjEWISzsLp9wg3aY36dY1j+gfIEL3pIF+XlJJfbA=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-17.5.1.tgz"
  "version" "17.5.1"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.3"
    "y18n" "^5.0.5"
    "yargs-parser" "^21.0.0"

"yn@^3.0.0":
  "integrity" "sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q=="
  "resolved" "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz"
  "version" "3.1.1"

"zone.js@~0.11.4":
  "integrity" "sha512-82bctBg2hKcEJ21humWIkXRlLBBmrc3nN7DFh5LGGhcyycO2S7FN8NmdvlcKaGFDNVL4/9kFLmwmInTavdJERA=="
  "resolved" "https://registry.npmjs.org/zone.js/-/zone.js-0.11.8.tgz"
  "version" "0.11.8"
  dependencies:
    "tslib" "^2.3.0"

"zrender@5.6.1":
  "version" "5.6.1"
  dependencies:
    "tslib" "2.3.0"
