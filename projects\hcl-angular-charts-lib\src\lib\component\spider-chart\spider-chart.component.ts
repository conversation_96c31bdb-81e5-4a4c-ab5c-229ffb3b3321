import { Component, OnInit, Input, ViewEncapsulation } from '@angular/core';
import * as d3 from 'd3';
import { SpiderChartConfig } from './spider-chart';
@Component({
  selector: 'spider-chart',
  templateUrl: './spider-chart.component.html',
  styleUrls: ['./spider-chart.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SpiderChartComponent implements OnInit {

  constructor() {}
  @Input() chartData: { name: string, value: number, valueLabel: string}[] = [
    { name: "Readability", value: 52, valueLabel: 'D'},
    { name: "Power", value: 94.5, valueLabel: 'F'},
    { name: "Action", value: 86, valueLabel: 'F'},
    { name: "Polarity", value: 30, valueLabel: 'C'},
    { name: "Subjectivity", value: 87, valueLabel: 'F'},
    { name: "Spam", value: 93, valueLabel: 'C'},

    // { name: "Clarity", value: 100, valueLabel: 'A'},
    // { name: "ConcisionConcisionConcision", value: 100, valueLabel: 'A' },
    // { name: "Engagement", value: 80, valueLabel: 'B' },
    // { name: "Spam", value: 100, valueLabel: 'A'},
    // { name: "Spelling", value: 0, valueLabel: 'A' }
    ]
  

    @Input() spiderChartConfig : SpiderChartConfig = {
      numofLevels : 5,
      width: 320,
      size : 300,
      drawDataTextLabels: true,
      chartBgColor: '#FFF',
      gridLineColor: '#808080',
      levelsStrokeColor: '#808080',
      gradientColor1: "#F7B500",
      gradientColor2: "#B620E0",
      gradientColor3:  "#32C5FF",
      dataPointCircleColor: 'rgb(96 128 149)',
      tickTextColor: '#000',
      labelsTextColor: '#000',
      radiusForDataPointsCircle: 3
    }

  width: number
  height: number
  ticks = [];
  parentGroupElem: any
  svg: any
  r: any = 0.7 * this.spiderChartConfig.size
  r_0: any = this.r / 2
  radialScale: any

  ngOnInit(): void {
    this.createParentSVG();
    this.setChartDimensions();
    this.drawChart(this.chartData)
  }

  drawChart(chartdata){
    this.drawScale();
    this.generateAndDrawLevels();
    this.drawLine();
    this.drawData(chartdata);
    if(this.spiderChartConfig.drawDataTextLabels){
      this.drawAxisLabels()
      this.generateTicks();
    }
    d3.selectAll('.showellipsis').call(this.showEllipsis);
  }

  redrawChart(chartdata) {
    this.recreateSvgAndParentGroup();
    this.drawChart(chartdata)
  }

  recreateSvgAndParentGroup() {   
    d3.select("#svg_"+ this.spiderChartConfig.chartContainerId).remove();
    this.ticks = []
    this.createParentSVG() 
 }

  createParentSVG() {
    this.width = this.spiderChartConfig.width;
    this.height = this.spiderChartConfig.size;
    const selector = "#" + this.spiderChartConfig.chartContainerId;
    d3.select(selector ? selector + ' .chart' : '.chart')
      .append("svg")
      .attr('id', 'svg_' + this.spiderChartConfig.chartContainerId)
      .attr("width", this.width)
      .attr("height", this.height)
    this.parentGroupElem = d3.select('#svg_' + this.spiderChartConfig.chartContainerId).append("g");
    this.parentGroupElem.append("linearGradient")
      .attr("id", "line-gradient")
      .attr("gradientUnits", "userSpaceOnUse")
      .attr("x1", 0)
      .attr("y1", 100)
      .attr("x2", 100)
      .attr("y2", 0)
      .attr("x3", 0)
      .attr("y3", 100)
      .selectAll("stop")
      .data([
        { offset: this.spiderChartConfig.gradientOffset1 + "%", color:this.spiderChartConfig.gradientColor1 },
        { offset: this.spiderChartConfig.gradientOffset2 + "%", color: this.spiderChartConfig.gradientColor2 },
        { offset: this.spiderChartConfig.gradientOffset3 + "%", color: this.spiderChartConfig.gradientColor3 },
      ])
      .enter().append("stop")
      .attr("offset", function (d) { return d.offset; })
      .attr("stop-color", function (d) { return d.color; })
      .attr('stop-opacity', 0.6)
  }

  setChartDimensions() {
    this.r = 0.7 * this.spiderChartConfig.size,
    this.r_0 = this.r / 2
  }

  drawScale(){
    this.radialScale = d3.scaleLinear()
       .domain([0, 100])
       .range([0, this.r_0])   
  }

  generateAndDrawLevels() {
    this.parentGroupElem.selectAll("circle")
      .data(this.genTicks())
      .join(
        enter => enter.append("circle")
          .attr("cx", this.width / 2)
          .attr("cy", this.height / 2)
          .attr("fill", "none")
          .attr("stroke", this.spiderChartConfig.levelsStrokeColor)
          .attr("r", d => this.radialScale(d))
      );
  }

  genTicks = (): number[] => {
    const step = 100 / 5
    for (let i = 0; i <= 5; i++) {
      const num = step * i;       
        this.ticks.push(Math.round(num));
    }
    return this.ticks;
  };

  generateTicks() {
    let angle = (Math.PI / 2) + (2 * Math.PI * 0.54 / this.chartData.length);
    this.parentGroupElem.selectAll(".ticklabel")
      .data(this.ticks)
      .join(
        enter => {
          const newTick = enter.append("g").classed("tickNewClass", true);
          newTick.append("circle").classed("node", true)
          .attr("cx", (d,i) => {
            if(i >= 0){
              let line_coord = this.angleToCoordinate(angle, d - 4)
              return line_coord.x + 2
            }
            
          })
          .attr("cy", (d,i) => {
            if(i >= 0){
              let line_coord = this.angleToCoordinate(angle, d - 4)
              return line_coord.y - 5
            }  
          })
          .each(function(d) {
            if (d !== 0) {
              d3.select(this)
                .attr('r', '9px')
                .attr("fill", "#FFF")
                .attr("stroke", "rgba(0,0,0,0.9)")
                .attr("stroke-width", 1)
                .attr("stroke-opacity", 0.1);
            }
          });
          newTick.append("text")
          .attr("class", "ticklabel")
          .attr("x", d => {
            let line_coord = this.angleToCoordinate(angle, d + 1)
            return line_coord.x
          })
          .attr("y", d => {
            let line_coord = this.angleToCoordinate(angle, d - 2)
            return line_coord.y
          })
          .text(d => {
            return d;
          })
          .attr("font-size", "7px")
          .attr("fill",d => d !== 0 ?"#6D7692" : "#FFF")
          .attr("font-family","Montserrat")
          .attr("font-weight",600)
        });
  }

  showEllipsis(text) {    
    text.each(function() {
         var text = d3.select(this);
         var words = text.text().split(/\s+/);       
         var ellipsis = text.text('').append('tspan').attr('class', 'elip').text('...');
         var width = parseFloat(text.attr('width')) - ellipsis.node().getComputedTextLength();
         var numWords = words.length;
         var tspan = text.insert('tspan', ':first-child').text(words.join(' '));
         
         while (tspan.node().getComputedTextLength() > width && words.length) {
             var newStr = words[0].substring(0,9)
             words.pop();
             tspan.text(newStr);
         }
         
         if (words.length === numWords) {
             ellipsis.remove();
         }
     });
     
 }

  angleToCoordinate(angle, value) {
    let x = Math.cos(angle) * this.radialScale(value);
    let y = Math.sin(angle) * this.radialScale(value);
    return { "x": this.width / 2 + x, "y": this.height / 2 - y, value: value };
  }

  drawLine() {
    let featureData = this.chartData.map((f, i) => {
      let angle = (Math.PI / 2) + (2 * Math.PI * i / this.chartData.length);
      return {
        "name": f.name,
        "angle": angle,
        "line_coord": this.angleToCoordinate(angle, 100),
        "innerCirclecoord": this.angleToCoordinate(angle, 20)
      };

    });

    this.parentGroupElem.selectAll("line")
      .data(featureData)
      .join(
        enter => enter.append("line")
          .attr("x1", d => d.innerCirclecoord.x)
          .attr("y1", d => d.innerCirclecoord.y)
          .attr("x2", d => d.line_coord.x)
          .attr("y2", d => d.line_coord.y)
          .attr("stroke", this.spiderChartConfig.gridLineColor)
      );
  }

  getPathCoordinates(data_point,angle) {    
    let coordinates = {};  
    coordinates = this.angleToCoordinate(angle, data_point.value);
    this.drawCircles(coordinates)
    return coordinates;
  }


  drawPath(points, parent) {
    let line = d3.line()
      .x((d: any) => d.x)
      .y((d: any) => d.y);
      parent.append("path")
      .attr("d", line(points))
      .attr("fill", 'url(#line-gradient)')
         
    }

  drawData = (dataset) => {
    const points: any = [];
    dataset.forEach((d, i) => {
      let angle = (Math.PI / 2) + (2 * Math.PI * i / dataset.length);  
      points.push(
        {
          ...this.getPathCoordinates(d,angle),
        });
    });

    const group = this.parentGroupElem.append("g")

    this.drawPath([...points], group);
    this.drawCircles(points);
  };

  drawCircles = (points) => {
    const tooltip = d3.select( ".tooltip" );
    const mouseover = d =>    
    {     
        tooltip.style( "opacity", 1 );
        const { x, y, value } = d;
        tooltip
        .style("top", (d3.event.y + 10)+"px")
        .style("left",(d3.event.x)+"px")
        tooltip.text( d.value );
    };
  
      const mouseLeave = d =>
      {
          tooltip.style( "opacity", 0 );
      };

    
    this.parentGroupElem.append("g")
      .style("fill", this.spiderChartConfig.dataPointCircleColor)
      .selectAll("circle")
      .data(points)
      .enter()
      .append("circle")
      .attr("cx", (d: any) => d.x)
      .attr("cy", (d: any) => d.y)
      .attr("r", this.spiderChartConfig.radiusForDataPointsCircle)
      .on( "mouseenter", mouseover)
      .on( "mouseleave", mouseLeave )
  };

  drawAxisLabels() {
    const tooltip = d3.select( ".tooltip" );
    const mouseover = d =>    
    {       
        tooltip.style( "opacity", 1 );
        const { x, y } = d.line_coord;
        tooltip
        .style("top", (d3.event.y + 10)+"px")
        .style("left",(d3.event.x)+"px")
        tooltip.text( d.name );
    };
  
      const mouseLeave = d =>
      {
          tooltip.style( "opacity", 0 );
      };
  
    let featureData = this.chartData.map((f, i) => {
      let angle = (Math.PI / 2) + (2 * Math.PI * i / this.chartData.length);
      return {
        "name": f.name,
        "angle": angle,
        "line_coord": this.angleToCoordinate(angle, 130)
      };
    });
    this.parentGroupElem.selectAll(".axislabel")
      .data(featureData)
      .join(
        enter => enter.append("text")
        .attr("class", "showellipsis")
        .attr("id", "circleCustomTooltip")
          .attr("x", ( d,i) => {       
            if(i == 1){
            return d.line_coord.x - 28
            }
            else{
              return d.line_coord.x - 22
            }
          })
          .attr("y",( d,i) => {
            if(i == 0){
            return d.line_coord.y + 15
            }
            else{
              return d.line_coord.y
            }
          })
          .attr("width","70px")
          .text(d => d.name)
          .attr('font-size', '10px')
          .attr('font-family', 'Montserrat')
          .attr('font-weight',500)
          .attr('fill',this.spiderChartConfig.labelsTextColor)
          .style("cursor", "pointer")
          .on( "mouseenter", mouseover)
          .on( "mouseleave", mouseLeave )
      ); 
  }

}