import { NgModule, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { HttpClientModule, HttpClient, HTTP_INTERCEPTORS } from '@angular/common/http';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { RequestInterceptor } from './offer-picker/request.interceptor';
import { SelectOffersAppComponent } from './offer-picker/select-offers-app/select-offers-app.component';
import { SelectOffersComponent } from './offer-picker/select-offers/select-offers.component';
import { OfferFoldersComponent } from './offer-picker/select-offers/offer-folders/offer-folders.component';
import { OfferListingComponent } from './offer-picker/select-offers/offer-listing/offer-listing.component';
import { OfferVariationsListingComponent } from './offer-picker/select-offers/offer-variations-listing/offer-variations-listing.component';
import { ViewOfferComponent } from './offer-picker/select-offers/view-offer/view-offer.component';
import { OfferVariationComponent } from './offer-picker/select-offers/offer-variation/offer-variation.component';
import { OfferSelectionComponent } from './offer-picker/select-offers/offer-selection/offer-selection.component';
import { RouterModule } from '@angular/router';
import { GridsterModule } from 'angular-gridster2';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CdkStepperModule } from '@angular/cdk/stepper';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { HclAngularWidgetsLibModule } from 'hcl-angular-widgets-lib';
import { HclDataGridModule } from 'hcl-data-grid-lib';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { A11yModule } from '@angular/cdk/a11y';
import { NgxTributeModule } from 'ngx-tribute';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { PickerModule } from '@ctrl/ngx-emoji-mart';
import { AgGridModule } from 'ag-grid-angular';
import { FroalaEditorModule, FroalaViewModule } from 'angular-froala-wysiwyg';
import 'froala-editor/js/plugins.pkgd.min.js';
import 'froala-editor/js/languages/de.js';
import 'froala-editor/js/languages/en_gb.js';
import 'froala-editor/js/languages/es.js';
import 'froala-editor/js/languages/fr.js';
import 'froala-editor/js/languages/it.js';
import 'froala-editor/js/languages/ja.js';
import 'froala-editor/js/languages/ko.js';
import 'froala-editor/js/languages/pt_br.js';
import 'froala-editor/js/languages/ru.js';
import 'froala-editor/js/languages/zh_cn.js';
import 'froala-editor/js/languages/zh_tw.js';
import { OfferListListingComponent } from './offer-picker/select-offers/offer-list-listing/offer-list-listing.component';
import { ViewOfferListComponent } from './offer-picker/select-offers/view-offer-list/view-offer-list.component';
import { OfferCardViewComponent } from './offer-picker/select-offers/offer-card-view/offer-card-view.component';
import { OfferCardListComponent } from './offer-picker/select-offers/offer-card-view/offer-card-list/offer-card-list.component';
import { OfferlistCardViewComponent } from './offer-picker/select-offers/offerlist-card-view/offerlist-card-view.component';
import { OfferlistCardListComponent } from './offer-picker/select-offers/offerlist-card-view/offerlist-card-list/offerlist-card-list.component';
import { OfferlistLpCardListComponent } from './offer-picker/select-offers/offerlist-card-view/offerlist-lp-card-list/offerlist-lp-card-list.component';
import { OfferlistLpOffersDetailsComponent } from './offer-picker/select-offers/offerlist-card-view/offerlist-lp-card-list/offerlist-lp-offers-details/offerlist-lp-offers-details.component';

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  declarations: [
    SelectOffersAppComponent,
    SelectOffersComponent,
    OfferFoldersComponent,
    OfferListingComponent,
    OfferListListingComponent,
    OfferVariationsListingComponent,
    ViewOfferComponent,
    OfferVariationComponent,
    OfferSelectionComponent,
    ViewOfferListComponent,
    OfferCardViewComponent,
    OfferCardListComponent,
    OfferlistCardViewComponent,
    OfferlistCardListComponent,
    OfferlistLpCardListComponent,
    OfferlistLpOffersDetailsComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    GridsterModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    CdkStepperModule,
    HclAngularWidgetsLibModule,
    HclDataGridModule,
    DragDropModule,
    ScrollingModule,
    A11yModule,
    NgxTributeModule,
    NgxSkeletonLoaderModule,
    PickerModule,
    FroalaEditorModule.forRoot(),
    FroalaViewModule.forRoot(),
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      },
      isolate: false
    }),
  ],
  exports: [
    SelectOffersAppComponent,
    SelectOffersComponent,
    OfferFoldersComponent,
    OfferListingComponent,
    OfferListListingComponent,
    FroalaEditorModule,
    FroalaViewModule,
    OfferVariationsListingComponent,
    ViewOfferComponent,
    OfferVariationComponent,
    OfferSelectionComponent,
    TranslateModule,
    ViewOfferListComponent,
    OfferCardViewComponent,
    OfferCardListComponent,
    OfferlistCardViewComponent,
    OfferlistCardListComponent,
    OfferlistLpCardListComponent,
    OfferlistLpOffersDetailsComponent
  ],
  providers: [DatePipe,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: RequestInterceptor,
      multi: true
    }]
})
export class HclOfferLibModule { }
