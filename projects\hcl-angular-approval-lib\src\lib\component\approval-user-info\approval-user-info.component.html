<div class="w-100 approval-user-info-container">
    <div class="info-title mb-2">{{ 'APPROVALPICKER.TITLES.SENT_BY' | translate }}</div>
    <ul class="owner-list" *ngIf="approvalOwners.length > 0">
        <li *ngFor="let owner of approvalOwners; let index=index">
            <div class="owner">{{owner?.user?.nameWithTimeZone}}
                <span>({{owner?.role}})</span>
            </div>
        </li>
    </ul>
    <div class="no-data-message" *ngIf="approvalOwners.length === 0">
        {{ 'APPROVALPICKER.TITLES.NO_RESULT_FOUND' | translate }}</div>

    <div class="info-title mb-2">{{ 'APPROVALPICKER.TITLES.APPROVERS' | translate }}</div>
    <ul class="approver-list" *ngIf="approvalApprovers.length > 0">
        <li *ngFor="let approver of approvalApprovers; let index=index">
            <div class="approver">{{approver?.user?.nameWithTimeZone}}
                <span>({{approver?.role}})</span>
            </div>
        </li>
    </ul>
    <div class="no-data-message" *ngIf="approvalApprovers.length === 0">
        {{ 'APPROVALPICKER.TITLES.NO_RESULT_FOUND' | translate }}</div>
</div>