<div class="variant-outer-container">
    <div *ngIf="!selectOffersService.directPathAccess" (click)="loadOffersListing($event)" class="toggle-selection">
        <h3 [ngClass]="{'reset-transform-rotate': verticalLocale()}">{{'OFFER_PICKER.TITLES.SELECT_OFFERS'| translate}}
        </h3>
    </div>
    <div class="variant-inner-container">
        <div class="variant-container-heading">
            <div class="variant-container-label">
                <span *ngIf="!selectOffersService.directPathAccess"> {{'OFFER_PICKER.TITLES.SELECT_VARIANT'|
                    translate}}</span>
            </div>
        </div>
        <div class="variation-container">
            <div *ngIf="variationPanelState !== 3" class="variation-listing-container"
                [ngClass]="{'variation-panel-minimized': variationPanelState == 1, 'variation-panel-normal': variationPanelState == 2}">
                <div class="top-panel">
                    <span class="offer-logo ellipsis"
                        *ngIf="selectOffersService.loadVariationType !== 'viewMaster' && selectOffersService.getVariantDataForVariantSelection() && selectOffersService.getVariantDataForVariantSelection().displayName">{{
                        selectOffersService.getVariantDataForVariantSelection().displayName }}</span>
                    <span class="offer-logo ellipsis" *ngIf="selectOffersService.loadVariationType === 'viewMaster'">
                        {{selectOffersService.offerName}}
                    </span>
                    <div class="max-min-icon-container">
                        <div class="expand-collapse-icon-container">
                            <div class="expand-collapse-seperator">
                            </div>
                            <div class="max-min-button-container">
                                <div class="min-button-container" *ngIf="variationPanelState == 2">
                                    <hcl-button [config]="minimizeVariationsIconButton"
                                        (onclick)="minimizeVariationsList($event)">
                                    </hcl-button>
                                </div>
                                <div class="max-button-container" *ngIf="variationPanelState == 1">
                                    <hcl-button [config]="maxVariationsIconButton"
                                        (onclick)="maximizeVariationsList($event)">
                                    </hcl-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="vertical-label" *ngIf="variationPanelState == 1">
                    <span [ngClass]="{'reset-transform-rotate': verticalLocale()}"
                        *ngIf="selectOffersService.loadVariationType !== 'viewMaster' && selectOffersService.getVariantDataForVariantSelection() && selectOffersService.getVariantDataForVariantSelection().displayName">{{
                        selectOffersService.getVariantDataForVariantSelection().displayName }}</span>
                </div>
                <div class="variations-inner-container">
                    <hcl-offer-variations-listing (reload)="reload($event)" (disableSelect)="disableSelect($event)">
                    </hcl-offer-variations-listing>
                </div>
            </div>
            <ng-container *ngIf="load">
                <div *ngIf="selectOffersService.loadVariationType === 'viewMaster' || selectOffersService.loadVariationType === 'viewVariation'"
                    class="offer-summery-container" [ngClass]="{'offer-panel-normal': variationPanelState == 2, 'offer-panel-maximized': variationPanelState == 1,
                'offer-panel-maximized2': variationPanelState == 3}">
                    <hcl-view-offer></hcl-view-offer>
                </div>
            </ng-container>
        </div>
        <div class="offer-actions">
            <hcl-button [config]="cancelActionsConf" (onclick)="loadOffersListing($event)" class="action-button">
            </hcl-button>
            <hcl-button *ngIf="!selectOffersService.directPathAccess" [config]="selectVariantButtonConf"
                (onclick)="selectedVariantOrOffer($event)" class="action-button">
            </hcl-button>
        </div>
    </div>
</div>