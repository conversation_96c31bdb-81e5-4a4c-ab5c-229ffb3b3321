<div class="folder-container"
    [ngClass]="{ 'folder-default-state' : folderPanelState === 'DEFAULT', 'folder-minimized-state': folderPanelState === 'MINIMIZED'}">
    <div class="hcl-card">
        <div class="hcl-card-body">
            <div class="folder-header clearfix">
                <div class="folder-list-label float-left"
                    *ngIf="foldersConfig.folderPanelHeader && folderPanelState === 'DEFAULT'">
                    <span>{{ foldersConfig.folderPanelHeader }}</span>
                </div>
                <div class="icon-container float-right">
                    <div class="line-seperator" *ngIf="folderPanelState === 'DEFAULT'"></div>
                    <div class="nav-icon-container">
                        <i class="hcl-icon-left-without-border" *ngIf="folderPanelState === 'DEFAULT'"
                            (click)="updateFolderPanelState('MINIMIZED')"></i>
                        <i class="hcl-icon-right-without-border" *ngIf="folderPanelState === 'MINIMIZED'"
                            (click)="updateFolderPanelState('DEFAULT')"></i>
                    </div>
                </div>
            </div>
            <div [ngStyle]="{'display':folderPanelState === 'DEFAULT' ? 'block' : 'none' }" class="folder-body">
                <hcl-folder-v2 #folderComponent (componentReady)="folderComponentReadyCallback($event)"
                    [config]="foldersConfig" (folderClicked)="folderClicked($event)">
                </hcl-folder-v2>
            </div>
            <div *ngIf="folderPanelState === 'MINIMIZED'" class="w-100 h-100 minimized-container">
                <span class="folder-label-minimized">{{ foldersConfig.folderPanelHeader }}</span>
            </div>
        </div>
    </div>
</div>