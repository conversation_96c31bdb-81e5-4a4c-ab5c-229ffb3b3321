<div class='w-100 unsubscribe-channels-section' [ngStyle]="getParentStyles()">
  <div class="d-flex" [ngStyle]="getTextStyles()">
    <div class="d-flex contact-details" >
      <i class="hcl-icon-mail pr-1"></i>
      <div>{{field.emailPf ? field.emailPf : 'Choose your email'}}</div>
    </div>
    <div class="d-flex contact-details">
      <i class="hcl-icon-mobile"></i>
      <div>{{field.phoneNumberPf ? field.phoneNumberPf : 'Choose your phone'}}</div>
    </div>
  </div>
  <mat-error *ngIf="field.errors && field.errors.length > 0">
    <div class="error-container">
      <span class="hcl-icon-error" [matTooltip]="fetchComponentErrorMessages()" [matTooltipClass]="'multiline-tooltip'"></span>
    </div>
  </mat-error>
</div>