import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import { Component, ElementRef, EventEmitter, Input, NgZone, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Subscription, SubscriptionLike } from 'rxjs';
import { filter, map, pairwise, throttleTime } from 'rxjs/operators';

import { Offer, OfferListGetResponse } from '../../select-offers.model';
import { OfferDataService } from '../../../offer-data.service';
import { ButtonConf, ModalConfig, ModalService, HclAssetPickerService, CheckboxConfig } from 'hcl-angular-widgets-lib';

@Component({
  selector: 'hcl-offerlist-card-list',
  templateUrl: './offerlist-card-list.component.html',
  styleUrls: ['./offerlist-card-list.component.scss']
})
export class OfferlistCardListComponent implements OnInit {
  @ViewChild('scrollViewport') scrollViewport: CdkVirtualScrollViewport;
  @Input() offerlistResponseData: any;
  @Input() isDataLoading: boolean;
  @Input() selectedOfferlistList: any[];
  @Input() isSummaryPage: boolean;
  @Input() chunkSize: number;
  @Output() loadNewOfferlists: EventEmitter<number> = new EventEmitter();
  @Output() moveOfferlistData: EventEmitter<any> = new EventEmitter<any>();
  @Output() offerlistSelected: EventEmitter<any> = new EventEmitter<any>();
  @Output() moveOfferList: EventEmitter<any> = new EventEmitter<any>();

  subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();

  yesRetireActionConfig: ButtonConf;
  yesDeleteActionConfig: ButtonConf;
  noActionConfig: ButtonConf;
  okActionConfig: ButtonConf;
  retireOfferListDialogConfig: ModalConfig;
  deleteOfferListDialogConfig: ModalConfig;
  offerlistPermissionSubscription: Subscription;
  offerlistViewPermissionSubscription: Subscription;
  offerListId: number;
  loaderItems = [];
  constructor(
    private zone: NgZone,
    public router: Router,
    public modalService: ModalService,
    private translate: TranslateService,
    public offerDataService: OfferDataService,
    private hclAssetPickerService: HclAssetPickerService,
  ) { }

  ngAfterViewInit(): void {
    this.scrollViewport.elementScrolled().pipe(
      map(() => this.scrollViewport.measureScrollOffset('bottom')),
      pairwise(),
      filter(([y1, y2]) => (y2 < y1 && y2 < 100)),
      throttleTime(500)
    ).subscribe(() => {
      this.zone.run(() => {
        this.loadMoreOfferlists();
      });
    }
    );
  }

  ngOnInit(): void {
    this.setConfiguration();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['chunkSize'] && changes['chunkSize'].currentValue) {
      this.setLoaderItems();
    }
  }

  setLoaderItems() {
    this.loaderItems = [];
    for (let i = 0; i < 4; i++) {
      const dummyItems = '1'.repeat(this.chunkSize).split('');
      this.loaderItems.push(dummyItems);
    }
  }

  setConfiguration() {
    this.yesRetireActionConfig = {
      name: 'retire',
      value: this.translate.instant('BUTTONS.RETIRE'),
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };

    this.yesDeleteActionConfig = {
      name: 'delete',
      value: this.translate.instant('BUTTONS.DELETE'),
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };

    this.noActionConfig = {
      name: 'no',
      value: this.translate.instant('BUTTONS.CANCEL'),
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };

    this.okActionConfig = {
      name: 'ok',
      value: this.translate.instant('BUTTONS.OK'),
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };
  }



  // set checkbox config of current asset
  public setAssetCheckboxConf(item: any): CheckboxConfig {
    const checkboxConf: CheckboxConfig = {
      type: 'single',
      formControl: new UntypedFormControl(),
      singleCheckboxData: {
        value: item,
        name: 'offerSelection',
        color: 'primary',
        checked: item.isSelected,
        disabled: false
      }
    };
    checkboxConf.formControl.setValue(item.isSelected);
    return checkboxConf;
  }



  /**
  * Thumbnail Image loaded callback event
  * @param event
  * @param offer
  * @param imageElement
  */
  updateOfferThumbnailState(event: string, offer: Offer, imageElement: ElementRef) {
    if (event === 'error' && !offer.isImageLoaded) {
      // check if thumbnail is asset picker item and all properties has value 
      const { url, applicationId, objectType, objectId } = offer.thumbnailProperties;
      this.subscriptionList.push(this.offerDataService.getAssetPickerRepositories().subscribe((repos) => {
        if (repos) {
          const selectedRepoForPreview = repos.find(repo => repo.identifier === applicationId);
          const isAnonymousContent = selectedRepoForPreview && !selectedRepoForPreview.anonymousContent;
          if (isAnonymousContent && url && applicationId && objectType && objectId) {
            const resourceUrl = this.hclAssetPickerService.baseUrl + '/' + applicationId + '/download?resourceId=' + objectId +
              '&resource=' + url;
            this.offerDataService.downloadAssetPickerAnonymousContent(resourceUrl).then(data => {
              offer.isImageLoaded = true;
              imageElement['src'] = data;
            }, error => {
              offer.brokenThumbnail = true;
              offer.isImageLoaded = true;
            });
          } else {
            offer.brokenThumbnail = true;
            offer.isImageLoaded = true;
          }
        } else {
          this.offerDataService.getAndSetAssetPickerInstances(true);
        }
      }));
    } else {
      offer.isImageLoaded = true;
    }
  }

  getOfferCodeWithDelimiter(offerCodeArray) {
    return offerCodeArray.join(this.offerDataService.offerApplicationConfig.offerCodeDelimiter);
  }

  getStatusClass(offer: Offer) {
    const stateClass = offer.state === "PUBLISHED" ? 'published' : offer.state === "RETIRED" ? 'retired' : 'draft';
    return stateClass;
  }

  getStatusCopy(offer: Offer) {
    const stateClass = offer.state === "PUBLISHED" ? this.translate.instant('TITLES.PUBLISHED') :
      offer.state === "RETIRED" ? this.translate.instant('LIST_OFFER_TEMPLATES.HEADERS.RETIRED') : this.translate.instant('TITLES.DRAFT');
    return stateClass;
  }

  /*
   Check if user scroll has reached to bottom and data available in DB and trigger next page content
  */
  loadMoreOfferlists(): void {
    if (!this.isDataLoading) {
      if ((this.offerlistResponseData.page.pageNumber + 1) < this.offerlistResponseData.page.totalPages) {
        this.loadNewOfferlists.emit(this.offerlistResponseData.page.pageNumber + 1);
      }
    }
  }
}
