import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { DonutChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/config/chart-config';
import { DonutChartComponent } from 'projects/hcl-angular-charts-lib/src/lib/component/donut-chart/donut-chart.component';


@Component({
  selector: 'app-d3-donut-chart-demo',
  templateUrl: './d3-donut-chart-demo.component.html',
  styleUrls: ['./d3-donut-chart-demo.component.scss']
})
export class D3DonutChartDemoComponent implements OnInit, AfterViewInit {

  @ViewChild('progressD3DonutChart', { static: true }) progressD3DonutChart:DonutChartComponent;

  constructor() { }

  donutChartdata = [{label: "Nurturing (1.1%) 10,732", color: "blue", value: 10723}, 
  {label: "Keep Sending (42.6%) 4,15,287", color: "green", value: 415287}]

  donutChartConfig : DonutChartConfig = {
    chartID: `d3_donut_chart1`,
    height : 260,
    width: 260,
    outerRadius: 120,
    innerRadius: 70,
    data: [ {tooltipValueLabel: "1.1% (10,732)", tooltipHeadingLabel: "Nurturing", color: "blue", value: 10723, tooltipDescriptionLabel: "Keep sending emails and nurturing these newer contacts. They are not included in your Dark Pool."}, 
            {tooltipValueLabel: "42.6% (4,15,287)", tooltipHeadingLabel: "Keep Sending", color: "green", value: 415287, tooltipDescriptionLabel: "Keep sending emails. Contacts are healthy and responding."},
            {tooltipValueLabel: "5.2% (50,692)", tooltipHeadingLabel: "Investigate", color: "orange", value: 50692, tooltipDescriptionLabel: "Investigate. Contacts somewhat fatigued. Good chance they will re-engage."}, 
            {tooltipValueLabel: "23.1% (2,25,191)", tooltipHeadingLabel: "Throttle and Investigate", color: "brown", value: 225191, tooltipDescriptionLabel: "Send fewer emails. Contacts are fatigued. Slight chance they will re-engage."}, 
            {tooltipValueLabel: "23.2% (2,26,166)", tooltipHeadingLabel: "Stop Sending and Shift to a Different Channel", color: "red", value: 226166, tooltipDescriptionLabel: "Stop sending emails. Try another chanel besides email."}, 
            {tooltipValueLabel: "3.0% (29,246)", tooltipHeadingLabel: "Remove", color: "black", value: 29246, tooltipDescriptionLabel: "Contacts are dark. Stop sending emails and remove them from the database."}, 
          ],
    middleText: "Total \n9,57,305 \nContacts",
    middleTextFontSize: "27",
    tooltipHTML: ({data}) => {
      return `<div class="donut-chart-tooltip-container">
                  <div class="donut-chart-tooltip-row heading">
                      <span class="donut-chart-tooltip-header">${data.tooltipHeadingLabel}</span>
                  </div>
                  <div class="donut-chart-tooltip-row value">
                      <span class="donut-chart-tooltip-value">${data.tooltipValueLabel}</span>
                  </div>
                  <div class="donut-chart-tooltip-row description">
                      <span class="donut-chart-tooltip-description">${data.tooltipDescriptionLabel}</span>
                  </div>
              </div>`;
    },
    showTooltip: true,
    returnValue: (d: any) => {
      return d.value;
    }
  }

  donutChartProgressConfig : DonutChartConfig = {
    chartID: `d3_donut_chart2`,
    height : 260,
    width: 260,
    outerRadius: 120,
    innerRadius: 70,
    data: [ 
            {tooltipValueLabel: "53,070", tooltipHeadingLabel: "Failed", color: "red", value: 150000, tooltipDescriptionLabel: "Failed in sending mails to the recipients from the OLT"}, {tooltipValueLabel: "11,00,000 ", tooltipHeadingLabel: "Sent", color: "blue", value: 1300000, tooltipDescriptionLabel: "Successfully sent mails to the recipients from the OLT"}
          ],
    middleText: "80% \nIn Progress",
    middleTextFontSize: "27",
    showTooltip: true,
    tooltipHTML: ({data}) => {
      return `<div class="donut-chart-tooltip-container">
                  <div class="donut-chart-tooltip-row heading">
                      <span class="donut-chart-tooltip-header">${data.tooltipHeadingLabel}</span>
                  </div>
                  <div class="donut-chart-tooltip-row value">
                      <span class="donut-chart-tooltip-value">${data.tooltipValueLabel}</span>
                  </div>
                  <div class="donut-chart-tooltip-row description">
                      <span class="donut-chart-tooltip-description">${data.tooltipDescriptionLabel}</span>
                  </div>
              </div>`;
    },
    returnValue: (d: any) => {
      return d.value;
    },
    progressChart: true
  }

  ngOnInit() {

  }

  ngAfterViewInit(): void {
    window.setTimeout(() => {
      this.donutChartProgressConfig.middleText = "100% \nComplete";
      this.progressD3DonutChart.updateChartData([
      {tooltipValueLabel: "2,00,000", tooltipHeadingLabel: "Failed", color: "red", value: 200000, tooltipDescriptionLabel: "Failed in sending mails to the recipients from the OLT"}, {tooltipValueLabel: "14,00,000 ", tooltipHeadingLabel: "Sent", color: "blue", value: 1400000, tooltipDescriptionLabel: "Successfully sent mails to the recipients from the OLT"}]);
    }, 3000)
  }

}