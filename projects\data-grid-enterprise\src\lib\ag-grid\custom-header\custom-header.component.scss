.header-default-container{
  .column-header-label {
    font-family: Mont<PERSON><PERSON>-bold, <PERSON><PERSON><PERSON>, sans-serif;
    font-weight: 600;
    font-size: 16px;
    align-items: center;
    color: #6D7692;
    letter-spacing: 0.2px;
  }
  .icon-container{
    margin-left: 6.5px;
    i{
      line-height: 0;
      display: block;
      font-size: 13.5px;
      margin-top: -6px;
      &.hcl-icon-up-dir{
        margin-top: 0px;
      }
    }
  }
}

.filter-icon {
  height: 3.25px;
  width: 3.25px;
  margin-left: 15px;
  cursor: pointer;
}

.hcl-custom-popover {
  position: fixed;
  z-index: 1000;
  width: 0;
  height: 0;
}

.triangle {
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid black;
}

.popOver {
  z-index: 1000;
  position: fixed;
  right: 60%;
  left: 0;
  top: 20%;
  margin-right: auto;
  margin-left: auto;
  min-height: 200px;
  width: 90%;
  max-width: 250px;
  background-color: #fff;
  padding: 12px;
  border: solid;
  border-width: 1px;
  border-color: lightgrey;
  height: auto;
}

.tabMenu {
  list-style: none;
  display: flex;
}

.tabList {
  width: 50%;
  cursor: pointer
}

.inputRadio {
  margin: 5% 0 5% 6%
}

.popupBtn {
  width: auto;
  display: inline;
  font-family: inherit;
  font-size: inherit;
  padding: none;
  width: auto;
  color: blue;
  background: white;
  border: none;
  outline: none
}

.btnDiv {
  display: inline-flex;
  width: 100%
}
