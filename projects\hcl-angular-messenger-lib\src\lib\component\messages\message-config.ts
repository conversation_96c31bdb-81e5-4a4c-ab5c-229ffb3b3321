/**
 * The configuration of the message can be defined in this configuration
 */
export interface MessageConfig {
  //This flag tells if the message is ent by the current user or not
  // default value is false
  isCurrentUser?: boolean;
  // this flag will tell if this message was edited, default is false
  isEdited?: boolean;
  // the details of the user
  userName: string;
  // the time when this message was sent
  timeSent?: Date;
  // the actual message that was sent
  message: string;
  // a unique id for each message
  id: number;
  // the status of the message
  // 1 >> delivered & read
  // 2 >> delivered
  // 3 >> not yet delivered
  // 4 >> failure
  status: 1 | 2 | 3 | 4;
}
