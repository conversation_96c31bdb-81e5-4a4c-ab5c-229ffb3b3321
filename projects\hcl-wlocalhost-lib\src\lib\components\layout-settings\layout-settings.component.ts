import {
  Component,
  ViewEncapsulation,
  ChangeDetectionStrategy, Output, EventEmitter
} from '@angular/core';
import { ILayout } from '../../interfaces';
import { BehaviorSubject } from 'rxjs';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import { getDefaultColumnWidthObj } from '../../utils';
import { TLayouts } from '../../classes/Layouts';

@Component({
  selector: 'ip-layout-settings',
  templateUrl: './layout-settings.component.html',
  styleUrls: ['./layout-settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class LayoutSettingsComponent {
  layout: BehaviorSubject<ILayout>;
  @Output() uploadImage: EventEmitter<any> = new EventEmitter();
  @Output() selectLandingPage: EventEmitter<any> = new EventEmitter();


  constructor(ngjs: IpEmailBuilderService) {
    this.layout = ngjs.currentEditingLayout$;
    this.layout.getValue().options?.columns?.forEach((col) => {
      if (!col.width) {
        col.width = getDefaultColumnWidthObj()
      }
    })
  }

  get disableResponsive() {
    return this.layout.getValue().options.disableResponsive;
  }

  set disableResponsive(state: boolean) {
    this.layout.value.options.disableResponsive = state;
  }

  uploadImageFromPicker() {
    this.uploadImage.emit();
  }

  selectLandingPageFromPicker() {
    this.selectLandingPage.emit();
  }

  emptyDownloadDetails() {
    const valueObject: any = this.layout.getValue();
    valueObject.downloadDetails = null;
  }
  
  // get gaps() {
  //   return this.layout.getValue().options.gaps;
  // }

  // set gaps(gaps: number) {
  //   this.layout.value.options.gaps = gaps;
  // }
}
