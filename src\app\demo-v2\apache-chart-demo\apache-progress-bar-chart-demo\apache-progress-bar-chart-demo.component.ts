import { Component, OnInit, ViewChild } from '@angular/core';
import { ApacheChartComponent } from 'projects/hcl-angular-charts-lib/src/public-api';
import { ApacheColumnChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/apache-chart/apache-chart';

@Component({
  selector: 'app-apache-progress-bar-chart-demo',
  templateUrl: './apache-progress-bar-chart-demo.component.html',
  styleUrls: ['./apache-progress-bar-chart-demo.component.scss']
})
export class ApacheProgressBarChartDemoComponent implements OnInit {

  @ViewChild('apacheProgressBarChart') apacheProgressBarChart: ApacheChartComponent;

  apacheProgressBarChartConfig: ApacheColumnChartConfig = {
    title: {
      id: 'progressBar',
      title: 'Progress Bar Chart',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      show: false,
      max: 100
    },
    yAxis: {
      type: 'category',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      }
    },
    series: [
      {
        name: '2011',
        type: 'bar',
        data: [58,89],
        barWidth: '50',
        showBackground: true,
        label:{
          show: true,
          position: 'inside'
        },
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]

  }
  constructor() { }

  ngOnInit(): void {
  }

}
