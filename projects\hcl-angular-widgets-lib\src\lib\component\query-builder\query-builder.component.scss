$level0-ruler: #0078d8;
$level1-ruler: #f5821e;
$level2-ruler: #c4d056;
$level3-ruler: #7d5aa6;
$level4-ruler: #ddbb4d;
$level5-ruler: #444444;

.query-builder {
  height: calc(100% - 230px);
  overflow: auto;
  overflow-x: hidden;

  .static-and-or-label {
    width: 100px;
    position: relative;
    border-bottom: 1px solid #0078d8;
    color: #f5821e;
    font-family: Roboto;
    font-size: 12px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 14px;
    padding-bottom: 10px;
    text-align: center;
  }

  .mat-slide-toggle .mat-slide-toggle-bar .mat-slide-toggle-thumb {
    background-color: #f5821e !important;
  }

  .selectedToggle {
    color: #f5821e;
  }

  .parent-height {
    height: calc(100% - 100px);
    overflow: auto;
  }

  .dropdown {
    width: 130px;
    margin-right: 20px;
  }

  .level1 {
    margin-left: 27px;
    margin-top: 8px;
    position: relative;
    &::before {
      top: -1px;
      border-left-style: dotted;
      border-width: 0 0 2px;
    }

    &::before {
      content: "";
      left: -27px;
      top: -95px;
      border-color: #0078d8;
      border-style: solid;
      width: 17px;
      height: calc(100% + 10px);
      position: absolute;
      border-left-style: dotted;
      border-width: 0 0 2px 2px;
    }
  }

  .level2 {
    margin-left: 49px;
    margin-top: 8px;
    position: relative;
    &::before {
      content: "";
      left: -49px;
      top: -102px;
      border-color: #0078d8;
      border-style: solid;
      width: 17px;
      height: calc(100% + 10px);
      position: absolute;
      border-left-style: dotted;
      border-width: 0 0 0 2px;
    }

    &::after {
      content: "";
      left: -22px;
      top: -40px;
      border-color: #f5821e;
      border-style: solid;
      width: 17px;
      height: calc(100% + 10px);
      position: absolute;
      border-left-style: dotted;
      border-width: 0 0 2px 2px;
    }
  }

  .level3 {
    margin-left: 74px;
    margin-top: 8px;
    position: relative;
    &::before {
      content: "";
      left: -74px;
      top: -97px;
      border-color: #0078d8;
      border-style: solid;
      width: 17px;
      height: calc(100% + 10px);
      position: absolute;
      border-left-style: dotted;
      border-width: 0 0 0 2px;
    }

    &::after {
      content: "";
      left: -47px;
      top: -40px;
      border-color: #f5821e;
      border-style: solid;
      width: 17px;
      height: calc(100% + 10px);
      position: absolute;
      border-left-style: dotted;
      border-width: 0 0 0 2px;
    }
  }

  .level4 {
    margin-left: 98px;
    margin-top: 8px;
    position: relative;
    &::before {
      content: "";
      left: -98px;
      top: -89px;
      border-color: #0078d8;
      border-style: solid;
      width: 17px;
      height: calc(100% + 10px);
      position: absolute;
      border-left-style: dotted;
      border-width: 0 0 0 2px;
    }
    &::after {
      content: "";
      left: -71px;
      top: -40px;
      border-color: #f5821e;
      border-style: solid;
      width: 17px;
      height: calc(100% + 10px);
      position: absolute;
      border-left-style: dotted;
      border-width: 0 0 0 2px;
    }
  }

  .level5 {
    margin-left: 122px;
    margin-top: 8px;
    position: relative;
  }

  .ruler {
    position: relative;

    &::before {
      top: -1px;
      border-left-style: dotted;
      border-width: 0 0 2px 2px;
    }

    &::after {
      border-width: 0 0 0 2px;
      border-left-style: dotted;
      top: 50%;
    }

    &::before,
    &::after {
      content: "";
      left: 0px;
      border-color: #ccc;
      border-style: solid;
      width: 17px;
      height: calc(50% + 6px);
      position: absolute;
      border-left-style: dotted;
    }

    &:last-child::after {
      content: none;
    }
  }

  .ruler-row {
    background-color: #d2dfe9;
    color: #444;
  }

  .level0 {
    .ruler {
      &::before,
      &::after {
        border-color: $level0-ruler;
      }
    }
    .rulerow {
      &::after {
        border-color: $level0-ruler;
      }
    }
  }

  .level1 {
    .ruler {
      &::before,
      &::after {
        border-color: $level1-ruler;
      }
    }
  }

  .level2 {
    .ruler {
      &::before,
      &::after {
        border-color: $level2-ruler;
      }
    }
  }

  .level3 {
    .ruler {
      &::before,
      &::after {
        border-color: $level3-ruler;
      }
    }
  }

  .level4 {
    .ruler {
      &::before,
      &::after {
        border-color: $level4-ruler;
      }
    }
  }

  .level5 {
    .ruler {
      &::before,
      &::after {
        border-color: $level5-ruler;
      }
    }
  }

  .subLevel1 {
    // &::before {
    //   content: "";
    //   left: -49px;
    //   top: -102px;
    //   border-color: #0078D8;
    //   border-style: solid;
    //   width: 17px;
    //   height: calc(100% + 10px);
    //   position: absolute;
    //   border-left-style: dotted;
    //   border-width: 0 0 0 2px;
    // }
  }

  .subLevel2 {
    // &::before {
    //   content: "";
    //   left: -49px;
    //   top: -102px;
    //   border-color: #0078D8;
    //   border-style: solid;
    //   width: 17px;
    //   height: calc(100% + 10px);
    //   position: absolute;
    //   border-left-style: dotted;
    //   border-width: 0 0 0 2px;
    // }
  }

  .subLevel3 {
    margin-left: 74px;
    margin-top: 8px;
    position: relative;
    &::before {
      content: "";
      left: -41px;
      top: -130px;
      border-color: #c4d056;
      border-style: solid;
      width: 17px;
      height: calc(100% + 45px);
      position: absolute;
      border-left-style: dotted;
      border-width: 0 0 2px 2px;
    }

    // &::after {
    //   content: "";
    //   left: -41px;
    //   top: -97px;
    //   border-color:#C4D056;
    //   border-style: solid;
    //   width: 17px;
    //   height: calc(100% + 10px);
    //   position: absolute;
    //   border-left-style: dotted;
    //   border-width: 0 0 2px 2px;
    // }
  }

  .subLevel4 {
    margin-left: 98px;
    margin-top: 8px;
    position: relative;
    &::before {
      content: "";
      left: -65px;
      top: -130px;
      border-color: #c4d056;
      border-style: solid;
      width: 17px;
      height: calc(100% + 45px);
      position: absolute;
      border-left-style: dotted;
      border-width: 0 0 0 2px;
    }
    &::after {
      content: "";
      left: -40px;
      top: -116px;
      border-color: #7d5aa6;
      border-style: solid;
      width: 17px;
      height: calc(100% + 40px);
      position: absolute;
      border-left-style: dotted;
      border-width: 0 0 2px 2px;
    }
  }

  .subLevel5 {
    margin-left: 98px;
    margin-top: 8px;
    position: relative;

    &::after {
      content: "";
      left: -40px;
      top: -116px;
      border-color: #ddbb4d;
      border-style: solid;
      width: 17px;
      height: calc(100% + 40px);
      position: absolute;
      border-left-style: dotted;
      border-width: 0 0 2px 2px;
    }
  }

  .hideLevel1 {
    & ~ .level2 {
      &::before {
        display: none !important;
      }
    }

    & ~ .level3 {
      &::before {
        display: none !important;
      }
    }

    & ~ .level4 {
      &::before {
        display: none !important;
      }
    }
  }

  .hideLevel2 {
    & ~ .level3 {
      &::after {
        display: none !important;
      }
    }

    & ~ .level4 {
      &::after {
        display: none !important;
      }
    }
  }

  .hideLevel3 {
    & ~ .level3 {
      // &::after {
      //   display: none !important;
      // }
    }

    & ~ .level4 {
      // &::after {
      //   display: none !important;
      // }
    }

    & ~ .hideLevel4 {
      .subLevel4 {
        &::before {
          display: none !important;
        }
      }
    }
  }

  .hideLevel4 {
    & ~ .level4 {
    }

    & ~ .subLevel4 {
      &::before {
        display: none !important;
      }
    }
  }

  .hideLevel5 {
    & ~ .level5 {
    }

    & ~ .subLevel5 {
      &::before {
        display: none !important;
      }
    }
  }

  .mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-bar {
    background-color: #f5f5f5;
  }

  .mat-slide-toggle-thumb {
    background-color: #f5821e;
  }

  .mat-slide-toggle-bar {
    background-color: #f5f5f5;
    border: 2px solid #959595;
  }

  .mat-slide-toggle-thumb-container {
    top: -5px;
    left: -2px;
  }

  .no-result-found {
    margin-bottom: 10px;
    font-size: 16px;
  }
}
