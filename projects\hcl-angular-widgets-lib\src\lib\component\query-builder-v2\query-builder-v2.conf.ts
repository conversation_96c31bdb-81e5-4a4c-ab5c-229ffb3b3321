import { DatePickerConfig } from "../date-picker/date-picker-config";
import { TemplateRef } from "@angular/core";
import { SuggestionDirectiveConfig } from "../../directive/hcl-suggestion-config";

export interface QueryBuilderV2Conf {
  columnFields: ColumnFieldV2[];
  dynamicColumnFields?: any;
  conditionConfigStringType: {
    label: any;
    value: any;
  }[];
  conditionConfigNumberType?: {
    label: any;
    value: any;
  }[];
  conditionConfigDateType?: {
    label: any;
    value: any;
  }[];
  conditionConfigBitType?: {
    label: any;
    value: any;
  }[];
  conditionConfigExpressionType?: {
    label: any;
    value: any;
  }[];
  conditionConfigBooleanType?: {
    label: any;
    value: any;
  }[];
  conditionConfigAllType?: {
    label: any;
    value: any;
  }[];
  conditionConfigSegmentType?: {
    label: any;
    value: any;
  }[];
  dynamicValues?: any[];
  dropdownColumns?: any[];
  folderColumns?: any[];
  columnTypes?: ColumnType[];
  isHideSwitch?: boolean;
  showDatePicker?: boolean;
  timeZone?: any;
  jsonData?: any;
  emitQueryJsonOnFormChange?: boolean;
  keepOneRulePresent?: boolean;
  enableExpressionBuilder?: boolean;
  disableQueryBuilder?: boolean;
  dateConfigObj?: {
    dateConfig?: DatePickerConfig;
    dateLocaleJson?: any;
    dateFormat?: string;
    datePipeFormat?: string;
    showTime?: boolean;
  };
  needGroupName?: boolean;
  translations: {
    columntypePlaceHolder?: string;
    cancelModalBtnLabel?: string;
    deleteGroupModalMsg?: string;
    deleteNestedGroupsModalMsg?: string;
    deleteGroupLabel?: string;
    areYouSure?: string;
    addRuleLabel?: string;
    addGroupLabel?: string;
    andLabel?: string;
    orLabel?: string;
    noResultFoundMsg?: string;
    filterFieldsPlaceholder?: string;
    fieldNamePlaceHolder?: string;
    conditionPlaceHolder?: string;
    conditionValuePlaceHolder?: string;
    requiredField: string;
    fieldOptionsDidNotLoad?: string;
    add?: string;
    valueAlreadyPresent?: string;
    toLabel?: string;
    more?: string;
    close?: string;
    invalidDigitsRangeMsg?: string;
    invalidDateFormat?: string;
    dynamicPlaceholder?: string;
    selectFolderPlaceHolder?: string;
    folderName?: string;
    save?: string;
    delete?: string;
    deleteExpressionLabel?: string;
    deleteExpressionModalMsg?: string;
    expressionInputTitle?: string;
    expressionInputHelpMsg?: string;
    checkSyntaxBtnLabel?: string;
    validExpression?: string;
    deleteGroupsLabel?: string;
    expressionBuilderHeader?: string;
    expressionBuilderBodyContent?: string;
    noRuleHeader?: string;
    noRuleContext?: string;
    subgroupLabel?: string;
    ruleLabel?: string;
    firstLevelWarning?:string;
    viewProfile?: string;
  };
  expressionBuilderPanelConfig?: {
    isChildSidePanel?: boolean;
    parentTitle?: string;
    headerTemplate?: TemplateRef<any>;
    contentTemplate?: TemplateRef<any>;
    suggestionTextareaConfig?: SuggestionDirectiveConfig;
  };
  maxRuleCount?: number,
  autoSingleValueSelect?: boolean
}

export interface ColumnFieldV2 {
  fieldDataType:
    | "String"
    | "Numeric"
    | "Integer"
    | "Double"
    | "Float"
    | "Long"
    | "Bit"
    | "Date"
    | "Expression"
    | "STRING"
    | "NUMERIC"
    | "INTEGER"
    | "DOUBLE"
    | "FLOAT"
    | "LONG"
    | "BIT"
    | "DATE"
    | "EXPRESSION"; // Date is in epoch
  timeZone?: any;
  dateFormat?: any;
  fieldLabel: string;
  regex?: any;
  fieldLength?: any;
  fieldValue: any;
  selectionType?: string;
  dynamicValues?: any[];
  expressionField?: string;
  isDynamic?: boolean;
  controlType?: "hcl-select-box" | "hcl-drop-down" | "htmlDiv";
  isFirstLevel?: boolean;
  profile?: boolean;
  fieldId?: number;
}

export interface ColumnType {
  fieldLabel: string;
  fieldValue: any;
  canReset: boolean;
}
