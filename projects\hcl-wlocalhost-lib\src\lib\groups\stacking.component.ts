import { ChangeDetectionStrategy, Component, Input, SimpleChanges } from '@angular/core';

@Component({
  selector: 'hcl-stacking',
  template: `
    <div class="stacking-warapper">
      <div class="stacking-label">{{ 'settings.stackOnMob' | translate }}</div>
      <div class="toggle-group">
        <mat-slide-toggle [(ngModel)]="stackOnMobile" [color]="'primary'" (change)="toggleSlideToggle($event)"></mat-slide-toggle>
      </div>
    </div>
    <div class="stacking-warapper" [ngClass]="{ 'disabled': !stackOnMobile }">
      <div class="stacking-label">{{ 'settings.StackOrder' | translate}}</div>
      <div class="toggle-group">
        <mat-button-toggle-group [(ngModel)]="stacking" class="toggle-buttons" [disabled]="!stackOnMobile">
          <mat-button-toggle value="leftToRight" (click)="toggleltr()" hclTooltip="{{ 'settings.left-to-right' | translate}}">
          <i class="hcl-icon-up hcl-icon"></i>
          </mat-button-toggle>
          <mat-button-toggle value="rightToLEFT" (click)="togglertl()" hclTooltip="{{ 'settings.right-to-left' | translate}}" matTooltipPosition="bottom">
          <i class="hcl-icon-down hcl-icon"></i>
          </mat-button-toggle>
        </mat-button-toggle-group>
      </div>
    </div>
  `,
  styles: [`
    .stacking-warapper ::ng-deep .mat-button-toggle-label-content {
      line-height: initial !important;
      padding: 2px 0px !important;
    }
    .stacking-warapper ::ng-deep .mat-button-toggle-checked .mat-button-toggle-label-content {
      background-color: #f5821e!important;
      color: rgba(0, 0, 0, 0.54);
      .hcl-icon {
        color: white;
      }
    }
    .stacking-warapper ::ng-deep .hcl-icon {
      font-size: 19px;
      overflow: hidden;
      color:#6d7692;
    }
    .stacking-warapper {
      padding: 0px;
      display: flex;
      flex: 2;
      flex-direction: row;
      justify-content: space-between;
      overflow: hidden;
      margin-bottom: 16px;
    }
    .stacking {
      font-family: "Montserrat";
      color: #6d7692;
      font-family: Roboto;
      font-size: 14px;
      letter-spacing: 0;
      line-height: 16px;
    }
    .stacking-warapper {
      transition: opacity 0.3s ease-in-out;
    }
    .stacking-warapper.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StackingComponent {
  @Input() model: { stacking: string, disableResponsive: boolean};
  stackOnMobile: boolean;
  stacking: 'rightToLEFT' | 'leftToRight' | '';

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.model && changes.model.currentValue) {
      this.stacking = changes.model.currentValue.stacking;
      this.stackOnMobile = changes.model.currentValue.disableResponsive ? false : true;
      if (!changes.model.currentValue.disableResponsive && this.stacking != 'rightToLEFT') {
        this.stacking = 'leftToRight';
        this.model.stacking = 'leftToRight';
        this.model.disableResponsive = false;
      } else if (changes.model.currentValue.disableResponsive) {
        this.stacking = '';
        this.model.stacking = '';
        this.model.disableResponsive = true;
      }
    }
  }
  
  togglertl() {
    this.stacking = 'rightToLEFT';
    this.model.stacking = 'rightToLEFT';
  }

  toggleltr() {
    this.stacking = 'leftToRight';
    this.model.stacking = 'leftToRight';
  }

  toggleSlideToggle(event) {
    if (!event.checked) {
      this.stacking = '';
      this.model.stacking = '';
      this.model.disableResponsive = true;
    } else {
      this.stacking = 'leftToRight';
      this.model.stacking = 'leftToRight';
      this.model.disableResponsive = false;
    }
  }
}
