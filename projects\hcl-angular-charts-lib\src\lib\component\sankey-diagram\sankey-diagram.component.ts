import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import * as d3 from 'd3';
import * as d3Sankey from 'd3-sankey';
import { isEmpty } from 'lodash';
import { SankeyConfig, SankeyJSON } from './sankey-diagram';
import { jsPDF } from 'jspdf';

@Component({
  selector: 'hcl-sankey-diagram',
  templateUrl: './sankey-diagram.component.html',
  styleUrls: ['./sankey-diagram.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SankeyDiagramComponent implements OnInit {

  @Input() sankeyJson: SankeyJSON;
  @Input() userSankeyConfig: SankeyConfig;

  //default sankey config//
  sankeyConfig: SankeyConfig = {
    margin: 30,
    svgBackground: '#eee',
    svgBorder: '1px solid #333',
    nodeWidth: 24,
    nodePadding: 16,
    nodeOpacity: 1,
    linkOpacity: 0.2,
    nodeDarkenFactor: 0.9,
    nodeStrokeWidth: 4,
    separator: '\u2192',
    isDragableNode: true,
    needGradient: false,
    svgWidth: 1000,
    svgHeight: 400,
    nodeGap: 175,
    fontSize: 12
  }

  initialMousePosition: any = {};
  initialNodePosition: any = {};

  graphSize = null;
  sankeyPath = null;
  sankeySVGWidth = 1000000;
  sankeySVGheight = 500;
  nodeAlignment = d3Sankey.sankeyLeft;
  colorScale = d3.interpolateRainbow;
  sankeyZoom = d3.zoom();
  svgStyle: any = {};
  svgNodes: any;
  svgLinks: any;
  graph: any;
  sankeySVG: any;
  isHideLabels = false;
  isGreyNodeSelected = false;
  isOrientationChanged = false;
  // created instance of sankey
  sankey = d3Sankey.sankey();

  constructor() { }

  ngOnInit(): void {
    this.initilaizedSankey();
  }

  initilaizedSankey() {
    //taking user config//
    this.overrideSankeyConfig();

    // creating svg
    this.sankeySVG = d3.select('.svg-container #svg-sankey')
      .attr('width', this.sankeySVGWidth)
      .attr('height', this.isOrientationChanged ? this.sankeySVGWidth : this.sankeySVGheight)
      .style('background-color', this.sankeyConfig.svgBackground)
      .style('border', this.sankeyConfig.svgBorder)
      .call(this.sankeyZoom.on('zoom', () => this.handleSankeyZoom()))
      .append('g')
      .attr('transform', this.isOrientationChanged ? `rotate(90,338,349)` : `translate(${this.sankeyConfig.margin * 2},${this.sankeyConfig.margin})`);

    this.sankey.size(this.graphSize)
      .nodeId(d => d.id)
      .nodeWidth(this.sankeyConfig.nodeWidth)
      .nodePadding(this.sankeyConfig.nodePadding)
      .nodeAlign(this.nodeAlignment);

    //create Graph
    this.createGraph();
    //create links
    this.addNodeLinks();
    //create nodes
    this.addNodeRect();
  }

  handleSankeyZoom() {
    this.sankeySVG.attr('transform', this.isOrientationChanged ? `rotate(90,${278 + d3.event.transform.x},${319 + d3.event.transform.y}) scale(${d3.event.transform.k})` : d3.event.transform);
  }

  overrideSankeyConfig() {
    d3.zoomIdentity.constructor(1, this.sankeyConfig.margin * 2, this.sankeyConfig.margin);
    this.sankeyConfig = { ...this.sankeyConfig, ...this.userSankeyConfig };
    this.graphSize = [this.sankeySVGWidth - 2 * this.sankeyConfig.margin, this.sankeySVGheight - 2 * this.sankeyConfig.margin];
    this.svgStyle['width.px'] = this.sankeyConfig.svgWidth;
    this.svgStyle['height.px'] = this.sankeyConfig.svgHeight;
    this.sankeySVGheight = this.sankeyConfig.svgHeight * 1.5;
    if (this.sankeyJson.nodes.length > 50) {
      this.sankeyConfig.fontSize = 10;
    }
  }

  createGraph() {
    this.graph = this.sankey(this.sankeyJson);
    this.graph.nodes.forEach(node => {
      let fillColor = this.color(node.index);
      node.fillColor = fillColor;
      node.strokeColor = this.darkenColor(fillColor, this.sankeyConfig.nodeDarkenFactor);
      node.width = this.sankeyConfig.nodeWidth;
      node.height = node.y1 - node.y0;
      node.x0 = this.sankeyConfig.nodeGap * node.layer;
      node.x1 = this.sankeyConfig.nodeGap * node.layer + this.sankeyConfig.nodeWidth;
      if (node.height < 5) {
        node.height = 5;
      }
    });
  }

  addNodeLinks() {
    this.svgLinks = this.sankeySVG.append('g')
      .classed('sankey-links', true)
      .selectAll('g')
      .data(this.graph.links)
      .enter()
      .append('g');

    let gradients = this.svgLinks.append('linearGradient')
      .attr('gradientUnits', 'userSpaceOnUse')
      .attr('x1', (d: any) => this.sankeyConfig.nodeGap * d.source.layer)
      .attr('x2', (d: any) => this.sankeyConfig.nodeGap * d.target.layer)
      .attr('id', (d: any) => this.getGradientId(d));

    this.addGradientStop(gradients, this.sankeyConfig.needGradient ? 0.1 : 1.0, d => this.color(d.source.index));
    this.addGradientStop(gradients, 1.0, d => this.color(d.target.index));

    this.sankeyPath = d3Sankey.sankeyLinkHorizontal();
    this.svgLinks.append('path')
      .classed('sankey-link', true)
      .attr('d', this.sankeyPath)
      .attr('fill', 'none')
      .style('stroke', d => `url(#${this.getGradientId(d)})`)
      .style('stroke-width', (d: any) => Math.max(1.0, d.width))
      .style('stroke-opacity', this.sankeyConfig.linkOpacity);

    //hover title..
    this.svgLinks.append('title')
      .text((d: any) => `${d.source.id} ${this.sankeyConfig.separator} ${d.target.id}\n${d.toolTip || ''}`);
  }

  addNodeRect() {
    this.svgNodes = this.sankeySVG.append('g')
      .classed('sankey-nodes', true)
      .selectAll('rect')
      .data(this.graph.nodes)
      .enter()
      .append('g');

    // check for dragable nodes or not.
    if (this.sankeyConfig.isDragableNode) {
      this.svgNodes.append('rect')
        .classed('sankey-node', true)
        .attr('x', (d: any) => this.sankeyConfig.nodeGap * d.layer)
        .attr('y', (d: any) => d.y0)
        .style('width', (d: any) => d.width)
        .style('height', (d: any) => d.height)
        .attr('fill', (d: any) => d.fillColor)
        .style('opacity', this.sankeyConfig.nodeOpacity)
        .style('stroke', (d: any) => d.strokeColor)
        .style('stroke-width', 0)
        .style('cursor', 'pointer')
        .call(d3.drag()
          .on('start', this.onNodeDragStart.bind(this))
          .on('drag', this.onNodeDragging.bind(this))
          .on('end', this.onNodeDragEnd.bind(this))
        )
        .on('mouseover', (node) => { this.addHighLightClass(node) })
        .on('mouseleave', () => { this.removeHighLightClass() })
        .append('title')
        .text((d: any) => `${d.id}`);
    } else {
      this.svgNodes.append('rect')
        .classed('sankey-node', true)
        .attr('x', (d: any) => this.sankeyConfig.nodeGap * d.layer)
        .attr('y', (d: any) => d.y0)
        .style('width', (d: any) => d.width)
        .style('height', (d: any) => d.height)
        .attr('fill', (d: any) => d.fillColor)
        .style('opacity', this.sankeyConfig.nodeOpacity)
        .style('stroke', (d: any) => d.strokeColor)
        .style('stroke-width', 0)
        .style('cursor', 'pointer')
        .on('mouseover', (node) => { this.addHighLightClass(node) })
        .on('mouseleave', () => { this.removeHighLightClass() })
        .append('title')
        .text((d: any) => `${d.id}`);
    }
    this.addTextToNode();
  }

  addTextToNode() {
    this.svgNodes.append('text')
      .attr('x', (d: any) => d.x0)
      .attr('y', (d: any) => d.y0)
      .classed('sankey-node-text', true)
      .text((d: any) => `${d.id}`)
      .attr('text-anchor', 'text-after-edge')
      .attr('dominant-baseline', 'text-after-edge')
      .style('font-size', this.sankeyConfig.fontSize);
  }

  removeTextNode() {
    let allNodeTexts = document.querySelectorAll('.sankey-node-text');
    allNodeTexts.forEach(ele => ele.remove());
  }

  addHighLightClass(node: any) {
    let selectedNode = [...node.sourceLinks, ...node.targetLinks];
    selectedNode = selectedNode.map((ele: any) => { return { s: ele.source.id, t: ele.target.id } });
    //Highlight Linked Node
    this.svgNodes.classed('no-highlight-node', (linkedNode: any) => {
      let hn = selectedNode.find(n => (n.s === linkedNode.id || n.t === linkedNode.id));
      return isEmpty(hn) ? true : false;
    });
    //Highlight Linked Links
    this.svgLinks.classed('no-highlight-link', (link: any) => {
      return !(link.source === node || link.target === node);
    });
  }

  removeHighLightClass() {
    //remove highlighting nodes and links
    this.svgNodes.classed('no-highlight-node', false);
    this.svgLinks.classed('no-highlight-link', false);
  }

  addGradientStop(gradients, offset, fn) {
    return gradients.append('stop')
      .attr('offset', offset)
      .attr('stop-color', fn);
  }

  color(index) {
    let ratio = index / (this.sankeyJson.nodes.length - 1.0);
    return this.colorScale(ratio);
  }

  darkenColor(color, factor) {
    return d3.color(color).darker(factor)
  }

  getGradientId(d) {
    return `gradient_${d.source.id}_${d.target.id}`;
  }

  getMousePosition(e) {
    e = e || d3.event;
    return {
      x: e.x,
      y: e.y
    };
  }

  getNodePosition(node) {
    return {
      x: +node.attr('x'),
      y: +node.attr('y'),
      width: +node.attr('width'),
      height: +node.attr('height')
    };
  }

  moveSelectedNode(node, position) {
    position.width = position.width || +(node.attr('width'));
    position.height = position.height || +(node.attr('height'));
    if (position.x < 0) {
      position.x = 0;
    }
    if (position.y < 0) {
      position.y = 0;
    }
    if (position.x + position.width > this.graphSize[0]) {
      position.x = this.graphSize[0] - position.width;
    }
    if (position.y + position.height > this.graphSize[1]) {
      position.y = this.graphSize[1] - position.height;
    }
    node.attr('x', position.x).attr('y', position.y);
    let nodeData = node.data()[0];
    nodeData.x0 = position.x
    nodeData.x1 = position.x + position.width;
    nodeData.y0 = position.y;
    nodeData.y1 = position.y + position.height;
    this.sankey.update(this.graph);
    this.svgLinks.selectAll('linearGradient')
      .attr('x1', (d: any) => d.source.x1)
      .attr('x2', (d: any) => d.target.x0);
    this.svgLinks.selectAll('path')
      .attr('d', this.sankeyPath);
  }

  onNodeDragging() {
    let currentMousePosition = this.getMousePosition(d3.event);
    let delta = {
      x: currentMousePosition.x - this.initialMousePosition.x,
      y: currentMousePosition.y - this.initialMousePosition.y
    };
    let thisNode = d3.select(arguments[2][arguments[1]]);
    let newNodePosition = {
      x: this.initialNodePosition.x + delta.x,
      y: this.initialNodePosition.y + delta.y,
      width: this.initialNodePosition.width,
      height: this.initialNodePosition.height
    };
    this.moveSelectedNode(thisNode, newNodePosition);
  }

  onNodeDragEnd() {
    d3.select(arguments[2][arguments[1]]).attr('stroke-width', 0);
    if (!this.isHideLabels) {
      this.addTextToNode();
    }
  }

  onNodeDragStart() {
    this.removeTextNode();
    let node = d3.select(arguments[2][arguments[1]])
      .raise()
      .attr('stroke-width', this.sankeyConfig.nodeStrokeWidth);
    this.setInitialNodePosition(node);
    this.initialNodePosition = this.getNodePosition(node);
    this.initialMousePosition = this.getMousePosition(d3.event);
  }

  setInitialMousePosition(e) {
    this.initialMousePosition.x = e.x;
    this.initialMousePosition.y = e.y;
  }

  setInitialNodePosition(node) {
    let pos = node ? this.getNodePosition(node) : { x: 0, y: 0, width: 0, height: 0 };
    this.initialNodePosition.x = pos.x;
    this.initialNodePosition.y = pos.y;
    this.initialNodePosition.width = pos.width;
    this.initialNodePosition.height = pos.height;
  }

  getSVGObject() {
    const svgString = new XMLSerializer()
      .serializeToString(document.getElementById('svg-sankey')),
      canvas: any = document.getElementById("canvas-sankey"),
      ctx = canvas.getContext("2d"),
      DOMURL: any = self.URL || self.webkitURL || self,
      img = new Image(),
      svg = new Blob([svgString], {
        type: "image/svg+xml;charset=utf-8"
      });
    return { DOMURL, ctx, img, svg, canvas };
  }

  public exportCurrentSVGtoPNG(fileName) {
    this.resetZoom();
    let { DOMURL, ctx, img, svg, canvas } = this.getSVGObject();
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    const url = DOMURL.createObjectURL(svg);
    img.onload = () => {
      ctx.drawImage(img, 0, 0);
      const imgURL = canvas.toDataURL("image/png");
      DOMURL.revokeObjectURL(imgURL);
      const dlLink = document.createElement('a');
      dlLink.download = fileName;
      dlLink.href = imgURL;
      dlLink.dataset.downloadurl = ["image/png", dlLink.download, dlLink.href].join(':');
      document.body.appendChild(dlLink);
      dlLink.click();
      document.body.removeChild(dlLink);
    }
    img.src = url;
  }

  public exportCurrentSVGtoPDF(fileName) {
    this.resetZoom();
    let { DOMURL, ctx, img, svg, canvas } = this.getSVGObject();
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    var url = DOMURL.createObjectURL(svg);
    img.onload = () => {
      ctx.drawImage(img, 0, 0);
      var imgURL = canvas.toDataURL("image/jpeg", 1.0);
      DOMURL.revokeObjectURL(imgURL);
      const jspdfDoc = new jsPDF({
        orientation: 'l'
      });
      jspdfDoc.addImage(imgURL, 'JPEG', 0, 0, 1000, 1000);
      jspdfDoc.save(fileName + ".pdf");
    }
    img.src = url;
  }

  public resetZoom() {
    let zoomedSVG: any = d3.select('svg');
    d3.zoomIdentity.constructor(1, this.sankeyConfig.margin * 2, this.sankeyConfig.margin);
    zoomedSVG.call(this.sankeyZoom.transform, d3.zoomIdentity);
    this.sankeySVG.attr('transform', this.isOrientationChanged ? `rotate(90,338,349)` : `translate(${this.sankeyConfig.margin * 2},${this.sankeyConfig.margin})`);
  }

  public switchLabels() {
    this.isHideLabels = !this.isHideLabels;
    this.removeTextNode();
    if (!this.isHideLabels) {
      this.addTextToNode();
    }
  }

  public changeColorScheme() {
    this.isGreyNodeSelected = !this.isGreyNodeSelected;
  }

  public changeLayout() {
    this.resetZoom();
    d3.select('.svg-container #svg-sankey').selectAll("*").remove();
    this.isOrientationChanged = !this.isOrientationChanged;
    this.initilaizedSankey();
  }
}
