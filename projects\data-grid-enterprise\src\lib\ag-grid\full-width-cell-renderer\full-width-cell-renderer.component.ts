/**
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'hcl-full-width-cell-renderer',
  templateUrl: './full-width-cell-renderer.component.html',
  styleUrls: ['./full-width-cell-renderer.component.css']
})
export class FullWidthCellRendererComponent implements OnInit {

  constructor() { }

  ngOnInit() {
  }

}
 */

import {Component, ViewChild, ViewContainerRef} from "@angular/core";

import {IFilterParams} from "ag-grid-community";
import {IFilterAngularComp} from "ag-grid-angular";

import {IAfterGuiAttachedParams, IDoesFilterPassParams, RowNode} from "ag-grid-community";

@Component({
  selector: 'hcl-full-width',
  templateUrl: './full-width-cell-renderer.component.html',
  styleUrls: ['./full-width-cell-renderer.component.css']
})
export class FullWidthCellRenderer implements IFilterAngularComp {
    valueGetter: (rowNode: RowNode) => any;
    isFilterActive(): boolean {
        throw new Error("Method not implemented.");
    }
    doesFilterPass(params: IDoesFilterPassParams): boolean {
        throw new Error("Method not implemented.");
    }
    getModel() {
        throw new Error("Method not implemented.");
    }
    setModel(model: any): void {
        throw new Error("Method not implemented.");
    }
    onNewRowsLoaded?(): void {
        throw new Error("Method not implemented.");
    }
    getFrameworkComponentInstance?(): void {
        throw new Error("Method not implemented.");
    }
    getModelAsString?(model: any): string {
        throw new Error("Method not implemented.");
    }
    afterGuiAttached?(params?:IAfterGuiAttachedParams): void {
        throw new Error("Method not implemented.");
    }

    /**
   * the default row Defination that is set on the ag-grid
   */
  rowDefinition: any;
  /**
   * Variable to store params provided by ag-grid
   */
  public params: any;

    agInit(params: IFilterParams): void {
        this.params = params;
    // Set the row definition
    this.rowDefinition = params.api.getRowNode('1').data;
    // if (params.node) {
    //     this.rowDefinition = params.node.data;
    // }
    
    }
}
