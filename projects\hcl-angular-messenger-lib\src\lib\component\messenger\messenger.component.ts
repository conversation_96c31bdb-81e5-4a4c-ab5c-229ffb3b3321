import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { TabsConfig } from 'hcl-angular-widgets-lib';
import {HclAngularMessengerLibService} from '../../hcl-angular-messenger-lib.service';

@Component({
  selector: 'hcl-messenger',
  templateUrl: './messenger.component.html',
  styleUrls: ['./messenger.component.scss'],
  encapsulation: ViewEncapsulation.None,
  providers: [HclAngularMessengerLibService]
})
export class MessengerComponent implements OnInit {
  /**
   * The configuration for the tabs in the messenger panel
   */
  messengerTabConfig: TabsConfig = {
    selectedTab: 0,
    isLazy: false,
    elements: [
      { header: 'Chats', templateName: 'chatTemplate' }
    ]
  };
  /**
   * The default constructor, we will require the messangerService
   * for faster interactions, so we have included it as provider in this component
   */
  constructor(private messangerService: HclAngularMessengerLibService) { }

  ngOnInit(): void {
  }

}
