<div class='w-100 channel-preferences-section'>
  <div class="preferences-title">{{field.options.title ? field.options.title : defaultTitle}}</div>
  <div class="d-flex">
    <div class="d-flex">
      <mat-icon>mail_outline</mat-icon>
      <div><EMAIL></div>
    </div>
    <div class="d-flex">
      <mat-icon>stay_current_portrait</mat-icon>
      <div>987654321</div>
    </div>
  </div>
  <div class="select preference message">
    {{field.options.selectChannelMessage ? field.options.selectChannelMessage : defaultSelectMessage}}
  </div>
  <div>
    <div class='d-flex flex-direction-row preferences-header'>
      <div class='w-20 row-padding pl-3 pr-3'>Contact Channel</div>
      <div class='w-25 row-padding pl-3 pr-3'>Set Frequency</div>
      <div class='w-15 row-padding pl-3 pr-3'>Select time</div>
      <div class='w-40 row-padding pl-3 pr-3'>Pause between</div>
    </div>
    <div class='d-flex flex-direction-row row-padding pl-0 pr-0' *ngFor="let item of field.options.channels">
      <div class='w-20 pl-3'>
        <mat-checkbox>{{item.type}}</mat-checkbox>
      </div>
      <div class='w-25 d-flex flex-direction-row pl-3'>
        <div class='w-30'>
          <div class="input-group pr-1">
            <input type="number" class="form-control" value="4">
          </div>
        </div>
        <div class='w-20'>every</div>
        <div class='w-50'>
          <div class="input-group pr-3">
            <select class="custom-select">
              <option>Day</option>
              <option>Week</option>
              <option>Month</option>
              <option>Year</option>
            </select>
          </div>
        </div>
      </div>
      <div class='w-15 pl-3'>
        <div class="input-group pr-3">
          <input type="text" class="form-control" readonly value="8:30">
          <div class="input-group-append">
            <span class="input-group-text"><mat-icon>access_time</mat-icon></span>
          </div>
        </div>
      </div>
      <div class='w-40 d-flex flex-direction-row pl-3'>
        <div class="w-50">
          <div class="input-group pr-3">
            <input type="text" class="form-control" readonly value="02/02/2022">
            <div class="input-group-append">
              <span class="input-group-text"><mat-icon>date_range</mat-icon></span>
            </div>
          </div>
        </div>
        <div class="w-50">
          <div class="input-group pr-3">
            <input type="text" class="form-control" readonly value="02/02/2022">
            <div class="input-group-append">
              <span class="input-group-text"><mat-icon>date_range</mat-icon></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>