import {
  Component,
  Input,
  HostBinding,
  ChangeDetectorRef,
  SimpleChanges,
  OnChanges
} from '@angular/core';
import { ContentConnectorField } from '../../classes/Fields';
import {   
  createFont,
  createPadding,
  createBorder,
  createLineHeight,
  createWidthHeight,
  createBackground, } from '../../utils';
import { ImageUploader } from '../../ip-image-uploader.service';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'ip-contentConnector-field',
  templateUrl: './contentConnector-field.component.html',
  styleUrls: ['./contentConnector-field.component.css']
})
export class contentConnectorFieldComponent {
  @Input() field: ContentConnectorField;

  constructor(
    private imageUploader: ImageUploader,
    private chRef: ChangeDetectorRef,
    public ngb: IpEmailBuilderService,
    public translate: TranslateService
  ) {}
  
  transformContentSourceType(value: string): string {
    return this.translate.instant('CONTENT_CONNECTOR.CONTENT.LABELS.' + value
      .split('-')
      .join('_')
      .toUpperCase()
    );
  }

}

