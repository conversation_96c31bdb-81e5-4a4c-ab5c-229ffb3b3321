/**
 * SMS Editor Key handling
 */
export interface EditorFormat {
  value?: string,
  key: number
}

/**
 * Editor Key Press Events
 */
 export class EditorEvents {
  static DELETE: EditorFormat = { value: 'delete', key: 8 };
  static ENTER: EditorFormat = { value: 'esc', key: 13 };
  static ESC: EditorFormat = { value: 'esc', key: 27 };
  static ARROWUP: EditorFormat = { value: 'arrowup', key: 38 };
  static ARROWDOWN: EditorFormat = { value: 'arrowdown', key: 40 };
  static HASH: EditorFormat = { value: 'hash', key: 51 };
  static SPACE: EditorFormat = { value: 'space', key: 32 };
  static TAB: EditorFormat = { value: 'tab', key: 9 };
}
