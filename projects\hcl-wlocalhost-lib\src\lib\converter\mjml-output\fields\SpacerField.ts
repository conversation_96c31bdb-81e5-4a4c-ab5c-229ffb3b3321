import {
  RenderingClass,
  SpacerLayoutField,
  ISpacerFieldOptions
} from '../interfaces';
import { createWidthHeight } from '../utils';

export class Spacer<PERSON>ield implements SpacerLayoutField, RenderingClass {
  constructor(public options: ISpacerFieldOptions) {}

  render() {
    const spacerTemplate = `
      <mj-spacer
        css-class="ip-spacer-field hide-on-${this.options.hideOn}"
        padding="0"
        height="${createWidthHeight(this.options.height)}"></mj-spacer>
    `;

  if (this.options.hideOn === 'desktop') {
    return `
      <mj-raw>
      <!--[if !mso]><!-- --></mj-raw>
      ${spacerTemplate}
      <mj-raw><!--<![endif]--></mj-raw>
    `;
  } else {
    return spacerTemplate;
  }
  }
}
