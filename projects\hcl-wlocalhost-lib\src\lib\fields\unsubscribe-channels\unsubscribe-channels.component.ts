import { ChangeDetectorRef, Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { UnsubscribeChannelsField } from '../../classes/Fields';
import { Channels } from '../../interfaces';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import { createFont, createLineHeight, createPadding, createWidthHeight } from '../../utils';

@Component({
  selector: 'ip-unsubscribe-channels',
  templateUrl: './unsubscribe-channels.component.html',
  styleUrls: ['./unsubscribe-channels.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class UnsubscribeChannelsComponent implements OnInit {
  @Input() field: UnsubscribeChannelsField;
  constructor(private translate: TranslateService, 
    private _ngb: IpEmailBuilderService,
    private chRef: ChangeDetectorRef) {}

  ngOnInit(): void {
    this._ngb.currentChannels$.subscribe(x => {
      this.chRef.markForCheck();
    })
  }

  getParentStyles() {
    const { align } = this.field.options;

    return {
      'text-align': (align === 'center' ? 'center' : (align === 'left' ? 'left' : 'right')),
      width: '100%',
      display: 'block'
    };
  }
  
  getTextStyles() {
    const { color, font, lineHeight, padding, width } = this.field.options;

    return {
      color,
      width: (createWidthHeight(width) === 'auto' ? '100%' : createWidthHeight(width)),
      display: 'inline-block',
      ...createLineHeight(lineHeight),
      ...createFont(font),
      ...createPadding(padding)
    };
  }

  fetchComponentErrorMessages() {
    const errorMessages = new Set();
    for (let iErrorCounter = 0; iErrorCounter < this.field.errors.length; iErrorCounter++) {
      errorMessages.add(this.translate.instant('messages.'+this.field.errors[iErrorCounter].key+'-collated'))
    }
    return `${Array.from(errorMessages).join(`\n\r`)}`;
  }

}
