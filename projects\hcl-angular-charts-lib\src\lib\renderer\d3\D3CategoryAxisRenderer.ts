/**
 * this will help in rendering the Scale band
 */
import {ChartConfig} from '../../config/chart-config';
import * as d3 from 'd3';
import {D3ChartRenderer} from './D3ChartRenderer';

export class D3CategoryAxisRenderer {
  /**
   * the instance of scale band
   */
  private scaleBand: any;
  /**
   * the object that we get after rendering the Axis on the chart
   */
  private catAxisObject: any;
  /**
   * The default constructor
   * param {ChartConfig} chartConfig
   */
  constructor(private chartConfig: ChartConfig, protected chartRenderer: D3ChartRenderer) {
    this.createScale();
  }

  /**
   * This wiull get the min & max range for this axis
   * returns {{min: number; max: number}}
   */
  public getMinAndMaxRange(): {min: number, max: number} {
    let minRange: number = this.chartConfig.margin.left;
    let maxRange: number = this.chartConfig.dimension.width - this.chartConfig.margin.right;
    switch (this.chartConfig.categoryAxis.position) {
      case 'right':
      case 'left':
        minRange = this.chartConfig.margin.top;
        maxRange = this.chartConfig.dimension.height - this.chartConfig.margin.bottom;
        break;
    }
    return {
      min : minRange,
      max : maxRange
    };
  }

  /**
   * Get the position of the current category
   * param {string} category
   * returns {any}
   */
  public getPosition(category: string): any {
    return this.scaleBand(category);
  }

  /**
   * this function will return the default set band width for the category axis
   * returns {any}
   */
  public getBandWidth(): number {
    return this.scaleBand.bandwidth();
  }

  public getPadding(): number {
    return this.scaleBand.padding();
  }

  /**
   * this function will render the Aixs
   * param {ElementRef} element
   */
  renderAxis(chartObject: any, transition?: number): void {
    if (!this.catAxisObject) {
      this.catAxisObject = chartObject.append('g');
    }
    // We have to make sure that the Axis is positioned properly
    switch (this.chartConfig.categoryAxis.position) {
      case 'bottom':
        this.catAxisObject
          .call(d3.axisBottom(this.scaleBand))
          .attr('transform', 'translate(0,' + (this.chartConfig.dimension.height - this.chartConfig.margin.bottom) + ')');
        break;
      case 'right':
        this.catAxisObject
          .call(d3.axisRight(this.scaleBand))
          .attr('transform', 'translate(' + (this.chartConfig.dimension.width - this.chartConfig.margin.right) + ',0)');
        break;
      case 'top':
        this.catAxisObject
          .call(d3.axisTop(this.scaleBand))
          .attr('transform', 'translate(0,' + this.chartConfig.margin.top + ')');
        break;
      case 'left':
        this.catAxisObject
          .transition()
          .duration(transition ? transition : 0)
          .call(this.renderGridLines(d3.axisLeft(this.scaleBand)))
          .attr('transform', 'translate(' + this.chartConfig.margin.left + ',0)');
        break;
    }
    if (!this.chartConfig.categoryAxis.disableAutoAlignText) {
      // time to make sure the labels are set properly
      this.catAxisObject.selectAll('.tick text')
        .call(this.customizeXAxisTextObject, this, this.scaleBand.bandwidth());
    }
    // if we have grid lines we will have to set the styling
    if (this.chartConfig.categoryAxis.gridLines) {
      // transform='translate(0,78.67647058823525)'
      // we may have a transform
      let tx: number = 0;
      let ty: number = 0;
      // if we have transform
      if (this.chartConfig.categoryAxis.gridLines.transform) {
        tx = this.chartConfig.categoryAxis.gridLines.transform.x;
        ty = this.chartConfig.categoryAxis.gridLines.transform.y;
      }
      const trasnform: string = `translate(${tx},${ty})`;
      // get all tick lines
      this.catAxisObject
        .selectAll('.tick line')
        .attr('class', this.chartConfig.categoryAxis.gridLines.class
          ? ('hcl-cat-axis ' + this.chartConfig.categoryAxis.gridLines.class)
          : 'hcl-cat-axis')
        .attr('transform', trasnform);

    }
  }

  private createScale(): void {
    const minMaxRange: {min: number, max: number} = this.getMinAndMaxRange();
    // since this is the category Axis it means this will have the labels
    this.scaleBand = d3.scaleBand()
    // the category that we need to plot can be a part of the
      .domain(this.chartConfig.categoryAxis.categoriesFunction ? this.chartConfig.categoryAxis.categoriesFunction(this.chartConfig)
        : this.chartConfig.categoryAxis.categories)
      .range([minMaxRange.min, minMaxRange.max])
      .padding(this.chartConfig.categoryAxis.padding ? this.chartConfig.categoryAxis.padding : 0.2);
  }

  /**
   * this function will change the scale of the linear axis
   * param {string} displayIn
   */
  public updateScale() {
    this.createScale();
    // render the Axis
    this.renderAxis(null, 800);
  }


  /**
   * Call when we want to display grid lines
   * returns {any}
   */
  private renderGridLines(axis: any): any {
    // if we have grid lines object
    if (this.chartConfig.categoryAxis.gridLines) {
      // we need the cat axis range as the grid dimension will depend on that
      const catAxisRange: {min: number, max: number} = this.chartRenderer.getLinearAxisMinAndMaxRange();
      return axis
      // .ticks(6)
        .tickFormat((d) => d)
        // the size will be the width or the height of the cat axis
        .tickSizeInner(-(catAxisRange.max - catAxisRange.min));
    }
    // no grid lines
    return axis;
  }
  /**
   * this function will format the texts of the X-Axis
   * param text : any[] = the list of text nodes that will be rendered on the chart
   * param width: number = the width within which th etext needs to be rendered
   */
  private customizeXAxisTextObject(text: any,
                                   renderer: D3CategoryAxisRenderer, width: number) {
    // we need to access this within the function we will do it using a
    // lets iterate through the all the text nodes
    // we need to use a function & not a arrow function because in the arrow function
    // we will not get the instance of this object which is the text node that we want use
    // also in case the texts are too long we will have the text of odd a bit
    text.each(function(t, index)  {
      // lets get the text object instance
      const textObject: any = d3.select(this);
      // call the format
      renderer.formatXAxisText(t, textObject, width, index);
    });
  }
  /**
   * We can do the formatting of the text object here
   * majorly if required we will be required to wrap the text
   * param textObject
   * param {number} width
   */
  protected formatXAxisText(text: string,
                            textObject: any,
                            width: number,
                            index: number): void {
    // lets split the word using the spaces and add it to a array
    const words: string[] = text.split(/\s+/).reverse();
    const y: number = +textObject.attr('y');
    const dyStr: string = textObject.attr('dy')
    const dy: number = +dyStr.substr(0, dyStr.indexOf('em'));
    const lineHeight: number = textObject.nodes()[0].getBBox().height;
    let lineNumber: number = 0;
    // the main logic is we will keep pusing each word in the array
    // and if the pixels taken by the line is more then the width then we will
    // remove 1 word & the next word will go in the next line
    let line: string[] = [];
    // we want to wrap it so lets use tspan inside the text object
    let tspan: any = textObject.text(null)
      .append('tspan')
      // place it at the same location as the text
      .attr('x', -15)
      // same location as we have in text
      .attr('y', y)
      .attr('dy', dy + 'em');
    // lets add the words to the span
    words.forEach((word: string) => {
      // lets push to the line & chk what is the size of the tspan
      line.push(word);
      tspan.text(line.join(' '));
      if (tspan.node().getComputedTextLength() > width) {
        // seems like the length is bit more, so pop the line
        line.pop();
        tspan.text(line.join(' '));
        line = [word];
        tspan = textObject.append('tspan')
          .attr('x', -15)
          .attr('y', y)
          // dy needs to be increased as per the number of lines
          // 0.25 is a correction to provide a proper spacing between the lines
          // 0.0625 will convert px int em
          .attr('dy', (++lineNumber * lineHeight * 0.0625) + 0.35 + dy + 'em')
          .text(word);
      }
    });
    // lets append the tooltip as well
    textObject.append('title').text(text);
  }

  /**
   * Get the placement position of the cat axis
   * returns {"top" | "bottom" | "right" | "left"}
   */
  public getPlacementPosition(): 'top' | 'bottom' | 'right' | 'left' {
    return this.chartConfig.categoryAxis.position;
  }
}
