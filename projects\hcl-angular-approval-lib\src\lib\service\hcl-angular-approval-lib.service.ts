import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Approval } from '../models/approval';
import { Role } from '../models/role';
import { ApprovalSharedDataService } from './approval-shared-data.service';

@Injectable()
export class HclAngularApprovalLibService {

  private planUrl: string = '/api/plan/v2';
  private approvalUrl: string = '/approvalService';
  private wfUrl: string = '/workflowService';

  constructor(private http: HttpClient, private _sharedDataService: ApprovalSharedDataService) { }

  private getBaseUrl(): string {
    return this._sharedDataService.serverUrl + this.planUrl;
  }

  private getApprovalBaseUrl(): string {
    return this.getBaseUrl() + this.approvalUrl;
  }

  private getWfBaseUrl(): string {
    return this.getBaseUrl() + this.wfUrl;
  }

  public getListOfAllApprovals(idList: number[]): Observable<any> {
    const url: string = this.getApprovalBaseUrl() + '/SelectedApprovalsRequest';
    return this.http.post<any>(url, idList);
  }

  public getSingleApproval(id: number): Observable<Approval> {
    const url: string = this.getApprovalBaseUrl() + '/approvals/' + id;
    return this.http.get<Approval>(url);
  }

  public getAvailableRoles(): Observable<Role[]> {
    const url: string = this.getWfBaseUrl() + '/managedListRoles';
    return this.http.get<Role[]>(url);
  }

  public getResponseHistory(approvalId: number) {
    const url: string = this.getApprovalBaseUrl() + '/approvals/' + approvalId + '/history?size=-1';
    return this.http.get(url);
  }

  public getSecurityPolicies(): Observable<any> {
    return this.http.get(this.getBaseUrl() + '/securityPolicy/');
  }
}
