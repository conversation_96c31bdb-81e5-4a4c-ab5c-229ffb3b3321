import { Component, Input, OnInit } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { HclSegmentationLibService } from '../../hcl-segmentation-lib.service';
import { StatsData } from '../../models/segment';
import { ProgressSpinner } from 'hcl-angular-widgets-lib';

@Component({
  selector: 'hcl-data-profile-statistics',
  templateUrl: './data-profile-statistics.component.html',
  styleUrls: ['./data-profile-statistics.component.scss']
})
export class DataProfileStatisticsComponent implements OnInit {
  @Input() config: any;
  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  statsData: StatsData;

  dataProfileStatsSpinner: ProgressSpinner = {
    isInline: true,
    isLoading: false,
    color: 'primary',
    mode: 'indeterminate',
    value: 50,
    diameter: 50,
    strokeWidth: 3
  }

  constructor(
    private hclSegmentationLibService: HclSegmentationLibService
  ) { }

  ngOnInit(): void {
    this.dataProfileStatsSpinner.isLoading = true;
    this.subscriptionList.push(this.hclSegmentationLibService.getStatsForSelectedColumns(this.config.audienceTableId, this.config.fieldId).subscribe((data: StatsData) => {
      this.statsData = data;
      this.dataProfileStatsSpinner.isLoading = false;
    }));
  }

  ngDestroy() {
    this.subscriptionList.forEach(sub => sub.unsubscribe());
  }
}
