import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ContentConnectorComponent } from './content-connector.component';

describe('URLComponent', () => {
  let component: ContentConnectorComponent;
  let fixture: ComponentFixture<ContentConnectorComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
    declarations: [ContentConnectorComponent],
    teardown: { destroyAfterEach: false }
})
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ContentConnectorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
