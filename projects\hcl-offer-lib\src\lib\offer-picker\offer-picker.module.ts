import { APP_INITIALIZER, NgModule } from '@angular/core';
import { SelectOffersAppComponent } from './select-offers-app/select-offers-app.component';
import { OfferFoldersComponent } from './select-offers/offer-folders/offer-folders.component';
import { OfferListingComponent } from './select-offers/offer-listing/offer-listing.component';
import { OfferSelectionComponent } from './select-offers/offer-selection/offer-selection.component';
import { OfferVariationComponent } from './select-offers/offer-variation/offer-variation.component';
import { OfferVariationsListingComponent } from './select-offers/offer-variations-listing/offer-variations-listing.component';
import { SelectOffersComponent } from './select-offers/select-offers.component';
import { ViewOfferComponent } from './select-offers/view-offer/view-offer.component';
import { OfferListListingComponent } from './select-offers/offer-list-listing/offer-list-listing.component';
import { ViewOfferListComponent } from './select-offers/view-offer-list/view-offer-list.component';
import { OfferCardViewComponent } from './select-offers/offer-card-view/offer-card-view.component';
import { OfferCardListComponent } from './select-offers/offer-card-view/offer-card-list/offer-card-list.component';
import { OfferlistCardViewComponent } from './select-offers/offerlist-card-view/offerlist-card-view.component';
import { OfferlistCardListComponent } from './select-offers/offerlist-card-view/offerlist-card-list/offerlist-card-list.component';
import { OfferlistLpCardListComponent } from './select-offers/offerlist-card-view/offerlist-lp-card-list/offerlist-lp-card-list.component';
import { OfferlistLpOffersDetailsComponent } from './select-offers/offerlist-card-view/offerlist-lp-card-list/offerlist-lp-offers-details/offerlist-lp-offers-details.component';

@NgModule({
    declarations: [
        SelectOffersComponent,
        OfferFoldersComponent,
        OfferListingComponent,
        OfferVariationsListingComponent,
        ViewOfferComponent,
        OfferVariationComponent,
        OfferSelectionComponent,
        SelectOffersAppComponent,
        OfferListListingComponent,
        ViewOfferListComponent,
        OfferCardViewComponent,
        OfferCardListComponent,
        OfferlistCardViewComponent,
        OfferlistCardListComponent,
        OfferlistLpCardListComponent,
        OfferlistLpOffersDetailsComponent
    ],
    exports: [
        SelectOffersComponent,
        OfferFoldersComponent,
        OfferListingComponent,
        OfferVariationsListingComponent,
        ViewOfferComponent,
        OfferVariationComponent,
        OfferSelectionComponent,
        SelectOffersAppComponent,
        OfferListListingComponent,
        ViewOfferListComponent,
        OfferCardViewComponent,
        OfferlistCardViewComponent,
        OfferlistCardListComponent,
        OfferlistLpCardListComponent
    ],
    providers: [
    ]
})
export class OfferPickerModule {

}