import { DropdownLayoutField, IInputFieldOptions, InputLayoutField, RenderingClass } from '../interfaces';
import { createPadding, createLineHeight, ignoreHTMLMinParse, createBorder, uniqueId, createWidthHeight, createBackground } from '../utils';
import { IpEmailBuilderService } from '../../../ip-email-builder.service';

export class DropdownField implements DropdownLayoutField, RenderingClass {
  constructor(public reportFieldName: string, public label: string, public dropdownOptionList: any, public placeholder: string, public options: any, private _ngb: IpEmailBuilderService) { }

  render() {
    const { inputColor, labelColor, labelFont, inputFont, labelLineHeight, padding, labelPadding, background, border, attributesLabel, align, innerPadding, width, inline, labelWidth, isRequired, height} = this.options;
    const uniqueIdForInput = uniqueId();

    this._ngb.customHTMLIdClassMap.push(`        
      <mj-style inline="inline">
        td.input-field.input_${uniqueIdForInput} {
          text-align: ${(align === 'center' ? 'center' : (align === 'left' ? 'left' : 'right'))};
          width: 100%
          overflow-x: auto;
          flex-direction: row;
          box-sizing: border-box;
          display: inline-block;
        }
        td.input-field.input_${uniqueIdForInput} > div {
          text-align: ${(attributesLabel.align === 'center' ? 'center' : (attributesLabel.align === 'left' ? 'left' : 'right'))} !important;
          display: inline-block;
          width: ${(createWidthHeight(width) === 'auto' ? '100%' : 'auto')};
          padding: ${createPadding(padding)};
          background:${createBackground(background)} !important;
          background-size:${background.size ? createWidthHeight(background.size) : 'auto'} !important;
          border: ${createBorder(border)};
          border-radius: ${border.radius}px;
          box-sizing: border-box;
        }
      </mj-style>`);

      const inputFieldTemplate = `
      <mj-text
      css-class="input-field input_${uniqueIdForInput} hide-on-${this.options.hideOn}"
      font-size="0px">
        <label for="${uniqueIdForInput}" style="margin-bottom: .5rem; 
                                                color: ${labelColor};
                                                display: ${this.label !== '' ? (inline ? 'inline-block' : 'block') : 'none'};
                                                padding: ${createPadding(labelPadding)};
                                                font-family: ${labelFont.family}, ${labelFont.fallback};
                                                font-size: ${labelFont.size}px;
                                                font-style: ${labelFont.style};
                                                font-weight: ${labelFont.weight};
                                                width: ${(createWidthHeight(labelWidth) === 'auto' ? '100%' : createWidthHeight(labelWidth))};
                                                line-height: ${createLineHeight(labelLineHeight)};
                                                box-sizing: border-box;" class="${isRequired ? 'required-input-field' : ''}">
                                                ${this.label}</label>
        <div style="display: ${(inline ? 'inline-block' : 'block')};
                    width: ${(createWidthHeight(width) === 'auto' ? (labelWidth.auto === true ? '100%' : `calc(100% - ${createWidthHeight(labelWidth)})`) : 'auto')};
                    padding: ${createPadding(innerPadding)};
                    vertical-align: top;
                    box-sizing: border-box;">
          
            <select onchange="optionChanged(this)" id="${uniqueIdForInput}" name="${this.reportFieldName}" class="droppable select-droppable ip-dropdown-field _ID_${uniqueIdForInput}" style=" font-style: ${inputFont.style};
              font-weight: ${inputFont.weight};
              font-family: ${inputFont.family}, ${inputFont.fallback};
              font-size:${inputFont.size}px;width:${(createWidthHeight(width) === 'auto' ? '100%' : createWidthHeight(width))};height: ${createWidthHeight(height)};border:none;border-bottom:1px solid ${inputColor};color:${inputColor}" ${isRequired ? 'required' : ''}>
            ${this.placeholder ? `<option value="" selected hidden disabled>${this.placeholder}</option>`: ''}
            ` + 
            this.dropdownOptionList.map((option) => {
              if(option.isVisible || (this._ngb.refreshEnabled && !this._ngb.Email.version)) {
                return ` <option value="${option.value}" title="${option.label}">${option.label}</option>
              `;
              }   
              }).join('') + 
          `</select>
        </div>
    </mj-text>`;

    if (this.options.hideOn === 'desktop') {
      return `
        <mj-raw>
        <!--[if !mso]><!-- --></mj-raw>
        ${inputFieldTemplate}
        <mj-raw><!--<![endif]--></mj-raw>
      `;
    } else {
      return inputFieldTemplate;
    }
  }
}
