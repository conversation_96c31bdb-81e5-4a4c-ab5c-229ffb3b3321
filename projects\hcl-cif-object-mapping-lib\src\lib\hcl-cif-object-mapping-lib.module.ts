import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import {  HclAngularWidgetsLibModule } from 'hcl-angular-widgets-lib';
import { HclCifObjectMappingComponent } from './component/object-mapping/hcl-cif-object-mapping.component';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { CifOmAttrsSubscriptionComponent } from './component/object-mapping/cif-om-attrs-subscription/cif-om-attrs-subscription.component';
import { CifFolderBaseEntitiesComponent } from './component/object-mapping/cif-folder-base-entities/cif-folder-base-entities.component';
import { FolderContainerComponent } from './component/object-mapping/cif-folder-base-entities/folder-container/folder-container.component';
import { GridContainerComponent } from './component/object-mapping/cif-folder-base-entities/grid-container/grid-container.component';
import { HclDataGridModule } from 'hcl-data-grid-lib';


export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}


@NgModule({
  declarations: [
    HclCifObjectMappingComponent,
    CifOmAttrsSubscriptionComponent,
    CifFolderBaseEntitiesComponent,
    FolderContainerComponent,
    GridContainerComponent
  ],
  imports: [
    CommonModule,
    HclAngularWidgetsLibModule,
    HclDataGridModule,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      },
      isolate: false
    }),
  ],
  exports: [
    HclCifObjectMappingComponent
  ],
  providers: []
})
export class HclCifObjectMappingLibModule { }
