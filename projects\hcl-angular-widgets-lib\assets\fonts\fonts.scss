/* montserrat-regular - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.eot'); /* IE9 Compat Modes */
  src: local('Montserrat Regular'), local('Montserrat-Regular'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-regular.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-italic - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat-italic';
  font-style: italic;
  font-weight: 400;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.eot'); /* IE9 Compat Modes */
  src: local('Montserrat Italic'), local('Montserrat-Italic'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-italic.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-500 - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.eot'); /* IE9 Compat Modes */
  src: local('Montserrat Medium'), local('Montserrat-Medium'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-500italic - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 500;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.eot'); /* IE9 Compat Modes */
  src: local('Montserrat Medium Italic'), local('Montserrat-MediumItalic'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-500italic.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-600 - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 600;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.eot'); /* IE9 Compat Modes */
  src: local('Montserrat SemiBold'), local('Montserrat-SemiBold'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-600italic - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 600;
  src: url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.eot'); /* IE9 Compat Modes */
  src: local('Montserrat SemiBold Italic'), local('Montserrat-SemiBoldItalic'),
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.woff2') format('woff2'), /* Super Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.woff') format('woff'), /* Modern Browsers */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Montserrat/montserrat-v14-latin-ext_cyrillic-ext_cyrillic_latin-600italic.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* roboto-regular - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  src: url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.eot'); /* IE9 Compat Modes */
  src: local('Roboto'), local('Roboto-Regular'),
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.woff') format('woff'), /* Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-regular.svg#Roboto') format('svg'); /* Legacy iOS */
}
/* roboto-italic - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Roboto';
  font-style: italic;
  font-weight: 400;
  src: url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.eot'); /* IE9 Compat Modes */
  src: local('Roboto Italic'), local('Roboto-Italic'),
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.woff2') format('woff2'), /* Super Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.woff') format('woff'), /* Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-italic.svg#Roboto') format('svg'); /* Legacy iOS */
}
/* roboto-700 - latin-ext_cyrillic-ext_cyrillic_latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  src: url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.eot'); /* IE9 Compat Modes */
  src: local('Roboto Bold'), local('Roboto-Bold'),
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.woff2') format('woff2'), /* Super Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.woff') format('woff'), /* Modern Browsers */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.ttf') format('truetype'), /* Safari, Android, iOS */
  url('Roboto/roboto-v20-latin-ext_cyrillic-ext_cyrillic_latin-700.svg#Roboto') format('svg'); /* Legacy iOS */
}
