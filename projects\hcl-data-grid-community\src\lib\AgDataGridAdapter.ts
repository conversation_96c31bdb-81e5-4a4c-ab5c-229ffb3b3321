import { SafeHtml } from "@angular/platform-browser";
import { <PERSON>umn<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>N<PERSON> } from "ag-grid-community";
import { CustomCellEditorComponent } from "./ag-grid/custom-cell-editor/custom-cell-editor.component";
import { CustomCellComponent } from "./ag-grid/custom-cell/custom-cell.component";
import { CustomHeaderComponent } from "./ag-grid/custom-header/custom-header.component";
import { CustomTooltipComponent } from "./ag-grid/custom-tooltip/custom-tooltip.component";
import { FullWidthCellRenderer } from "./ag-grid/full-width-cell-renderer/full-width-cell-renderer.component";
import { DataGridColumnConf, DataGridConf } from "./data-grid.conf";
import { DataGridV2Component } from "./data-grid-v2.component";

export class AgDataGridAdapter {
  isClientSideRowModel: boolean;
  /**
   * A map that has a colId & the col Definition
   * type {Map<string, TemplateRef<any>>}
   * private
   */
  _columnMap: Map<string, DataGridColumnConf> = new Map<
    string,
    DataGridColumnConf
  >();
  /**
   * array to maintain the columndefinitions
   */
  columnDefinitionsArray: Array<any> = [];
  /**
   * the grid API that will help us to play with the ag-grid
   */
  private gridApi: GridApi;
  /**
   * the grid-column API that will help us to play with the ag-grid columns
   */
  private gridColumnApi: ColumnApi;
  /**
   * The data grid component
   */
  private dataGrid: DataGridV2Component;
  /**
   * The default grid options that are set on teh grid
   */
  gridOptions: any = {
    suppressColumnVirtualisation: false,
    // remove the warnings on the custom properties added
    suppressPropertyNamesCheck: true,
  };
  /**
   * the object which stores the sort information
   */
  sortdata: any;
  /**
   * The data that is received from the configuration or the URL
   * that will be displayed in the grid
   */
  data: {}[];
  /**
   * the column definition that is understood by the ag-grid
   */
  columns: any[] = [];
  /**
   * the column definition that is understood by the ag-grid
   */
  rowSelection: string;
  /**
   * variable to decide if on row click the checkbox will be selected or not
   */
  suppressRowClickSelection: boolean;
  suppressRowTransform: boolean;

  /**
   * variable to decide if columns dragged outside of grid will get hidden
   */
  suppressDragLeaveHidesColumns: boolean;

  /**
   * variable to implement the isFullWidthCell(rowNode) callback,
   * to tell the grid which rows should be treated as fullWidth.
   */
  isFullWidthCell;
  // cacheBlocksize for lazy load, number of records to be rendered by grid as API success callback.
  cacheBlockSize;
  // rows to initially allow the user to scroll to
  infiniteInitialRowCount;
  /**
   * variable to provide a fullWidthCellRenderer,
   * to tell the grid what cellRenderer to use when doing fullWidth rendering.
   */
  fullWidthCellRenderer;
  getRowHeight;

  /**
  /**
   * In Ag-grid we can customize the rendering of the column header & the cells; however to achieve this
   * we need a component for each such renderer & the component needs to be registered with the
   * ag-grid framework to be used
   * type {{agColumnHeader: CustomHeaderComponent}}
   */
  frameworkComponents: any = {
    cellRenderer: CustomCellComponent,
    cellEditor: CustomCellEditorComponent,
    fullWidthCellRenderer: FullWidthCellRenderer,
    customTooltip: CustomTooltipComponent,
  };

  /**
   * The default elements that will be applied on all the columns
   * type {{tooltipComponent: string}}
   */
  defaultColDef: any = {
    tooltipComponent: "customTooltip",
  };
  /**
   * variables to hold the positions of the fixed column
   */
  dragAndDropPosition = 0;
  checkboxPosition = 0;
  actionsPosition = 0;
  /**
   * The configuration that is set for the Grid
   */
  private dataGridConfig: DataGridConf;
  noRowsTemplate: string;
  loadingTemplate: string;
  rowClassRules: any;
  isExternalFilterPresent: Function;
  doesExternalFilterPass: Function;
  /**
   * The constructor that takes the configuration & based on that the conversion will happen
   * param {DataGridConfig} config
   */

  /**
   * gets cacheBlocksize for lazy load,
   * number of records to be rendered by grid as API success callback.
   */
  getCacheBlockSize() {
    const gridRowModel = this.dataGrid.getRowModel();
    if (gridRowModel === "infinite") {
      return (
        this.dataGridConfig.infiniteScroll &&
        this.dataGridConfig.infiniteScroll.cacheBlockSize
      );
    } 
    // else if (gridRowModel === "serverSide") {
    //   return (
    //     this.dataGridConfig.serverSideScroll &&
    //     this.dataGridConfig.serverSideScroll.cacheBlockSize
    //   );
    // }
  }
  constructor(dataGrid: DataGridV2Component, config: DataGridConf) {
    // set default grid conf
    this.gridOptions.suppressColumnVirtualisation =
      config.suppressColumnVirtualisation ? true : false;
    // set the data-grid component
    this.dataGrid = dataGrid;
    // Lets set the dat ain the local variable
    this.dataGridConfig = config;
    this.isClientSideRowModel = config.isClientSideRowModel;
    this.data = config.data;
    this.rowSelection = config.rowSelectMode;
    this.cacheBlockSize = this.getCacheBlockSize();
    this.infiniteInitialRowCount = config.infiniteInitialRowCount;
    this.noRowsTemplate =
      config.noRowsTemplate ||
      config.noDataRetOnHttpResTemp ||
      "No rows to show.";
    this.loadingTemplate = config.loadingTemplate;
    this.suppressRowClickSelection = config.suppressRowClickSelection;
    this.suppressDragLeaveHidesColumns = config.suppressDragLeaveHidesColumns;
    this.suppressRowTransform = config.suppressRowTransform;
    this.rowClassRules = config.rowClassRules;
    this.isExternalFilterPresent = config.isExternalFilterPresent;
    this.doesExternalFilterPass = config.doesExternalFilterPass;
    const self = this;
    this.getRowHeight = (params) => {
      if (config.rowHeight) {
        return config.rowHeight;
      } else if (self.getFullWidthCell(params)) {
        return 100;
      } else {
        return params.node.rowHeight;
      }
    };
    /**
     * The isFullWidthCell(rowNode) callback takes a rowNode as input
     * and should return a boolean true (use fullWidth) or false .
     */
    this.isFullWidthCell = (rowNode) => {
      return self.getFullWidthCell(rowNode);
    };
    this.fullWidthCellRenderer = "fullWidthCellRenderer";
    this.populateSortOrderInColumns();
    // Populate the column definitions
    this.populateColumnDefinition();
    // IF we have the URL, make sure data is empty array
    if (this.dataGridConfig.dataUrl) {
      this.data = [];
    }
    this.sortdata = {};

    if (this.dataGridConfig.suppressKeyboardEvent) {
      this.defaultColDef.suppressHeaderKeyboardEvent = () => true;
      this.defaultColDef.suppressKeyboardEvent = () => true;
    }
  }

  /**
   * Gets all those cells whose width you want to set as 100%, loop over config -> fullWidthRow and compare
   * it with grid row nodes, it is called for each node one by one and gets cell / row info needed for drawing by framework.
   * Variable propertyNameToMatch should be common between config --> fullWidthRow object and rowNode,
   * the common property should be first property in fullWidthRow object.
   * params :- rowNode, the row in grid.
   */
  getFullWidthCell = (rowNode) => {
    let flag = false;
    // tslint:disable-next-line: no-unused-expression
    this.dataGridConfig.fullWidthRow &&
      this.dataGridConfig.fullWidthRow.forEach((fullWidthRow) => {
        const propertyNameToMatch =
          Object.keys(fullWidthRow) && Object.keys(fullWidthRow)[0];
        if (
          rowNode.data &&
          fullWidthRow[propertyNameToMatch] ===
            rowNode.data[propertyNameToMatch]
        ) {
          flag = true;
        }
      });
    return flag;
  };
  /**
   * This function will be called when a row is selected in the ag-grid
   * param data
   */
  selectionChanged(data: any): void {
    if (data.node.selected) {
      // This row has been selected
      this.dataGrid._rowSelected(data.node);
    } else {
      // The row has been deselected
      this.dataGrid._rowUnSelected(data.node);
    }
  }
  /**
   * this function will read the column defination from the DataGridConf & then
   * convert it into the meta-data that is required by the ag-datagrid
   */
  private populateColumnDefinition(): void {
    let columnSelection = false;
    this.dataGridConfig.columns.forEach((column: DataGridColumnConf) => {
      // Lets put it in the map
      this._columnMap.set(column.colId, column);

      const def: any = {
        headerName: column.header,
        field: column.field,
        suppressPaste: this.dataGridConfig.suppressPasteForAllColumns
          ? this.dataGridConfig.suppressPasteForAllColumns
          : false,
        resizable:
          column.resizable === undefined || column.resizable == null
            ? true
            : column.resizable,
        checkboxSelection:
          column.checkboxSelection === undefined ||
          column.checkboxSelection == null
            ? false
            : column.checkboxSelection,
        valueFormatter: column.dataFormatter
          ? this.cellDataFormatter.bind(this, column)
          : null,
        width: column.width,
        suppressMovable: column.suppressColumnReorder,
        headerTooltip: column.header,
        rowSpan: column.rowSpan,
        // headerComponentFramework: CustomHeaderComponent,
        // headerComponentParams: { gridAdapter: this, column: column },
        // _headerTemplateRef: column._headerTemplateRef,
        _templateRef: column._templateRef,
        _popoverTemplateRef: column._popoverTemplateRef,
        cellRendererParams: { gridAdapter: this },
        hide:
          column.isHidden === undefined || column.isHidden == null
            ? false
            : column.isHidden,
        colId: column.colId ? column.colId : column.field,
        editable: column.editable ? true : false,
        cellEditor: column.editable ? "cellEditor" : null,
        cellEditorParams: column.editable ? column.editableCellConf : null,
        sortable: column.sortable || false,
        _sorting: column._sorting || "NONE",
        suppressSizeToFit: !column.autoResizeToFit ? true : false,
        cellClassRules: column.cellClassRules ? column.cellClassRules : null,
        cellClass: column.cellClass ? column.cellClass : null,
        mapData: column.mapData ? column.mapData : null,
        canEdit:
          column.editableCellConf && column.editableCellConf.canEdit
            ? column.editableCellConf.canEdit
            : null,
        initEdit:
          column.editableCellConf && column.editableCellConf.initEdit
            ? column.editableCellConf.initEdit
            : null,
      };
      // if do not have have default useDefaultHeaderRenderer
      if (!column.useDefaultHeaderRenderer) {
        // we need default renderer, so chk if we have tooltip for header
        def.headerComponentFramework = CustomHeaderComponent;
        def.headerComponentParams = { gridAdapter: this, column: column };
        def._headerTemplateRef = column._headerTemplateRef;
      }
      if (column.minWidth) {
        def.minWidth = column.minWidth;
      }
      if (column.comparator) {
        def.comparator = column.comparator;
      }
      if (column.pinned) {
        def.pinned = column.pinned;
      }
      if (!column.useDefaultRenderer) {
        def.cellRenderer = "cellRenderer";
      }
      def.lockPinned = !!column.lockPinned;

      if (column.customLoadingRenderer && this.dataGridConfig.infiniteScroll) {
        def.cellRenderer = function (params) {
          if (params.value === undefined) {
            // when no node id, display the spinner image
            if (
              !column.customLoadingRenderer.defaultRenderer &&
              column.customLoadingRenderer.infiniteLoadingTemplate
            ) {
              return column.customLoadingRenderer.infiniteLoadingTemplate;
            } else {
              return '<div> <i class="spinner-border fa-pulse" style="color:#f5821e; height:15px; width:15px"> </div>';
            }
          } else {
            // otherwise just display node ID (or whatever you wish for this column)
            return params.value;
          }
        };
      }

      this.columns.push(def);
      if (column.checkboxSelection) {
        columnSelection = true;
      }
      // chk if we have a tooltip on the column
      if (column.tooltip) {
        def.tooltipField = column.field;
        def.tooltipComponentParams = {
          gridPosition:
            this.dataGrid.element.nativeElement.getBoundingClientRect(),
          getTooltip: column.tooltip.getTooltip,
        };
      }
    });
    // In case there is selection & no column has checkboxSelection, so we make the 1st column as check box
    if (
      (this.dataGridConfig.rowSelectMode === "single" ||
        this.dataGridConfig.rowSelectMode === "multiple") &&
      !columnSelection
    ) {
      // add checkbox column only when hideSelectionCheckBox is true, default value is false.
      if (!this.dataGridConfig.hideSelectionCheckBox) {
        this.columns.unshift({
          headerName: "",
          field: "",
          resizable: false,
          checkboxSelection: true,
          width: 60,
          colId: "checkbox",
          suppressSizeToFit: true,
          suppressMovable: true,
          headerCheckboxSelection:
            this.dataGridConfig.rowSelectMode === "multiple",
          headerCheckboxSelectionFilteredOnly:
            this.dataGridConfig.rowSelectMode === "multiple",
          cellClass: ["ag-custom-action-cell"],
        });
        this.actionsPosition++;
      }
    }

    // Row Drag and Drop Column
    if (this.dataGridConfig.dragAndDrop) {
      this.columns.unshift({
        headerName: "",
        field: "",
        resizable: false,
        suppressMovable: true,
        width: 12,
        suppressSizeToFit: true,
        cellRenderer: "cellRenderer",
        cellRendererParams: { gridAdapter: this },
        hide: false,
        colId: "dragAndDrop",
        editable: false,
        cellClass: ["ag-custom-action-cell"],
      });
      this.checkboxPosition++;
      this.actionsPosition++;
    }

    // In case we have the actions array we add 1 more column
    if (this.dataGridConfig.actions) {
      this.columns.push({
        headerName: "",
        field: "",
        width: 24,
        resizable: false,
        suppressMovable: true,
        cellRenderer: "cellRenderer",
        actions: this.dataGridConfig.actions,
        hide: this.dataGridConfig.hideActions,
        colId: "actions",
        editable: false,
        cellRendererParams: this.dataGridConfig.actions,
        suppressSizeToFit: true,
        pinned: "right",
        lockPinned: true,
        cellClass: ["ag-custom-action-cell"], // , 'lock-pinned']
      });
    }
  }

  /**
   * Add custom column definition dynamically in the grid
   * param {DataGridColumnConf} column
   */
  public addColumnDefination(column: DataGridColumnConf): void {
    this._columnMap.set(column.colId, column);

    let actionColumn = {};
    const def: any = {
      headerName: column.header,
      field: column.field,
      resizable:
        column.resizable === undefined || column.resizable == null
          ? true
          : column.resizable,
      checkboxSelection:
        column.checkboxSelection === undefined ||
        column.checkboxSelection == null
          ? false
          : column.checkboxSelection,
      valueFormatter: column.dataFormatter
        ? this.cellDataFormatter.bind(this, column)
        : null,
      width: column.width,
      suppressMovable: column.suppressColumnReorder,
      headerComponentFramework: CustomHeaderComponent,
      headerComponentParams: { gridAdapter: this, column: column },
      _headerTemplateRef: column._headerTemplateRef,
      _templateRef: column._templateRef,
      _popoverTemplateRef: column._popoverTemplateRef,
      cellRendererParams: { gridAdapter: this },
      hide:
        column.isHidden === undefined || column.isHidden == null
          ? false
          : column.isHidden,
      colId: column.colId ? column.colId : column.field,
      editable: column.editable ? true : false,
      cellEditor: column.editable ? "cellEditor" : null,
      cellEditorParams: column.editable ? column.editableCellConf : null,
      sortable: column.sortable || false,
      _sorting: column._sorting || "NONE",
      suppressSizeToFit: !column.autoResizeToFit ? true : false,
      cellClassRules: column.cellClassRules ? column.cellClassRules : null,
      cellClass: column.cellClass ? column.cellClass : null,
      mapData: column.mapData ? column.mapData : null,
      canEdit:
        column.editableCellConf && column.editableCellConf.canEdit
          ? column.editableCellConf.canEdit
          : null,
      initEdit:
        column.editableCellConf && column.editableCellConf.initEdit
          ? column.editableCellConf.initEdit
          : null,
    };

    if (this.dataGridConfig.actions) {
      actionColumn = this.columns.pop();
    }

    if (column.minWidth) {
      def.minWidth = column.minWidth;
    }
    if (column.comparator) {
      def.comparator = column.comparator;
    }
    if (!column.useDefaultRenderer) {
      def.cellRenderer = "cellRenderer";
    }

    // chk if we have a tooltip on the column
    if (column.tooltip) {
      def.tooltipField = column.field;
      def.tooltipComponentParams = {
        gridPosition:
          this.dataGrid.element.nativeElement.getBoundingClientRect(),
        getTooltip: column.tooltip.getTooltip,
      };
    }

    this.columns.push(def);
    this.columns.push(actionColumn);
    this.columnDefinitionsArray.push(def);
  }

  /**
   * This function will return the list of all the columns and the current positions & states
   * return {DataGridColumnConf[]}
   */
  getAllColumns(): DataGridColumnConf[] {
    return this.dataGridConfig.columns;
  }
  /**
   * This function will display the column in the data-grid
   */
  showColumn(colId: string) {
    this.gridColumnApi.setColumnVisible(colId, true);
    this.gridApi.sizeColumnsToFit();
    if (colId !== "actions") {
      // Lets now update in the def
      this._columnMap.get(colId).isHidden = false;
    }
  }
  /**
   * This function will hide the column in the data-grid
   */
  hideColumn(colId: string) {
    this.gridColumnApi.setColumnVisible(colId, false);
    this.gridApi.sizeColumnsToFit();
    if (colId !== "actions") {
      this._columnMap.get(colId).isHidden = true;
    }
  }
  /**
   * This function will return clicked grid row
   */
  rowClicked(data) {
    this.dataGrid._rowClicked(data.node);
  }
  /**
   * This function will return clicked grid cell
   */
  cellClicked(data) {
    this.dataGrid._cellClicked(data);
  }

  setNoRowTemplate(content) {
    if (this.gridApi) {
      content && content.length
        ? this.gridApi.hideOverlay()
        : this.gridApi.showNoRowsOverlay();
    }
  }
  /**
   * This function will set the data in the data grid
   * param data
   */
  setData(data: any): void {
    const topRowData = this.dataGridConfig.topRowData || [];

    this.data =
      data && data.content ? topRowData.concat(data.content) : topRowData;

    this.setNoRowTemplate(data);
  }

  showLoadingOverlay() {
    this.gridApi.showLoadingOverlay();
  }

  /**
   * Add and Remove the selected row unique identifier data into a set
   * for re-selection later on page change
   * @param data 
   * @param selectedRowsUniqueIdentifierData 
   * @param uniqueDataKeyForSelectionAcrossPages 
   * @param paginator 
   */
  public addRemoveSelectedRowData(data: any, selectedRowsUniqueIdentifierData: Set<number>, uniqueDataKeyForSelectionAcrossPages: string, paginator) {
    if (data.selected) {
      selectedRowsUniqueIdentifierData.add(data.data[uniqueDataKeyForSelectionAcrossPages]);
    } else {
      selectedRowsUniqueIdentifierData.delete(data.data[uniqueDataKeyForSelectionAcrossPages]);
    }
    paginator.totalSelectedItems = selectedRowsUniqueIdentifierData.size;
    paginator.refreshPage();
  }

  /**
   * This function would go in a loop thorugh all the records 
   * in the current page and select the required rows
   * @param selectedRowsUniqueIdentifierData 
   * @param uniqueDataKeyForSelectionAcrossPages 
   */
  public selectRowsForTheCurrentPage(selectedRowsUniqueIdentifierData : Set<number>, uniqueDataKeyForSelectionAcrossPages: string) {
    this.gridApi.forEachNode(node => {
      if (selectedRowsUniqueIdentifierData.has(node.data[uniqueDataKeyForSelectionAcrossPages])) {
       node.setSelected(true)
      }
    });
  }

  /**
   * This function will remove a row from the grid
   * param {number} index
   */
  public removeRow(data: number[]): any[] {
    const delArr: any[] = [];
    data.forEach((i) => {
      const d: any = this.gridApi.getModel().getRow(i).data;
      delArr.push(d);
    });
    this.gridApi.updateRowData({ remove: delArr });
    return delArr;
  }

  /**
   * Remove the mentioned object
   * param data
   * returns {any}
   */
  public removeRowObject(data: any): any {
    this.gridApi.updateRowData({ remove: [data] });
    return data;
  }
  /**
   * This function will remove a row from the grid
   * param {number} index
   */
  public addRow(index: number, data: any): void {
    this.gridApi.updateRowData({ add: [data], addIndex: index });
  }

  /**
   * In case the user wants to customize the manner in which the cells are being rendered, this function will be called
   * by the AgGrid & this function will internally call the dataFormatter that is set in the column Definition.
   * param {DataGridColumnConf} column
   * param data
   * return {SafeHtml}
   */
  private cellDataFormatter(column: DataGridColumnConf, data: any): SafeHtml {
    if (column.dataFormatter) {
      return column.dataFormatter({ column: column, data: data.data });
    }
    return data.data[column.field];
  }
  /**
   * called when the component is ready
   * param params
   */
  public componentReady(params: any) {
    // set the apis
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    // Make sure that we call the resize of the columns
    this.autoFit();
  }

  /**
   * Auto fit the grid
   */
  public autoFit(): void {
    // const arr: string[] = [];
    // this.columns.forEach(col => {
    //   if (col.colId !== 'checkbox') {
    //     arr.push(col.colId);
    //   }
    // });
    this.gridApi.sizeColumnsToFit();
    this.gridApi.resetRowHeights();
    // this.gridColumnApi.autoSizeColumns(arr, false);
  }

  /**
   *  Called when we want to do a sort on a particular column
   * param col
   */
  public doSort(col: any) {
    if (this.isClientSideRowModel) {
      this.columnDefinitionsArray.forEach((element, index) => {
        if (element.colId !== col.colId && element.sortable) {
          this.columnDefinitionsArray[index]._sorting = "NONE";
        }
      });

      if (col._sorting === "ASC") {
        this.gridApi.setSortModel([
          {
            colId: col.colId,
            sort: "asc",
          },
        ]);
      } else if (col._sorting === "DESC") {
        this.gridApi.setSortModel([
          {
            colId: col.colId,
            sort: "desc",
          },
        ]);
      } else {
        this.gridApi.setSortModel([]);
      }
    } else {
      this.dataGrid.sort(col);
    }
  }

  // Make sure the actions column is at last and checbox column is 1st one in all scenarios of column drag
  public fixedColumns() {
    if (this.dataGridConfig.actions) {
      this.gridColumnApi.moveColumn(
        "actions",
        this.dataGridConfig.columns.length + this.actionsPosition
      );
    }
    if (this.dataGridConfig.dragAndDrop) {
      this.gridColumnApi.moveColumn("dragAndDrop", this.dragAndDropPosition);
    }
    this.gridColumnApi.moveColumn("checkbox", this.checkboxPosition);
  }

  // Populates the sort order sent by user in the column config
  populateSortOrderInColumns() {
    if (this.dataGridConfig.queryParams) {
      this.dataGridConfig.columns.forEach((column) => {
        if (
          this.dataGridConfig.queryParams &&
          this.dataGridConfig.queryParams.sort &&
          this.dataGridConfig.queryParams.sort.length > 0
        ) {
          if (Array.isArray(this.dataGridConfig.queryParams["sort"])) {
            this.dataGridConfig.queryParams.sort.forEach((element) => {
              if (column.colId === element.split(",")[0]) {
                column._sorting = element.split(",")[1];
              }
            });
          } else {
            if (
              column.colId ===
              this.dataGridConfig.queryParams["sort"].split(",")[0]
            ) {
              column._sorting =
                this.dataGridConfig.queryParams["sort"].split(",")[1];
            }
          }
        }
      });
    }
  }

  public fetchSortInformation() {
    return this.sortdata;
  }
  convert(): any {}

  showActions(state: boolean) {
    if (state) {
      this.showColumn("actions");
    } else {
      this.hideColumn("actions");
    }
  }

  selectIndex(selectIndex: any, suppressEvents: boolean) {
    const rn: RowNode = this.gridApi.getModel().getRow(selectIndex);
    if (rn) {
      rn.setSelected(true, false, suppressEvents);
    } else {
      console.warn("Unable to find row at index " + selectIndex);
    }
  }

  deSelectAllRows(): void {
    this.gridApi.deselectAll();
  }

  updateCellData(colIds, value, selectIndex: any) {
    const rn: RowNode = this.gridApi.getModel().getRow(selectIndex);
    if (rn) {
      colIds.forEach((colId) => {
        rn.setDataValue(colId, value[colId]);
      });
    } else {
      console.warn("Unable to find row at index " + selectIndex);
    }
  }
  /**
   * this function will update the data in the rows as per the data sent to this function
   * param {any[]} data
   */
  updateRowData(data: any[]): void {
    this.gridApi.updateRowData({ update: data });
  }
}
