@use '../../assets/scss/variables' as *;

hcl-segment-metadata {
  height: 400px;
  overflow: hidden;
  display: block;
}

.segment-metadata-container {
  height: 400px;

  .w-300 {
    max-width: 300px;
  }

  .mt-20 {
    margin-top: 20px;
  }

  .browse-folder {
    display: flex;
    justify-content: space-between;
    align-items: baseline;

    hcl-input {
      flex: 1;
      margin-right: 20px;
    }
  }

  .chips-container {
    height: 120px;
    overflow-y: auto;
    margin-top: -15px;
    padding: 5px 0;

    .hcl-icon-close {
      color: #959595;
    }

    .rounded-chip {
      background-color: $card-background;
      border: 2px solid $tango-border-color;
      border-radius: 14px;
      display: inline-block;
      height: 32px;
      margin: 3px;
      padding: 2px 6px 2px 10px;
      max-width: 216px;

      &:hover {
        border-color: $ascent-color;
        background-color: $card-background;
        box-shadow: 6px 6px 10px 0 rgba(0, 0, 0, 0.3);
      }

      .chip-child {
        display: flex;
        align-items: center;

        .select-option {
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-left: 5px;
          margin-right: 3px;
          font-size: 14px;
          font-family: $font-secondary;
          margin-top: 1px;
        }

        i:hover {
          color: $ascent-color;
        }
      }
    }
  }
}

[class^="hcl-icon-"] {
  cursor: pointer;
  font-family: fontello;
  font-style: normal;
  font-weight: 400;
  display: inline-block;
  text-decoration: inherit;
  text-align: center;
  font-feature-settings: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.required {
  .mat-form-field-appearance-legacy .mat-form-field-label {
    &::before {
      content: "* ";
      color: $message-error;
      font-size: 14px;
    }
  }
}

.required-asterisk {
  &::after {
    content: " *";
    color: $message-error;
    font-size: 12px;
  }
}

.sidebarContainer {
  width: 600px;
  height: 100%;
  overflow: hidden;
}
