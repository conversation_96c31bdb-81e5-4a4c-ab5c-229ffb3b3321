
<div class="column" *ngFor="let column of layout.fields; let columnKey = index" cdkDropList mwlResizable
  [enableGhostResize]="false" [allowNegativeResizes]="false" [resizeSnapGrid]="{ left: fr, right: fr }" [resizeEdges]="{
    right: columnKey + 1 !== layout.columns,
    left: columnKey !== 0
  }" [validateResize]="validate" (resizing)="onResizeEnd($event, columnKey)"
  (cdkDropListDropped)="drop($event, column)" [class.empty]="!column.length" [ngStyle]="getColumnStyles(columnKey)" [cdkDropListConnectedTo]="ngb.fieldsDropList | async" [id]="createColumnId(columnKey)" [cdkDropListData]="column">
  <div *ngIf="!column.length" class="empty-field">{{ 'settings.drop-some-fields' | translate }}</div>
  <ip-field *ngFor="let field of column; let key = index" cdkDrag [cdkDragData]="field"
    [class.active]="(ngb.currentEditingField$ | async) === field" (mousedown)="editField.emit(field)" (click)="editField.emit(field)"
    (dublicate)="dublicateField(key, column, field)" (remove)="removeField(key, column)" [field]="field" (saveSnippet)="saveHtmlSnippet(field)" (invokeMaxAi)="openMaxAI($event)" >
    <!-- <div class="custom-placeholder" *cdkDragPlaceholder>
      <span>Drop me here</span>
    </div> -->
    <button mat-icon-button class="move" style="z-index: 1;" cdkDragHandle matTooltip="{{ 'settings.change-field-order' | translate }}">
      <mat-icon aria-label="Move field" inline>pan_tool</mat-icon>
    </button>
  </ip-field>

</div>

<ng-content select=".move" *ngIf="!layout.options.actions?.removeShuffle"></ng-content>
<div class="tools" fxLayoutGap="0.25rem">
  <button mat-icon-button class="edit" (click)="edit.emit()" color="primary"
    matTooltip="{{ 'settings.change-layout-settings' | translate }}">
    <mat-icon aria-label="Move layout" inline>edit</mat-icon>
  </button>
  <button *ngIf="!layout.options.actions?.removeDuplicate" mat-icon-button (click)="dublicate.emit()" matTooltip="{{ 'settings.duplicate-layout' | translate }}">
    <mat-icon aria-label="Dublicate layout" inline>file_copy</mat-icon>
  </button>
  <button *ngIf="!layout.options.actions?.removeDelete" mat-icon-button color="warn" (click)="remove.emit()" matTooltip="{{ 'settings.delete-layout' | translate }}">
    <mat-icon aria-label="Remove Layout" inline>delete_forever</mat-icon>
  </button>
</div>
<div class="tools hiddenOn" *ngIf="layout.options.hideOn" fxLayoutGap="0.25rem">
  <button mat-icon-button *ngIf="layout.options.hideOn=='desktop'" class="mt-1" style="z-index: 1;" cdkDragHandle value="desktop" hclTooltip="{{ 'settings.hidden-on-desktop' | translate}}">
    <i class="hcl-icon-hide-on-desktop"></i>
  </button>
  <button mat-icon-button *ngIf="layout.options.hideOn=='mobile'" class="mt-1" style="z-index: 1;" cdkDragHandle value="mobile" hclTooltip="{{ 'settings.hidden-on-mobile' | translate}}">
    <i class="hcl-icon-hide-on-mobile"></i>
  </button>
</div>