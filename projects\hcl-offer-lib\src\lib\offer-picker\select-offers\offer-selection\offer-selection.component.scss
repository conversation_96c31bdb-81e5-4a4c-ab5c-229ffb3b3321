.outer-manage-selection-container {
  width: 100%;
  .manage-selection-container {
    display: flex;
    align-items: center;
    height: 85vh;
    .toggle-selection {
      height: 100%;
      width: 60px;
      background-color: #ececec;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      h3 {
        color: #6d7692;
        font-family: "Montserrat";
        font-size: 20px;
        font-weight: bold;
        line-height: 24px;
        writing-mode: vertical-lr;
        transform: rotate(180deg);

        &.reset-transform-rotate {
          transform: rotate(0deg);
        }
      }
    }

    .manage-selection-listing {
      box-shadow: -15px 0 10px -2px rgba(0, 0, 0, 0.3);
      flex-grow: 1;
      height: 100%;
      .manage-selection-label {
        color: #6d7692;
        font-family: "Montserrat";
        font-size: 20px;
        font-weight: bold;
        letter-spacing: 0;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin: 0 0 0 40px;
      }
      .selection-count {
        margin: 0 0 0 40px;
      }
      .manage-selection-list {
        margin: 0 0 10px 0;
        overflow: auto;
        height: calc(100% - 0px) !important;
        .selection-row {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          height: 57px;
          background-color: #f5f5f5;
          box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
          margin: 10px 0;
          .checkbox-container {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px 10px 10px 10px;
          }
          .cell {
            width: 300px;
            padding: 5px 10px;
            color: #6d7692;
            font-family: "Montserrat";
            font-size: 14px;
            letter-spacing: 0;
          }
        }
      }
    }
  }

  .manage-selection-list-container {
    padding: 0 40px;
  }

  .link {
    color: #0078d8;
    &:hover {
      cursor: pointer;
    }
  }
  .ellipsis {
    &:hover {
      cursor: pointer;
    }
  }
  .offer-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin: 10px 0 0 0;
    .action-button {
      margin: 0 10px;
    }
  }

  .top-label {
    position: absolute;
    top: -10px;
  }

  .bottom-label {
    position: absolute;
    top: 10px;
    font-family: "Roboto";
    font-size: 12px;
    letter-spacing: 0.4px;
  }

  .bullet-round {
    height: 10px;
    width: 10px;
    border-radius: 50%;
    display: inline-block;
  }

  .green-bullet {
    background-color: #c4d056;
  }
  .grey-bullet {
    background-color: #bcbbbb;
  }
  .orange-bullet {
    background-color: #ffb755;
  }
}

.manage-selection-list {
  // height: calc(100% - 135px) !important;

  .hcl-grid-container {
    height: calc(100% - 10px) !important;
  }
}

.retire-row-text-color {
  color: #b8b7b7;
  pointer-events: none;
}

.opacity1,
.opacity2 {
  height: calc(100% - 0px) !important;
}
