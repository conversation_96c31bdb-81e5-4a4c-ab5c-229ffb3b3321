import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OfferPreviewComponent } from './offer-preview/offer-preview.component';
import { HclAngularWidgetsLibModule } from 'hcl-angular-widgets-lib';
import { OfferMetadataComponent } from './offer-metadata/offer-metadata.component';
import { AssetPickerItemDetailsComponent } from './asset-picker-item-details/asset-picker-item-details.component';
import { OfferListPreviewComponent } from './offer-list-preview/offer-list-preview.component';
import { HclDataGridModule } from 'hcl-data-grid-lib';




@NgModule({
  declarations: [
    OfferPreviewComponent,
    OfferMetadataComponent,
    AssetPickerItemDetailsComponent,
    OfferListPreviewComponent
  ],
  imports: [
    CommonModule,
    HclAngularWidgetsLibModule,
    HclDataGridModule
  ],
  exports: [
    OfferPreviewComponent,
    OfferListPreviewComponent
  ]
})
export class PreviewModule { }
