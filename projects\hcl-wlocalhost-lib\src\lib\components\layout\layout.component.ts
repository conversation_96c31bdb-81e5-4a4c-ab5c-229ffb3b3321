import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewEncapsulation,
  ChangeDetectorRef,
  ChangeDetectionStrategy,
  Do<PERSON>heck,
  HostListener,
  OnInit,
  HostBinding
} from '@angular/core';
import { TLayouts, defaultColumnsOptions } from '../../classes/Layouts';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { TFields } from '../../classes/Fields';
import { createBorder } from '../../utils';
import { ResizeEvent } from 'angular-resizable-element';

@Component({
  selector: 'ip-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.Emulated
})
export class LayoutComponent implements DoCheck, OnInit {
  @Input() layout = new TLayouts();
  @Input() index: number;
  @Input() frameKey: number;
  @Output() editField = new EventEmitter();
  @Output() edit = new EventEmitter();
  @Output() dublicate = new EventEmitter();
  @Output() remove = new EventEmitter();
  @Output() saveSnippet = new EventEmitter();
  @Output() openMaxAISidebar = new EventEmitter();

  public fr: number;
  public validate = function ({ rectangle }: ResizeEvent) {
    const colFr = +(rectangle.width / this.fr).toFixed(2);
    return colFr > 1 && colFr <= 10 - this.layout.columns;
  }.bind(this);

  // Don't close right panel on layout click
  @HostListener('click', ['$event']) onHostClikc(event: MouseEvent) {
    event.stopImmediatePropagation();
  }

  // @HostBinding('style.gridTemplateColumns') get getColumns() {
  //   // console.log(this.layout.options.columnsWidth);
  //   return this.layout.options.columnsWidth.map(fr => `${fr}fr`).join(' ');
  // }

  constructor(
    public ngb: IpEmailBuilderService,
    private chRef: ChangeDetectorRef
  ) {
  }

  async removeField(key: number, column: TFields[]) {
    const hasRemoved = await this.ngb.removeField(key, column);
    if (hasRemoved) {
      this.editField.emit(null);
    }
  }

  dublicateField(key: number, column: TFields[], field: TFields) {
    this.ngb.dublicateField(key, column, field);
  }

  drop(event: CdkDragDrop<TFields[]>, column: TFields[]) {
    if (event.previousContainer === event.container) {
      this.ngb.changeFieldOrder(event, column);
    } else {
      this.ngb.addField(event, column);
    }
  }

  createColumnId(columnKey: number) {
    return `column-droplist-fields-${columnKey}-${this.index}-${this.frameKey}`;
  }

  getColumnStyles(columnKey: number) {
    const {
      options: { gaps = [4, 4], columns = [] }
    } = this.layout;
    const column = columns[columnKey] || defaultColumnsOptions;

    let verticalAlign = 'center';
    if (column.verticalAlign === 'bottom') {
      verticalAlign = 'flex-end';
    } else if (column.verticalAlign === 'top') {
      verticalAlign = 'flex-start';
    }

    let width;

    if (this.layout.type == 'cols_1') {
      width = '100%'
    }

    if (this.layout.type == 'cols_2') {
      width = '50%'
    }

    if (this.layout.type == 'cols_3') {
      width = '33.3%'
    }

    if (this.layout.type == 'cols_4') {
      width = '25%'
    }

    if (this.layout.type == 'cols_21') {
      if (columnKey === 0) {
        width = '33.3%'
      } else {
        width = '66.6%'
      }
    }

    if (this.layout.type == 'cols_12') {
      if (columnKey === 0) {
        width = '66.6%'
      } else {
        width = '33.3%'
      }
    }

    return {
      padding: gaps.map(gap => `${gap}px`).join(' '),
      backgroundColor: column.background.color,
      placeSelf: `${verticalAlign} stretch`,
      ...createBorder(column.border),
      float: 'left',
      display: 'inline-block',
      width
    };
  }

  onResizeEnd({ rectangle }: ResizeEvent, key: number) {
    this.layout.options.columnsWidth[key] = +(
      rectangle.width / this.fr
    ).toFixed(2);
  }

  ngDoCheck() {
    const field = this.ngb.currentEditingField$.getValue();
    if (
      this.layout === this.ngb.currentEditingLayout$.getValue() ||
      this.layout.fields.some(columns => columns.indexOf(field) > -1)
    ) {
      this.chRef.markForCheck();
    }
  }

  ngOnInit() {
    // Add columns options in case of old template
    if (
      !this.layout.options.columns ||
      !this.layout.options.columnsWidth
    ) {
      const { type, fields, options } = this.layout;
      this.ngb.currentEditingLayout$.next(
        new TLayouts(type, fields, options)
      );
    }

    const width = this.ngb.Email.general.width.value;
    const [, horizontalGap] = this.layout.options.gaps;
    this.fr = (width - horizontalGap * this.layout.columns) / 10;
  }

  saveHtmlSnippet(column) {
    this.saveSnippet.emit(column);
  }
  
  openMaxAI(event) {  
    this.openMaxAISidebar.emit(event);
  }
}
