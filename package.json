{"name": "hcl-angular-widgets", "version": "25.1", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "widgets": "ng build --configuration production hcl-angular-widgets-lib", "data-grid-community": "ng build --configuration production hcl-data-grid-community", "data-grid-enterprise": "ng build --configuration production data-grid-enterprise", "approval": "ng build --configuration production hcl-angular-approval-lib", "wlocalhost": "ng build --configuration production hcl-wlocalhost-lib", "charts": "ng build --configuration production hcl-angular-charts-lib", "com-resources": "ng build --configuration production com-resources-lib", "cif-object-mapping": "ng build --configuration production hcl-cif-object-mapping-lib", "cif-export-object": "ng build --configuration production hcl-export-object-cif-lib", "segmentation": "ng build --configuration production hcl-segmentation-lib", "code-editor": "ng build --configuration production hcl-code-editor-lib", "offer": "ng build --configuration production hcl-offer-lib", "flowchart": "ng build --configuration production hcl-angular-flowchart-lib", "messenger": "ng build --configuration production hcl-angular-messenger-lib", "build-all": "yarn run widgets && yarn run data-grid-community && yarn run data-grid-enterprise && yarn run approval && yarn run wlocalhost && yarn run charts && yarn run cif-object-mapping && yarn run cif-export-object && yarn run segmentation && yarn run code-editor && yarn run offer && yarn run flowchart && yarn run messenger && yarn run com-resources"}, "private": true, "dependencies": {"@ag-grid-enterprise/all-modules": "22.0.0", "@ag-grid-enterprise/core": "22.0.0", "@angular/animations": "14.3.0", "@angular/cdk": "14.2.7", "@angular/common": "14.3.0", "@angular/compiler": "14.3.0", "@angular/core": "14.3.0", "@angular/flex-layout": "12.0.0-beta.34", "@angular/forms": "14.3.0", "@angular/material": "14.2.7", "@angular/platform-browser": "14.3.0", "@angular/platform-browser-dynamic": "14.3.0", "@angular/router": "14.3.0", "@ctrl/ngx-emoji-mart": "^6.0.0", "@ngx-translate/core": "11.0.1", "@ngx-translate/http-loader": "4.0.0", "@types/d3": "^5.7.2", "ag-grid-angular": "^22.0.0", "ag-grid-community": "^22.0.0", "alwan": "^2.0.2", "angular-froala-wysiwyg": "4.0.19", "angular-gridster2": "8.0.0", "angular-resizable-element": "^3.3.3", "backbone": "1.4.0", "bootstrap": "4.3.1", "codemirror": "^5.65.12", "core-js": "^2.5.7", "d3": "^5.12.0", "d3-sankey": "^0.12.3", "dagre": "0.8.4", "echarts": "^5.5.0", "froala-editor": "4.0.19", "graphlib": "2.1.7", "html2canvas": "1.4.1", "htmlhint": "^1.1.4", "jquery": "3.5.1", "js-beautify": "^1.14.7", "jspdf": "3.0.1", "lodash": "4.17.21", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "ngx-color": "7.2.0", "ngx-quill": "14.2.0", "ngx-skeleton-loader": "4.0.0", "ngx-tribute": "^1.5.0", "package-json": "^10.0.1", "quill": "1.3.7", "rxjs": "6.6.7", "tributejs": "^4.0.0", "tslib": "^2.0.0", "typewriter-effect": "^2.21.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.11", "@angular/cli": "14.2.11", "@angular/compiler-cli": "14.3.0", "@angular/language-service": "14.3.0", "@types/backbone": "1.4.1", "@types/codemirror": "^5.60.7", "@types/jasmine": "3.4.0", "@types/jasminewd2": "2.0.6", "@types/jquery": "3.3.31", "@types/js-beautify": "^1.13.3", "@types/lodash": "4.14.137", "@types/node": "~12.7.1", "codelyzer": "~5.1.0", "cpx": "^1.5.0", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~5.0.0", "karma": "6.3.16", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "ng-packagr": "^14.2.2", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tsickle": "0.39.1", "tslint": "~6.1.0", "typescript": "~4.6.4"}, "resolutions": {"webpack": "^5.0.0", "ansi-regex": "5.0.1", "chart.js": "3.5.1", "glob-parent": "5.1.2", "nth-check": "2.0.1", "engine.io": "6.4.2", "json-schema": "0.4.0", "log4js": "6.4.1", "node-forge": "1.0.0", "nanoid": "3.1.31", "follow-redirects": "1.14.8", "url-parse": "1.5.9", "tar": "6.2.1", "jszip": "3.10.1", "minimist": "1.2.6", "async": "3.2.2", "jquery-ui": "1.13.1", "d3-color": "3.1.0", "@types/d3-color": "3.1.0", "d3-interpolate": "3.0.1", "@types/d3-interpolate": "3.0.1", "json5": "2.2.2", "minimatch": "3.0.5", "loader-utils": "2.0.4", "xml2js": "0.5.0", "semver": "7.5.2", "tough-cookie": "4.1.3", "postcss": "8.4.31", "@babel/traverse": "7.25.3", "jackspeak": "2.1.1", "webpack-dev-middleware": "6.1.2", "micromatch": "4.0.8", "braces": "3.0.3", "ws": "8.17.1", "cookie": "0.7.0", "selenium-webdriver": "4.21.0", "canvg": "4.0.3", "@babel/helpers": "7.26.10", "@babel/runtime": "7.26.10", "cross-spawn": "7.0.5", "dompurify": "3.2.4", "@babel/core": "7.26.10", "https-proxy-agent": "5.0.1"}}