.mapping-wrapper {
  display: block;
  position: relative;

  .content-mapping {
    display: flex;
    align-items: baseline;
    border-bottom: 1px solid #e0e0e0;

    .attribute-tile {
      width: 50%;
      color: #444444;
      font-family: <PERSON>o;
      font-size: 15px;
      letter-spacing: 0;
      line-height: 16px;

      &.ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      span {
        color: #6d7692;
        font-family: Roboto;
        font-size: 12px;
        letter-spacing: 0.4px;
        line-height: 15px;
      }
    }

    .mapped-to-container {
      width: 50%;

      .cms-attr-container {
        padding-top: 3px;
        display: flex;

        .mapping-error {
          color: #ffb755;
          position: absolute;
          top: 18px;
          right: 20px;
          z-index: 1;
        }
      }
    }
  }
}

.empty-search-result {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 0 10px 0;
}

.mat-form-field {
  .mat-form-field-suffix {
    .hcl-icon-close-x {
      font-size: 10px;
    }
  }
}
