<!-- split equation -->
<div class="pt-3" *ngIf="config.showSplitEquation">
    <span class="font-roboto">{{splitEquationLabel}} - {{splitEquation}}</span>
</div>

<div [formGroup]="queryBuilderForm">
    <div class="row parent-height">
        <div class="col-12 connector query-builder pr-5">
            <!-- group loop -->
            <div class="group" *ngFor="let group of groupArray, let i = index"
                [ngClass]="group.grplevel === 1 ? 'level1': group.grplevel === 2 ? 'level2':
    group.grplevel === 3 ? 'level3' : group.grplevel === 4 ? 'level4' : group.grplevel === 5 ? 'level5' : 'mt-4 ml-6 level0'">
                <div class="d-flex justify-content-between">
                    <div class="font-roboto"
                        [ngClass]="{'border-bottom':!config.hideConditionToggle, 'border-dark': !config.hideConditionToggle}"
                        *ngIf="!config.readOnlyMode">
                        <ng-container *ngIf="!config.hideConditionToggle">
                            <span class="font-roboto"
                                [ngClass]="{'selectedToggle': !groupArray[i].toggleConfig.checked}">{{orLabel}}</span>
                            <hcl-slide-toggle class="mx-2" [config]="group.toggleConfig"
                                (toggleChange)="toggleChange($event, i)">
                            </hcl-slide-toggle>
                            <span class="font-roboto"
                                [ngClass]="{'selectedToggle': groupArray[i].toggleConfig.checked}">{{andLabel}}</span>
                        </ng-container>
                        <ng-container *ngIf="config.hideConditionToggle && config.staticAndOrLabel">
                            <div class="static-and-or-label ellipsis" hclTooltip="{{config.staticAndOrLabel}}">
                                {{config.staticAndOrLabel}}</div>
                        </ng-container>
                    </div>
                    <div class="mr-3 border-bottom border-dark font-roboto selectedToggle" *ngIf="config.readOnlyMode">
                        {{groupArray[i].toggleConfig.checked ? andLabel : orLabel}}
                    </div>
                    <div class="" *ngIf="!config.readOnlyMode">
                        <a href="javascript:void(0)" class="mr-2 font-roboto font-size-14" (click)="addRule(i)">
                            <i title="{{addRuleLabel}}" class="hcl-icon-add-button"></i>{{addRuleLabel}}</a>
                        <a href="javascript:void(0)" class="mr-2 font-roboto font-size-14"
                            *ngIf="group.grplevel !== config.maxGrpLevel" (click)="addGroup(group, i)">
                            <i title="{{addGroupLabel}}" class="hcl-icon-add-button"></i>{{addGroupLabel}}</a>
                        <a href="javascript:void(0)" class="font-roboto font-size-14" *ngIf="group.grplevel !== 0"
                            (click)="deleteGroup(i)">
                            <i title="{{deleteGroupLabel}}" class="hcl-icon-delete"></i>{{deleteGroupLabel}}</a>
                    </div>
                </div>

                <!-- rule loop -->
                <div formArrayName="ruleArray">
                    <div class="row ml-0 ruler" *ngFor="let row of ruleArray.at(i)['controls'], let j = index"
                        [formArrayName]="i">
                        <div class="row mt-2 ml-3 ruler-row justify-content-between"
                            style="width: 97%; padding-top: 10px"
                            [ngClass]="group.grplevel === 1 ? 'subLevel1': group.grplevel === 2 ? 'subLevel2':
                            group.grplevel === 3 ? 'subLevel3' : group.grplevel === 4 ? 'subLevel4' : group.grplevel === 5 ? 'subLevel5' : ''">
                            <!-- Row Fields -->
                            <div class="row ml-3">
                                <hcl-drop-down class="dropdown ml-3" [config]="configArray[i][j].fieldName"
                                    (select)="onFieldChange($event, i, j)"
                                    (panelStateChange)="panelStateChanged($event)">
                                    <ng-template hclTemplate hclTemplateName="dynamicAction" #dynamicAction>
                                        <div *ngIf="fieldNameConfig.options.length > 4">
                                            <hcl-input (keydown)="$event.stopPropagation()"
                                                (valueEntered)="filterOptions($event, i, j)" [config]="sourceConfig">
                                            </hcl-input>
                                            <div class="no-result-found"
                                                *ngIf="configArray[i][j].fieldName.options.length === 0">
                                                {{noResultFoundMsg}}</div>
                                        </div>
                                    </ng-template>
                                </hcl-drop-down>

                                <hcl-drop-down class="dropdown" [config]="configArray[i][j].condition"
                                    (select)="onConditionChange($event, i, j)"></hcl-drop-down>

                                <hcl-input [config]="configArray[i][j].conditionValue"
                                    (keypress)="restrictInput($event,i, j)" (paste)="restrictInput($event,i, j)">
                                </hcl-input>
                                <span class="mx-3 mt-4" *ngIf="configArray[i][j].condition.formControl.value === 'between' ||
                    configArray[i][j].condition.formControl.value === 'notbetween'"> & </span>
                                <hcl-input *ngIf="configArray[i][j].condition.formControl.value === 'between' ||
                    configArray[i][j].condition.formControl.value === 'notbetween'"
                                    [config]="configArray[i][j].conditionValue2">
                                </hcl-input>
                            </div>
                            <div class="hcl-hover-icons mr-3  d-flex align-items-center"
                                *ngIf="this.configArray[i].length !== 1 && !config.readOnlyMode">
                                <i title="Delete Rule" [class]="config.deleteRuleIcon"
                                    style="color:#959595;font-size: 20px;" (click)="deleteRule(i, j)"></i>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<ng-template #actionsTemplate>
    <div class="template-actions">
        <hcl-button class="pr-2" [config]="cancelModalBtnConf" (onclick)="modalService.closeDialog()"></hcl-button>
        <hcl-button [config]="deleteGroupBtnConf" (onclick)="modalService.closeDialog();deleteNestedGroups();">
        </hcl-button>
    </div>
</ng-template>
<!-- Form Approach -->
<!-- <form> -->
<!-- Group Div-->
<!-- <div *ngFor="let group of groups, let gi = index"> -->
<!-- And OR Logic-->
<!-- <div>
            <span>AND</span>
            <hcl-slide-toggle [config]="group.toggleConfig" (toggleChange)="toggleChange($event, gi)"></hcl-slide-toggle>
            <span>OR</span>
        </div> -->

<!-- Add Rule Btn & Add Group Btn -->
<!-- <div>
            <hcl-button (click)="addRule(gi)">Add Rule</hcl-button>
            <hcl-button (click)="addGroup(gi)">Add Group</hcl-button>
        </div> -->

<!-- Condition Div -->
<!-- <div formArrayName="ruleRows">
            <div *ngFor="let row of ruleRows.at(gi).controls, let ri = index"> -->
<!-- Field -->
<!-- <hcl-drop-down [config]="configArray[gi][ri].fieldName"></hcl-drop-down> -->
<!-- Condition -->
<!-- Value 1 -->
<!-- Optional Value 2 -->
<!-- </div>
        </div>
    </div>
</form> -->

<!-- without Form -->
<!-- Use <mat-select> instead of <hcl-drop-down> -->