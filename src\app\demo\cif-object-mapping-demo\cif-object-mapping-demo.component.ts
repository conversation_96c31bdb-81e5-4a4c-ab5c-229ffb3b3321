import { Component, OnInit, ViewChild } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ButtonConf, SideBarComponent } from 'hcl-angular-widgets-lib';
import { ObjectMappingConf } from 'projects/hcl-cif-object-mapping-lib/src/lib/object-mapping-configs';
import { HclCifObjectMappingLibService } from 'projects/hcl-cif-object-mapping-lib/src/lib/hcl-cif-object-mapping-lib.service';

@Component({
    selector: 'app-cif-object-mapping-demo',
    templateUrl: './cif-object-mapping-demo.component.html',
    styleUrls: ['./cif-object-mapping-demo.component.scss']
})
export class CifObjectMappingDemoComponent implements OnInit {

    @ViewChild('sideBar') sideBarComponentRef1: SideBarComponent;

    readyToRender = false;
    buttonConfig: ButtonConf;

    previousData = {
        "attributeMappings": [
            {
                "attribute1Id": "CUSTOMER_ID",
                "attribute2Id": "firedAt"
            }
        ],
        "object1": {
            "applicationId": "Journey",
            "objectType": "EntrySource",
            "schema": "{\"$schema\":\"https://json-schema.org/draft/2019-09/schema\",\"$defs\":null,\"type\":\"object\",\"properties\":{\"CUSTOMER_ID\":{\"$id\":\"CUSTOMER_ID\",\"title\":\"CUSTOMER_ID\",\"type\":\"number\",\"jsonPointer\":\"/CUSTOMER_ID\",\"maxItems\":1},\"FIRST_NAME\":{\"$id\":\"FIRST_NAME\",\"title\":\"FIRST_NAME\",\"type\":\"string\",\"jsonPointer\":\"/FIRST_NAME\",\"maxItems\":1},\"LAST_NAME\":{\"$id\":\"LAST_NAME\",\"title\":\"LAST_NAME\",\"type\":\"string\",\"jsonPointer\":\"/LAST_NAME\",\"maxItems\":1},\"GENDER\":{\"$id\":\"GENDER\",\"title\":\"GENDER\",\"type\":\"string\",\"jsonPointer\":\"/GENDER\",\"maxItems\":1},\"AGE\":{\"$id\":\"AGE\",\"title\":\"AGE\",\"type\":\"number\",\"jsonPointer\":\"/AGE\",\"maxItems\":1},\"EMAIL\":{\"$id\":\"EMAIL\",\"title\":\"EMAIL (Email ID)\",\"type\":\"string\",\"format\":\"email\",\"jsonPointer\":\"/EMAIL\",\"maxItems\":1},\"MOBILE\":{\"$id\":\"MOBILE\",\"title\":\"MOBILE (Mobile number)\",\"type\":\"string\",\"jsonPointer\":\"/MOBILE\",\"maxItems\":1},\"EDUCATION\":{\"$id\":\"EDUCATION\",\"title\":\"EDUCATION\",\"type\":\"string\",\"jsonPointer\":\"/EDUCATION\",\"maxItems\":1},\"OCCUPATION\":{\"$id\":\"OCCUPATION\",\"title\":\"OCCUPATION\",\"type\":\"string\",\"jsonPointer\":\"/OCCUPATION\",\"maxItems\":1},\"EXPERIENCE\":{\"$id\":\"EXPERIENCE\",\"title\":\"EXPERIENCE\",\"type\":\"number\",\"jsonPointer\":\"/EXPERIENCE\",\"maxItems\":1},\"SALARY\":{\"$id\":\"SALARY\",\"title\":\"SALARY\",\"type\":\"number\",\"jsonPointer\":\"/SALARY\",\"maxItems\":1},\"MARITAL_STATUS\":{\"$id\":\"MARITAL_STATUS\",\"title\":\"MARITAL_STATUS\",\"type\":\"string\",\"jsonPointer\":\"/MARITAL_STATUS\",\"maxItems\":1},\"NUMBER_OF_CHILDREN\":{\"$id\":\"NUMBER_OF_CHILDREN\",\"title\":\"NUMBER_OF_CHILDREN\",\"type\":\"number\",\"jsonPointer\":\"/NUMBER_OF_CHILDREN\",\"maxItems\":1},\"CREDIT_SCORE\":{\"$id\":\"CREDIT_SCORE\",\"title\":\"CREDIT_SCORE\",\"type\":\"number\",\"jsonPointer\":\"/CREDIT_SCORE\",\"maxItems\":1},\"STATE\":{\"$id\":\"STATE\",\"title\":\"STATE\",\"type\":\"string\",\"jsonPointer\":\"/STATE\",\"maxItems\":1},\"CITY\":{\"$id\":\"CITY\",\"title\":\"CITY\",\"type\":\"string\",\"jsonPointer\":\"/CITY\",\"maxItems\":1},\"ZIP_CODE\":{\"$id\":\"ZIP_CODE\",\"title\":\"ZIP_CODE\",\"type\":\"number\",\"jsonPointer\":\"/ZIP_CODE\",\"maxItems\":1},\"COUNTRY\":{\"$id\":\"COUNTRY\",\"title\":\"COUNTRY\",\"type\":\"string\",\"jsonPointer\":\"/COUNTRY\",\"maxItems\":1},\"CARD_MEMBER_ID\":{\"$id\":\"CARD_MEMBER_ID\",\"title\":\"CARD_MEMBER_ID\",\"type\":\"number\",\"jsonPointer\":\"/CARD_MEMBER_ID\",\"maxItems\":1},\"FIRST_PURCHASE\":{\"$id\":\"FIRST_PURCHASE\",\"title\":\"FIRST_PURCHASE\",\"type\":\"string\",\"jsonPointer\":\"/FIRST_PURCHASE\",\"maxItems\":1},\"HAS_DEBITCARD\":{\"$id\":\"HAS_DEBITCARD\",\"title\":\"HAS_DEBITCARD\",\"type\":\"string\",\"jsonPointer\":\"/HAS_DEBITCARD\",\"maxItems\":1},\"MONTHLY_USAGE_NUMBER\":{\"$id\":\"MONTHLY_USAGE_NUMBER\",\"title\":\"MONTHLY_USAGE_NUMBER\",\"type\":\"number\",\"jsonPointer\":\"/MONTHLY_USAGE_NUMBER\",\"maxItems\":1},\"CALL_OPTIN\":{\"$id\":\"CALL_OPTIN\",\"title\":\"CALL_OPTIN\",\"type\":\"string\",\"jsonPointer\":\"/CALL_OPTIN\",\"maxItems\":1},\"EMAIL_OPTIN\":{\"$id\":\"EMAIL_OPTIN\",\"title\":\"EMAIL_OPTIN\",\"type\":\"string\",\"jsonPointer\":\"/EMAIL_OPTIN\",\"maxItems\":1},\"SMS_OPTIN\":{\"$id\":\"SMS_OPTIN\",\"title\":\"SMS_OPTIN\",\"type\":\"string\",\"jsonPointer\":\"/SMS_OPTIN\",\"maxItems\":1},\"PUSH_OPTIN\":{\"$id\":\"PUSH_OPTIN\",\"title\":\"PUSH_OPTIN\",\"type\":\"string\",\"jsonPointer\":\"/PUSH_OPTIN\",\"maxItems\":1},\"WHATSAPP_OPTIN\":{\"$id\":\"WHATSAPP_OPTIN\",\"title\":\"WHATSAPP_OPTIN\",\"type\":\"string\",\"jsonPointer\":\"/WHATSAPP_OPTIN\",\"maxItems\":1},\"IS_SUPPRESSED\":{\"$id\":\"IS_SUPPRESSED\",\"title\":\"IS_SUPPRESSED\",\"type\":\"string\",\"jsonPointer\":\"/IS_SUPPRESSED\",\"maxItems\":1},\"IS_ACTIVATED\":{\"$id\":\"IS_ACTIVATED\",\"title\":\"IS_ACTIVATED\",\"type\":\"string\",\"jsonPointer\":\"/IS_ACTIVATED\",\"maxItems\":1},\"APPLY_LOYALTY\":{\"$id\":\"APPLY_LOYALTY\",\"title\":\"APPLY_LOYALTY\",\"type\":\"string\",\"jsonPointer\":\"/APPLY_LOYALTY\",\"maxItems\":1}}}",
            "objectId": "tempId-1664345566418"
        },
        "object2": {
            "applicationId": "Mailchimp",
            "objectType": "dc72705d40",
            "objectId": null,
            "schema": "{\"$schema\":\"https://json-schema.org/draft/2019-09/schema\",\"$defs\":null,\"type\":\"object\",\"properties\":{\"id\":{\"$id\":\"id\",\"title\":\"Identifier\",\"type\":\"string\",\"jsonPointer\":\"/id\",\"maxItems\":1},\"list_id\":{\"$id\":\"list_id\",\"title\":\"Unique audience id\",\"type\":\"string\",\"jsonPointer\":\"/list_id\",\"maxItems\":1},\"email_address\":{\"$id\":\"email_address\",\"title\":\"Email User Identity\",\"type\":\"string\",\"format\":\"email\",\"jsonPointer\":\"/email_address\",\"maxItems\":1},\"web_id\":{\"$id\":\"web_id\",\"title\":\"Web id\",\"type\":\"string\",\"jsonPointer\":\"/web_id\",\"maxItems\":1},\"ip_opt\":{\"$id\":\"ip_opt\",\"title\":\"Client IP Address\",\"type\":\"string\",\"jsonPointer\":\"/ip_opt\",\"maxItems\":1},\"email_type\":{\"$id\":\"email_type\",\"title\":\"Email type\",\"type\":\"string\",\"jsonPointer\":\"/email_type\",\"maxItems\":1},\"type\":{\"$id\":\"type\",\"title\":\"Event type\",\"type\":\"string\",\"jsonPointer\":\"/type\",\"maxItems\":1},\"firedAt\":{\"$id\":\"firedAt\",\"title\":\"Fired at\",\"type\":\"integer\",\"format\":\"datetime\",\"jsonPointer\":\"/firedAt\",\"maxItems\":1},\"merge_fields.ADDRESS.addr1\":{\"$id\":\"merge_fields.ADDRESS.addr1\",\"title\":\"Address- Street Address\",\"type\":\"string\",\"jsonPointer\":\"/merge_fields/ADDRESS/addr1\",\"maxItems\":1},\"merge_fields.ADDRESS.addr2\":{\"$id\":\"merge_fields.ADDRESS.addr2\",\"title\":\"Address- Address Line 2\",\"type\":\"string\",\"jsonPointer\":\"/merge_fields/ADDRESS/addr2\",\"maxItems\":1},\"merge_fields.ADDRESS.city\":{\"$id\":\"merge_fields.ADDRESS.city\",\"title\":\"Address- City\",\"type\":\"string\",\"jsonPointer\":\"/merge_fields/ADDRESS/city\",\"maxItems\":1},\"merge_fields.ADDRESS.state\":{\"$id\":\"merge_fields.ADDRESS.state\",\"title\":\"Address- State/Prov/Region\",\"type\":\"string\",\"jsonPointer\":\"/merge_fields/ADDRESS/state\",\"maxItems\":1},\"merge_fields.ADDRESS.zip\":{\"$id\":\"merge_fields.ADDRESS.zip\",\"title\":\"Address- Postal/Zip\",\"type\":\"string\",\"jsonPointer\":\"/merge_fields/ADDRESS/zip\",\"maxItems\":1},\"merge_fields.ADDRESS.country\":{\"$id\":\"merge_fields.ADDRESS.country\",\"title\":\"Address- Country\",\"type\":\"string\",\"jsonPointer\":\"/merge_fields/ADDRESS/country\",\"maxItems\":1},\"merge_fields.BIRTHDAY\":{\"$id\":\"merge_fields.BIRTHDAY\",\"title\":\"Birthday\",\"type\":\"string\",\"jsonPointer\":\"/merge_fields/BIRTHDAY\",\"maxItems\":1},\"merge_fields.FNAME\":{\"$id\":\"merge_fields.FNAME\",\"title\":\"First Name\",\"type\":\"string\",\"jsonPointer\":\"/merge_fields/FNAME\",\"maxItems\":1},\"merge_fields.LNAME\":{\"$id\":\"merge_fields.LNAME\",\"title\":\"Last Name\",\"type\":\"string\",\"jsonPointer\":\"/merge_fields/LNAME\",\"maxItems\":1},\"merge_fields.PHONE\":{\"$id\":\"merge_fields.PHONE\",\"title\":\"Phone Number\",\"type\":\"string\",\"jsonPointer\":\"/merge_fields/PHONE\",\"maxItems\":1},\"oldEmail\":{\"$id\":\"oldEmail\",\"title\":\"Old email\",\"type\":\"string\",\"jsonPointer\":\"/oldEmail\",\"maxItems\":1},\"newEmail\":{\"$id\":\"newEmail\",\"title\":\"New email\",\"type\":\"string\",\"jsonPointer\":\"/newEmail\",\"maxItems\":1}}}"
        },
        "autoSynchronizationSupport": "INBOUND",
        "context": {
            "appSchemaObj": {
                "id": "120",
                "label": ""
            },
            "cifCategoryLabel": "UNICA Dev"
        }
    };

    constructor(private http: HttpClient, public hclCifObjectMappingLibService: HclCifObjectMappingLibService) { }

    config: ObjectMappingConf = {
        userApplication: 'Journey',
        cifBaseUrl: 'https://unicabeta.hcltechsw.com/asset-viewer',
        objectType: 'EntrySource',
        schemaObjectName: 'DataDefinition',
        appSchemaEmbeded: true,
        cifHeaders: null,
        applicationMode: 'CREATE',
        // previousData: this.previousData,
        translations: {
            createModeTitle: 'Add source',
            editModeTitle: 'Edit source',
            viewModeTitle: 'View source',
            selectRepository: 'Select source repository',
            selectAudienceEventType: 'Select audience or event type',
            browse: 'Browse',
            name: 'Name',
            noRowsToShow: 'No rows to show.',
            loading: 'Loading...',
            replace: 'Replace',
            selectApplicationData: 'Select Journey data definition',
            fieldMapping: 'Field mapping',
            mapFieldsToAutosync: 'Map only those fields that you want to auto-sync for this Entry Source.',
            serviceFieldTitle: 'subscribers fields',
            hostAppFieldTitle: 'Journey DD for Subscribers fields',
            cancel: 'Cancel',
            saveClose: 'Save & close',
            select: 'Select',
            search: 'Search',
            selectAttribute: 'Select attribute',
            selectDataDefinition: 'Select data definition',
            unableToFetchData: 'Unable to fetch data. Please try again.',
            noCompatibleAttrAvailableForMapping: 'No compatible attribute available for mapping',
            generalError: 'Something went wrong. Please contact system administrator, if problem persist.',
            noResultFound: 'No results found!',
            selectedFolder: 'Selected Folder :',
            gridTitle: 'Data Definitions',
            gridSearchPlaceholder: 'Search data definition',
            previousMappingError: 'Previously mapped attribute is no longer valid here. Please map another attribute.',
            noOptionAvailable: 'No option available',
            allCategories: 'All Categories',

            folders: {
                panelHeader: 'Folder list',
                sortBy: 'Sort by',
                ascendingLabel: 'Ascending',
                descendingLabel: 'Descending',
                sortByName: 'Name',
                createdBy: 'Created'
            }
        },
        allMappingsList: [
            //     {
            //     mappingId: 123,
            //     object1: {
            //         'applicationId': 'Journey',
            //         'objectType': 'EntrySource',
            //         'objectId': 'tempId-1635329789277',
            //     },
            //     object2: {
            //         'applicationId': 'Mailchimp',
            //         'objectType': '3c1768b473',
            //     }
            // },
            // {
            //     mappingId: 123,
            //     object1: {
            //         'applicationId': 'Journey',
            //         'objectType': 'EntrySource',
            //         'objectId': 'tempId-1635329789277',
            //     },
            //     object2: {
            //         'applicationId': 'Mailchimp',
            //         'objectType': 'ca939b3cbf',
            //     }
            // },
            // {
            //     mappingId: 123,
            //     object1: {
            //         'applicationId': 'Journey',
            //         'objectType': 'EntrySource',
            //         'objectId': 'tempId-1635329789277',
            //     },
            //     object2: {
            //         'applicationId': 'Mailchimp',
            //         'objectType': 'd85363d799',
            //     }
            // },
            // {
            //     mappingId: 123,
            //     object1: {
            //         'applicationId': 'Journey',
            //         'objectType': 'EntrySource',
            //         'objectId': 'tempId-1635329789277',
            //     },
            //     object2: {
            //         'applicationId': 'Mailchimp',
            //         'objectType': 'dc72705d40',
            //     }
            // },
            // {
            //     mappingId: 123,
            //     object1: {
            //         'applicationId': 'Journey',
            //         'objectType': 'EntrySource',
            //         'objectId': 'tempId-1635329789277',
            //     },
            //     object2: {
            //         'applicationId': 'Mailchimp',
            //         'objectType': 'f239295fe8',
            //     }
            // }
        ]
    };

    ngOnInit() {
        this.buttonConfig = {
            name: 'previous',
            value: 'Open Mapping Sec',
            color: 'accent',
            buttonType: 'flat',
            type: 'button',
            styleClass: 'medium-btn',
            borderRadius: 5,
            // disabled: true
        };
    }

    openMappingSec() {
        this.http.get('https://unicabeta.hcltechsw.com/journey/api/contentintegration/token',
            {
                headers: {
                    Authorization: '1678168246230-6-g3PFYFzn-nBpx-1mbWRt1V-OKIL-d5dC8Iu5'
                }
            }).subscribe((data: any) => {
                const headers: HttpHeaders = new HttpHeaders()
                    .set('m_user_name', 'asm_admin')
                    .set('m_tokenId', data.token)
                    .set('api_auth_mode', 'manager')
                    .set('401', 'ignore')
                    .set('client_app_id', '120');
                // this.hclCifObjectMappingLibService.headers = headers;
                this.config.cifHeaders = headers;
                this.readyToRender = true;
                setTimeout(() => {
                    this.sideBarComponentRef1.openSideBar();
                }, 1000);
            });
        // this.hclCifObjectMappingLibService.baseUrl = 'http://ip-0a862d52.nonprod.hclpnp.com:7001/asset-viewer';

        // const headers: HttpHeaders = new HttpHeaders()
        //     .set('m_user_name', 'asm_admin')
        //     .set('m_tokenId', '1632987178091-2-LoLjzzzP-JeTp-Y1GWwy7p-PaHM-etOq1UTt')
        //     .set('api_auth_mode', 'manager')
        //     .set('401', 'ignore')
        //     .set('client_app_id', '119');

        // this.config.cifHeaders = headers;
        // this.readyToRender = true;

        // setTimeout(() => {
        //     this.sideBarComponentRef1.openSideBar();
        // }, 1000);
    }

    mappedObject(mappingData: any) {
        this.readyToRender = false;
        this.sideBarComponentRef1.close('close');
        console.log(mappingData);
    }

    cancleMapping() {
        this.readyToRender = false;
        this.sideBarComponentRef1.close('close');
        console.log('Object mapping cancled!');
    }
}
