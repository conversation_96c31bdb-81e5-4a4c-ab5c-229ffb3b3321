import { Injectable } from "@angular/core";
import { UntypedFormGroup } from "@angular/forms";
import { SegmentObj, UserConfig } from "./models/segment";

@Injectable({
  providedIn: "root",
})
export class HclSegmentationLibDataService {
  private _rulebasedSegmentForm: UntypedFormGroup;
  private _segmentMetadataForm: UntypedFormGroup;
  private _userConfig: UserConfig;
  private _textSizeValidationType: "char" | "byte" | "unicode-byte" | "";
  // private _segmentsFolder: number;

  constructor() {}

  get textSizeValidationType(): "char" | "byte" | "unicode-byte" | "" {
    return this._textSizeValidationType;
  }

  set textSizeValidationType(value: "char" | "byte" | "unicode-byte" | "") {
    this._textSizeValidationType = value;
  }

  /**
   * Set RuleBased segment form data
   * @param form
   */
  set rulebasedSegmentForm(form: UntypedFormGroup) {
    this._rulebasedSegmentForm = form;
  }

  /**
   * Get RuleBased segment form data
   * @returns
   */
  get rulebasedSegmentForm(): UntypedFormGroup {
    return this._rulebasedSegmentForm;
  }

  /**
   * Set Segment metadata form data
   * @param form
   */
  set segmentMetadataForm(form: UntypedFormGroup) {
    this._segmentMetadataForm = form;
  }

  /**
   * Get Segment metadata form data
   * @returns
   */
  get segmentMetadataForm(): UntypedFormGroup {
    return this._segmentMetadataForm;
  }

  get userConfig(): UserConfig {
    return this._userConfig;
  }

  set userConfig(value: UserConfig) {
    this._userConfig = value;
  }

  // get segmentsFolder(): number {
  //   return this._segmentsFolder;
  // }

  // set segmentsFolder(value: number) {
  //   this._segmentsFolder = value;
  // }

  /**
   * This method will return the segment object to be created / updated which will be used to send it to API
   * @param segment
   * @returns
   */
  getSegmentData() {
    const { displayName, description, folderId, policyId, tags } =
      this.segmentMetadataForm.value;
    let segAudienceName: string;

    const subType: any = {
      type: "RuleBased", //
    };

    // if (this.segmentType === SegmentConstants.ruleBased) {
    const { audienceName, audienceTableId, nestedCondition } =
      this.rulebasedSegmentForm.value;
    segAudienceName = audienceName;
    subType.audienceTableId = audienceTableId;
    subType.nestedCondition = nestedCondition;
    // } else {
    //   const { audience, operation } = this.compositeSegmentForm.value;
    //   segAudienceName = audience;
    //   subType.setOperation = {
    //     logicalOp: operation,
    //     ids: this.getSelectedSegments().map((d) => d.data.id),
    //   };
    // }

    return {
      folderId,
      displayName,
      description,
      policyId,
      audienceName: segAudienceName,
      segmentType: 1,
      subType,
      tags,
    };
  }
}
