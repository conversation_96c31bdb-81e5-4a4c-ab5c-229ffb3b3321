import { Component, EventEmitter, OnInit, Output, Input, ViewEncapsulation, ViewChild, OnDestroy } from '@angular/core';
import { DatePipe } from '@angular/common';
import { ButtonConf, SideBarComponent } from 'hcl-angular-widgets-lib';
import { DataGridV2Component, DataGridColumnConf } from 'hcl-data-grid-lib';
import { HclAngularApprovalLibService } from '../../service/hcl-angular-approval-lib.service';
import { SubscriptionLike } from 'rxjs';
import { Approval } from '../../models/approval';
import { ApprovalListConfig } from '../../models/approval-list-config';
import { TranslateService } from '@ngx-translate/core';
import { ApprovalSharedDataService } from '../../service/approval-shared-data.service';

@Component({
  selector: 'hcl-approval-list',
  templateUrl: './approval-list.component.html',
  styleUrls: ['./approval-list.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ApprovalListComponent implements OnInit, OnDestroy {

  //@Input() config: ApprovalListConfig;
  private _config: ApprovalListConfig;
  @Input() set config(c: ApprovalListConfig) {
    this._config = c;
    if (this._config.approvalIdList && this._config.approvalIdList.length === 1) {
      // we have directly display the details
      this.approvalId = this._config.approvalIdList[0];
    }
    if (this._config.token) {
      this._sharedDataService.tokenId = this._config.token;
    }
  }
  get config(): ApprovalListConfig {
    return this._config
  }
  @Output() closeMainPanel = new EventEmitter();
  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  @ViewChild('approvalListGrid', { static: true }) approvalListGrid: DataGridV2Component;
  approvalGridApi: any;
  approvalGridColumns: DataGridColumnConf[] = [];
  @ViewChild(SideBarComponent) approvalDetailSidebar: SideBarComponent;
  showChildSideBar: boolean = false;
  approvalId: number = null;

  closeButtonConfig: ButtonConf = {
    buttonType: 'stroked',
    color: 'accent',
    borderRadius: 5,
    name: 'closeBtn',
    type: 'button',
    styleClass: 'medium-btn',
    value: this._translate.instant('APPROVALPICKER.BUTTONS.CLOSE_BTN')
  };

  constructor(private _approvalService: HclAngularApprovalLibService,
    private _datePipe: DatePipe,
    private _translate: TranslateService,
    private _sharedDataService: ApprovalSharedDataService) { }

  ngOnInit(): void {
    this._sharedDataService.approvalConfig = this.config;
    this._sharedDataService.reloginTimer();
    this.setDefaultValues();
    this.getApprovalGridData();
  }

  private setDefaultValues() {
    this._sharedDataService.serverUrl = this.config.url;
    this._sharedDataService.tokenId = this.config.token;
    this._sharedDataService.username = this.config.username;
    this.config.pageHeader = this.config.pageHeader || 'Custom header';
    this.approvalGridColumns = [
      {
        field: 'name',
        header: this._translate.instant('APPROVALPICKER.TITLES.NAME'),
        colId: 'name',
        rendererTemplateName: 'nameCell',
        autoResizeToFit: true,
        minWidth: 250,
        sortable: true
      },
      {
        field: 'description',
        header: this._translate.instant('APPROVALPICKER.TITLES.DESCRIPTION'),
        colId: 'description',
        useDefaultRenderer: true,
        autoResizeToFit: true,
        minWidth: 390,
        tooltip: {
          getTooltip: (attr: any) => attr.description
        }
      },
      {
        field: 'stateCode',
        header: this._translate.instant('APPROVALPICKER.TITLES.STATUS'),
        colId: 'stateCode',
        useDefaultRenderer: true,
        autoResizeToFit: true,
        minWidth: 150,
        tooltip: {
          getTooltip: (attr: any) => this._sharedDataService.getApprovalStatus(attr)
        },
        dataFormatter: (attr: any) => {
          return this._sharedDataService.getApprovalStatus(attr.data);
        }
      },
      {
        field: 'approvalDate',
        header: this._translate.instant('APPROVALPICKER.TITLES.END_DATE'),
        colId: 'approvalDate',
        useDefaultRenderer: true,
        autoResizeToFit: true,
        minWidth: 200,
        tooltip: {
          getTooltip: (attr: any) => {
            return this.transformDates(attr);
          }
        },
        dataFormatter: (attr: any) => {
          return this.transformDates(attr.data);
        }
      }
    ];
    this.config.approvalListGridConf.columns = this.approvalGridColumns;
  }

  private transformDates(data: Approval) {
    return data.approvalDate ? this._datePipe.transform(data.approvalDate, 'MM/dd/yyyy') : '';
  }

  private getApprovalGridData() {
    this.subscriptionList.push(this._approvalService.getListOfAllApprovals(this.config.approvalIdList).subscribe((res: any[]) => {
      const gridData: Approval[] = [];
      for (const prop in res) {
        gridData.push(res[prop]);
      }
      this.setapprovalGridData(gridData);
    }, error => {
      console.log('can not load approval list', error);
      this._sharedDataService.displayErrorNotification(error);
    }));
  }

  private setapprovalGridData(data: Approval[]) {
    this.config.approvalListGridConf.data = [...data];
    if (this.approvalGridApi) {
      this.approvalGridApi.setRowData(this.config.approvalListGridConf.data);
    }
  }

  closePanel(event) {
    this._sharedDataService.clearTimer();
    this.closeMainPanel.emit(this.config.approvalListGridConf.data);
  }

  onGridReady(data) {
    this.approvalGridApi = data.params.api;
    if (this.approvalGridApi) {
      this.approvalGridApi.setRowData(this.config.approvalListGridConf.data);
    }
  }

  onCellClicked(data) {
    this.approvalId = data.approvalId;
    this.approvalDetailSidebar.openSideBar();
    this.showChildSideBar = true;
  }

  closeApprovalDetails() {
    if (this.config.approvalIdList && this.config.approvalIdList.length === 1) {
      this.closePanel(null);
    } else {
      this.approvalDetailSidebar.close('close');
      this.showChildSideBar = false;
    }
  }

  ngOnDestroy() {
    this._sharedDataService.clearTimer();
    this.subscriptionList.forEach(sub => {
      sub.unsubscribe();
    });
  }

}
