import {
  Component,
  ViewEncapsulation, Output, EventEmitter, Input
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../dialog.component';
import { ApplicationConfigurationService } from 'hcl-angular-widgets-lib';

@Component({
  selector: 'ip-rules-listing',
  templateUrl: './rules-listing.component.html',
  styleUrls: ['./rules-listing.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class RulesListingComponent {
  // @Input() getDigitalAsset: (assetId, ignoreLoader) => any;
  @Input() configType: string;
  dynamicToolbarBtn: string = '';
  // assetsDownloadStatus = {};

  constructor(public ngb: IpEmailBuilderService,
    public translate: TranslateService,
    private confirmDialog: MatDialog,
    private applicationConfig: ApplicationConfigurationService) {
    this.dynamicToolbarBtn = this.applicationConfig.applicationBaseURL + 'Angular/assets/images/Dynamic-content-button.png'
  }

  ngOnInit() {
  }

  updateRuleWithLinkText(rule, link) {
    return { ...rule, text: (rule.text ? rule.text : link.text) };
  }

  getCurrentElementType(): string {
    const currentElem = this.ngb.getCurrentElement(this.configType);
    return currentElem.type;
  }

  public get getRulesInfo(): any[] {
    const currentElement = this.ngb.getCurrentElement(this.configType);
    return currentElement?.options?.rules;
  }

  getCurrentElement(rule?) {
    const el = this.ngb.getCurrentElement(this.configType);
    if (rule && (el.type === 'html' || el.type === 'html-field')) {
      return {
        type: el.type,
        options: {
          id: rule.digitalAssetId
        },
        src: ''
      }
    } else {
      return el;
    }
  }

  callbackForDigitalAssetApi() {
    return (function (digitalAsset) { return digitalAsset; }).bind(this)
  }



  addRule() {
    this.ngb.addEditRuleBuilder.next();
  }

  getDefaultRule(): any {
    const currentElem = this.ngb.getCurrentElement(this.configType);
    const ruleObject = {
      name: this.translate.instant('RULE_BUILDER.DEFAULT_RULE'),
      isDefault: true,
      ruleMsg: this.translate.instant('RULE_BUILDER.DEFAULT_RULE_MSG'),
      ...((currentElem.type === "image" || currentElem.type === "image-field") && { imageSrc: currentElem.src }),
      ...((currentElem.type === "html" /*|| currentElem.type === "html-field"*/) && { htmlSrc: currentElem.options.innerHtml }),
      url: currentElem.options.url,
      redirection: currentElem.options.redirection,
      landingPage: currentElem.options.landingPage,
      ...((currentElem.type === "button" || currentElem.type === "button-field") && { buttonText: currentElem.innerText })
    }
    return ruleObject;
  }

  getDefaultLinkRule(linkId): any {
    const currentElem: any = this.ngb.getCurrentElement(this.configType);
    const domObject = new DOMParser().parseFromString(currentElem.innerText, 'text/html');
    const linkObject = domObject.querySelector("a[data-id='" + linkId + "']");
    return {
      name: this.translate.instant('RULE_BUILDER.DEFAULT_RULE'),
      text: linkObject['innerText'],
      isDefault: true,
      ruleMsg: this.translate.instant('RULE_BUILDER.DEFAULT_RULE_MSG'),
      url: linkObject.getAttribute('data-href'),
      redirection: linkObject.getAttribute('data-type') === 'landing-page' ? 'lp' : 'url',
      ...(linkObject.getAttribute('data-name') && { landingPage: linkObject.getAttribute('data-name') }),
    }
  }

  async deleteRule(ruleInfo: any, confirm = true) {
    const currentElem = this.ngb.getCurrentElement(this.configType);
    const currentIndex = currentElem.options.rules.findIndex(x => x.id === ruleInfo.id);
    let rulesArray: any[] = currentElem.options?.rules;
    let result = 1;
    if (confirm) {
      result = await this.confirmDialog
        .open(ConfirmDialogComponent, {
          hasBackdrop: false,
          width: '560px',
          data: {
            message: this.translate.instant('RULE_BUILDER.DELETE_RULE', { 0: ruleInfo.name })
          }
        })
        .afterClosed()
        .toPromise();
      if (+result === 1) {
        rulesArray.splice(currentIndex, 1);
      }
    } else {
      rulesArray.splice(currentIndex, 1);
    }
    this.ngb.getHyperLinks(this.configType);
    this.ngb.validateLandingPages();
    this.ngb.notifyEmailChanged();
    return Promise.resolve(+result === 1);
  }

  addRuleToHyperlink(linkInfo) {
    const { id, startIndex, endIndex, text } = linkInfo;
    this.ngb.addEditRuleBuilder.next({
      hyperlinkInfo: {
        id,
        startIndex,
        endIndex,
        linkText: text
      }
    });
  }

  hideAddRuleButton() {
    if (this.ngb.isStaticLP) {
      return true;
    } else {
      const currentElem = this.getCurrentElement();
      if ((currentElem.type === "image" || currentElem.type === "image-field") && (currentElem.src === "" || currentElem.src === "https://via.placeholder.com/600x200?text=CHANGE+ME")) {
        return true;
      }
    }
    return false;
  }

  getInfoMsg() {
    if (this.ngb.isStaticLP) {
      return this.translate.instant('RULE_BUILDER.RULES_NA_FOR_STATIC_LP');
    } else if (this.hideAddRuleButton()) {
      return this.translate.instant('RULE_BUILDER.DEFAULT_IMAGE_MANDATORY');
    }
    return this.translate.instant('messages.no-rule-added');
  }

}