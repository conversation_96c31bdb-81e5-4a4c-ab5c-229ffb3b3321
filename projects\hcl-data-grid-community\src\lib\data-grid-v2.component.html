<div (dragover)="gridDragOver($event)" (drop)="onGridDrop($event)" class="hcl-grid-container" [ngStyle]="{'min-height' : config.scrollHeight ? config.scrollHeight+'px' : '100%'}">
  <ag-grid-angular
    #agGrid
    class="ag-theme-material agGridContainer"
    [isExternalFilterPresent]="_adapter.isExternalFilterPresent"
    [doesExternalFilterPass]="_adapter.doesExternalFilterPass"
    [columnDefs]="_adapter.columns"
    [autoGroupColumnDef]="config.autoGroupColumnDef"
    [frameworkComponents]="_adapter.frameworkComponents"
    [isFullWidthCell]="_adapter.isFullWidthCell"
    [fullWidthCellRenderer]="_adapter.fullWidthCellRenderer"
    [overlayNoRowsTemplate]="config.noRowsTemplate || config.noDataRetOnHttpResTemp || 'No rows to show.'"
    [overlayLoadingTemplate]="_adapter.loadingTemplate"
    [rowData]="_adapter.data"
    [rowDragManaged]="true"
    [rowSelection]="_adapter.rowSelection"
    [suppressRowClickSelection]="_adapter.suppressRowClickSelection"
    [suppressRowTransform]="_adapter.suppressRowTransform"
    [suppressDragLeaveHidesColumns]="_adapter.suppressDragLeaveHidesColumns"
    (dragStopped)="onDragStop($event)"
    (gridReady)="onGridReady($event)"
    (bodyScroll)="onBodyScroll($event)"
    [gridOptions]="_adapter.gridOptions"
    (viewportChanged)="viewportChanged($event)"
    (rowDataChanged)="onRowDataChange($event)"
    (rowDataUpdated)="onRowDataChange($event)"
    (firstDataRendered)="onFirstDataLoad($event)"
    (rowSelected)="_adapter.selectionChanged($event)"
    (rowClicked)="_adapter.rowClicked($event)"
    (cellClicked)="_adapter.cellClicked($event)"
    [getRowHeight]="_adapter.getRowHeight"
    [animateRows]="!config.disableRowAnimation"
    [rowClassRules]="_adapter.rowClassRules"
    [rowModelType]="getRowModel()"
    [cacheBlockSize]="_adapter.cacheBlockSize"
    [infiniteInitialRowCount]="_adapter.infiniteInitialRowCount"
    [rowBuffer]="!config.rowBuffer ? (config.infiniteScroll && config.infiniteScroll.rowBuffer) : config.rowBuffer"
    [treeData]="config.treeData"
    [getDataPath]="config.getDataPath"
    [isRowSelectable] = "config.isRowSelectable || isRowSelectable"
    [enableRangeSelection]="config.enableRangeSelection"
    (cellEditingStarted)="onCellEditingStarted($event)"
    (cellEditingStopped)="onCellEditingStopped($event)"
    [groupUseEntireRow]="config.rowGroupType && config.rowGroupType.groupUseEntireRow"
    [groupRowInnerRenderer]="config.rowGroupType && config.rowGroupType.groupRowInnerRenderer"
    [groupRemoveSingleChildren]= "config.rowGroupType && config.rowGroupType.groupRemoveSingleChildren"
    [groupDefaultExpanded]= "config.rowGroupType && config.rowGroupType.groupDefaultExpanded"
    [defaultColDef]="_adapter.defaultColDef"
    [processCellForClipboard]="config.processCellForClipboard">
  </ag-grid-angular>
</div>
<div *ngIf="config.pagination" [hidden]="config.noDataFlag">
  <hcl-paginator [config]="config.pagination" (pageChange)="onPageChange($event)" (pageSizeChange)="onpageSizeChange($event)" #paginator></hcl-paginator>
</div>

