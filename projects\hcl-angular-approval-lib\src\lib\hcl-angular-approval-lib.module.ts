import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HclAngularWidgetsLibModule } from 'hcl-angular-widgets-lib';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { HclDataGridModule } from 'hcl-data-grid-lib';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { ApprovalListComponent } from './component/approval-list/approval-list.component';
import { ApprovalDetailsComponent } from './component/approval-details/approval-details.component';
import { HclAngularApprovalLibService } from './service/hcl-angular-approval-lib.service';
import { ApprovalRequestInterceptor } from './approval-request.interceptor';
import { HttpClient, HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { ApprovalBasicInfoComponent } from './component/approval-basic-info/approval-basic-info.component';
import { ApprovalGeneralInfoComponent } from './component/approval-general-info/approval-general-info.component';
import { ApprovalUserInfoComponent } from './component/approval-user-info/approval-user-info.component';
import { ApprovalRulesInfoComponent } from './component/approval-rules-info/approval-rules-info.component';
import { ApprovalItemsInfoComponent } from './component/approval-items-info/approval-items-info.component';
import { ApprovalResponderHistoryInfoComponent } from './component/approval-responder-history-info/approval-responder-history-info.component';
import { ApprovalAnalysisInfoComponent } from './component/approval-analysis-info/approval-analysis-info.component';
import { ApprovalDependencyInfoComponent } from './component/approval-dependency-info/approval-dependency-info.component';
import { ApprovalSharedDataService } from './service/approval-shared-data.service';
import { SmartEllipseComponent } from './component/smart-ellipse/smart-ellipse.component';

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  declarations: [
    ApprovalListComponent,
    ApprovalDetailsComponent,
    ApprovalBasicInfoComponent,
    ApprovalGeneralInfoComponent,
    ApprovalUserInfoComponent,
    ApprovalRulesInfoComponent,
    ApprovalItemsInfoComponent,
    ApprovalResponderHistoryInfoComponent,
    ApprovalAnalysisInfoComponent,
    ApprovalDependencyInfoComponent,
    SmartEllipseComponent
  ],
  imports: [
    CommonModule,
    HttpClientModule,
    HclAngularWidgetsLibModule,
    HclDataGridModule,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      },
      isolate: false
    })
  ],
  exports: [
    ApprovalListComponent
  ],
  providers: [
    HclAngularApprovalLibService,
    ApprovalSharedDataService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ApprovalRequestInterceptor,
      multi: true
    }
  ]
})
export class HclAngularApprovalLibModule { }
