<aside class="all-folders-container">
  <ng-container *ngIf="config.showSelectedItemDetails">
    <div class="selected-items-section">
      <p class="selected-items-section-title">
        {{ config.translations.selectedItemSectionTitle }}
      </p>
      <div class="selected-items">
        <div class="rectangular-chip" *ngFor="let item of itemsData">
          <div class="select-option" hclTooltip="{{ item.displayName }}">
            <i class="hcl-icon-info"></i>
            {{ item.displayName }}
          </div>
          <i class="hcl-icon-close" (click)="removeItem(item)"></i>
        </div>
      </div>
    </div>
  </ng-container>

  <div
    class="folder-selection-content"
    [ngClass]="{ 'full-height': !config.showSelectedItemDetails }"
  >
    <p class="folder-selection-title">
      {{ config.translations.selectionSectionTitle }}
    </p>

    <div class="breadcrumb-container">
      <hcl-breadcrumb (clicked)="breadCrumbMenuItemClicked($event)">
        <ng-template
          *ngFor="let breadCrumb of breadCrumbData"
          hclTemplate
          hclTemplateName="breadCrumbData"
          [hclTemplateConfig]="{
            label: breadCrumb.folder.displayName,
            value: breadCrumb
          }"
        >
          <div
            class="breadcrumb-item"
            hclTooltip="{{ breadCrumb.folder.displayName }}"
            (click)="fetchFolders(breadCrumb.folder, breadCrumb.index)"
          >
            {{ breadCrumb.folder.displayName }}
          </div>
        </ng-template>
      </hcl-breadcrumb>
    </div>
    <div class="folder-listing-body">
      <ng-container
        *ngIf="
          foldersObject.childrenData && foldersObject.childrenData.length !== 0
        "
      >
        <ul class="folder-list">
          <li *ngFor="let child of foldersObject.childrenData">
            <div
              class="folder"
              [ngClass]="{
                'folder-disabled':
                  itemType === 'folders' &&
                  checkIfPresentInSelectedItems(child.id, foldersObject.parent)
              }"
              (click)="
                onFolderClick(
                  child,
                  foldersObject.parent,
                  foldersObject.parentIndex
                )
              "
            >
              <i class="hcl-icon-folder-normal"></i>
              <div
                hclTooltip="{{ child.displayName }}"
                class="folder-name ellipsis"
              >
                {{ child.displayName }}
              </div>
            </div>
          </li>
        </ul>
      </ng-container>

      <ng-container
        *ngIf="
          !foldersObject.childrenData || foldersObject.childrenData.length === 0
        "
      >
        <div class="no-folders">
          <span>{{ config.translations.noFolderData }}</span>
        </div>
      </ng-container>
    </div>
  </div>

  <div class="folder-selection-actions">
    <hcl-button [config]="cancelActionConfig" (onclick)="cancelAction()">
    </hcl-button>
    <hcl-button [config]="selectActionConfig" (onclick)="selectAction()">
    </hcl-button>
  </div>
</aside>
