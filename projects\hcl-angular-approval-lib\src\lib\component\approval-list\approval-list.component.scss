$header-font-color: #6d7692;
$header-font-family: <PERSON>ser<PERSON>;
$content-font-family: <PERSON><PERSON>;
$content-font-color: #444444;
$link-font-color: #0078d8;
$background-color: #ececec;

.approval-list-container {
    font-family: $header-font-family;
    color: $header-font-color;
    .approval-list-header {
        font-size: 20px;
        font-weight: bold;;
        margin-bottom: 20px;
    }

    .approval-grid-container {
        height: 72vh;
        .hcl-grid-container {
            .link {
                color: $link-font-color;
                &:hover {
                    cursor: pointer;
                }
            }
            .ag-cell {
                font-family: $content-font-family;
                font-size: 14px;
                color: $content-font-color;
            }
            .ag-body-horizontal-scroll {
                display: none !important;
            }
        }
    }

    .close-btn-container {
        bottom: 4%;
        right: 0;
    }
}

.slider-part {
    width: 60px;
    background-color: #ececec;
    box-shadow: -15px 0 10px -2px rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    h3 {
        color: #6d7692;
        font-family: $header-font-family;
        font-size: 20px;
        font-weight: bold;
        line-height: 24px;
        writing-mode: vertical-lr;
        transform: rotate(180deg);
        text-orientation: sideways;
        text-align: right;
        text-orientation: sideways;
        margin: 1rem;
    }
}

.approval-details-component {
    width: 1200px;
    height: 100%;
    background-color: $background-color;
    box-shadow: -15px 0 10px -2px rgba(0, 0, 0, 0.3);
    flex-grow: 1;
    padding: 30px 40px;
    z-index: 1;
}
