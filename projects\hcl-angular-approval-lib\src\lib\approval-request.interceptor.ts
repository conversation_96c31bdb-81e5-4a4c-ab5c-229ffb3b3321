import { Injectable } from '@angular/core';
import { <PERSON>ttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApprovalSharedDataService } from './service/approval-shared-data.service';

@Injectable()
export class ApprovalRequestInterceptor implements HttpInterceptor {

    constructor(private _sharedDataService: ApprovalSharedDataService) { }

    intercept(req: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
        if (this.isPlanRequest(req.url)) {
            if (!req.headers.has('Accept')) {
                req = req.clone(
                    { headers: req.headers.set('Accept', 'application/json') });
            }

            if (!req.headers.has('Access-Control-Allow-Origin')) {
                req = req.clone({ headers: req.headers.set('Access-Control-Allow-Origin', '*') });
            }

            if (!req.headers.has('m_user_name') && this._sharedDataService.username) {
                req = req.clone({
                    headers: req.headers.set('m_user_name', this._sharedDataService.username)
                });
            }

            if (!req.headers.has('m_tokenId') && this._sharedDataService.tokenId) {
                req = req.clone({
                    headers: req.headers.set('m_tokenId', this._sharedDataService.tokenId)
                });
            }

            if (!req.headers.has('api_auth_mode')) {
                req = req.clone({ headers: req.headers.set('api_auth_mode', 'manager') });
            }

            req = req.clone({
                headers: req.headers.set('Cache-Control', 'no-cache')
                    .set('Pragma', 'no-cache')
            });
        }
        return next.handle(req);
    }

    isPlanRequest(requestUrl: string): boolean {
        const planString = '/api/plan/v2/';
        return requestUrl.indexOf(planString) > 0 ? true : false;
    }
}