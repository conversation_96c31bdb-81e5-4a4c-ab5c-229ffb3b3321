export class SelectOffersConstant {
    public static get all(): string { return 'all'; }
    public static get draft(): string { return 'draft'; }
    public static get published(): string { return 'published'; }
    public static get refreshOnGlobalSearch(): string { return 'refreshOnGlobalSearch'; }
    public static get offerlists(): string { return 'offerlists'; }
    public static get offerLists(): string { return 'offerLists'; }
    public static get smartOfferListSmallCase(): string { return 'smart'; }
    public static get staticOfferListSmallCase(): string { return 'static'; }
    public static get staticOfferListCapital(): string { return 'STATIC'; }
    public static get smartOfferListCapital(): string { return 'SMART'; }
    public static get gridView(): string { return 'Grid view'; }
    public static get cardView(): string { return 'Card view'; }
    public static get sortBy(): string { return 'sort by'; }
    public static get sortByDisplayNameAsc(): string { return 'displayName,ASC'; }
    public static get sortByDisplayNameDesc(): string { return 'displayName,DESC'; }
    public static get sortByOfferCodeAsc(): string { return 'offerCodes,ASC'; }
    public static get sortByOfferCodeDesc(): string { return 'offerCodes,DESC'; }
    public static get sortByDescriptionAsc(): string { return 'description,ASC'; }
    public static get sortByDescriptionDesc(): string { return 'description,DESC'; }
    public static get type(): string { return 'type'; }
}
