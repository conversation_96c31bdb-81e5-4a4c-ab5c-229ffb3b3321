import { Component, Input, ViewEncapsulation } from '@angular/core';
import { DropdownField, TextField } from '../../classes/Fields';
import { createPadding, createFont, createLineHeight, createWidthHeight, createBackgroundWithSize, createBorder } from '../../utils';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import { uniqueId } from '../../converter/mjml-output/utils';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'ip-dropdown-field',
  templateUrl: './dropdown-field.component.html',
  styleUrls: ['./dropdown-field.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class DropdownFieldComponent {
  @Input() field: DropdownField;
  id: string;
  constructor(public ngjs: IpEmailBuilderService, private translate: TranslateService) {
    this.id = uniqueId();
  }
  
  ngOnInit() {
    this.ngjs.unSubReasonDropDownList = this.field;
  }

  getInputFieldStyles() {
    const {
      inputColor,
      inputFont,
      inputLineHeight, 
      width,
      height
    } = this.field.options;

    return {
      color: inputColor,
      width: (createWidthHeight(width) === 'auto' ? '100%' : createWidthHeight(width)),
      height: createWidthHeight(height),
      ...createFont(inputFont),
      ...createLineHeight(inputLineHeight),
      border: 'none',
      borderBottom: `1px solid ${inputColor}`
    };
  }

  getInputFieldContainerStyles() {
    const {
      innerPadding,
      inline,
      labelWidth,
      width
    } = this.field.options;

    return {
      'display': (inline ? 'inline-block' : 'block'),
      width: (createWidthHeight(width) === 'auto' ? (labelWidth.auto === true ? '100%' : `calc(100% - ${createWidthHeight(labelWidth)})`) : 'auto'),
      ...createPadding(innerPadding),
      'vertical-align': 'top'
    };
  }

  getParentStyles() {
    const { align } = this.field.options;

    return {
      'text-align': (align === 'center' ? 'center' : (align === 'left' ? 'left' : 'right')),
      width: '100%',
      'overflow-x': 'auto'
    };
  }

  getAlignmentStyles() {
    const { background, padding, border, attributesLabel, width } = this.field.options;

    return {
      'text-align': (attributesLabel.align === 'center' ? 'center' : (attributesLabel.align === 'left' ? 'left' : 'right')),
      display: 'inline-block',
      width: (createWidthHeight(width) === 'auto' ? '100%' : 'auto'),
      ...createPadding(padding),
      ...createBackgroundWithSize(background),
      ...createBorder(border)
    };
  }

  getLabelStyles() {
    const { labelFont, labelColor, labelLineHeight, labelPadding, inline, labelWidth } = this.field.options;

    return {
      color: labelColor,
      'word-break': 'break-word',
      'display': (inline ? 'inline-block' : 'block'),
      ...createPadding(labelPadding),
      ...createFont(labelFont),
      width: (createWidthHeight(labelWidth) === 'auto' ? '100%' : createWidthHeight(labelWidth)),
      ...createLineHeight(labelLineHeight)
    };
  }

  fetchComponentErrorMessages() {
    const errorMessages = new Set();
    for (let iErrorCounter = 0; iErrorCounter < this.field.errors.length; iErrorCounter++) {
      errorMessages.add(this.translate.instant('messages.'+this.field.errors[iErrorCounter].key+'-collated'))
    }
    return `${Array.from(errorMessages).join(`\n\r`)}`;
  }
}
