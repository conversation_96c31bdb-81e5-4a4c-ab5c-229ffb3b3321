.hcl-query-builder-v2 {
  width: 100%;
  height: 100%;
  overflow: auto;
  display: block;
  position: relative;
  background-color: #f5f5f5;
  font-family: Roboto;
  padding: 10px 25px;

  .hcl-qb-v2-page-loader {
    position: absolute;
    z-index: 10;
    width: 95%;
    height: 95%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .no-rule-container {
    // margin: 180px auto 0 auto;
    line-height: 24px;
    font-size: 14px;
    max-width: 1200px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .nr-h {
      color: #6d7692;
      font-family: Montserrat;
      font-weight: 500;
    }

    .nr-b {
      color: #444;
      font-family: Roboto;
      max-width: 280px;
      margin: auto;
    }

    .add-link-inline {
      color: #0078d8;
      cursor: pointer;

      &:hover {
        color: #f5821e;
      }
    }
  }

  .group-container {
    width: 100%;
    float: left;
    margin: 10px 0;
    padding: 10px;
    position: relative;

    .group-container {
      margin-bottom: 0;
    }

    &.gc-or-condition {
      color: #32c5ff;
    }

    &.gc-and-condition {
      color: #44d7b6;
    }

    &.group-with-next-sibling {
      &::after {
        content: "";
        left: -42px;
        top: 30px;
        width: 1px;
        height: calc(100% - 30px);
        position: absolute;
        border-style: dashed;
        border-width: 0px 1px 0px 0px;
      }
    }

    &.has-adjacent-group {
      &::after {
        height: calc(100% + 12px);
      }
    }

    &.has-group-as-firstchild {
      &::before {
        content: "";
        left: 10px;
        top: 55px;
        width: 1px;
        height: 35px;
        position: absolute;
        border-style: dashed;
        border-width: 0px 1px 0px 0px;
      }
    }

    &.parent-condition-green {
      &::after {
        color: #44d7b6;
      }

      > .group-header {
        > .condition-toggle {
          &::before {
            color: #44d7b6;
          }
        }
      }
    }

    &.parent-condition-blue {
      &::after {
        color: #32c5ff;
      }

      > .group-header {
        > .condition-toggle {
          &::before {
            color: #32c5ff;
          }
        }
      }
    }

    &#group_1 {
      margin-top: 0;

      & > .group-content {
        & > .group-rules {
          & > .group-container {
            .group-with-next-sibling {
              &::after {
                left: -30px;
              }
            }
          }
        }
      }

      &::before {
        content: "";
        left: -2px;
        top: -10px;
        border-style: dashed;
        width: 12px;
        height: 40px;
        position: absolute;
        border-width: 0px 0 1px 0px;
      }

      &::after {
        content: "";
        left: -2px;
        top: 30px;
        height: 60px;
        width: 1px;
        position: absolute;
        border-style: dashed;
        border-width: 0px 1px 0px 0px;
      }

      &.has-group-as-firstchild {
        &::before {
          height: 1px;
          top: 30px;
        }
      }

      &.no-rules {
        &::before {
          content: none;
        }

        &::after {
          content: none;
        }
      }
    }

    &.no-rules {
      .group-header {
        border-color: #bcbbbb;
      }

      .selected-toggle {
        color: #8b8b8b !important;
      }

      .mat-slide-toggle-thumb {
        background-color: #d2d6e1 !important;
      }
    }

    &.group-container-collapsed {
      &::before {
        content: none;
      }
    }

    .group-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      min-width: 600px;
      max-width: 1200px;
      background: #fff;
      border: 1px solid;
      border-radius: 4px;

      .group-condition-symbol {
        left: -8px;
        top: calc(50% - 6px);
        width: 12px;
        height: 12px;
        position: absolute;
        font-size: 8px;
        color: #ffffff;
        text-align: center;
        line-height: 12px;

        .and-symbol {
          background-color: #44d7b6;
        }

        .or-symbol {
          background-color: #32c5ff;
        }
      }

      &.dynamic-header {
        min-width: 1040px;
      }

      &.highlight-group-header {
        border: 1px solid #fa6400;
      }

      .condition-toggle {
        display: inline-block;
        font-size: 12px;
        font-weight: bold;
        margin: 10px 10px;

        span {
          color: #8b8b8b;

          &.selected-toggle {
            color: #f5821e;
          }
        }

        .expand-collapse {
          position: absolute;
          left: -55px;
          top: 6px;
          font-size: 20px;
          z-index: 1;
          color: #444444;
          cursor: pointer;

          & i {
            &::before {
              background: #f5f5f5;
            }
          }

          &.collapsed {
            .hcl-icon-expand-group {
              display: none;
            }

            .hcl-icon-add-button {
              display: block;
            }
          }

          &.expanded {
            .hcl-icon-expand-group {
              display: block;
            }

            .hcl-icon-add-button {
              display: none;
            }
          }
        }

        .rule-group-name {
          font-size: 14px;
          font-family: "Montserrat";
          font-weight: 500;

          .cursor-pointer {
            cursor: pointer;
          }
        }

        &::before {
          content: "";
          left: -32px;
          top: 20px;
          border-style: dashed;
          width: 22px;
          position: absolute;
          border-width: 1px 0 0 1px;
        }

        &#group_1 {
          .expand-collapse {
            left: -66px;
          }

          &::before {
            left: -42px;
            width: 32px;
          }
        }
      }

      &.no-expand-collapse {
        .condition-toggle {
          &::before {
            content: none;
          }
        }
      }

      .action-buttons {
        button.mat-button {
          padding: 0 10px 0 10px;
          font-weight: normal;
        }
      }
    }

    .group-content {
      position: relative;
      left: 20px;
      width: calc(100% - 40px);
      margin-left: 10px;

      .group-rules {
        // &:last-of-type {
        //   .single-query {
        //     &::after {
        //       height: calc(50% + 10px);
        //     }
        //   }
        // }

        // &:last-child {
        //   .has-no-subgroup {
        //     .single-query {
        //       &::after {
        //         content: "";
        //         left: -21px;
        //         top: -10px;
        //         border-style: dashed;
        //         width: 1px;
        //         height: calc(50% + 10px);
        //         position: absolute;
        //         border-width: 0px 1px 0px 0px;
        //       }
        //     }
        //   }
        // }
        .single-query {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          background-color: #cce4f7;
          margin-top: 2px;
          padding: 5px 0 5px 10px;
          min-width: 570px;
          max-width: 1170px;
          margin-left: -10px;
          position: relative;
          border: 1px solid transparent;
          margin-top: 10px;
          border-radius: 4px;

          .rule-condition-symbol {
            left: -8px;
            top: calc(50% - 6px);
            width: 12px;
            height: 12px;
            position: absolute;
            font-size: 8px;
            color: #ffffff;
            text-align: center;
            line-height: 12px;

            .and-symbol {
              background-color: #44d7b6;
            }

            .or-symbol {
              background-color: #32c5ff;
            }
          }

          &.dynamic-rule {
            min-width: 1000px;
          }

          &.highlight-single-rule {
            border: 1px solid #fa6400;
          }

          .query-fields-container {
            display: block;
            width: calc(100% - 50px);

            .query-field {
              margin-right: 20px;
              width: calc(35% - 20px);
              float: left;

              &.query-operators {
                width: calc(20% - 20px);
              }

              &.query-column {
                width: calc(25% - 20px);
              }

              &.query-column-type {
                width: calc(15% - 20px);
              }

              .dynamic-input-box {
                width: 225px !important;
              }

              .folder-selection {
                width: 225px;
                height: 25px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.42);
                font-size: 16px;
                color: #444;
                position: relative;

                .folder-placeholder {
                  position: absolute;
                  top: -18px;
                  left: 0;
                  color: #6d7692 !important;
                  letter-spacing: 0.7px;
                  font-size: 12px;
                  font-family: Roboto;

                  &::after {
                    content: "*";
                    color: #cc0000;
                    font-size: 14px;
                    font-family: Roboto;
                    right: -10px;
                    position: absolute;
                    opacity: 0.7;
                  }
                }

                .selected-folder {
                  width: 90%;
                  float: left;
                  font-size: 14px;
                  font-family: Roboto;
                }

                .no-folder {
                  font-size: 14px;
                  color: #6d7692 !important;
                  position: relative;
                  float: left;
                  font-family: Roboto;

                  &::after {
                    content: "*";
                    color: #cc0000;
                    font-size: 14px;
                    font-family: Roboto;
                    right: -10px;
                    position: absolute;
                    opacity: 0.7;
                  }
                }

                .folder-required {
                  font-size: 12px;
                  color: #cc0000;
                  float: left;
                  position: absolute;
                  top: 30px;
                  font-family: Roboto;
                  right: 0;
                }
              }

              &.dynamic-value {
                margin-right: 0;
                height: 100%;

                .dynamic-value-radio {
                  min-width: 490px;
                  position: relative;
                }

                .or-span-text {
                  position: absolute;
                  top: 30px;
                  left: 215px;
                  font-size: 14px;
                  color: #6d7692;
                  width: 40px;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  height: 14px;
                }

                .disableInput {
                  opacity: 0.3;
                  pointer-events: none;
                }

                .dynamic-input-box {
                  width: 165px !important;
                }

                .extra-padding {
                  padding-top: 20px;
                }

                .mat-radio-button {
                  width: 20px;
                  margin-top: 25px;
                  float: left;
                }

                .mat-radio-group {
                  & > div {
                    float: left;
                    width: 100%;
                  }
                }
              }

              &.range-value {
                width: calc(20% - 20px);
              }

              .custom-expression-container {
                color: #444444;
                font-family: Roboto;
                font-size: 14px;
                width: 100%;
                border-bottom: 1px solid #959595;
                padding-bottom: 3px;
                display: flex;
                padding-top: 20px;

                div.expression-label {
                  width: 90%;
                  font-style: italic;
                  font-weight: 600;
                }

                div.icon-container {
                  width: 10%;

                  i.hcl-icon-edit {
                    color: #959595;
                    float: right;
                    cursor: pointer;
                  }
                }
              }

              .expression-value {
                position: relative;
                top: 20px;
                left: 10px;
              }
            }

            .to-label {
              color: #444444;
              font-family: Roboto;
              font-size: 14px;
              letter-spacing: 0;
              line-height: 24px;
              margin: 25px 20px 0 5px;
            }

            .in-opt-add-btn {
              button {
                height: 30px;
              }
            }
          }

          .in-opt-values {
            width: 50%;
            height: 30px;
            overflow: hidden;
            display: flex;
            position: relative;
            right: calc(-40% - 20px);

            .in-opt-value {
              display: flex;
              align-items: center;
              justify-content: space-between;
              height: 25px;
              padding-left: 10px;
              min-width: 50px;
              margin: 0 5px 5px 0;
              border: 2px solid #d2d6e1;
              border-radius: 14px;
              background-color: #f5f5f5;

              span {
                color: #0078d8;
                font-family: Roboto;
                font-size: 12px;
                letter-spacing: 0.5px;
                line-height: 14px;
                display: block;
                max-width: 150px;

                &.hcl-icon-close {
                  color: #959595;
                  padding: 0 3px 0 5px;
                  cursor: pointer;
                }
              }
            }

            .more-values-button {
              height: 25px;
              margin: 0 5px 5px 0;

              button {
                line-height: 25px;
              }
            }
          }

          .delete-rule {
            width: 50px;
            display: flex;
            justify-content: center;
            align-items: center;

            .hcl-icon-close {
              font-size: 18px;
              color: #959595;
              cursor: pointer;
            }
          }

          &::before {
            content: "";
            left: -18px;
            top: -50%;
            border-style: dashed;
            width: 8px;
            height: 100%;
            position: absolute;
            border-width: 0 0 1px 0px;
          }

          &::after {
            content: "";
            left: -21px;
            top: -10px;
            border-style: dashed;
            width: 1px;
            height: calc(100% + 45px);
            position: absolute;
            border-width: 0px 1px 0px 0px;
          }

          &#group_1 {
            &::before {
              left: -30px;
              width: 20px;
            }

            &::after {
              left: -33px;
            }
          }

          &.last-rule {
            &::after {
              height: calc(50% + 10px);
            }
          }
        }
      }

      // &>.has-no-subgroup:first-child {
      //   >.single-query:not(#group_1) {
      //     border-top-left-radius: 4px;
      //     border-top-right-radius: 4px;
      //   }
      // }

      // &>[class~='has-no-subgroup']:last-of-type {
      //   >.single-query:not(#group_1) {
      //     border-bottom-left-radius: 4px;
      //     border-bottom-right-radius: 4px;
      //   }
      // }

      // & > [class~="has-no-subgroup"]:last-of-type {
      //   > .single-query:not(#group_1) {
      //     border-bottom-left-radius: 4px;
      //     border-bottom-right-radius: 4px;
      //   }
      // }
    }
  }

  .mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-bar {
    background-color: #f5f5f5;
  }

  .mat-slide-toggle-thumb {
    background-color: #f5821e;
  }

  .mat-slide-toggle-bar {
    background-color: #f5f5f5;
    border: 2px solid #959595;
  }

  .mat-slide-toggle-thumb-container {
    top: -5px;
    left: -2px;
  }

  .mat-input-element {
    color: #444444;
  }
}

.qb-in-notin-values-modal-container {
  .values-modal-title {
    color: #6d7692;
    font-family: Montserrat;
    font-size: 20px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 24px;
  }

  .opt-values-list {
    display: flex;
    flex-wrap: wrap;
    max-height: 120px;
    min-height: 80px;
    overflow: auto;

    .in-opt-value {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 25px;
      padding-left: 10px;
      min-width: 50px;
      margin: 0 5px 5px 0;
      border: 2px solid #d2d6e1;
      border-radius: 14px;
      background-color: #f5f5f5;

      span {
        color: #0078d8;
        font-family: Roboto;
        font-size: 12px;
        letter-spacing: 0.5px;
        line-height: 14px;
        display: block;
        max-width: 150px;

        &.hcl-icon-close {
          color: #959595;
          padding: 0 3px 0 5px;
          cursor: pointer;
        }
      }
    }
  }
}

.delete-nested-group {
  display: flex;

  button {
    line-height: 30px !important;
    margin-left: 20px;
  }
}

.expression-builder {
  .slider-part {
    width: 60px;
    background-color: #ececec;
    box-shadow: -15px 0 10px -2px rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    h3 {
      color: #6d7692;
      font-family: Montserrat;
      font-size: 20px;
      font-weight: bold;
      line-height: 24px;
      writing-mode: vertical-lr;
      transform: rotate(180deg);
      text-orientation: sideways;
      text-align: right;
      text-orientation: sideways;
      margin: 1rem;
    }
  }

  .side-panel-container {
    width: 990px;
    height: 100%;
    background-color: #ececec;
    box-shadow: -15px 0 10px -2px rgba(0, 0, 0, 0.3);
    flex-grow: 1;
    padding: 20px 30px;
    z-index: 1;

    .header-container {
      margin-bottom: 20px;
      max-height: 10%;
    }

    .content-container {
      max-height: 80%;

      .custom-expression-upper-container {
        max-height: 220px;

        .expression-input-title {
          color: #6d7692;
          font-family: Montserrat;
          font-size: 14px;
        }

        .expression-input-help-msg {
          color: #b8b7b7;
          font-family: Roboto;
          font-size: 13px;
          margin-bottom: 10px;
        }

        .expression-text-area-container {
          background-color: #f5f5f5;
          padding: 0px 10px;
          height: calc(100% - 51px);
          box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);

          textarea {
            color: #0091ff;
          }

          .mat-form-field-underline {
            display: none;
          }
        }

        .check-syntax-btn-container {
          right: 0;
          bottom: -20px;
        }
      }

      .expression-msg-container {
        font-family: Roboto;
        font-size: 12px;
        padding: 0px;
        margin-top: -10px;
      }

      .error-msg {
        color: #e12424;
      }

      .success-msg {
        color: green;
      }
    }

    .action-btn-container {
      max-height: 10%;
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      position: absolute;
      bottom: 20px;
      right: 40px;

      button {
        margin-left: 20px;
      }
    }
  }
}

.tribute-container {
  z-index: 9999 !important;
}
