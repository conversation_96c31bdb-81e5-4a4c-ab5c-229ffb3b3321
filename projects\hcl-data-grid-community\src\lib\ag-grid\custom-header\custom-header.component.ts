import { Component, ElementRef, OnInit, Renderer2, HostListener } from '@angular/core';
import { IHeaderAngularComp } from 'ag-grid-angular';
import { AgDataGridAdapter } from '../../AgDataGridAdapter';
import { IHeaderParams, IAfterGuiAttachedParams } from 'ag-grid-community';

@Component({
  selector: 'hcl-custom-header',
  templateUrl: './custom-header.component.html',
  styleUrls: ['./custom-header.component.scss']
})
export class CustomHeaderComponent implements IHeaderAngularComp, OnInit {
  /**
  * the data-grid adapter
  */
  private gridAdapter: AgDataGridAdapter;
  /**
   * the flag for custom popover
   */
  headerPopover: boolean;
  /**
   * the default column Definition that is set on the ag-grid
   */
  columnDefinition: any;
  /**
     * The default constructor
     */
  constructor(private elementRef: ElementRef, private renderer: Renderer2) {

  }
  refresh(params: IHeaderParams): boolean {
    throw new Error('Method not implemented.');
  }
  afterGuiAttached?(params?: IAfterGuiAttachedParams): void {
    throw new Error('Method not implemented.');
  }
  /**
   * Called by the AgGrid
   * param params
   */
  agInit(params: any): void {
    // set the adapter
    this.gridAdapter = params.gridAdapter;
    this.columnDefinition = params.column.colDef;
    this.gridAdapter.columnDefinitionsArray.push(this.columnDefinition);
    this.headerPopover = false;
  }
  /**
   * This function will be called when the filter icon is clicked
   */
  displayFilter(event) {
    if (this.elementRef.nativeElement.querySelector('hcl-custom-popover') && this.headerPopover) {
      event.stopPropagation();
    } else {
      this.headerPopover = !this.headerPopover;
    }
  }
  /**
   * This function will be calculate offset postion of popover according to window size
   */
  calculatePosition() {
    const width: number = this.elementRef.nativeElement.querySelector('hcl-custom-popover').offsetLeft +
      this.elementRef.nativeElement.querySelector('hcl-custom-popover > div').offsetWidth;

    if (width > window.innerWidth) {
      this.elementRef.nativeElement.querySelector('hcl-custom-popover > div').style.left = window.innerWidth - width - 25 + 'px';
      this.elementRef.nativeElement.querySelector('hcl-custom-popover .triangle').style.left = '60%';
    }
  }
  /**
   * This function will be used for hiding popover on outside click of popover
   */
  @HostListener('document:click', ['$event.target'])
  onMouseEnter(targetElement) {
    if (this.elementRef.nativeElement.contains(targetElement)) {
      if (targetElement === this.elementRef.nativeElement.querySelector('.filter-icon')) {
        if (!this.headerPopover) {
          this.headerPopover = !this.headerPopover;
        }
      }
    } else {
      if (this.headerPopover) {
        this.headerPopover = !this.headerPopover;
      }
    }
    if (this.headerPopover) {
      this.calculatePosition();
    }
  }
  /**
   * when the initialization of this component happens we need to set the
   * styles for the parent
   */
  ngOnInit() {
    this.renderer.setStyle(this.elementRef.nativeElement, 'width', '100%');
    //  this.renderer.setStyle(this.elementRef.nativeElement, 'padding' , '10px');
  }
  /**
   * Called when the column header is clicked
   * param event
   */
  doSort(event, column): void {
    if (column.sortable) {
      switch (column._sorting) {
        case 'ASC': {
          column._sorting = 'DESC';
          break;
        }
        case 'DESC': {
          column._sorting = 'NONE';
          break;
        }
        default: {
          column._sorting = 'ASC';
          break;
        }
      }

      this.gridAdapter.doSort(column);
    }
  }
}
