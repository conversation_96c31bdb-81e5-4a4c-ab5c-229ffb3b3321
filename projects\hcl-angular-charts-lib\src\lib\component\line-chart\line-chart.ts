export interface LineChartConfig {
    id: string;
    height: number;
    width: number;
    margin: number;
    type: 'curveLinear' | 'curveBasis' | 'curveBasisClosed' | 'curveMonotoneX' | 'curveMonotoneY'
    | 'curveNatural' | 'curveStep' | 'curveStepAfter' | 'curveStepBefore';
    xAxis: {
        type: 'DATETIME' | 'NUMBER';
        labelFormat?: any;
        min?: any;
        max?: any;
        ticks?: any;
        tickValues?: any;
        isInteger?: boolean;
        gridLines?: any;
    };
    yAxis: {
        type: 'DATETIME' | 'NUMBER';
        labelFormat?: any;
        min?: any;
        max?: any;
        ticks?: any;
        tickValues?: any;
        isInteger?: boolean;
        gridLines?: any
    };
    crosshair?: {
        horizontal: {
            color: string;
            strokeWidth: number;
        },
        vertical: {
            color: string;
            strokeWidth: number;
        }
    };
    series: Series[];
    threshold?: {
        value: any;
        label?: string;
    }
}

export interface Series {
    xName: string;
    yName: string;
    color: string;
    data: any[];
}