import { Component, OnInit, ViewEncapsulation, AfterViewInit } from '@angular/core';
import { DataGridColumnConf, HoverIcon } from '../../data-grid.conf';


@Component({
  selector: 'hcl-custom-cell',
  templateUrl: './custom-cell.component.html',
  styleUrls: ['./custom-cell.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class CustomCellComponent implements OnInit, AfterViewInit {
  loadIcons = false;
  /**
   * the default column Defination that is set on the ag-grid
   */
  columnDefinition: DataGridColumnConf;
  /**
   * the data that is associated to this row
   */
  rowData: any;
  /**
   * Array to hold hover icons on the row hover
   */
  hoverIcons: HoverIcon[];
  /**
  * Property to check if the cell is draggable or not
  */
  draggable: boolean;
  /**
  * Check if the selected nodes/rows are dragged
  */
  selectedRowsdragged = false;
  /**
   * Variable to store params provided by ag-grid
   */
  public params: any;
  /**
     * Variable to store selected nodes/rows
     */
  nodesArray = [];
  /**
   * Check if the column is drag icon column
   */
  draggableRow: boolean;
  /**
   * Ids of dragged rows
   */
  ids: string;
  /**
   * Called by the AgGrid
   * param params
   */
  agInit(params: any): void {
    // Check if drag and drop is true, then make the cell dragable
    if (params.gridAdapter && params.gridAdapter.dataGridConfig.dragAndDrop) {
      this.draggable = true;
    }
    this.params = params;
    // Set the column definition
    this.columnDefinition = params.column.colDef;
    // Set the row data
    this.rowData = params.data;
    if (params.colDef.actions) {
      this.hoverIcons = params.colDef.actions.hoverIcons;
    }
    this.draggableRow = params.colDef.colId === 'dragAndDrop';
  }

  itemClicked(event, hoverIconObject) {
    if (event.stopPropagation) {    // standard
      event.stopPropagation();
    } else {    // IE6-8
      event.cancelBubble = true;
    }
    const retObj: any = {
      rowObj: this.params.node.data
    };
    const selectedAction = this.hoverIcons.filter(ele => {
      return ele.name === hoverIconObject.name;
    });
    if (selectedAction[0] && selectedAction[0].iconClickHandler) {
      selectedAction[0].iconClickHandler(retObj);
    }
  }

  visibleActionClicked(event, index) {
    if (event.stopPropagation) {    // standard
      event.stopPropagation();
    } else {    // IE6-8
      event.cancelBubble = true;
    }

    this.rowData.visibleActions[index].iconClickHandler(this.rowData);
  }

  disableIcon(hoverIconObject, rowDataObject) {
    const retObj: any = {
      rowObj: rowDataObject
    };
    const selectedAction = this.hoverIcons.filter(ele => {
      return ele.name === hoverIconObject.name;
    });
    if (selectedAction[0] && selectedAction[0].disableIconHandler) {
      if (selectedAction[0].disableIconHandler(retObj)) {
        return true;
      }
    }
    return false;
  }

  ngAfterViewInit() {
    this.loadIcons = true;
  }

  onDragStart(event: DragEvent) {
    const ids = this.populateRowIDs();
    const userAgent = window.navigator.userAgent;
    // check if the browser is IE
    const isIE = userAgent.indexOf('Trident/') >= 0;
    event.dataTransfer.setData(isIE ? 'text' : 'text/plain', ids);
    if (!this.selectedRowsdragged) {
      this.params.api.deselectAll();
    }
    // Drag Image Override
    // const canvas = document.createElement('canvas');
    // const context = canvas.getContext('2d');
    // canvas.width = 120;
    // canvas.height = 20;
    // context.fillStyle = '#333333';
    // context.fillRect(0, 0, canvas.width, canvas.height);
    // context.fillStyle = '#999999';
    // context.font = 'bold 13px Arial';
    // const idsLength = ids.split(',').length;
    // if (idsLength === 1) {
    //   context.fillText(1 + ' Row Dragged', 5, 15);
    // } else {
    //   context.fillText(idsLength + ' Rows Dragged', 5, 15);
    // }
    // document.body.append(canvas);
    // event.dataTransfer.setDragImage(canvas, -15, 9);
  }

  /**
   * The scenario in which row other than selected rows is dragged
   */
  onDragEnd(event) {
    if (!this.selectedRowsdragged && this.nodesArray.length !== 0) {
      this.nodesArray.forEach((element) => {
        element.setSelected(true);
      });
    }
  }

  /**
   * Polpulate the rows upon drag according to the scenarios
   */
  populateRowIDs() {
    this.nodesArray = this.params.api.getSelectedNodes();
    if (this.nodesArray.length !== 0) {
      for (let i = 0; i < this.nodesArray.length; i++) {
        if (this.nodesArray[i].id === this.params.node.id) {
          this.selectedRowsdragged = true;
          break;
        } else {
          this.selectedRowsdragged = false;
        }
      }
      if (this.selectedRowsdragged) {
        this.nodesArray.forEach((element, index) => {
          if (index === 0) {
            this.ids = element.id;
          } else {
            this.ids += ',' + element.id;
          }
        });
      } else {
        this.ids = this.params.node.id;
      }
    } else {
      this.ids = this.params.node.id;
    }
    return this.ids;
  }

  constructor() { }

  ngOnInit() {
  }

  /**
   * @param event . Function to stop the hover action container row click event propogation.
   */
  stopProp(event) {
    event.stopPropagation();
    event.cancelBubble = true;
  }
}
