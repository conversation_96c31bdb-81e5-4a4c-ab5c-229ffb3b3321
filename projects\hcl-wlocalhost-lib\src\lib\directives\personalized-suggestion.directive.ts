import { AfterViewInit, Directive, ElementRef, Input, OnChanges, OnInit, Renderer2, SimpleChanges } from '@angular/core';
import Tribute from "tributejs";
import { PersonalizationSuggestionDirectiveConfig } from '../interfaces';

@Directive({
  selector: '[personalisedSuggestions]'
})
export class PersonalisedSuggestionsDirective implements AfterViewInit, OnChanges {

  @Input('personalisedSuggestions') configSuggestion: PersonalizationSuggestionDirectiveConfig;

  tribute = null;

  constructor(private el: ElementRef) { }

  suggestionAdded(event) {
    event.currentTarget.value = event.currentTarget.value.substr(0, (event.currentTarget.value.length - 1)).replace(/<span>/g,'').replace(/<\/span>/g,'');
    this.configSuggestion.modelValue[this.configSuggestion.propertyValue] = event.currentTarget.value;
  }

  ngAfterViewInit() {
    this.el.nativeElement.addEventListener("tribute-replaced", this.suggestionAdded.bind(this));
    if (!this.configSuggestion.suggestion.trigger) {
      this.configSuggestion.suggestion.trigger = '@';
    }
    const conf = {...this.configSuggestion.suggestion, selectClass: 'selected-suggestions', itemClass: 'suggestion-item', containerClass: 'suggestion-container'}
    this.tribute = new Tribute(conf);
    const e: any = this.el.nativeElement;
    this.tribute.attach(e);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (this.tribute && this.tribute.collection && this.tribute.collection[0] && this.tribute.collection[0].values) {
      this.tribute.collection[0].values = changes.configSuggestion.currentValue.suggestion.values;
    }
  }
}
