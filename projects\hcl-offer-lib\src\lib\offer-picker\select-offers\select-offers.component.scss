.offers-container {
  padding: 10px;
  display: flex;
  height: calc(100% - 50px);
  overflow-x: hidden;
  justify-content: space-between;

  .mat-tab-group {
    .opacity1 {
      height: 100%;
    }
    height: calc(100% - 30px);
    .mat-tab-body-wrapper {
      height: calc(100% - 28px);
    }
  }

  .folders-pane {
    // margin-right: 1%;
    .folders-logo {
      width: 100%;
      background-color: #f5f5f5;
      height: 54px;
      display: flex;
      align-items: center;
      color: #6d7692;
      font-family: Montserrat;
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 7px;
      box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
      justify-content: space-between;
    }
    .side-pane {
      width: 100%;
      height: calc(100% - 60px) !important;
      background-color: #f5f5f5;

      box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
    }
  }

  .hcl-icon-close-x {
    font-size: 12px;
  }

  // The CSS for the max & min buttons on the Folder panel ****STARTS
  .vertical-label > span {
    writing-mode: vertical-lr;
    transform: rotate(180deg);
    margin-top: 15px;
    color: #6d7692;
    font-family: Montserrat, sans-serif;
    font-size: 20px;
    margin-left: 7px;
    font-weight: 600;

    &.reset-transform-rotate {
      transform: rotate(0deg);
    }
  }
  .max-min-icon-container {
    float: right;
    display: inline-flex;

    .expand-collapse-icon-container {
      float: left;
      width: 70px;
      display: flex;
      height: 54px;
      align-items: center;

      .max-min-button-container {
        float: left;
        width: 100%;
        height: 56px;
        display: flex;
        padding-left: 5px;

        .max-button-container,
        .min-button-container {
          //width: 68px;
          align-items: center;
          display: flex;
          width: 100%;
          justify-content: center;

          hcl-button > .navBtnCls {
            border-radius: 0px;
            padding: 0 !important;
            min-width: 24px !important;
            min-height: 24px !important;
          }
        }
      }
    }
    .expand-collapse-seperator {
      width: 1px;
      height: 26px;
      margin: 14px 0;
      background-color: #bcbbbb;
    }
  }
  .max-button-offer-container {
    background-color: #f5f5f5;
    height: 54px;
    align-items: center;
    margin-bottom: 7px;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
    display: flex;
    .hcl-md-button {
      display: flex !important;
    }
    button {
      min-width: 100%;
      width: 100%;
    }
  }
  .max-text-offer-container {
    background-color: #f5f5f5;
    height: calc(100% - 60px);
    margin-bottom: 7px;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
    padding-left: 5px;
  }
  // The CSS for the max & min buttons on the Folder panel ****ENDS
  // The CSS for animation starts hear ****STARTS
  .offer-grid-panel-minimized {
    -webkit-transition: width 0.5s linear;
    transition: width 0.5s linear;
    width: 3%;
    flex-grow: 100;

    .max-container-offer-list {
      height: 100%;
    }
    /* justify-content: center; */
    /* text-align: center; */

    //background: #f5f5f5;
    hcl-tabs,
    .list-conatiner-header {
      display: none !important;
    }
  }
  .folder-panel-minimized {
    -webkit-transition: width 0.5s linear;
    transition: width 0.5s linear;
    width: 3%;
    min-width: 50px;
    .side-pane {
      height: calc(100% - 60px) !important;
      hcl-offer-folders > div {
        display: none;
      }
    }
    .folders-logo {
      .ml-3 {
        -webkit-transition: width 1.5s linear;
        transition: width 1.5s linear;
        display: none;
      }
      .max-min-icon-container {
        width: 50px;
        .expand-collapse-seperator {
          display: none;
        }
        .expand-collapse-icon-container {
          margin-left: 0px;
        }
      }
    }
  }
  .offer-grid-panel-normal {
    -webkit-transition: width 0.5s linear;
    transition: width 0.5s linear;
    width: calc(65% - 15px)!important;
  }
  .folder-panel-normal {
    -webkit-transition: width 0.5s linear;
    transition: width 0.5s linear;
    width: 35%;
    //-webkit-transition: min-width 1.5s linear;
    //transition: min-width 1.5s linear;
    //min-width: 485px;
    .side-pane {
      width: 100%;
      height: calc(100% - 60px) !important;
      hcl-offer-folders > div {
        display: block;
      }
    }
    .folders-logo {
      .ml-3 {
        display: block;
      }
    }
  }
  .offer-grid-panel-maximized {
    -webkit-transition: width 0.5s linear;
    transition: width 0.5s linear;
    width: calc(97% - 15px) !important;
    height: 100%;
  }
  .folder-panel-maximized {
    -webkit-transition: width 0.5s linear;
    transition: width 0.5s linear;
    width: 96%;
    .side-pane {
      width: 100%;
      height: 65vh;
      .folders {
        width: 100%;
      }
      hcl-offer-folders > div {
        width: 100%;
        display: block;
      }
    }
    .folders .split-panel-container {
      width: 100%;
    }
    .folders-logo {
      .ml-3 {
        display: block;
      }
      .max-min-icon-container {
        .expand-collapse-icon-container {
          width: 53px;
        }
      }
    }
  }
  // The CSS for animation starts hear ****ENDS
  .header-and-grid-container {
    .breadcrumb-container-non-clickable {
      .custom-breadcrumb-item {
        cursor: default !important;
      }

      i {
        cursor: default !important;
      }
    }
    //width: 100% !important;
    .list-conatiner-header {
      height: 30px;
      display: flex;
      align-items: center;
      font-family: Roboto, Roboto Regular, sans-serif;
      font-size: 14px;
      overflow: hidden;
      .custom-breadcrumb-item {
        cursor: default !important;
      }

      i {
        cursor: default !important;
      }
      .selected-folder {
        display: inline-block;
        width: 90%;
        overflow: hidden;
      }
      .selected-folder-text {
        width: 110px;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .parent-tab-container {
      //margin: 0 auto 0 auto;
      //max-height: 700px;
      //max-width: 1552px;
      height: 100%;
      overflow: hidden;
      .offers-grid-container {
        height: calc(100% - 60px);

        .hcl-grid-container {
          margin: 0 2px;
          box-shadow: 0 2px 2px 0 rgb(0 0 0 / 25%);
        }
      }
      .offer-header-row {
        min-width: 600px;
        padding: 9px 9px 0 9px;
        height: 55px;
        display: flex;
        align-items: center;

        .search-container-offer {
          align-items: center;
          display: flex;
        }
        .search-container-offer-list {
          align-items: center;
          display: flex;
          visibility: hidden;
          span {
            visibility: visible;
          }
        }
        .action-container {
          margin-left: auto;
          align-items: center;
          display: flex;
          //   top: 13px;
          //   right: 0px;
          //   position: absolute;

          .refresh-btn {
            button {
              border: navajowhite;
              min-width: auto;
              padding: 0;
              font-size: 16px;
            }
          }

          .sort-container {
            margin: 0 20px;
            button {
              padding: 0 0 0 10px;
            }
          }
          .menu-container {
            display: inline-block;
          }
          .filter-container {
            display: inline-flex;
            justify-content: center;
            align-items: center;

            .hcl-icon-filter {
              color: #0078d8;
              cursor: pointer;
              font-size: 12px;
              margin: 10px 20px 10px 10px;
            }
          }
        }
        .reset-position {
          top: 0;
        }
        background-color: #f5f5f5;
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
        display: flex;
        flex-direction: row;
        .offers-search {
          width: 250px;
          height: 48px;
        }
      }
    }
  }
  .mat-select-arrow {
    border: 4px solid #0078d8;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3.5px;
    margin-bottom: 3px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
  }

  // override code

  .header-and-grid-container {
    .mat-tab-label {
      height: 30.5px !important;
    }
  }
  .breadcrumb {
    margin-bottom: 0;
    padding: 0;
    margin: 0;
    .bIcon {
      font-size: 9px;
      margin: 0 3px;
    }
    .breadcrumb-item {
      padding-bottom: 0;
    }
    .custom-breadcrumb-item {
      font-size: 13px;
      padding-left: 0;
      display: flex;
      align-items: center;
      font-family: Roboto, Roboto Regular, sans-serif;
    }
    i {
      color: #959595;
    }
  }
  .folders-breadcrum {
    margin: 12px 0 0 12px !important;
    .breadcrumb {
      padding: 0 0 0 6px;
      .breadcrumb-item {
        cursor: pointer !important;
      }
    }
  }
  .folders {
    height: 100%;
    padding: 15px 5px 0 5px;
    .split-panel-container {
      .split-area {
        height: auto;
      }
    }
  }

  .hcl-sidebar .mat-drawer-backdrop {
    position: fixed;
    z-index: 3;
  }
  .mat-drawer-inner-container {
    background-color: #ececec;
    box-shadow: -15px 0 10px -2px rgba(0, 0, 0, 0.3);
    width: 35vw;
    overflow: hidden;
    overflow-y: auto;
  }
  .offer-header-row {
    [class^="hcl-icon-"] {
      margin-right: 6px;
    }

    .divider {
      flex: 1 1 auto;
    }

    .view-controls {
      display: flex;
      align-items: center;
      margin-left: 10px;

      [class^="hcl-icon-"] {
        font-size: 24px;
        color: #959595;
        cursor: pointer;
        &.active-view {
          color: #f5821e;
          cursor: default;
        }
      }
    }
  }
  .parent-tab-container {
    .hcl-grid-container {
      height: 58vh;
    }
    .mat-tab-label {
      font-size: 13px;
      height: 31px !important;
    }
    .child-tab-container {
      .hcl-grid-container {
        height: 62vh;
        min-height: 350px !important;
      }
      .mat-tab-body-content {
        height: 72vh;
      }
    }
  }
  .hcl-icon-down-without-border {
    font-size: 7px;
    margin-left: 4px;
    margin-top: 2px;
    display: inline-block;
    width: 15px;
  }
  .custom-icon {
    height: 30px;
    line-height: 6px;
  }
  .print-icon {
    height: 30px;
    line-height: 16px;
  }
  .hcl-icon-filter {
    font-size: 9px;
  }
}

.offer-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .action-button {
    margin: 0 10px;
  }
}

.breadcrumb-menu-item > span,
.context-menu-item,
.menu-item {
  padding: 0 16px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px !important;
  width: 100%;
}

.action-menu-item {
  display: inline-flex;
  align-items: center;
  min-width: 250px;
  padding: 05px 10px 05px 20px;

  .mat-radio-group {
    .mat-radio-button {
      .mat-radio-label {
        margin-bottom: 0;
        margin-top: 5px;
        .mat-radio-container {
          .mat-radio-outer-circle {
            height: 15px;
            width: 15px;
          }
          .mat-radio-inner-circle {
            height: 15px;
            width: 15px;
          }
        }
      }
    }
  }
}

.action-menu-header {
  display: flex;
  align-items: end;
  min-width: 250px;
  padding: 10px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  color: #6d7692;
  font-family: Montserrat;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 19px;
  cursor: default;
}

.offer-sort-btn {
  button {
    i.hcl-icon-sort {
      font-size: 10px;
    }
  }
}

.offer-menu-list button.mat-menu-item {
  padding: 0 !important;
  max-height: 50px;
  height: auto;
  .menuDiv.offer-menu-row {
    padding: 0;
  }
}
