import { Component, OnInit, ViewChild } from '@angular/core';
import { ApacheLineChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/apache-chart/apache-chart';
import { ApacheChartComponent } from 'projects/lib/hcl-angular-charts-lib';

@Component({
  selector: 'app-apache-line-chart-demo',
  templateUrl: './apache-line-chart-demo.component.html',
  styleUrls: ['./apache-line-chart-demo.component.scss']
})
export class ApacheLineChartDemoComponent {

  @ViewChild('apacheLineChart') apacheLineChart: ApacheChartComponent;
  @ViewChild('apacheLineChart1') apacheLineChart1: ApacheChartComponent;
  @ViewChild('apacheLineChart2') apacheLineChart2: ApacheChartComponent;

  apacheLineChartConfig: ApacheLineChartConfig = {
    title: {
      id:"LC1",
      text: 'Line chart'
    },
    tooltip: {
      trigger: 'item',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
      height: 'auto',
      width: 'auto'
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      axisLabel: {
        show: true,
        interval: 0,
        rotate: 0 
      },
      axisTick: {
        show: true,
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      scale: true,
      splitNumber: 11 
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 1,
        end: 50,
        height: 20,
      },
      {
        type: 'inside',
        xAxisIndex: [0],
        start: 1,
        end: 35
      },
    ],
    series: [
      {
        name: 'Email',
        type: 'line',
        stack: {
          stacktype: 'Total'
        },
        symbol: 'emptyCircle',
        symbolSize: 10,
        showAllSymbol: false,
        lineStyle: {
          width: 1.5
        },
        itemStyle: {
          color: 'red'
        },
        data: [
          ['Feb-2023', 0], 
          ['March-2023', 0],
          ['April-2023', 0],
          ['May-2023', 0],
          ['June-2023', 0],
          ['July-2023', 0],
          ['Aug-2023', 0],
          ['Sept-2023', 6],
          ['Oct-2023', 0],
          ['Feb-2024', 0],
          ['March-2024', 10],
          ['April-2024', 5],
          ['May-2024', 9],
          ['June-2024', 0],
          ['July-2024', 2],
          ['Aug-2024', 0],
          ['Sept-2024', 1],
          ['Aug-2024', 0],
          ['Feb-2025', 0], 
          ['March-2025', 10],
          ['April-2025', 5],
          ['May-2025', 9],
          ['June-2025', 0],
          ['July-2025', 2],
          ['Aug-2025', 0],
          ['Sept-2025', 1],
          ['Aug-2025', 0],
        ]
      },
      {
        name: 'SMS',
        type: 'line',
        stack: {
          stacktype: 'Total'
        },
        symbol: 'emptyCircle',
        symbolSize: 10,
        lineStyle: {
          width: 1.5
        },
        itemStyle: {
          color: 'green' 
        },
        data: [
          ['Feb-2023', 0], 
          ['March-2023', 0],
          ['April-2023', 0],
          ['May-2023', 0],
          ['June-2023', 0],
          ['July-2023', 0],
          ['Aug-2023', 0],
          ['Sept-2023', 0],
          ['Oct-2023', 0],
          ['Feb-2024', 0], 
          ['March-2024', 10],
          ['April-2024', 5],
          ['May-2024', 9],
          ['June-2024', 0],
          ['July-2024', 2],
          ['Aug-2024', 0],
          ['Sept-2024', 1],
          ['Aug-2024', 0],
          ['Feb-2025', 0], 
          ['March-2025', 10],
          ['April-2025', 5],
          ['May-2025', 9],
          ['June-2025', 0],
          ['July-2025', 2],
          ['Aug-2025', 0],
          ['Sept-2025', 1],
          ['Aug-2025', 0],
        ]
      }
    ]
  }

  apacheLineChartConfig1: ApacheLineChartConfig = {
    title: {
      id:"LC2",
      text: 'Line chart 1'
    },
    tooltip: {
      trigger: 'item',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
      height: 'auto',
      width: 'auto'
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      axisLabel: {
        show: true,
        interval: 0,
        rotate: 0 
      },
      axisTick: {
        show: true,
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      scale: true,
      splitNumber: 11 
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 1,
        end: 50,
        height: 20,
      },
      {
        type: 'inside',
        xAxisIndex: [0],
        start: 1,
        end: 35
      },
    ],
    series: [
      {
        name: 'Email',
        type: 'line',
        stack: {
          stacktype: 'Total'
        },
        symbol: 'emptyCircle',
        symbolSize: 10,
        showAllSymbol: false,
        lineStyle: {
          width: 1.5
        },
        itemStyle: {
          color: 'red'
        },
        data: [
          ['Feb-2023', 0], 
          ['March-2023', 0],
          ['April-2023', 0],
          ['May-2023', 0],
          ['June-2023', 0],
          ['July-2023', 0],
          ['Aug-2023', 0],
          ['Sept-2023', 6],
          ['Oct-2023', 0],
          ['Feb-2024', 0],
          ['March-2024', 10],
          ['April-2024', 5],
          ['May-2024', 9],
          ['June-2024', 0],
          ['July-2024', 2],
          ['Aug-2024', 0],
          ['Sept-2024', 1],
          ['Aug-2024', 0],
          ['Feb-2025', 0], 
          ['March-2025', 10],
          ['April-2025', 5],
          ['May-2025', 9],
          ['June-2025', 0],
          ['July-2025', 2],
          ['Aug-2025', 0],
          ['Sept-2025', 1],
          ['Aug-2025', 0],
        ]
      },
      {
        name: 'SMS',
        type: 'line',
        stack: {
          stacktype: 'Total'
        },
        symbol: 'emptyCircle',
        symbolSize: 10,
        lineStyle: {
          width: 1.5
        },
        itemStyle: {
          color: 'green' 
        },
        data: [
          ['Feb-2023', 0], 
          ['March-2023', 0],
          ['April-2023', 0],
          ['May-2023', 0],
          ['June-2023', 0],
          ['July-2023', 0],
          ['Aug-2023', 0],
          ['Sept-2023', 0],
          ['Oct-2023', 0],
          ['Feb-2024', 0], 
          ['March-2024', 10],
          ['April-2024', 5],
          ['May-2024', 9],
          ['June-2024', 0],
          ['July-2024', 2],
          ['Aug-2024', 0],
          ['Sept-2024', 1],
          ['Aug-2024', 0],
          ['Feb-2025', 0], 
          ['March-2025', 10],
          ['April-2025', 5],
          ['May-2025', 9],
          ['June-2025', 0],
          ['July-2025', 2],
          ['Aug-2025', 0],
          ['Sept-2025', 1],
          ['Aug-2025', 0],
        ]
      }
    ]
  }

  apacheLineChartConfig2: ApacheLineChartConfig = {
    title: {
      id:"LC3",
      text: 'Line chart 1'
    },
    tooltip: {
      trigger: 'item',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
      height: 'auto',
      width: 'auto'
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      axisLabel: {
        show: true,
        interval: 0,
        rotate: 0,
        margin: 8,
        fontSize: 12,
        fontFamily: 'Segoe UI',
      },
      axisTick: {
        show: true,
        interval: 0,
        lineStyle: {
          color: '#444444',
          width: 1,
          opacity: 0.3
        }
      }
    },
    yAxis: {
      type: 'value',
      scale: true,
      splitNumber: 10,
      splitLine: {
        show: true, 
        lineStyle: {
            type: [10,10,10],
            color: '#d3d3d3',
        }
    }
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 50,
        end: 100,
        height: 15,
        zoomLock: false,
        borderRadius: 5,
        showDetail: false,
        brushSelect: false,
        handleIcon: "circle",
      },
      {
        type: 'inside',
        xAxisIndex: [0],
        start: 1,
        end: 100,
        show: true,
        zoomLock: true
      },
    ],
    series: [
      {
        name: 'Email',
        type: 'line',
        stack: {
          stacktype: 'Total'
        },
        symbol: 'emptyCircle',
        symbolSize: 10,
        showAllSymbol: true,
        lineStyle: {
          width: 1.5
        },
        itemStyle: {
          color: 'red'
        },
        data: [
          [1,1],
          [2,1],
          [3,3],
          [4,2],
          [5,1],
          [6,1],
          [7,1],
          [8,1],
          [9,1],
          [10,1],
          [11,1],
          [12,1],
          [13,3],
          [14,2],
          [15,1],
          [16,1],
          [17,1],
          [18,1],
          [19,1],
          [20,1],
          [21,1],
          [22,1],
          [23,3],
          [24,2],
          [25,1],
          [26,1],
          [27,1],
          [28,1],
          [29,1],
          [30,1],
          [31,1],
          [32,1],
          [33,3],
          [34,2],
          [35,1],
          [36,1],
          [37,1],
          [38,1],
          [39,1],
          [40,1],
          [41,1],
          [42,1],
          [43,3],
          [44,2],
          [45,1],
          [46,1],
          [47,1],
          [48,1],
          [49,1],
          [50,1],
          [51,1],
          [52,1],
          [53,3],
          [54,2],
          [55,1],
          [56,1],
          [57,1],
          [58,1],
          [59,1],
          [60,1],
          [61,1],
          [62,1],
          [63,3],
          [64,2],
          [65,1],
          [66,1],
          [67,1],
          [68,1],
          [69,1],
          [70,1],
          [71,1],
          [72,1],
          [73,3],
          [74,2],
          [75,1],
          [76,1],
          [77,1],
          [78,1],
          [79,1],
          [80,1],
          [81,1],
          [82,1],
          [83,3],
          [84,2],
          [85,1],
          [86,1],
          [87,1],
          [88,1],
          [89,1],
          [90,1],
          [91,1],
          [92,1],
          [93,3],
          [94,2],
          [95,1],
          [96,1],
          [97,1],
          [98,1],
          [99,1],
          [100,1],
        ]
      },
    ]
  }

  ngAfterViewInit() {
    this.apacheLineChart.resizeChart();
    this.apacheLineChart2.adjustIntervalOnDataZoom();
  }
}
