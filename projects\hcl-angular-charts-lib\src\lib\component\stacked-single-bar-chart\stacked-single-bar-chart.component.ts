import {AfterViewInit, Component, ElementRef, Input, OnChanges, OnInit, Renderer2, SimpleChanges, ViewEncapsulation} from '@angular/core';
import {ChartConfig} from '../../config/chart-config';
import {BaseChartRenderer} from '../../renderer/BaseChartRenderer';
import {CHART_TYPE, RendererFactory} from '../../renderer/RendererFactory';
import * as d3 from 'd3';
import { StackedSingleBarChartConfig } from './stacked-single-bar-chart';

@Component({
  selector: 'hcl-stacked-single-bar-chart-v2',
  templateUrl: './stacked-single-bar-chart.component.html',
  styleUrls: ['./stacked-single-bar-chart.component.scss'],
  encapsulation : ViewEncapsulation.None
})
export class StackedSingleBarChartComponent implements OnInit, AfterViewInit, OnChanges {
  /**
   * The default chart renderer
   */
  private chartRenderer: BaseChartRenderer;
  xScale: any;
  xScaleLinearGauge: any;
  yScale: any;
  linearGuage: any;

  barMouseOver: Function = () => {};
  barMouseMove: Function = () => {};
  barMouseLeave: Function = () => {};

  @Input() data = [];

  parentGroupElem: any

  /**
   * The configuration of the chart
   */
  @Input() config : StackedSingleBarChartConfig = null;
  /**
   * The default constructor
   * renderer: we need to play with the DOM so we need the Renderer
   * element: we will add the Charting to the current element
   */
  constructor(private renderer: Renderer2,
              private element: ElementRef) { }
  ngOnChanges(changes: SimpleChanges): void {
    console.log(changes.data)
    if (changes.data.currentValue && Array.isArray(changes.data.currentValue) && changes.data.currentValue.length > 0) {
      window.setTimeout(() => {
        if (d3.select('#svg_stacked_single_bar_chart_' + this.config.chartContainerId)) {
          d3.select('#svg_stacked_single_bar_chart_' + this.config.chartContainerId).remove()
        }
        this.createParentSVG();
        this.createBar();
        this.setupBarMouseEvents();
      }, 0)
    }
  }

  /**
   * We need to create the renderer
   */
  ngOnInit() {

  }

  /**
   * After the initialization we need to render the chart
   */
  ngAfterViewInit(): void {

  }

  createParentSVG() {
    d3.select('.stacked-single-bar-chart')
      .append(`svg`)
      .attr('id', 'svg_stacked_single_bar_chart_' + this.config.chartContainerId)
      .attr(`width`, this.config.width?this.config.width:"100%")
      .attr(`height`, this.config.height)
    this.parentGroupElem = d3.select('#svg_stacked_single_bar_chart_' + this.config.chartContainerId).append(`g`);
  }

  createBar() {
    let cumulative = 0;
    const total = d3.sum(this.data, d => d.value);
    const percent = d3.scaleLinear()
        .domain([0, total])
        .range([0, 100])
    this.data = this.data.map((item) => {
      cumulative += item.value;
      return {
        ...item,
        cumulative: cumulative - item.value,
        percent: percent(item.value)
      }
    }).filter(d => d.value > 0)
    
  
    // set up scales for horizontal placement
    const xScale = d3.scaleLinear()
      .domain([0, total])
      .range([0, this.config.width?this.config.width:100])

    // stack rect for each data value
    this.parentGroupElem.selectAll('rect')
      .data(this.data)
      .enter().append('rect')
      .attr('class', 'rect-stacked')
      .attr('x', (d) => { 
        return this.config.width?xScale(d.cumulative):xScale(d.cumulative)+"%";})
      .attr('y', 5)
      .attr('height', 30)
      .attr('width', d => this.config.width?xScale(d.value):d.percent+"%")
      .style('fill', (d, i) => d.color)
      .on('mouseenter', (data, index, node) => {this.barMouseOver(data, index, node);})
      .on('mousemove', (data, index, node) => {this.barMouseMove(data, index, node);})
      .on('mouseout', (data, index, node) => {this.barMouseLeave(data, index, node)})

  }

  setupBarMouseEvents() {
    const tooltipContainer = document.createElement("div");
    tooltipContainer.className = "stacked-single-bar-chart-tooltip";
    const tooltipArrowElement = document.createElement("div");
    tooltipArrowElement.className = "stacked-single-bar-chart-tooltip-arrow";
    const tooltipContentsElement = document.createElement("div");
    tooltipContentsElement.className = "stacked-single-bar-chart-tooltip-contents";
    tooltipContainer.appendChild(tooltipArrowElement);
    tooltipContainer.appendChild(tooltipContentsElement);
    document.body.appendChild(tooltipContainer);  


    const tooltip = d3.select( ".stacked-single-bar-chart-tooltip" );
    const tooltipContents = d3.select( ".stacked-single-bar-chart-tooltip .stacked-single-bar-chart-tooltip-contents" );
    this.barMouseOver = (data, index, node) => {      
      tooltip
      .style('display', 'block')
      tooltipContents.html(this.config.tooltipHTML(data))
      node[index].style.stroke = "#f5821e";
      node[index].style["stroke-width"] = "3"
    };

    this.barMouseMove = (data, index, node) => {
      tooltip
      .style('left', `${d3.event.clientX - 150}px`)
      .style('top', `${(d3.event.clientY + 5)}px`);
    };
  
    this.barMouseLeave = (data, index, node) => {
        tooltip.style('display', 'none');
        node[index].style.stroke = "transparent";
    };
  }



}