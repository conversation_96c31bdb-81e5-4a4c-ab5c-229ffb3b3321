<div class="details-container">
    <div class="details-container-title-section">
        <h3>
            {{config.dpConfig.translations.details}}
        </h3>
    </div>
    <div class="deatails-action-container" >
        <div class="bincount-input-container">
            <ng-container  *ngIf="config.fieldDataType !== 'String'">
                <div class="input-sec">
                    <hcl-input [config]="binCountInputConfig"></hcl-input>
                </div>
                <div class="button-sec">
                    <hcl-button [config]="applyButtonConfig" class="pl-4" (onclick)="apply()">
                    </hcl-button>
                </div>
            </ng-container>
        </div>
        <div class="action-btn-container">
            <div class="icon-container" (click)="toggleView('graph-view')" 
                [ngClass]="{'select': isGraphView, 
                    'unselect': !isGraphView,
                    'border-left-top-bottom': !isGraphView 
                    }">
                <span>
                    <i class="hcl-icon-bar-chart"></i>
                </span>
            </div>
            <div class="icon-container select" (click)="toggleView('list-view')" 
                [ngClass]="{'select': !isGraphView,
                    'unselect': isGraphView, 
                    'border-right-top-bottom': isGraphView}">
                <span>
                    <i class="hcl-icon-list-view"></i>
                </span>
            </div>
        </div>
    </div>
    <div class="loader-sec" *ngIf="dataProfileDetailsSpinner.isLoading">
        <hcl-progress-spinner [config]="dataProfileDetailsSpinner">
        </hcl-progress-spinner>
    </div>
    <div class="bar-chart-container" *ngIf="!dataProfileDetailsSpinner.isLoading && isGraphView">
        <div class="bar-chart-sec">
            <hcl-apache-chart [apacheChartConfig]="barChartConfig" #barChart></hcl-apache-chart>
        </div>
    </div>
    <div class="list-view-container" *ngIf="!dataProfileDetailsSpinner.isLoading && !isGraphView">
        <div class="list-view-sec">
           <table>
                <thead>
                     <tr>
                        <th class="col-6" *ngIf="config.fieldDataType !== 'String'">
                            {{config.dpConfig.translations.bins}}
                        </th>
                        <th class="col-6 width-minuns-5" *ngIf="config.fieldDataType === 'String'">
                            {{config.dpConfig.translations.categories}}
                        </th>
                        <th class="col-6 width-minuns-5">
                            {{config.dpConfig.translations.count}}
                        </th>
                     </tr>
                </thead>
                <tbody class="scrollable-tbody">
                     <tr *ngFor="let item of distributionData">
                        <td class="col-6" *ngIf="config.fieldDataType !== 'String'">
                            {{ item.lowerBound }} - {{ item.upperBound }}
                        </td>
                        <td class="col-6" *ngIf="config.fieldDataType === 'String'">
                            {{ item.category }}
                        </td>
                        <td class="col-6">
                            {{item.frequency}}
                        </td>
                     </tr>
                </tbody>
           </table>
        </div>
    </div>
</div>