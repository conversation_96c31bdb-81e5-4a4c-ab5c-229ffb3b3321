import { Injectable } from '@angular/core';
import * as CodeMirror from "codemirror/lib/codemirror";
import "codemirror/addon/hint/css-hint.js";
import "codemirror/mode/htmlmixed/htmlmixed.js";
import "codemirror/mode/htmlembedded/htmlembedded.js";
import "codemirror/mode/javascript/javascript.js";
import "codemirror/mode/xml/xml.js";
import "codemirror/mode/css/css.js";
import "codemirror/addon/hint/javascript-hint.js";
import "codemirror/addon/lint/lint.js"
import "codemirror/addon/lint/html-lint.js";
import "codemirror/addon/lint/css-lint.js";
import "codemirror/addon/lint/javascript-lint.js";
import "codemirror/addon/fold/foldgutter.js";
import "codemirror/addon/fold/xml-fold.js";
import { Ruleset, Translation } from './HTMLHintService/HTMLHint/types';
import { HTMLHintCoreService } from './HTMLHintService/htmlhint-core.service';


@Injectable({
  providedIn: 'root'
})
export class HclCodeEditorLibService {
  editorInstance: CodeMirror.EditorFromTextArea | undefined

  constructor(private htmlHintCoreService: HTMLHintCoreService) { }

  buildEditorFromTextarea(host: HTMLTextAreaElement, options?: CodeMirror.EditorConfiguration | undefined): CodeMirror.EditorFromTextArea {
    this.editorInstance = CodeMirror.fromTextArea(host, options);
    return this.editorInstance;
  }

  setEditorValue(code: string) {
    this.editorInstance!.getDoc().setValue(code)
  }

  setCustomHTMLHinter(ruleSets: Ruleset, translations: Translation) {
    this.htmlHintCoreService.setErrorTranslations(translations);
    this.htmlHintCoreService.setHTMLRuleSets(ruleSets);
    CodeMirror.registerHelper("lint", "html", (text: any) => {
      const found: any[] = [];
      let message = null;
      var messages = this.htmlHintCoreService.verifyHTMLCode(text);
      for (var i = 0; i < messages.length; i++) {
        message = messages[i];
        var startLine = message.line - 1, endLine = message.line - 1, startCol = message.col - 1, endCol = message.col;
        found.push({
          from: CodeMirror.Pos(startLine, startCol),
          to: CodeMirror.Pos(endLine, endCol),
          message: message.message,
          severity: message.type
        });
      }
      return found;
    })
  }

  areErrorsPresent(htmlString) {
    const errorsWarnings = this.htmlHintCoreService.verifyHTMLCode(htmlString);
    let errorsPresent = errorsWarnings.filter((item) => {
      if (item.type === 'error') {
        return true;
      }
    });
    if (errorsPresent.length > 0) {
      return true;
    } else {
      return false;
    }
  }
}

