import { UnsubscribeManagePreferencesBlock, RenderingClass, IUnsubscribeManagePreferencesBlockOptions } from '../interfaces';
import { createPadding, createLineHeight, ignoreHTMLMinParse, createBorder, uniqueId } from '../utils';
import { createWidthHeight } from '../utils';

export class UnsubscribeManagePref implements UnsubscribeManagePreferencesBlock, RenderingClass {
  constructor(public isUnsubscribe: boolean = false,
    public isManagePreferences: boolean = false,
    public unsubscribeLabel: string,
    public managePreferencesLabel: string,
    public options: IUnsubscribeManagePreferencesBlockOptions
  ) {
  }

  render() {
    const { color, font, lineHeight, padding, background, border, fullWidth, align, url, newWindow, aliasNameInfo} = this.options;
    const borderStyle = (border.width) ? `border:${createBorder(border)};` : ``;
    const unsubscribeTemplate = `
    <mj-raw>
      <tr class="hide-on-${this.options.hideOn}">
        <td 
          align="${align}" 
          vertical-align="middle" 
          class="ip-unsubscribe-pref-block droppable unsubscribe-pref-droppable "  
          style="font-size:0px;padding:0px 0px 0px 0px;word-break:break-word;">
            <a 
              data-href="${url}"
              data-alias="${aliasNameInfo ?  aliasNameInfo.name : ''}"
              href="${url}"
              target="${newWindow?'_blank':'_self'}">
                <span  
                  style="display:${fullWidth ? 'block' : 'inline-block'};
                          ${borderStyle}
                          mso-padding-alt:0px;
                          background-color:${background.color};
                          color:${color};
                          font-family:${font.family}, ${font.fallback};
                          font-size:${font.size}px;
                          font-style:${font.style};
                          font-weight:${font.weight};
                          line-height:${createLineHeight(lineHeight)};
                          margin:0;
                          cursor:pointer;
                          text-decoration:underline;
                          text-transform:none;
                          padding:0px;
                          mso-padding-alt:0px;
                          border-radius:${border.radius}px;">${this.unsubscribeLabel}
                </span>
          </a>
        </td>
      </tr>
    </mj-raw>`;
    if (this.options.hideOn === 'desktop') {
      return `
        <mj-raw>
        <!--[if !mso]><!-- --></mj-raw>
        ${unsubscribeTemplate}
        <mj-raw><!--<![endif]--></mj-raw>
      `;
    } else {
      return unsubscribeTemplate;
    }  
  }
}
