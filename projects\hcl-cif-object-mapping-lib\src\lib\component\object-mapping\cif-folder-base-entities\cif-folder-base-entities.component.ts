import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ButtonConf } from 'hcl-angular-widgets-lib';
import { CifFolderBaseEntitiesConf } from './cif-folder-base-entities-config';

@Component({
  selector: 'hcl-cif-folder-base-entities',
  templateUrl: './cif-folder-base-entities.component.html',
  styleUrls: ['./cif-folder-base-entities.component.scss']
})
export class CifFolderBaseEntitiesComponent implements OnInit {

  @Input() config: CifFolderBaseEntitiesConf;
  @Output() closeCifFbs: EventEmitter<any> = new EventEmitter<any>();
  @Output() selectedCifFbc: EventEmitter<any> = new EventEmitter<any>();


  folderPanelState: string;
  cancelButtonConfig: ButtonConf;
  selectButtonConfig: ButtonConf;
  selectedCategoryDetails: any;
  selectedFolderDatails: any;

  constructor() { }

  ngOnInit(): void {
    this.setConfiguration();
  }

  setConfiguration() {
    this.cancelButtonConfig = {
      name: 'cancel',
      value: this.config.translations.cancel,
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };

    this.selectButtonConfig = {
      name: 'select',
      value: this.config.translations.select,
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5,
      disabled: true
    };
  }

  updateFolderData(folderData) {
    this.selectedFolderDatails = folderData;
  }

  updateCategory(category: any) {
    this.selectedCategoryDetails = category;
    this.selectButtonConfig.disabled = category ? false : true;
  }

  updatefolderState(state: string) {
    this.folderPanelState = state;
  }

  cancleSelection() {
    this.closeCifFbs.emit();
  }

  selectCategory() {
    this.selectedCifFbc.emit(this.selectedCategoryDetails);
  }

}
