import { DatePipe } from '@angular/common';
import { Component, EventEmitter, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { DataGridConf, DataGridV2Component } from 'hcl-data-grid-lib';
import { ButtonConf, TabsConfig } from 'hcl-angular-widgets-lib';
import { OfferDataService } from '../../offer-data.service';
import { SelectOffersService } from '../../select-offers.service';

@Component({
  selector: 'hcl-offer-selection',
  templateUrl: './offer-selection.component.html',
  styleUrls: ['./offer-selection.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class OfferSelectionComponent implements OnInit {

  @Output() cancel = new EventEmitter();

  @ViewChild('offersGrid', { static: true }) offersGrid: DataGridV2Component;

  cancelActionsConf: ButtonConf;
  selectVariantButtonConf: ButtonConf;
  checkboxConfigMap = new Map<number, any>();
  removedOffersSet = new Set<number>();
  customizedGridConfig: DataGridConf;
  olGridConfig: DataGridConf;
  manageSelectionGridApi: any;
  olManageSelectionGridApi: any;
  variantMap = new Map<number, any>();
  tabHorizontalConfig: TabsConfig;
  tabIndex = 0;

  constructor(
    private offerDataService: OfferDataService,
    private translate: TranslateService,
    private datePipe: DatePipe,
    public selectOffersService: SelectOffersService
  ) { }

  ngOnInit() {
    this.selectOffersService.selectedOffersData.forEach(offerData => {
      if (offerData.hasOwnProperty('variantId') && offerData.variantId) {
        this.variantMap.set(+offerData.offerId, offerData);
      }
    });
    this.setConfiguration();
    if (!this.selectOffersService.selectedOffersData.length && !this.selectOffersService.selectedOlData.length) {
      this.selectVariantButtonConf.disabled = true;
    }
  }

  setConfiguration() {
    this.createCheckboxConfig();
    this.tabHorizontalConfig = {
      selectedTab: 0,
      elements: []
    };

    if (this.selectOffersService.loadOfferOrOfferLists === 'both') {
      this.tabHorizontalConfig.elements.push({
        header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS'),
        templateName: 'offers'
      });
      this.tabHorizontalConfig.elements.push({
        header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS'),
        templateName: 'offerLists'
      });
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offerLists') {
      this.tabHorizontalConfig.elements.push({
        header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS_LISTS'),
        templateName: 'offerLists'
      });
    } else if (this.selectOffersService.loadOfferOrOfferLists === 'offers') {
      this.tabHorizontalConfig.elements.push({
        header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS'),
        templateName: 'offers'
      });
    }

    this.customizedGridConfig = {
      scrollHeight: 330,
      isClientSideRowModel: true,
      columns: [
        {
          field: 'offerDisplayName',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.OFFER_NAME'),
          colId: 'offerDisplayName',
          minWidth: 150,
          autoResizeToFit: true,
          rendererTemplateName: 'displayName',
          tooltip: {
            getTooltip: (attr: any) => {
              return attr.displayName;
            }
          }, cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.state !== 'PUBLISHED';
            }
          }
        },
        {
          field: 'offerCode',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.OFFER_CODE'),
          colId: 'offerCode',
          minWidth: 150,
          useDefaultRenderer: true,
          autoResizeToFit: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return attr.internalName;
            }
          },
          dataFormatter: (attr: any) => {
            return attr.data.internalName;
          }, cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.state !== 'PUBLISHED';
            }
          }
        },
        {
          field: 'variantAttributes',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.CHANNELS'),
          colId: 'variantAttributes',
          useDefaultRenderer: true,
          minWidth: 150,
          autoResizeToFit: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return this.variantChannel(this.variantMap.get(+attr.offerId));
            }
          },
          dataFormatter: (attr: any) => {
            return this.variantChannel(this.variantMap.get(+attr.data.offerId));
          }, cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.state !== 'PUBLISHED';
            }
          }
        },
        {
          field: 'state',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.STATUS'),
          colId: 'state',
          minWidth: 150,
          autoResizeToFit: true,
          useDefaultRenderer: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return this.returnOfferState(attr.state);
            }
          },
          dataFormatter: (attr: any) => {
            return this.returnOfferState(attr.data.state);
          }, cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.state !== 'PUBLISHED';
            }
          }
        },
        {
          field: 'variantAttributes',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.EFF_EXP_DATES'),
          colId: 'variantAttributes',
          minWidth: 150,
          useDefaultRenderer: true,
          autoResizeToFit: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return this.returnVariantEffAndExpDate(attr);
            }
          },
          dataFormatter: (attr: any) => {
            return this.returnVariantEffAndExpDate(attr.data);
          }, cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.state !== 'PUBLISHED';
            }
          }
        }
      ],
      data: [],
      rowSelectMode: this.selectOffersService.rowSelectMode,
      suppressRowClickSelection: true,
      suppressDragLeaveHidesColumns: true,
      noRowsTemplate: this.translate.instant('MESSAGES.NO_DATA_ON_HTTP_RESPONSE'),
      loadingTemplate: this.translate.instant('MESSAGES.LOADING'),
      dragAndDrop: true
    };
    this.olGridConfig = {
      scrollHeight: 330,
      isClientSideRowModel: true,
      columns: [
        {
          field: 'offerListDisplayName',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.OFFER_LIST_NAME'),
          colId: 'offerListDisplayName',
          minWidth: 150,
          autoResizeToFit: true,
          rendererTemplateName: 'displayName',
          tooltip: {
            getTooltip: (attr: any) => {
              return attr.offerListDisplayName;
            }
          }, cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.state !== 'PUBLISHED';
            }
          }
        },
        {
          field: 'type',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.OFFER_LIST_TYPE'),
          colId: 'type',
          minWidth: 150,
          useDefaultRenderer: true,
          autoResizeToFit: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return this.returnOfferListType(attr.type);
            }
          },
          dataFormatter: (attr: any) => {
            return this.returnOfferListType(attr.data.type);
          }, cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.state !== 'PUBLISHED';
            }
          }
        },
        {
          field: 'description',
          header: this.translate.instant('CREATE_OFFER.LABELS.DESCRIPTION'),
          colId: 'description',
          useDefaultRenderer: true,
          minWidth: 150,
          autoResizeToFit: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return attr.description;
            }
          },
          dataFormatter: (attr: any) => {
            return attr.data.description;
          }, cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.state !== 'PUBLISHED';
            }
          }
        },
        {
          field: 'state',
          header: this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.STATUS'),
          colId: 'state',
          minWidth: 150,
          autoResizeToFit: true,
          useDefaultRenderer: true,
          tooltip: {
            getTooltip: (attr: any) => {
              return this.returnOfferState(attr.state);
            }
          },
          dataFormatter: (attr: any) => {
            return this.returnOfferState(attr.data.state);
          }, cellClassRules: {
            ['retire-row-text-color']: function (node) {
              return node.data && node.data.state !== 'PUBLISHED';
            }
          }
        }
      ],
      data: [],
      rowSelectMode: this.selectOffersService.rowSelectMode,
      suppressRowClickSelection: true,
      suppressDragLeaveHidesColumns: true,
      noRowsTemplate: this.translate.instant('MESSAGES.NO_DATA_ON_HTTP_RESPONSE'),
      loadingTemplate: this.translate.instant('MESSAGES.LOADING'),
      dragAndDrop: true
    };
    this.cancelActionsConf = {
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      value: this.translate.instant('BUTTONS.CANCEL'),
      styleClass: 'medium-btn',
      name: 'cancel',
      borderRadius: 4
    };
    this.selectVariantButtonConf = {
      color: 'accent',
      buttonType: 'raised',
      type: 'button',
      value: this.translate.instant('BUTTONS.DONE'),
      styleClass: 'medium-btn',
      name: 'done',
      borderRadius: 4
    };
  }

  returnVariantEffAndExpDate(offerData) {
    let date = '- / -';
    if (offerData && offerData.variantAttributes && offerData.variantAttributes.length) {
      offerData.variantAttributes.some((attribute) => {
        if (attribute.type.id === 200) {
          date = this.extractDates(attribute.value);
          return;
        }
      });
    } else if (offerData && offerData.offerAttributes && offerData.offerAttributes.length) {
      offerData.offerAttributes.some((attribute) => {
        if (attribute.type.id === 200) {
          date = this.extractDates(attribute.value);
          return;
        }
      });
    }
    return date;
  }

  extractDates(value) {
    let str = '';
    if (value.effectiveDate) {
      const date = new Date(value.effectiveDate);
      str += this.datePipe.transform(date, this.offerDataService.getDatePipeFormatLocale()) + '-';
    } else if (value.isFlowChartRunDate) {
      str += this.translate.instant('CREATE_OFFER_TEMPLATE.LABELS.FLOWCHART_RUN_DATE') + '-';
    } else {
      str += '- /';
    }
    if (value.expirationDate) {
      const date = new Date(value.expirationDate);
      str += this.datePipe.transform(date, this.offerDataService.getDatePipeFormatLocale());
    } else if (value.expirationDuration) {
      str += value.expirationDuration + ' ' + this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.LABELS.DAYS_LATER');
    } else {
      str += '-';
    }
    return str;
  }

  createCheckboxConfig() {
    this.selectOffersService.selectedOffersData.forEach((offer) => {
      const config = {
        type: 'single',
        formControl: new UntypedFormControl(true),
        singleCheckboxData: {
          value: 'test',
          disabled: false,
          checked: true,
          indeterminate: true,
          label: '',
          disableRipple: true,
          labelPosition: 'before',
          color: 'primary'
        }
      };
      this.checkboxConfigMap.set(+offer.offerId, config);
    });
  }

  verticalLocale() {
    const userLanguage = this.offerDataService.userConfig.locale;
    return userLanguage === 'zh_TW' || userLanguage === 'zh_CN' || userLanguage === 'ja_JP' || userLanguage === 'ko_KR';
  }

  loadOffersListing(event) {
    this.cancel.emit(event);
  }

  onSelection(event, offer) {
    if (event.checked) {
      this.removedOffersSet.delete(+offer.offerId);
    } else {
      this.removedOffersSet.add(+offer.offerId);
    }

  }

  selectedOffersAndOlsNo() {
    return ` (${+this.selectOffersService.selectedOffersData.length + +this.selectOffersService.selectedOlData.length})`;
  }

  updateOfferSelection(event) {
    this.removedOffersSet.forEach(value => {
      this.selectOffersService.selectedOffersData.some((item, index) => {
        if (+value === +item.offerId) {
          this.selectOffersService.selectedOffersData.splice(index, 1);
          return;
        }
      });
    });
    this.cancel.emit(event);
  }

  rowSelected(data) {
    if (data.data && !this.selectOffersService.selectedOffersData.some(offer => +offer.offerId === +data.data.offerId)) {
      let offerData;

      if (this.variantMap.has(+data.data.offerId)) {
        offerData = {
          offerId: data.data.offerId,
          offerDisplayName: data.data.offerDisplayName,
          variantId: this.variantMap.get(+data.data.offerId).variantId,
          variantDisplayName: this.variantMap.get(+data.data.offerId).variantDisplayName,
          offerCode: data.data.offerCode,
          variantAttributes: this.variantMap.get(+data.data.offerId).variantAttributes,
          offerAttributes: null,
          state: data.data.state
        };
      } else {
        offerData = {
          offerId: data.data.offerId,
          offerDisplayName: data.data.offerDisplayName,
          variantId: null,
          variantDisplayName: null,
          offerCode: data.data.offerCode,
          variantAttributes: null,
          offerAttributes: this.returnVariantEffAndExpDate(data.data),
          state: data.data.state
        };
      }


      if (this.selectOffersService.selectedOffersData.length) {
        let hasOffer = false;
        hasOffer = this.selectOffersService.selectedOffersData.some((selectedData, index) => {
          if (+selectedData.offerId === +data.data.id) {
            return true;
          }
        });
        if (!hasOffer) {
          this.selectOffersService.selectedOffersData.push(offerData);
        }
      } else {
        this.selectOffersService.selectedOffersData.push(offerData);
      }
    }
  }

  returnOfferState(state) {
    if (state === 'RETIRED') {
      return this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.RETIRED');
    } else if (state === 'PUBLISHED') {
      return this.translate.instant('TITLES.PUBLISHED');
    } else if (state === 'DRAFT') {
      return this.translate.instant('TITLES.DRAFT');
    }
  }

  rowUnSelected(data) {
    if (data.data) {
      this.selectOffersService.selectedOffersData.some((selectedData, index) => {
        if (+selectedData.offerId === +data.data.offerId) {
          this.selectOffersService.selectedOffersData.splice(index, 1);
          return;
        }
      });
    }
  }

  onCellClicked(cell) {
    if (cell.row && cell.row.offerId && cell.row.state === 'PUBLISHED') {
      if (this.variantMap.has(+cell.row.offerId)) {
        this.selectOffersService.selectedVariantId = this.variantMap.get(+cell.row.offerId).variantId;
      } else {
        this.selectOffersService.selectedVariantId = null;
      }
      this.selectOffersService.viewRoute = 'manageOffers';
      this.selectOffersService.sendViewOfferClicked(+cell.row.offerId);
    }
  }

  manageSelectionGridReady(data) {
    this.manageSelectionGridApi = data.params.api;

    this.selectOffersService.selectedOffersData.forEach(element => {
      this.customizedGridConfig.data.push(element);
    });
    this.manageSelectionGridApi.setRowData(this.customizedGridConfig.data);
    setTimeout(() => {
      this.manageSelectionGridApi.forEachNode((node) => {
        node.setSelected(true);
      });
    }, 300);
  }

  variantChannel(offerData) {
    let channel = '';
    if (offerData && offerData.variantAttributes && offerData.variantAttributes.length) {
      offerData.variantAttributes.some((attribute) => {
        if (attribute.id === 10) {
          channel = attribute.value;
          return;
        }
      });
    } else if (offerData && offerData.offerAttributes && offerData.offerAttributes.length) {
      offerData.variantAttributes.some((attribute) => {
        if (attribute.id === 10) {
          channel = attribute.value;
          return;
        }
      });
    }
    return channel;
  }



  returnOfferListType(data) {
    if (data === 'STATIC') {
      return this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.STATIC');
    } else {
      return this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.SMART');
    }
  }

  olManageSelectionGridReady(data) {
    this.olManageSelectionGridApi = data.params.api;
    this.olGridConfig.data = [];
    this.selectOffersService.selectedOlData.forEach(element => {
      this.olGridConfig.data.push(element);
    });
    this.olManageSelectionGridApi.setRowData(this.olGridConfig.data);
    setTimeout(() => {
      this.olManageSelectionGridApi.forEachNode((node) => {
        node.setSelected(true);
      });
    }, 300);
  }


  olRowSelected(data) {
    if (data.data && !this.selectOffersService.selectedOlData.some(offer => +offer.offerListId === +data.data.offerListId)) {
      let offerListData;

      offerListData = {
        offerListId: data.data.offerListId,
        offerListDisplayName: data.data.offerListDisplayName,
        type: data.data.type.id,
        state: data.data.state
      };


      if (this.selectOffersService.selectedOlData.length) {
        let hasOfferList = false;
        hasOfferList = this.selectOffersService.selectedOlData.some(selectedData => {
          if (+selectedData.offerListId === +data.data.offerListId) {
            return true;
          }
        });
        if (!hasOfferList) {
          this.selectOffersService.selectedOlData.push(offerListData);
        }
      } else {
        this.selectOffersService.selectedOlData.push(offerListData);
      }
    }
  }

  olRowUnSelected(data) {
    if (data.data) {
      this.selectOffersService.selectedOlData.some((selectedData, index) => {
        if (+selectedData.offerListId === +data.data.offerListId) {
          this.selectOffersService.selectedOlData.splice(index, 1);
          return;
        }
      });
    }
  }

  onOlCellClicked(cell) {
    if (cell.row && cell.row.offerListId) {
      this.selectOffersService.viewRoute = 'manageOfferLists';
      this.selectOffersService.offerListId = +cell.row.offerListId;
      this.selectOffersService.sendViewOfferListClicked(+cell.row.offerListId);
    }
  }

  tabChange(event) {
    this.tabIndex = event.index;
	this.selectOffersService.lastTabIndex = this.tabIndex;
  }
}
