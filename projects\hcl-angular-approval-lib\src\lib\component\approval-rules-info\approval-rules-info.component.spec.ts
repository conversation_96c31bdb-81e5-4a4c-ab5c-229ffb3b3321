import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ApprovalRulesInfoComponent } from './approval-rules-info.component';

describe('ApprovalRulesInfoComponent', () => {
  let component: ApprovalRulesInfoComponent;
  let fixture: ComponentFixture<ApprovalRulesInfoComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ApprovalRulesInfoComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ApprovalRulesInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
