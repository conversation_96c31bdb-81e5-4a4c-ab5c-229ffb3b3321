import { Component, Input, OnInit, ViewChild, ChangeDetectorRef, Output, EventEmitter, ViewEncapsulation } from '@angular/core';
import { DataGridV2Component } from '../data-grid-v2.component';
import { UntypedFormControl } from '@angular/forms';
import { ButtonConf, CheckboxComponent, InputConfig, MenuComponent, MenuConfig } from 'hcl-angular-widgets-lib';
import * as _ from 'lodash';
import { ColumnSelectionConf } from './data-grid-v2-config';

@Component({
  selector: 'hcl-data-grid-column-selection',
  templateUrl: './column-selection.component.html',
  styleUrls: ['./column-selection.component.scss'],
  encapsulation: ViewEncapsulation.None
})
/**
 * this component is specifically targeted to display the column selection menu for the data grid
 */
export class ColumnSelectionComponent implements OnInit {
  /**
   * The instance of the menu
   */
  @ViewChild('menu') menuInstance: MenuComponent;
  @ViewChild('addRemoveCol') addRemoveCol: MenuComponent;
  @ViewChild('chckbx') chckbx: CheckboxComponent;
  /**
   * The menu configuration that will render the column selection menu
   * type {{style: {color: string; 'font-size': string; 'min-width': string}; items: {label: string; icon: string}[]}}
   */
  menuConfig: MenuConfig = {
    style: { 'color': 'red', 'font-size': '17px', 'min-width': '250px' },
    items: []
  };

  @Input() config: ColumnSelectionConf;
  @Output() applyBtnClick = new EventEmitter();
  @Output() resetBtnClick = new EventEmitter();

  /** for add remove columns */

  userColumnConfig = {
    checkboxData: [],
    type: 'multiple',
    vertical: true
  };
  selectedList: any;
  defaultList: any;
  defaultColLabel: string;

  searchColumnsArr = [];

  searchBoxConfig: InputConfig = {
    name: 'gridcolumnsearch',
    formControlName: new UntypedFormControl(''),
    type: 'text',
    maximumLength: 512,
    isShowLimit: false,
    autofocus: true,
    placeholder: '',
  }

  resetToDefBtnConfig: ButtonConf = {
    value: 'Reset Default',
    buttonType: 'stroked',
    color: 'accent',
    borderRadius: 4,
    name: 'cancelStrategyBtn',
    styleClass: 'cancel-modal'
  };
  /** Apply button config */
  applyBtnConfig: ButtonConf = {
    value: 'Apply',
    buttonType: 'flat',
    color: 'accent',
    borderRadius: 5,
    name: 'basicButton',
    type: 'button'
  };
  /** Add/Remove column button config */
  addRemoveColBtn: ButtonConf = {
    value: 'Add/Remove Column',
    buttonType: 'mat',
    color: 'accent',
    borderRadius: 5,
    name: 'basicButton',
    type: 'button',
    isIconButton: true,
    icon: 'hcl-icon-angle-down'
  };


  /**
   * the data-grid component on which this menu will be added
   */
  // tslint:disable-next-line: no-input-rename
  @Input('data-grid') dataGrid: DataGridV2Component;

  // variable to hold list of custom default columns
  customDefaultColumnList = [];

  /**
   * This function will open the column selection menu
   * private
   */
  _openMenu(): void {
    // tslint:disable-next-line: deprecation
    this.menuInstance.openMenu(event);
  }

  /**
   * The default constructor
   */
  constructor(private cd: ChangeDetectorRef) {
  }
  /**
   * At the time to init we have to listen to the grid rendering complete
   */
  ngOnInit() {
    setTimeout(() => {
      this.dataGrid.gridReady.subscribe(this.init.bind(this));
    });
  }

  /**
   * We initialize the menu here
   */
  private init() {
    /* // lets get all the columns from the dataGrid
    const columns: DataGridColumnConf[] = this.dataGrid.getAllColumns();
    // Iterate & chk which are hidden columns
    columns.forEach((column: DataGridColumnConf) => {
      // Only possible if we have a colId
      if (column.colId) {
        // check the visibility & add it to the menu
        if (column.isHidden === undefined) {
          column.isHidden = false;
        }
        this.menuConfig.items.push({
          label: column.header,
          isHidden: column.isHidden,
          data: column,
          icon: (!column.isHidden ? 'hcl-checkbox-selected' : 'hcl-checkbox-deselct'),
          command: (event, item) => {
            this.menuItemSelected(event, item);
          }
        });
      }
    }) */
    let colConfigTmpObj: any;
    colConfigTmpObj = this.prepareCheckBoxData();
    this.selectedList = colConfigTmpObj.defaultSelectedList;
    this.userColumnConfig.checkboxData = colConfigTmpObj.defaultColState;
    this.defaultList = JSON.parse(JSON.stringify(colConfigTmpObj.defaultAllList));
    this.searchColumnsArr = JSON.parse(JSON.stringify(this.userColumnConfig.checkboxData));

    if (this.config) {
      this.resetToDefBtnConfig.value = this.config.resetBtnLabel ? this.config.resetBtnLabel : 'Reset Default';
      this.applyBtnConfig.value = this.config.applyBtnLabel ? this.config.applyBtnLabel : 'Apply';
      this.addRemoveColBtn.value = this.config.addRemoveLabel ? this.config.addRemoveLabel : 'Add/Remove Column';
      this.defaultColLabel = this.config.defaultColLabel ? this.config.defaultColLabel : 'default column';
      this.defaultList = this.config.defaultList;
      this.searchBoxConfig.placeholder = this.config.searchLabel || 'Search';
    }

    this.cd.detectChanges();
  }
  /**
   * Called when the menu item is clicked
   */
  private menuItemSelected(event: any, item: any): void {
    item.isHidden ? this.dataGrid.showColumn(item.data.colId) : this.dataGrid.hideColumn(item.data.colId);
    item.icon = (item.icon === 'hcl-checkbox-selected') ? 'hcl-checkbox-deselct' : 'hcl-checkbox-selected';
    item.isHidden = !item.isHidden;
  }

  /**
  * for add/remove column on strategy
  */
  openAddColModal(event: any) {
    const colConfigTmpObj = this.prepareCheckBoxData();
    this.userColumnConfig.checkboxData = colConfigTmpObj.defaultColState;
    this.searchColumnsArr = JSON.parse(JSON.stringify(this.userColumnConfig.checkboxData));
    this.selectedList = colConfigTmpObj.defaultSelectedList;
    this.chckbx.selectedList = this.selectedList;
    this.addRemoveCol.openMenu(event);
  }

  /**
  * preparing data for current state of checkboxes on click of the add remove cols btn and returing the data.
  */
  prepareCheckBoxData() {
    const tmpArr = [];
    const tmpSelectedArr = [];
    const tempDefaultArr = [];
    this.dataGrid.config.columns.forEach((obj) => {
      const tmpObj = {
        label: obj.header,
        value: obj.colId,
        name: 'colConfig',
        disabled: obj.disable,
        checked: !obj.isHidden
      };
      if (tmpObj.disabled) {
        tmpObj.label = tmpObj.label + ' (' + this.defaultColLabel + ')';
      }
      tmpArr.push(tmpObj);
      if (tmpObj.checked) {
        tmpSelectedArr.push({ key: tmpObj.value, data: tmpObj });
      }
      tempDefaultArr.push({ key: tmpObj.value, data: tmpObj });
    });
    return {
      defaultColState: tmpArr,
      defaultSelectedList: tmpSelectedArr,
      defaultAllList: tempDefaultArr
    };
  }

  public setDefaultList(list: string[]) {
    if (this.dataGrid.params) {
      this.setDefaultCB(list);
    } else {
      this.dataGrid.gridReady.subscribe(this.setDefaultCB.bind(this, list));
    }
  }

  public setCustomDefaultColumns(columnList) {
    this.customDefaultColumnList = columnList;
  }

  private setDefaultCB(list) {
    this.defaultList = [];
    this.selectedList = [];
    this.dataGrid.config.columns.forEach((col) => {
      const index = list.indexOf(col.colId);
      const tmpObj = {
        label: col.header,
        value: col.colId,
        name: 'colConfig',
        disabled: col.disable,
        checked: index > -1 ? true : false
      };
      if (tmpObj.disabled) {
        tmpObj.label = tmpObj.label + ' (' + this.defaultColLabel + ')';
      }
      this.defaultList.push({ key: tmpObj.value, data: tmpObj });
      if (index > -1) {
        this.selectedList.push({ key: tmpObj.value, data: tmpObj });
        this.dataGrid.showColumn(col.colId);
      } else {
        this.dataGrid.hideColumn(col.colId);
      }
    });
    const temp1 = this.defaultList.map((e) => {
      return e.data;
    });
    this.userColumnConfig.checkboxData = JSON.parse(JSON.stringify(temp1));
    this.searchColumnsArr = JSON.parse(JSON.stringify(this.userColumnConfig.checkboxData));
  }

  /**
   * user selected col list.
   */
  userPrefferedCols(list: any) {
    this.selectedList = [...list];
  }

  colConfigApplyFn(event) {
    this.showHideColumns('apply', event);
    const temp = this.selectedList.map((e) => {
      return e.data.value;
    });
    this.applyBtnClick.emit(temp);
  }

  showHideColumns(type, event) {
    this.dataGrid.config.columns.forEach((col) => {
      const ele = _.find(this.selectedList, { key: col.colId });
      if ((type === 'apply' && ele) || (type === 'reset' && ele && ele.data && ele.data.checked)) {
        this.dataGrid.showColumn(col.colId);
      } else {
        this.dataGrid.hideColumn(col.colId);
      }
    });
    this.addRemoveCol.closeMenu(event);
  }

  resetToDefault(event) {
    this.resetBtnClick.emit();
    let temp1;
    if (this.customDefaultColumnList.length) {
      const temp = this.defaultList.filter((column) => this.customDefaultColumnList.indexOf(column.key) > -1);
      this.selectedList = JSON.parse(JSON.stringify(temp));
      temp1 = this.defaultList.map((e) => {
        e.data.checked = false;
        this.selectedList.forEach(element => {
          if (e.key === element.key) {
            e.data.checked = true;
            element.data.checked = true;
          }
        });
        return e.data;
      });
    } else {
      const temp = this.defaultList.filter((e) => {
        return e && e.data && e.data.checked;
      });
      this.selectedList = JSON.parse(JSON.stringify(temp));
      temp1 = this.defaultList.map((e) => {
        return e.data;
      });
    }
    this.userColumnConfig.checkboxData = JSON.parse(JSON.stringify(temp1));
    this.searchColumnsArr = JSON.parse(JSON.stringify(this.userColumnConfig.checkboxData));
    this.showHideColumns('reset', event);
  }

  searchColumn(searchTxt) {
    let searchedCol = [];
    for(let searchCol of this.searchColumnsArr){
      let filtererdVal = this.selectedList.filter(ele => ele.key === searchCol.value);
      if(filtererdVal?.length){
        searchedCol.push(filtererdVal?.[0]?.data);
      } else {
        searchCol.checked = false;
        searchedCol.push(searchCol);
      }
    }
    this.userColumnConfig.checkboxData = JSON.parse(JSON.stringify(searchedCol));
    if (searchTxt) {
      this.userColumnConfig.checkboxData = this.userColumnConfig.checkboxData.filter(ele => {
        let name = ele?.value?.toLowerCase(),
          searchItem = searchTxt.toLowerCase();
        if (name.includes(searchItem)) {
          return ele;
        }
      })
    }
  }
}
