import { Component, OnInit } from "@angular/core";
import {
  Actions,
  DataGridConf,
  DataGridPagination,
  HoverIcon,
} from "projects/hcl-data-grid-community/src/lib/data-grid.conf";

@Component({
  selector: "app-data-grid-community-demo",
  templateUrl: "./data-grid-community-demo.component.html",
  styleUrls: ["./data-grid-community-demo.component.scss"],
})
export class DataGridCommunityDemoComponent implements OnInit {
  actions?: Actions;
  hoverActions: HoverIcon[];
  paginatorConfig: DataGridPagination;
  conf: DataGridConf;

  constructor() {}

  ngOnInit(): void {
    this.paginatorConfig = {
      rowsPerPage: 10,
      pageSizeArray: [10, 50, 100],
      currentPageIndex: 1,
      rowsPerPageSuffix: "Files",
    };
    this.hoverActions = [
      {
        tooltip: "Retire",
        icon: "hcl-icon-status-off",
        name: "retire",
        iconClickHandler: this.retireAction,
      },
      {
        tooltip: "Delete",
        icon: "hcl-icon-delete",
        name: "delete",
        iconClickHandler: this.deleteAction,
      },
      {
        tooltip: "Edit",
        icon: "hcl-icon-edit",
        name: "edit",
        iconClickHandler: this.editAction,
      },
    ];
    this.actions = {
      hoverIcons: this.hoverActions,
    };

    this.conf = {
      scrollHeight: 400,
      isClientSideRowModel: true,
      data: [],
      suppressRowTransform: true,
      columns: [
        {
          field: "name",
          header: "Name",
          colId: "name",
          disable: true,
          headerRendererTemplateName: "dadsa",
        },
        {
          field: "description",
          header: "Description",
          colId: "description",
          headerRendererTemplateName: "dadsa",
          popoverTemplateName: "dasds",
          rendererTemplateName: "entrySourceCell",
          sortable: true,
        },
        {
          field: "isAssociated",
          header: "In Use",
          colId: "isAssociated",
          headerRendererTemplateName: "dadsa",
          rendererTemplateName: "inUseCell",
          popoverTemplateName: "customPopover",
        },
        {
          field: "createdDate",
          header: "Created On",
          colId: "createdDate",
          headerRendererTemplateName: "dadsa",
          rendererTemplateName: "createdDateCell",
          popoverTemplateName: "dasds"
        },
        {
          field: "lastModifiedDate",
          header: "Last Modified By",
          colId: "lastModifiedDate",
          headerRendererTemplateName: "dadsa",
          rendererTemplateName: "lastModifiedByCell",
          popoverTemplateName: "dasds",
          autoResizeToFit: true,
          width: 300
        },
      ],
      dataUrl: "assets/data/cars.json",
      queryParams: {
        sort: "name,ASC",
        test: "123",
        defaultSort: false,
        date: new Date(),
        id: 2,
      },
      rowSelectMode: "multiple",
      pagination: this.paginatorConfig,
      noRowsTemplate: "<span> No data to display</span>",
      actions: this.actions,
    };
  }
  editAction(editedObj: any) {
    console.log("edited->", editedObj);
  }

  parseDate(date: number) {
    return new Date();
  }

  retireAction(retiredObj: any) {
    console.log("retired->", retiredObj);
  }

  deleteAction(deletedObj: any) {
    console.log("delete->", deletedObj);
  }

  rowSelected(data: any) {
    const currentlySelectedRows = data.gridApi.getSelectedNodes();
  }
  /**
   * Gets the row which is unselected in current unselection,
   * and also gets all the rows which are still in selected
   **/
  rowUnSelected(data: any) {
    const currentlySelectedRows = data.gridApi.getSelectedNodes();
  }

  columnSorted(column) {
    console.log(column);
  }

  gridReady(data) {
    console.log(data);
  }

  onRowClicked() {
    console.log("row CLicked");
  }
}
