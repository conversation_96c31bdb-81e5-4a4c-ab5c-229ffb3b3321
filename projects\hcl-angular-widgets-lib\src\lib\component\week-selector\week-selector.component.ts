import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { weekConfig } from './week-selector.config';

@Component({
  selector: 'hcl-week-selector',
  templateUrl: './week-selector.component.html',
  styleUrls: ['./week-selector.component.scss']
})
export class WeekSelectorComponent implements OnInit {

  @Input() config:weekConfig;
  @Output() daySelected: EventEmitter<any> = new EventEmitter<any>();
  communicationListDays;
  
  constructor() {
   }

  ngOnInit(): void {
    this.communicationListDays = this.config.weekdays;
    this.config.minDays = this.config.minDays || 1;
    this.sendSelectedDays();
  }
  
  // select/ deselect communication days.
  selectCD(item:any){
      if(!item.disable){
      const selectedDayCount = this.communicationListDays.filter(x=> x.selected == true).length;
      let index = this.communicationListDays.findIndex(Ditem => Ditem === item);
      if(selectedDayCount>this.config.minDays){
        this.communicationListDays[index].selected = !item.selected;
      } else{
        this.communicationListDays[index].selected = true;
      }
      }
      this.sendSelectedDays();
  }
 // send selected data as output.
  sendSelectedDays(){
    const selectedDays = this.communicationListDays.filter(x=> x.selected == true).map(y => y.dayId);
     this.daySelected.emit(selectedDays);
  }

}
