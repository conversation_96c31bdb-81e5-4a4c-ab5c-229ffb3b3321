import { AfterViewInit, Component, Input, OnInit } from '@angular/core';
import * as echarts from 'echarts';
import { ApacheColumnChartConfig, ApacheDonutChartConfig, ApacheFunnelChartConfig, ApacheLineChartConfig } from './apache-chart';

@Component({
  selector: "hcl-apache-chart",
  templateUrl: "./apache-chart.component.html",
  styleUrls: ["./apache-chart.component.scss"],
})
export class ApacheChartComponent implements OnInit, AfterViewInit {
  @Input() apacheChartConfig: ApacheColumnChartConfig | ApacheLineChartConfig | ApacheDonutChartConfig | ApacheFunnelChartConfig;
  apacheChart: any;
  dynamicId: string;

  constructor() {}

  ngOnInit(): void {
    this.dynamicId = `apache_chart_${this.apacheChartConfig.title["id"]}`;
  }

  ngAfterViewInit() {
    this.apacheChart = echarts.init(
      document.getElementById(this.dynamicId),
      null,
      { renderer: "svg" }
    );
    this.updateChart();
    this.showTooltipOnXaxis();
  }

  public updateChart(): void {
    this.apacheChart.setOption({ ...this.apacheChartConfig });
  }

  public resizeChart(): void {
      window.addEventListener('resize', () => {
        this.apacheChart.resize();
      });
  }

  public generateImageUrl() {
    return this.apacheChart.getDataURL({
      type: "svg",
      pixelRatio: 1,
      backgroundColor: "#fff",
      excludeComponents: ["toolbox","dataZoom"]
    });
  }

  public showTooltipOnXaxis() {
    const axisTooltipDOM = document.createElement('div');
    const axisTooltipContent = document.createElement('div');
    axisTooltipDOM.appendChild(axisTooltipContent);
    axisTooltipDOM.style.cssText =
      'position: absolute; visibility: hidden; max-width: 50%; background-color: #fff; color: #333; padding: 5px 15px; font-size: 14px;'+
      'border-radius: 2px; box-shadow: 0 0 2px #aaa; transition: transform ease .3s, visibility ease .3s; transform: scale(0); transform-origin: bottom;'
    const axisTooltipStyle = axisTooltipDOM.style;
    // append to ECharts container
    this.apacheChart.getDom().appendChild(axisTooltipDOM);

    this.apacheChart.on('mouseover', e => {
      if (e.targetType !== 'axisLabel') {
        return;
      }

      //It will show the full text of the x-axis and y-axis label if it is overflowed

      const currLabel = e.event.target;
      const fullText = e.value;
      const displayText = currLabel.style.text;
      if (fullText === displayText) {
        // not overflowed
        return;
      }
  
      // overflow, show full text
      axisTooltipContent.innerText = fullText;
  
      // Get chart dimensions
      const chartContainer = this.apacheChart.getDom();
      const chartWidth = chartContainer.clientWidth;
      const chartHeight = chartContainer.clientHeight;
  
      let leftPosition = currLabel.transform[4] - (axisTooltipDOM.offsetWidth / 2);
      let topPosition = currLabel.transform[5] - axisTooltipDOM.offsetHeight - 15;
  
      // Adjust if the tooltip goes outside the chart on the left
      if (leftPosition < 0) {
        leftPosition = 5; // Add some padding from the left edge
      }
  
      // Adjust if the tooltip goes outside the chart on the right
      if (leftPosition + axisTooltipDOM.offsetWidth > chartWidth) {
        leftPosition = chartWidth - axisTooltipDOM.offsetWidth - 5; // Add some padding from the right edge
      }
  
      // Adjust if the tooltip goes above the chart
      if (topPosition < 0) {
        topPosition = currLabel.transform[5] + 15; // Show below the label instead of above
      }
  
      // Set tooltip position
      axisTooltipStyle.left = leftPosition + 'px';
      axisTooltipStyle.top = topPosition + 'px';
      axisTooltipStyle.transform = '';
      axisTooltipStyle.visibility = 'visible';
    }).on('mouseout', e => {
      // hide tooltip
      axisTooltipStyle.visibility = 'hidden';
      axisTooltipStyle.transform = 'scale(0)';
    });
  }

  public adjustIntervalOnDataZoom() {
    // add datazoom scroll event on charts
    this.apacheChart.on('datazoom', e => {
      const xAxis = this.apacheChart.getOption().xAxis;
      const dataZoomStart = e.start;
      const dataZoomEnd = e.end;
      const dataZoomRange = dataZoomEnd - dataZoomStart;
      const xAxisData = xAxis[0].data;
      // const xAxisLength = xAxisData.length;
      // const xAxisInterval = Math.floor(xAxisLength / 5);
      // adjust xAxis label interval based on dataZoom range
      if ( dataZoomRange > 0 && dataZoomRange < 40) {
        xAxis[0].axisLabel.interval = 0;
      } else if (dataZoomRange > 40 && dataZoomRange < 60) {
        xAxis[0].axisLabel.interval = 1;
      } else if (dataZoomRange > 60 && dataZoomRange < 80) {
        xAxis[0].axisLabel.interval = 2;
      } else if (dataZoomRange > 80 && dataZoomRange < 100) {
        xAxis[0].axisLabel.interval = 3;
      }
      // else { xAxis[0].axisLabel.interval = xAxisInterval;}
      this.apacheChart.setOption({ xAxis });
    });
  }

  ngOnDestroy() {
    window.removeEventListener('resize', this.apacheChart.resize());
  }
}
