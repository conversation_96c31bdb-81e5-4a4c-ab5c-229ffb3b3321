import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CifOmAttrsSubscriptionComponent } from './cif-om-attrs-subscription.component';

describe('CifOmAttrsSubscriptionComponent', () => {
  let component: CifOmAttrsSubscriptionComponent;
  let fixture: ComponentFixture<CifOmAttrsSubscriptionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CifOmAttrsSubscriptionComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CifOmAttrsSubscriptionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
