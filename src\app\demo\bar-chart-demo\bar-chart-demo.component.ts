import { Component, OnInit } from '@angular/core';
import { BarChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/bar-chart/bar-chart';
import { LinearGaugeChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/linear-gauge-chart/linear-gauge-chart';


@Component({
  selector: 'app-bar-chart-demo',
  templateUrl: './bar-chart-demo.component.html',
  styleUrls: ['./bar-chart-demo.component.scss']
})
export class BarChartDemoComponent implements OnInit {

  constructor() { }

  barChartdata = [{yAxis: 1150, xAxis: 1, color: 5}, 
    {yAxis: 1250, xAxis: 2, color: 1}, 
    {yAxis: 2350, xAxis: 3, color: 5}, 
    {yAxis: 750, xAxis: 4, color: 2},
    {yAxis: 2450, xAxis: 5, color: 5},
    {yAxis: 1550, xAxis: 6, color: 3},
    {yAxis: 750, xAxis: 7, color: 5},
    {yAxis: 4650, xAxis: 8, color: 4},
    {yAxis: 770, xAxis: 9, color: 15},
    {yAxis: 1850, xAxis: 10, color: 10},
    {yAxis: 790, xAxis: 11, color: 12},
    {yAxis: 3150, xAxis: 12, color: 17},
    {yAxis: 720, xAxis: 13, color: 19},
    {yAxis: 1350, xAxis: 14, color: 20},
    {yAxis: 4450, xAxis: 15, color: 11},
    {yAxis: 5550, xAxis: 16, color: 2},
    {yAxis: 4650, xAxis: 17, color: 3},
    {yAxis: 4750, xAxis: 18, color: 1},
    {yAxis: 4150, xAxis: 19, color: 9},
    {yAxis: 4050, xAxis: 20, color: 22},
    {yAxis: 4250, xAxis: 21, color: 12},
    {yAxis: 3150, xAxis: 22, color: 21},
    {yAxis: 3950, xAxis: 23, color: 25},
    {yAxis: 3350, xAxis: 24, color: 25},
    {yAxis: 2550, xAxis: 25, color: 24},
    {yAxis: 2150, xAxis: 26, color: 23},
    {yAxis: 1050, xAxis: 27, color: 14},
    {yAxis: 1350, xAxis: 28, color: 13},
    {yAxis: 780, xAxis: 29, color: 11},
    {yAxis: 710, xAxis: 30, color: 10}]

  // barChartdata = []

  barChartConfig : BarChartConfig = {
    chartContainerId: `bar_chart1`,
    percentageColorStops: [{percentage: 0, label: "0%", color: "#245aa5"}, 
                          {percentage: 20, label: "5%", color: "#2b67bf"},
                          {percentage: 40, label: "10%", color: "#377ddb"},
                          {percentage: 60, label: "15%", color: "#6da7e2"},
                          {percentage: 80, label: "20%", color: "#a87a97"},
                          {percentage: 100, label: "25%", color: "#eb4b4b"}],
    // barColor: "red",
    height : 500,
    width: 1100,
    tooltipHTML: (data) => {
      return `<div class="bar-chart-tooltip-container">
                  <div class="bar-chart-tooltip-row">
                      <span class="bar-chart-tooltip-label">Average sent: </span>
                      <span class="bar-chart-tooltip-xAxis">${data.xAxis}</span>
                  </div>
                  <div class="bar-chart-tooltip-row">
                      <span class="bar-chart-tooltip-label">Domain count: </span>
                      <span class="bar-chart-tooltip-yAxis">${data.yAxis}</span>
                  </div>
                  <div class="bar-chart-tooltip-row">
                      <span class="bar-chart-tooltip-label">Unresponsive rate: </span>
                      <span class="bar-chart-tooltip-color">${data.color}%</span>
                  </div>
              </div>`;
    }
  }

  linearGuageChartConfig: LinearGaugeChartConfig = {
    chartContainerId: `linearGuage_chart1`,
    height : 100,
    width: 400,
    ticks: 5,
    percentageColorStops: [{percentage: 0, label: "0%", color: "#245aa5"}, 
                          {percentage: 20, label: "5%", color: "#2b67bf"},
                          {percentage: 40, label: "10%", color: "#377ddb"},
                          {percentage: 60, label: "15%", color: "#6da7e2"},
                          {percentage: 80, label: "20%", color: "#a87a97"},
                          {percentage: 100, label: "25%", color: "#eb4b4b"}],
    title: "Unresponsive color"
  }

  linearGuageChartData = [0,25];



  ngOnInit() {
    window.setTimeout(() => {
      this.barChartdata = this.barChartdata.map((data) => {
        return ({yAxis: (Math.ceil(data.yAxis * Math.random() * 10)), xAxis: data.xAxis, color: data.color})
      }).sort((first, second) =>  Number(first['xAxis']) - Number(second['xAxis'])
      )
    }, 3000)
  }

}