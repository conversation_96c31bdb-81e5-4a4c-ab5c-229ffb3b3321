import { AfterViewInit, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { HclSegmentationLibService } from '../../hcl-segmentation-lib.service';
import { SubscriptionLike } from 'rxjs';
import { ApacheChartComponent, ApacheColumnChartConfig } from 'hcl-angular-charts-lib';
import { HttpErrorResponse } from '@angular/common/http';
import { ButtonConf, InputConfig, ProgressSpinner } from 'hcl-angular-widgets-lib';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'hcl-data-profile-details',
  templateUrl: './data-profile-details.component.html',
  styleUrls: ['./data-profile-details.component.scss']
})
export class DataProfileDetailsComponent implements OnInit, AfterViewInit {
  @Input() config: any;
  @Output() errorMessage: EventEmitter<HttpErrorResponse> = new EventEmitter<HttpErrorResponse>();
  @ViewChild('barChart') barChart: ApacheChartComponent;

  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();

  distributionData: any[] = [];
  barChartConfig: ApacheColumnChartConfig;
  chartSliderColors: string[] = ['#808080', '#EAEAEA', '#ECECEC'];
  isGraphView: boolean = true;
  binCountInputConfig: InputConfig;
  applyButtonConfig: ButtonConf;
  binCountForm: UntypedFormGroup;
  defaultBinCount: number = 10;

  dataProfileDetailsSpinner: ProgressSpinner = {
    isInline: true,
    isLoading: true,
    color: 'primary',
    mode: 'indeterminate',
    value: 50,
    diameter: 50,
    strokeWidth: 3
  }

  constructor(
    private hclSegmentationLibService: HclSegmentationLibService
  ) { }

  ngOnInit(): void {
    this.constructForm();
    this.setConfiguration();
  }

  ngAfterViewInit(): void {
    this.getDistributionData();
  }

  constructForm(): void {
    this.binCountForm = new UntypedFormGroup({
      numberOfBins: new UntypedFormControl(this.defaultBinCount, [Validators.required, Validators.min(1), Validators.max(100)])
    });
  }

  setConfiguration(): void {
    this.applyButtonConfig = {
      name: 'apply',
      value: this.config.dpConfig.translations.apply,
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      borderRadius: 5
    };
    this.binCountInputConfig = {
      name: 'binCountInputConfig',
      placeholder: this.config.dpConfig.translations.numberOfBins,
      formControlName: this.binCountForm.controls.numberOfBins,
      errorList: [
        {
          errorCondition: 'required',
          errorMsg: this.config.dpConfig.translations.requiredFieldError
        },
        {
          errorCondition: 'min',
          errorMsg: this.config.dpConfig.translations.minValueError
        },
        {
          errorCondition: 'max',
          errorMsg: this.config.dpConfig.translations.maxValueError
        }
      ],
      type: 'integer',
      disabled: false,
      autofocus: true
    };

    this.barChartConfig = {
      legend: {
        show: true,
        left: '80%',
        icon: 'rect',
        selectedMode: false
      },
      title: {
        id: "column_chart",
        left: 50,
        text: ''
      },
      grid: {
        left: 50,
        right: 50,
        top: 50,
        bottom: 50,
        containLabel: true
      },
      dataZoom: [
        {
          show: false,
          xAxisIndex: Array[0],
          start: 0,
          end: 25,
          type: 'slider',
          zoomLock: false,
          height: 15,
          backgroundColor: '#e0e0e0',
          fillerColor: this.chartSliderColors[0],
          moveHandleStyle: {
            color: this.chartSliderColors[1],
          },
          brushSelect: false,
          selectedDataBackground: {
            lineStyle: {
              color: '#e0e0e0'
            },
            areaStyle: {
              color: '#e0e0e0'
            }
          },
          dataBackground: {
            lineStyle: {
              color: '#e0e0e0'
            },
            areaStyle: {
              color: '#e0e0e0'
            }
          },
          borderColor: 'none',
        },
        {
          type: 'inside',
          xAxisIndex: [0],
          start: 1,
          end: 100,
          show: false,
          zoomLock: false
        }
      ],
      tooltip: {
        showContent: true,
      },
      xAxis: {
        name: this.config.columnName,
        nameLocation: 'middle',
        nameGap: 55,
        nameTextStyle: {
          fontSize: 14,
          fontFamily: 'HCLTechRoobert',
          fontColor: '#1D1D23'
        },
        // nameTruncate: {
        //   maxWidth: 20,
        //   ellipsis: '...'
        // },
        boundaryGap: true,
        axisTick: {
          interval: 0
        },
        axisLabel: {
          interval: 0,
          rotate: 0,
          offset: 10,
          padding: [5, 0, 0, 0],
          fontSize: 14,
          fontFamily: 'HCLTechRoobert',
          fontColor: '#1D1D23',
          width: 100,
          overflow: 'truncate',
          ellipsis: '...',
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'solid',
            color: '#E0E6F1'
          }
        },
        type: 'category',
        data: [],
        triggerEvent: true
      },
      yAxis: {
        type: 'value',
        name: this.config.dpConfig.translations.frequency,
        nameLocation: 'middle',
        nameGap: 40,
        nameTextStyle: {
          // padding: [0, 0, 10, 0],
          fontSize: 14,
          fontFamily: 'HCLTechRoobert',
          fontColor: '#1D1D23'

        },
        axisLabel: {
          fontSize: 14,
          fontFamily: 'HCLTechRoobert',
          fontColor: '#1D1D23',
          width: 100,
          overflow: 'truncate',
          ellipsis: '...'
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'solid',
            color: '#E0E6F1'
          }
        },
        triggerEvent: true
      },
      series: [
        {
          name: this.config.columnName,
          nameLocation: 'middle',
          type: "bar",
          data: [],
          itemStyle: {
            color: '#6929C4'
          }
        }
      ]
    };

    if (this.config.fieldDataType === 'String') {
      this.barChartConfig.series[0].barMaxWidth = '200';
    } else {
      this.barChartConfig.series[0].barWidth = '99.3%';
      this.barChartConfig.series[0].transform = {
        type: 'ecStat:histogram',
        config: {}
      };
    }
    setTimeout(() => {
      if (this.barChart)
        this.barChart.resizeChart();

    }, 0);
  }

  toggleView(data: string): void {
    if (data === 'graph-view') {
      this.isGraphView = true;
    } else if (data === 'list-view') {
      this.isGraphView = false;
    }
  }
  apply(): void {
    this.dataProfileDetailsSpinner.isLoading = true;
    this.getDistributionData();
  }

  getDistributionData(): void {
    let numberOfBins = 0;
    if (this.config.fieldDataType !== 'String') {
      numberOfBins = this.binCountForm.controls.numberOfBins.value;
    }
    this.subscriptionList.push(this.hclSegmentationLibService.getDistributionForSelectedColumns(this.config.audienceTableId, this.config.fieldId, numberOfBins).subscribe((data: any) => {
      let xAxisData = [];

      this.barChartConfig.dataZoom[0].show = false;
      this.barChartConfig.dataZoom[1].show = false;
      if (data && data.interval) {
        this.distributionData = data.interval;
        if (this.config.fieldDataType.toString() === 'Numeric'.toString()) {
          this.distributionData.forEach(item => {
            item.lowerBound = Math.round(item.lowerBound * 100) / 100;
            item.upperBound = Math.round(item.upperBound * 100) / 100;
          });
        }
        if (data.interval.length <= 10) {
          this.barChartConfig.dataZoom[0].start = 0;
          this.barChartConfig.dataZoom[0].end = 100;
        } else if (data.interval.length > 10 && data.interval.length <= 30) {
          this.barChartConfig.dataZoom[0].start = 0;
          this.barChartConfig.dataZoom[0].end = 50;
          this.barChartConfig.dataZoom[0].show = true;
          this.barChartConfig.dataZoom[1].show = true;
        } else if (data.interval.length > 30) {
          this.barChartConfig.dataZoom[0].start = 0;
          this.barChartConfig.dataZoom[0].end = 25;
          this.barChartConfig.dataZoom[0].show = true;
          this.barChartConfig.dataZoom[1].show = true;
        }
        xAxisData = this.distributionData.map(item => `${item.lowerBound} - ${item.upperBound}`);
        if (data.interval.length <= 10) {
          for (let i = 0; i < (10 - data.interval.length); i++) {
            xAxisData.push('');
          }
        }
      } else if (data && data.category) {
        this.distributionData = data.category;
        if (data.category.length <= 10) {
          this.barChartConfig.dataZoom[0].start = 0;
          this.barChartConfig.dataZoom[0].end = 100;
        } else if (data.category.length > 10 && data.category.length <= 30) {
          this.barChartConfig.dataZoom[0].start = 0;
          this.barChartConfig.dataZoom[0].end = 50;
          this.barChartConfig.dataZoom[0].show = true;
          this.barChartConfig.dataZoom[1].show = true;
        } else if (data.category.length > 30) {
          this.barChartConfig.dataZoom[0].start = 0;
          this.barChartConfig.dataZoom[0].end = 25;
          this.barChartConfig.dataZoom[0].show = true;
          this.barChartConfig.dataZoom[1].show = true;
        }
        xAxisData = this.distributionData.map(item => item.category);
        if (data.category.length <= 10) {
          for (let i = 0; i < (10 - data.category.length); i++) {
            xAxisData.push('');
          }
        }
      }
      this.barChartConfig.xAxis.data = xAxisData;
      const seriesData = this.distributionData.map(item => item.frequency);
      this.barChartConfig.series[0].data = seriesData;
      if (!this.isGraphView) {
        this.toggleView('list-view');
      } else {
        this.toggleView('graph-view');
      }
      this.dataProfileDetailsSpinner.isLoading = false;
      setTimeout(() => {
        if (this.barChart) {
          this.barChart.updateChart();
        }
      }, 0);
    },
      (err: HttpErrorResponse) => {
        this.errorMessage.emit(err.error);
        this.dataProfileDetailsSpinner.isLoading = false;
      }
    ));
  }

  ngOnDestroy(): void {
    this.subscriptionList.forEach(sub => sub.unsubscribe());
  }
}
