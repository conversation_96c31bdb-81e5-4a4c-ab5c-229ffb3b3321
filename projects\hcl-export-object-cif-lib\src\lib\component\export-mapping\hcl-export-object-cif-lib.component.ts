import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ButtonConf, DropDownConfig, InputConfig, NotificationService, SideBarComponent } from 'hcl-angular-widgets-lib';
import { forkJoin, SubscriptionLike } from 'rxjs';
import { AttributesMappingData, CifCategory, CifRepository, ExportObjectConf } from '../../export-mapping-config';
import { HclExportObjectCifLibService } from '../../hcl-export-object-cif-lib.service';
import { CifFolderBaseEntitiesConf } from '../cif-folder-base-entities/cif-folder-base-entities-config';

@Component({
  selector: 'hcl-export-object-cif-lib',
  templateUrl: 'hcl-export-object-cif-lib.component.html',
  styleUrls: ['hcl-export-object-cif-lib.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HclExportObjectCifLibComponent implements OnInit, OnDestroy {
  @Input() config: ExportObjectConf;
  @Output() mappedObject: EventEmitter<AttributesMappingData> = new EventEmitter<AttributesMappingData>();
  @Output() cancleMapping = new EventEmitter();

  @ViewChild(SideBarComponent) sideBarComponentRef: SideBarComponent;

  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();

  exportObjectMappingForm: UntypedFormGroup;
  mappingNameInputConf: InputConfig;
  srcSubCategoriesConfig: DropDownConfig;
  targetSystemDropdownConfig: DropDownConfig;
  targetObjectIdInputConf: InputConfig;
  targetCategoryIdInputConf: InputConfig;
  browseButton1Conf: ButtonConf;
  browseButton2Conf: ButtonConf;
  cancelButtonConfig: ButtonConf;
  saveButtonConfig: ButtonConf;

  sourceOrTargetConfigMissing = false;
  isViewMode: boolean;
  isCreateMode: boolean;
  isEditMode: boolean;
  addCategorySelectionToDom: boolean;
  readyToRenderMappingSec: boolean;
  mappedAttributesData: any;
  targetRepository: CifRepository;
  repositories: CifRepository[];
  cifFolderBaseEntitiesConf: CifFolderBaseEntitiesConf;

  sourceObject = {
    applicationId: null,
    objectType: null,
    schema: null,
    objectId: null
  };

  targetObject = {
    applicationId: null,
    objectType: null,
    objectId: null,
    schema: null
  };

  context = {
    targetCategoryType: '',
    targetCategoryId: '',
    targetCategoryLabel: '',
    targetObjectLabel: '',
    targetObjectType: ''
  };

  targetObjectIdSelectionActive: boolean;
  targetCategorySelectionActive: boolean;
  // targetSchemaObj: CifCategory = {
  //   id: null,
  //   label: ''
  // };

  constructor(
    private hclExportObjectCifLibService: HclExportObjectCifLibService,
    private notificationService: NotificationService) { }

  ngOnInit(): void {
    this.hclExportObjectCifLibService.baseUrl = this.config.cifBaseUrl;
    this.hclExportObjectCifLibService.headers = this.config.cifHeaders;
    // this.sourceObject.applicationId = this.config.sourceApplication;


    if (this.config.applicationMode === 'EDIT') {
      this.isEditMode = true;
    } else {
      this.isCreateMode = true;
    }

    this.constructForm();
    this.setConfiguration();

    this.initApplication();

    this.subscriptionList.push(this.exportObjectMappingForm.valueChanges.subscribe(() => {
      this.saveButtonConfig.disabled = (this.exportObjectMappingForm.valid && this.mappedAttributesData) ? false : true;
      if (this.exportObjectMappingForm.value.targetSystem) {
        this.browseButton1Conf.disabled = false;
        this.browseButton2Conf.disabled = false;
      } else {
        this.browseButton1Conf.disabled = true;
        this.browseButton2Conf.disabled = true;
      }
    }));
  }

  constructForm() {
    this.exportObjectMappingForm = new UntypedFormGroup({
      name: new UntypedFormControl(null, Validators.compose([
        Validators.required,
        Validators.pattern('[^<]+'),
        Validators.maxLength(100)])),
      sourceSubcategory: new UntypedFormControl(null, Validators.required),
      targetSystem: new UntypedFormControl(null, Validators.required),
      targetObjectId: new UntypedFormControl(null, Validators.required),
      targetCategoryId: new UntypedFormControl(null, Validators.required)
    });
  }

  initApplication() {
    this.hclExportObjectCifLibService.getInstances(true).subscribe((repositories: CifRepository[]) => {
      this.setupRepositories(repositories);
    }, (error) => this.hclExportObjectCifLibService.handleServerError(error.message || this.config.translations.generalError));
  }

  setupRepositories(repos: CifRepository[]) {
    if (repos && repos.length) {
      repos.some((repo, index) => {
        if (repo.categories.find(cat => cat === 'UnicaSegmentCentral')) {
          const sourceRepository = repos.splice(index, 1)[0];
          this.sourceObject.applicationId = sourceRepository.identifier;
          return;
        }
      });

      this.repositories = this.isCreateMode ? repos.filter(repo => repo.categories.find(category =>
        category === this.config.targetCategoryFilter)) : this.repositories = repos.filter(repo =>
          repo.identifier === this.config.previousData.object1.applicationId);

      if (this.sourceObject.applicationId && this.repositories && this.repositories.length) {
        this.setSourceSubcategories();
      } else {
        this.sourceOrTargetConfigMissing = true;
      }

      if (this.isEditMode) {
        this.updateEditState();
      }

      this.targetSystemDropdownConfig.options = this.repositories.map((item: CifRepository) => {
        return {
          label: item.displayName,
          value: item.identifier
        };
      });

      if (this.isCreateMode && this.targetSystemDropdownConfig.options.length === 1) {
        this.targetSystemChanged(this.targetSystemDropdownConfig.options[0].value);
      }

    } else {
      this.sourceOrTargetConfigMissing = true;
    }
  }

  updateEditState() {
    const { label, object1, object2, context, attributeMappings } = this.config.previousData;

    forkJoin([this.hclExportObjectCifLibService.getSingleCategory(object1.applicationId, context.targetCategoryId),
    this.hclExportObjectCifLibService.getSingleContent(object1.applicationId, context.targetObjectType, object1.objectId)])
      .subscribe(([data1, data2]: any[]) => {
        this.exportObjectMappingForm.patchValue({
          targetObjectId: data2.presentationDetails.textual.heading,
          targetCategoryId: data1.label
        });
      });

    this.exportObjectMappingForm.patchValue({
      name: label,
      sourceSubcategory: object2.objectType,
      targetSystem: object1.applicationId
    });

    this.targetObject.applicationId = object1.applicationId;
    this.targetSystemDropdownConfig.disabled = true;
    this.targetObject.objectId = object1.objectId;
    this.targetObject.objectType = object1.objectType;
    this.sourceObject.objectType = object2.objectType;
    this.hclExportObjectCifLibService.mappingDataMap = attributeMappings;
    this.context = context;
    this.browseButton1Conf.value = this.config.translations.replace;
    this.browseButton2Conf.value = this.config.translations.replace;


    forkJoin([this.hclExportObjectCifLibService.getObjectMappingAttributes(object2.applicationId, object2.objectType),
    this.hclExportObjectCifLibService.getObjectMappingAttributes(object1.applicationId,
      context.targetCategoryType, context.targetCategoryId.toString())
    ]).subscribe(([obj2Schema, obj1Schema]: any[]) => {
      this.targetObject.schema = JSON.stringify(obj1Schema);
      this.sourceObject.schema = JSON.stringify(obj2Schema);
      this.hclExportObjectCifLibService.sourceSchema = this.sourceObject.schema;
      this.hclExportObjectCifLibService.targetSchema = this.targetObject.schema;
      this.readyToRenderMappingSec = true;
    });
  }

  setConfiguration() {
    this.mappingNameInputConf = {
      name: 'mappingNameInput',
      placeholder: this.config.translations.name,
      formControlName: this.exportObjectMappingForm.controls['name'],
      autofocus: false,
      type: 'text',
      errorList: [{
        errorCondition: 'required',
        errorMsg: this.config.translations.requiredField
      },
      {
        errorCondition: 'pattern',
        errorMsg: this.config.translations.invalidCharacter
      },
      {
        errorCondition: 'maxlength',
        errorMsg: this.config.translations.maxLimitError
      }]
    };

    this.srcSubCategoriesConfig = {
      options: [],
      placeholder: this.config.translations.sourceSubCatSelectionLabel,
      name: 'sourceSubcategory',
      formControl: this.exportObjectMappingForm.controls['sourceSubcategory']
    };

    this.targetSystemDropdownConfig = {
      options: [],
      placeholder: this.config.translations.targetCatSelectionLabel,
      name: 'targetSystem',
      formControl: this.exportObjectMappingForm.controls['targetSystem'],
      errorList: [{
        errorCondition: 'required',
        errorMsg: this.config.translations.requiredField
      }]
    };

    this.targetObjectIdInputConf = {
      name: 'targetObjectId',
      placeholder: this.config.translations.externalSourceSubCat2Label,
      formControlName: this.exportObjectMappingForm.controls['targetObjectId'],
      autofocus: false,
      type: 'text',
      disabled: true
    };

    this.targetCategoryIdInputConf = {
      name: 'targetCategoryId',
      placeholder: this.config.translations.externalSourceSubCat1Label,
      formControlName: this.exportObjectMappingForm.controls['targetCategoryId'],
      autofocus: false,
      type: 'text',
      disabled: true
    };

    this.browseButton1Conf = {
      name: 'browseButton',
      value: this.config.translations.browse,
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      borderRadius: 5,
      disabled: true
    };

    this.browseButton2Conf = {
      ...this.browseButton1Conf
    };

    this.cancelButtonConfig = {
      name: 'cancel',
      value: this.config.translations.cancel,
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };

    this.saveButtonConfig = {
      name: 'save',
      value: this.config.translations.save,
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5,
      disabled: true
    };

  }

  targetSystemChanged(targetRepo) {
    this.targetRepository = this.repositories.find((repo: CifRepository) => repo.identifier === targetRepo);
    this.exportObjectMappingForm.patchValue({ 'targetSystem': targetRepo });
    this.targetObject.applicationId = targetRepo;
    this.context.targetCategoryType = this.targetRepository.additionalFeatures.content.categorizationLayout.levels[0].code;
    this.context.targetObjectType = this.targetRepository.additionalFeatures.content.code;
    this.targetObject.objectType = this.targetRepository.additionalFeatures.content.code;
  }

  setSourceSubcategories() {
    this.hclExportObjectCifLibService.getCategoriesWithParentId(this.config.sourceSubcategoryId)
      .subscribe((cifCategories: CifCategory[]) => {
        if (cifCategories && cifCategories.length) {
          this.srcSubCategoriesConfig.options = cifCategories.map((item: CifCategory) => {
            return {
              label: item.label,
              value: item.id,
              disabled: item.disabled
            };
          });

          if (this.isCreateMode && cifCategories.length === 1) {
            this.exportObjectMappingForm.patchValue({ 'sourceSubcategory': this.srcSubCategoriesConfig.options[0].value });
            this.srcSubCategoryChanged(this.srcSubCategoriesConfig.options[0].value);
          }
        }
      }, (error) => this.hclExportObjectCifLibService.handleServerError(error.message || this.config.translations.generalError));
  }

  srcSubCategoryChanged(objectType: string) {
    this.readyToRenderMappingSec = false;
    this.mappedAttributesData = '';
    this.hclExportObjectCifLibService.mappingDataMap = new Map<number, any>();

    this.sourceObject.objectType = objectType;
    this.hclExportObjectCifLibService.getObjectMappingAttributes(this.sourceObject.applicationId, this.sourceObject.objectType)
      .subscribe(schema => {
        this.sourceObject.schema = JSON.stringify(schema);
        this.hclExportObjectCifLibService.sourceSchema = this.sourceObject.schema;
        if (this.targetObject.schema) {
          this.readyToRenderMappingSec = true;
        }
      });
  }

  updateLabelValue(blurEvent: any) {
    this.exportObjectMappingForm.patchValue({ 'name': blurEvent.target.value.trim() });
  }

  // getFirstActiveSubcategory(categories: CifCategory[]) {
  //   return categories.find(category => !category.disabled);
  // }

  openFolderCategorySelection(index: number) {
    this.cifFolderBaseEntitiesConf = {
      translations: this.config.translations,
      applicationService: this.hclExportObjectCifLibService,
      cifRepository: this.targetObject.applicationId || this.targetRepository.identifier,
      cifBaseUrl: this.config.cifBaseUrl,
      isPaginated: true,
      removeGlobalSearch: true,
      rootFolderAsRootFolderParent: true,
      rootFolder: index === 1 ? { id: 1, displayName: this.config.translations.allCategories, parentFolderId: 1 }
        : { id: 2, displayName: this.config.translations.allContents, parentFolderId: 2 }
    };

    if (index === 1) {
      this.readyToRenderMappingSec = false;
      this.targetCategorySelectionActive = true;
      this.cifFolderBaseEntitiesConf.translations.appTitle = this.config.translations.targetObjectIdSelectionAppTitle;
      this.cifFolderBaseEntitiesConf.translations.gridTitle = this.config.translations.targetObjectIdSelectionGridTitle;
      this.cifFolderBaseEntitiesConf.translations.gridSearchPlaceholder = this.config.translations.targetObjectIdSelectionGridSearchLabel;
      this.cifFolderBaseEntitiesConf.translations.selectedEntityLabel = this.config.translations.targetObjectIdSelectedItemLabel;
      this.cifFolderBaseEntitiesConf.assetContext = 'category';
      this.cifFolderBaseEntitiesConf.previousSelectedCategoryId = this.context.targetCategoryId || null;
      this.cifFolderBaseEntitiesConf.previousSelectedItemLable = this.targetCategoryIdInputConf.formControlName.value || '';
    } else {
      this.targetObjectIdSelectionActive = true;
      this.cifFolderBaseEntitiesConf.translations.appTitle = this.config.translations.targetObjectTypeSelectionAppTitle;
      this.cifFolderBaseEntitiesConf.translations.gridTitle = this.config.translations.targetObjectTypeSelectionGridTitle;
      this.cifFolderBaseEntitiesConf.translations.gridSearchPlaceholder = this.config.translations.targetObjectTypeSelectionGridSearchLabel;
      this.cifFolderBaseEntitiesConf.translations.selectedEntityLabel = this.config.translations.targetObjectTypeSelectedItemLabel;
      this.cifFolderBaseEntitiesConf.assetContext = 'content';
      this.cifFolderBaseEntitiesConf.previousSelectedCategoryId = this.targetObject.objectId || null;
      this.cifFolderBaseEntitiesConf.previousSelectedItemLable = this.targetObjectIdInputConf.formControlName.value || '';
    }

    this.addCategorySelectionToDom = true;
    setTimeout(() => {
      this.sideBarComponentRef.openSideBar();
    }, 600);

  }

  cancelMapping() {
    this.cancleMapping.emit();
  }

  updateCifFbs(exportedData: any) {
    if (this.targetCategorySelectionActive) {
      this.context.targetCategoryId = exportedData.data.id;
      this.context.targetCategoryLabel = exportedData.data.label;
      this.targetCategoryIdInputConf.formControlName.setValue(exportedData.data.label);
      this.browseButton1Conf.value = this.config.translations.replace;
      this.mappedAttributesData = '';
      this.hclExportObjectCifLibService.mappingDataMap = new Map<number, any>();
    } else {
      const objectLable = exportedData.data.presentationDetails.textual.heading;
      this.targetObjectIdInputConf.formControlName.setValue(objectLable);
      this.context.targetObjectLabel = objectLable;
      this.targetObject.objectId = exportedData.data.presentationDetails.multimedia.id;
      this.browseButton2Conf.value = this.config.translations.replace;
    }

    if (this.targetObject.objectId && this.context.targetCategoryId) {
      this.hclExportObjectCifLibService.getObjectMappingAttributes(this.targetObject.applicationId,
        this.context.targetCategoryType, this.context.targetCategoryId.toString()).subscribe(schema => {
          this.targetObject.schema = JSON.stringify(schema);
          this.hclExportObjectCifLibService.targetSchema = this.targetObject.schema;
          if (this.sourceObject.schema) {
            this.readyToRenderMappingSec = true;
          }
        }, (error) => this.hclExportObjectCifLibService.handleServerError(error.message || this.config.translations.generalError));
    }
    this.closeCifFbsSidebar();
  }


  closeCifFbsSidebar(updateMappingSectionState?: boolean) {
    if (updateMappingSectionState && this.sourceObject.schema && this.targetObject.schema) {
      this.readyToRenderMappingSec = true;
    }
    this.addCategorySelectionToDom = false;
    this.sideBarComponentRef.close('close');
    this.targetObjectIdSelectionActive = false;
    this.targetCategorySelectionActive = false;
  }

  disableForm() {
    // this.noRepositories = true;
  }

  updateMappedData(data: any) {
    if (data && data.contentMapping) {
      this.mappedAttributesData = data.contentMapping;
      this.hclExportObjectCifLibService.mappingDataMap = new Map<number, any>(data.contentMapping);
      this.saveButtonConfig.disabled = (data.formState && this.exportObjectMappingForm.valid) ? false : true;
    } else {
      this.mappedAttributesData = '';
      this.hclExportObjectCifLibService.mappingDataMap = new Map<number, any>();
      this.saveButtonConfig.disabled = true;
    }
  }

  saveExportObjectMapping() {
    const attributeMappings = [];
    let requestData;
    if (this.mappedAttributesData) {
      if (this.hclExportObjectCifLibService.invalidPreviousMappingMap.size) {
        this.mappedAttributesData.forEach((mapping, key) => {
          if (this.hclExportObjectCifLibService.invalidPreviousMappingMap.get(mapping.attribute2Id)) {
            this.mappedAttributesData.delete(key);
          }
        });
      }

      this.mappedAttributesData.forEach((value, key) => {
        const mappingValue = { ...value };
        const attributeId = value.attribute1Id;
        // const id = +attributeId.substring(attributeId.lastIndexOf('.') + 1, attributeId.length);
        if (mappingValue && mappingValue['attribute1Title']) {
          delete mappingValue.attribute1Title;
        }
        if (mappingValue && mappingValue['attribute2Title']) {
          delete mappingValue.attribute2Title;
        }
        attributeMappings.push(mappingValue);
      });

      if (attributeMappings.length) {
        const object1 = typeof this.targetObject.schema === 'object' ? {
          ...this.targetObject, schema: this.hclExportObjectCifLibService.targetSchema
        } : this.targetObject;

        // TODO - need to check below logic
        // if (this.isCreateMode && this.config.allMappingsList && this.config.allMappingsList.length && this.config.allMappingsList[0].object1.objectId) {
        //   object1.objectId = this.config.allMappingsList[0]?.object1.objectId;
        // }

        const object2 = typeof this.sourceObject.schema === 'object' ? {
          ...this.sourceObject, schema: this.hclExportObjectCifLibService.sourceSchema
        } : this.sourceObject;
        requestData = {
          label: this.exportObjectMappingForm.value.name,
          attributeMappings,
          object1,
          object2,
          autoSynchronizationSupport: 'INBOUND',
          context: this.context
        };
      }

      let mapingAlreadyExist = false;
      if (this.isCreateMode && this.config.allMappingsList && this.config.allMappingsList.length) {
        this.config.allMappingsList.some((mapping: any) => {
          if (mapping.object2.objectType === requestData.object2.objectType && mapping.object1.objectId === requestData.object1.objectId) {
            mapingAlreadyExist = true;
            return true;
          }
        });
      }

      if (mapingAlreadyExist) {
        this.notificationService.show({
          message: this.config.translations.duplicateMappingError,
          type: 'error', close: true, autoHide: 6000
        });
      } else if (this.isEditMode) {
        this.subscriptionList.push(this.hclExportObjectCifLibService.updateObjectMapping(this.config.previousData.mappingId,
          requestData).subscribe((data: AttributesMappingData) => {
            this.mappedObject.emit(data);
            this.notificationService.show({
              message: this.config.translations.updateMappingSuccessful,
              type: 'success', close: true, autoHide: 6000
            });
          }, (error) => this.hclExportObjectCifLibService.handleServerError(error.message || this.config.translations.generalError)));
      } else {
        this.subscriptionList.push(this.hclExportObjectCifLibService.createObjectMapping(requestData)
          .subscribe((data: AttributesMappingData) => {
            this.mappedObject.emit(data);
            this.notificationService.show({
              message: this.config.translations.createMappingSuccessful,
              type: 'success', close: true, autoHide: 6000
            });
          }, (error) => this.hclExportObjectCifLibService.handleServerError(error.message || this.config.translations.generalError)));
      }
    }
  }

  ngOnDestroy(): void {
    this.hclExportObjectCifLibService.cleanUpData();
    this.subscriptionList.forEach(sub => { sub.unsubscribe(); });
  }

}
