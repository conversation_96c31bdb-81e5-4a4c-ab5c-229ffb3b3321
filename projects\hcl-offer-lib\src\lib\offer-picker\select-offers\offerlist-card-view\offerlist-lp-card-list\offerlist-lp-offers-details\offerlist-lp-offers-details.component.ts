import { Component, ElementRef, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ButtonConf, InputConfig, HclAssetPickerService } from 'hcl-angular-widgets-lib';
import { Subscription, SubscriptionLike } from 'rxjs';
import { SelectOffersService } from '../../../../select-offers.service';
import { OfferDataService } from '../../../../offer-data.service';

@Component({
  selector: 'hcl-offerlist-lp-offers-details',
  templateUrl: './offerlist-lp-offers-details.component.html',
  styleUrls: ['./offerlist-lp-offers-details.component.scss']
})
export class OfferlistLpOffersDetailsComponent implements OnInit {

  @Input() offerlistInfo: { offerlistName: string, offerlistId: number, count: number };
  @Output() closeSidebar = new EventEmitter();

  subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  offerData: any[];
  offerBaseData: any[];
  cancelButtonConfig: ButtonConf;
  searchBoxConfig: InputConfig;
  isSearchOn = false;
  offerViewPermissionSubscription: Subscription;

  constructor(
    public translate: TranslateService,
    private selectOffersService: SelectOffersService,
    private hclAssetPickerService: HclAssetPickerService,
    private offerDataService: OfferDataService) { }

  ngOnInit(): void {
    this.subscriptionList.push(this.selectOffersService.getBulkOfferListOffersThumbnail(this.offerlistInfo.count,
      [this.offerlistInfo.offerlistId]).subscribe(offerlistOfferData => {
        this.offerBaseData = offerlistOfferData[this.offerlistInfo.offerlistId].offers.map(offerItem => {
          const { thumbnailProperties, displayName, offerCodes, id, state } = offerItem;
          return {
            thumbnailProperties,
            displayName,
            offerCodes: this.getOfferCodeWithDelimiter(offerCodes),
            id,
            state
          }
        })
        this.offerData = [...this.offerBaseData];
      }))

    this.setConfiguration();
  }

  setConfiguration() {
    this.cancelButtonConfig = {
      name: 'cancel',
      value: this.translate.instant('BUTTONS.CLOSE'),
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };

    this.searchBoxConfig = {
      formControlName: new UntypedFormControl(''),
      placeholder: this.translate.instant('BUTTONS.SEARCH'),
      icon: 'hcl-icon-search',
      type: 'text',
      name: 'search'
    };
  }

  search() {
    if (this.searchBoxConfig.formControlName.value) {
      this.isSearchOn = true;
      this.searchBoxConfig.icon = 'hcl-icon-close-x';
      this.offerData = this.offerBaseData.filter(offerBaseItem => {
        return (offerBaseItem.displayName.toLowerCase().indexOf(this.searchBoxConfig.formControlName.value.toLowerCase()) > -1)
          || (offerBaseItem.offerCodes.toLowerCase().indexOf(this.searchBoxConfig.formControlName.value.toLowerCase()) > -1);
      });
    } else {
      this.cleanUpOfferSearch();
    }
  }

  cleanUpOfferSearch() {
    this.isSearchOn = false;
    this.searchBoxConfig.icon = 'hcl-icon-search';
    this.searchBoxConfig.formControlName.reset();
    this.offerData = [...this.offerBaseData];
  }

  searchIconClick() {
    if (this.isSearchOn) {
      this.cleanUpOfferSearch();
    } else {
      this.search();
    }
  }

  /**
* Thumbnail Image loaded callback event
* @param event
* @param offer
* @param imageElement
*/
  updateOfferThumbnailState(event: string, offer: any, imageElement: ElementRef) {
    if (event === 'error' && !offer.isImageLoaded) {
      // check if thumbnail is asset picker item and all properties has value 
      const { url, applicationId, objectType, objectId } = offer.thumbnailProperties;
      this.subscriptionList.push(this.offerDataService.getAssetPickerRepositories().subscribe((repos) => {
        if (repos) {
          const selectedRepoForPreview = repos.find(repo => repo.identifier === applicationId);
          const isAnonymousContent = selectedRepoForPreview && !selectedRepoForPreview.anonymousContent;
          if (isAnonymousContent && url && applicationId && objectType && objectId) {
            const resourceUrl = this.hclAssetPickerService.baseUrl + '/' + applicationId + '/download?resourceId=' + objectId +
              '&resource=' + url;
            this.offerDataService.downloadAssetPickerAnonymousContent(resourceUrl).then(data => {
              offer.isImageLoaded = true;
              imageElement['src'] = data;
            }, error => {
              offer.brokenThumbnail = true;
              offer.isImageLoaded = true;
            });
          } else {
            offer.brokenThumbnail = true;
            offer.isImageLoaded = true;
          }
        } else {
          this.offerDataService.getAndSetAssetPickerInstances(true);
        }
      }))
    } else {
      offer.isImageLoaded = true;
    }
  }

  getOfferCodeWithDelimiter(offerCodeArray) {
    return offerCodeArray.join(this.offerDataService.offerApplicationConfig.offerCodeDelimiter);
  }

  getThumbnailDefaultSource() {
    return this.offerDataService.offerApplicationConfig.offerServerURL + '/om/assets/images/offer-default-thumbnail.png';
  }

  getThumbnailBrokenSource() {
    return this.offerDataService.offerApplicationConfig.offerServerURL + '/om/assets/images/offer-broken-thumbnail.png';
  }

  cancel() {
    this.closeSidebar.next();
  }
}
