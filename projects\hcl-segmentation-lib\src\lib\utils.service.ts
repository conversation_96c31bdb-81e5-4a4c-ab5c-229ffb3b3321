import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { SidePanelDetails } from './component/side-panel/side-panel-details';

@Injectable({
  providedIn: 'root'
})
export class UtilsService {private _sidePanelDetails = new Subject<SidePanelDetails>();
  public sidePanelDetails$ = this._sidePanelDetails.asObservable();

  constructor(
  ) {}
  /**
   * This method will open side panel for different entities such as baseTableMetadata / create base table / edit base table / create segment
   * @param panelDetail
   */
  public openSidePanel(panelDetail: SidePanelDetails): void {
    this._sidePanelDetails.next(panelDetail);
  }
}
