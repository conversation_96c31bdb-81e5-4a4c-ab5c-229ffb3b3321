/**
 * The type of charts this charting engine supports
 */
import {<PERSON><PERSON><PERSON><PERSON>enderer} from './BaseChartRenderer';
import {D3Bar<PERSON>hartRenderer} from './d3/D3BarChartRenderer';
import {D3GanttChartRenderer} from './d3/D3GanttChartRenderer';

export enum CHART_TYPE {
  // A bar chart
  BAR_CHART = 'bar',
  // A Gantt chart
  GANTT_CHART = 'gantt'
}
/**
 * The factory class that will return the default renderer
 */
export class RendererFactory {

  /**
   * this is the static function that will return the current chart renderer
   * param {CHART_TYPE} chartType
   * returns {BaseChartRenderer}
   */
  public getRenderer(chartType: CHART_TYPE): BaseChartRenderer {
      return this.getD3ChartRenderer(chartType);
  }

  /**
   * this function will return the D3 chart renderers
   * param {CHART_TYPE} chartType
   * returns {BaseChartRenderer}
   */
  private getD3ChartRenderer(chartType: CHART_TYPE): BaseChart<PERSON>enderer {
    switch (chartType) {
      case CHART_TYPE.BAR_CHART :
        return new D3BarChartRenderer();
      case CHART_TYPE.GANTT_CHART :
        return new D3GanttChartRenderer();
      default :
        console.error('Undefined chart type');
    }
    return null;
  }
}
