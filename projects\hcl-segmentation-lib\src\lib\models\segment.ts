import { DropDownConfig } from "hcl-angular-widgets-lib";

export interface SegmentObj {
  id?: number;
  displayName?: string;
  description?: string;
  policyId?: number;
  folderId?: number;
  createdTimeStamp?: string;
  createdBy?: number;
  updatedTimeStamp?: string;
  updatedBy?: number;
  lastRunDateTimeStamp?: string;
  runBy?: number;
  segmentCount?: number;
  tempTableName?: string;
  cellFileName?: string;
  audienceName?: string;
  activeFlag?: number;
  aclId?: number;
  tempTableDb?: string;
  segmentType?: number;
  segmentTypeDescription?: string;
  tags?: string[];
  subType?: SegmentSubType;
  segmentState?: string;
  sourceFlowChart?: FlowChart;
  createdByCampaign?: boolean;
}

export interface SegmentSubType {
  type: string;
  audienceTableId?: number;
  nestedCondition?: NestedCondition;
  setOperation?: {
    logicalOp: string;
    ids: number[];
  };
}

export interface NestedCondition {
  logicalOp: "AND" | "OR";
  conditions: Condition[];
}
export interface Condition {
  param: string;
  op: string;
  value: any;
}

export interface FlowChart {
  flowchartId?: number;
  flowchartName?: string;
  campaignId?: number;
  sessionId?: string;
}

export interface SegmentRunStats {
  segmentState?: string;
  totalCount?: number;
  segmentCount?: number;
  asOnTimeStamp?: string;
}

export interface AudienceApplicationConfig {
  isAppEnabled: boolean;
  token: string;
  serverURL: string;
  validity: number;
}

export interface DonutChartConfig {
  withoutTimeDatePipeFormat: string;
  translations: { [key: string]: any };
  segmentStats?: SegmentRunStats;
}

export interface SegmentMetadatConfig {
  textSizeValidationType: "char" | "byte" | "unicode-byte" | "";
  securityPolicies: { [key: string]: any };
  securityPoliciesForSegment: { [key: string]: any };
  securityPoliciesForFolder: { [key: string]: any };
  translations: { [key: string]: any };
  reLogin?: (callback) => void;
}

export interface Folder {
  id: number;
  displayName: string;
  description: string;
  policyId: number;
  parentFolderId: number;
  createdTimeStamp: string;
  createdBy: number;
  updatedTimeStamp: string;
  updatedBy: number;
  creatorSystemId: number;
  creatorObjectId: number;
}

export interface SegPermission {
  name: string;
  permitted: boolean;
}

export interface UserConfig {
  displayName: string;
  locale: string;
  folderSecurityPoliciesMap: object;
  segmentSecurityPoliciesMap: object;
  securityPoliciesMap: object;
  roles: Role[];
}
export interface Role {
  displayName: string;
  id: number;
  name: string;
}

export interface BaseTableMetadata {
  baseTableName: string;
  columns: BaseTableColumn[];
  jndiName: string;
}

export interface BaseTableColumn {
  columnSize?: number;
  dataTypeCode?: number;
  dbType?: string;
  decimalDigits?: number;
  name: string;
  sqlDataTypeCode?: number;
  type: "Text" | "Numeric" | "Integer" | "Date";
  displayName?: string;
  internalType?: "Text" | "Numeric" | "Integer" | "Date";
}

export interface AudienceTableDetails {
  audienceName: string;
  dataSourceName?: string;
  physicalTableName: string;
  fields: TableFields[];
  type: string;
  displayName?: string;
  id?: number;
}

export interface TableFields {
  name?: string;
  displayName?: string;
  idFieldName?: string;
  idFieldType?: string;
  dropdownConfig?: DropDownConfig;
}

export interface BaseTableColumn {
  columnSize?: number;
  dataTypeCode?: number;
  dbType?: string;
  decimalDigits?: number;
  name: string;
  sqlDataTypeCode?: number;
  type: "Text" | "Numeric" | "Integer" | "Date";
  displayName?: string;
  internalType?: "Text" | "Numeric" | "Integer" | "Date";
}

export interface SegmentCentralApplicationConfig {
  applicationId: number;
  serverURL: string;
  dataSourceForSegment?: string;
  textSizeValidationType: "char" | "byte" | "unicode-byte";
}
export interface DataProfileConfig {
  applicationUser?: "dataProfileFromCreateSegment";
  segmentBaseUrl?: string;
  translations: { [key: string]: any };
  reLogin?: (callback) => void;
}

export interface StatsData {
  count: number
  nulls: number
  categories: number
  mean: number
  stdev: number
  min: number
  max: number
}

export interface DistributionData {
  category?: Category[]
  interval?: Interval[]
}

export interface Category {
  category: string
  frequency: number
}

export interface Interval {
  lowerBound: number
  upperBound: number
  frequency: number
}
