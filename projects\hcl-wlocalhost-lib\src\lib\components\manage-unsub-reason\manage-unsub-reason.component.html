<hcl-side-bar>
    <aside class="h-100">
        <h2 class="mb-3">
            {{ 'settings.Manage_unsubscribe_reasons' | translate }}
        </h2>

        <div class="item-container">
            <div class="list-item start-end" *ngFor="let item of list; let i = index;">
                <div [attr.data-index]="i" class="item-label" (keydown)="keyDownHandler($event, item)">
                    {{item.label}}
                </div>

                <div class="start-end action-item">
                    <span class="pr-2 edit-container" hclTooltip="{{'general.edit' | translate }}"
                        (click)="editReason($event, i);">
                        <i class="hcl-icon-edit"></i>
                    </span>
                    <span hclTooltip="{{ 'general.delete' | translate }}" (click)="deleteReason($event, i);">
                        <i class="hcl-icon-delete"></i>
                    </span>
                </div>
            </div>
        </div>

        <div [formGroup]="managereason">
            <div class="add-action-container mt-3">
                <div class="mb-1">
                    {{ 'settings.Other' | translate }}
                </div>
                <hcl-slide-toggle [config]="otherconfig" (toggleChange)="toggleChange($event)">
                </hcl-slide-toggle>
            </div>
            <div>
                <hcl-input [config]="addReasonInputConfig"></hcl-input>
                <div class="right mt-3">

                    <hcl-button *ngIf="!(addReasonControl.valid && addReasonControl.value); else enableBtn;"
                        [config]="addReasonDisable"></hcl-button>

                    <ng-template #enableBtn>
                        <hcl-button [config]="addReason" (onclick)="addReasonToList($event)"></hcl-button>
                    </ng-template>

                </div>
            </div>

        </div>

        <div class="btn-container float-left col-12 pr-4">
            <hcl-button [config]="saveConfig" (onclick)="onSave()"></hcl-button>
            <hcl-button [config]="cancelConfig" (onclick)="cancelAction()"></hcl-button>
        </div>
    </aside>
</hcl-side-bar>