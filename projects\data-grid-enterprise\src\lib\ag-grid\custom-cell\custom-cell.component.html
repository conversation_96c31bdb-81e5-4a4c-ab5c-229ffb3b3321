<div [ngClass]="{'grabbable':draggable}" [draggable]=draggable (dragstart)="onDragStart($event)"
  (dragend)="onDragEnd($event)">
  <ng-container
    [ngTemplateOutlet]="columnDefinition._templateRef ? columnDefinition._templateRef : (hoverIcons ? hoverIconRenderer: (draggableRow ? dragIconRenderer :defaultCellRenderer))"
    [ngTemplateOutletContext]="{$implicit: {col : columnDefinition, row : rowData}}">
  </ng-container>
</div>
<div [ngClass]="{'grabbable':draggable}" [draggable]=draggable (dragstart)="onDragStart($event)"
  (dragend)="onDragEnd($event)">
  <ng-template #defaultCellRenderer>
    <div class="cell-tooltip">
      {{rowData && rowData[columnDefinition.field]}}
    </div>
  </ng-template>
</div>

<ng-template #hoverIconRenderer>
  <div *ngIf="rowData && rowData.visibleActions" id="hoverActionContainer">
    <ng-container *ngFor="let action of rowData.visibleActions; let index = index;">
      <div class="hoverContainer visible-actions">
        <i *ngIf="action.icon" hclTooltip="{{action.tooltip}}" [class]="action.icon"></i>
        <div [class]="action.styleClass" (click)="visibleActionClicked($event, index)">{{action.value}}</div>
      </div>
    </ng-container>
  </div>
  <div *ngIf="rowData && !rowData.visibleActions" id="hoverActionContainer" (click)="stopProp($event)">
    <div class="hcl-icon-kabab"></div>
    <div *ngIf="loadIcons" class="hcl-hover-icons hoverContainer">
      <ng-container *ngFor="let hoverIcon of hoverIcons">
        <i *ngIf="hoverIcon.icon" hclTooltip="{{hoverIcon.tooltip}}" [class]="hoverIcon.icon"
          [ngClass]="{'disable-hover-icon':disableIcon(hoverIcon, rowData)}"
          (click)="itemClicked($event,hoverIcon)"></i>
      </ng-container>
    </div>
  </div>
</ng-template>

<ng-template #dragIconRenderer>
  <i class="hcl-icon-drag cell-drag-icon"></i>
</ng-template>