.hcl-icon-down-without-border {
    font-size: 7px;
    margin-left: 4px;
    margin-top: 2px;
    display: inline-block;
    width: 15px;
}
.top-actions-container {
    justify-content: space-between;
    display: flex;
    width: 100%;
    align-items: center;
    .sort-menu-container {
        list-style: none;
        display: flex;
        padding-left: 7px;
        margin-bottom: 0;
        color: #6d7692;
        font-family: Roboto;
        font-size: 12px;
        letter-spacing: 0.4px;
        line-height: 14px;
        .sort-by-item {
            &:hover {
                cursor: hand;
                cursor: pointer;
            }
        }
        li {
            display: inline-flex;
            margin: 0 2px;
            height: 10px;
            line-height: 10px;
        }
        .icon-container {
            i {
                line-height: 0;
                display: block;
                font-size: 13.5px;
                margin-top: -5px;
            }
        }
    }
    .button-container {
        display: inline-flex;
    }
}
com-folders > div > hcl-folders > .folders,
.folders .split-panel-container {
    width: 100% !important;
}
.folders-search {
    width: 200px;
    height: 35px;
}
