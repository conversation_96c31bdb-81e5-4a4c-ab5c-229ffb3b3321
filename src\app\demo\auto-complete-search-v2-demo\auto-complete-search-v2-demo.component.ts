import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { AutoCompleteSearchV2Component } from 'projects/hcl-angular-widgets-lib/src/public_api';
import { AutoCompleteV2Conf } from '../../../../projects/hcl-angular-widgets-lib/src/lib/component/auto-complete-search-v2/auto-complete-search-v2-config';

@Component({
  selector: 'app-auto-complete-search-v2-demo',
  templateUrl: './auto-complete-search-v2-demo.component.html',
  styleUrls: ['./auto-complete-search-v2-demo.component.scss']
})
export class AutoCompleteSearchV2DemoComponent implements OnInit {

  countries = ['In Folder A', 'In Folder B', 'In Folder C', 'In Folder D'];
  @ViewChild('autoComplete') autoComplete: AutoCompleteSearchV2Component;
  acPanelConfig: AutoCompleteV2Conf;
  acInputConfig: AutoCompleteV2Conf;
  acConfigOpt: AutoCompleteV2Conf;
  inlineProgressSpinner: AutoCompleteV2Conf;
  zeroSuggestions: AutoCompleteV2Conf;

  constructor() { }

  ngOnInit() {
    this.acPanelConfig = {
      placeholder: 'Numbers',
      value: '',
      autoComplete: 'auto',
      name: 'search',
      inputType: 'text',
      formControl: new UntypedFormControl(),
      iconLoadOnOptionSuffix: 'document',
      iconLoadOnOptionPrefix: 'product',
      showInlineSpinner: true,
      //isLazyLoad: true,
      showRecentSearch: true,
      suggestions: this.countries,
      suffixIconClass: 'hcl-icon-search',
    };

    this.inlineProgressSpinner = {
      placeholder: 'Numbers',
      value: '',
      autoComplete: 'auto',
      name: 'search',
      inputType: 'text',
      formControl: new UntypedFormControl(),
      iconLoadOnOptionSuffix: 'document',
      iconLoadOnOptionPrefix: 'product',
      //isLazyLoad: true,
      showRecentSearch: true,
      suffixIconClass: 'hcl-icon-search',
      prefixIconClass: 'hcl-icon-delete',
      maxIconClass: 'hcl-icon-user'
    };

    this.zeroSuggestions = {
      placeholder: 'Zero Suggestions',
      value: '',
      autoComplete: 'auto',
      name: 'search',
      inputType: 'text',
      formControl: new UntypedFormControl(),
      iconLoadOnOptionSuffix: 'document',
      iconLoadOnOptionPrefix: 'product',
      //isLazyLoad: true,
      showRecentSearch: true,
      suffixIconClass: 'hcl-icon-search',
      prefixIconClass: 'hcl-icon-delete',
      maxIconClass: 'hcl-icon-user',
      recentSearch : [],
      recentSearchText: 'Recent search'
    };

    this.zeroSuggestions.suggestions = [];

    setTimeout(() => {
      this.acPanelConfig.suggestions = Array.from({ length: 10000 }).map((_, i) => `Item #${i}`);
      this.inlineProgressSpinner.suggestions =[{ type:"document", label:"one", iconClass:"hcl-icon-user",typeData:"Document Link",iconClsAfterString:"hcl-icon-delete" }, { type:"document", label:"two", iconClass:"hcl-icon-user",typeData:"Document Link" },
        { type:"document", label:"three", iconClass:"",typeData:"Document Link" },{ type:"product", label:"four", iconClass:"hcl-icon-user",typeData:"Application" },
        { type:"", label:"five", iconClass:"hcl-icon-user" ,typeData:""},{ type:"product", label:"six", iconClass:"",typeData:"Application" }];
      this.acPanelConfig = { ... this.acPanelConfig };
      this.zeroSuggestions.recentSearch = ['Data Definition', 'Entry source', 'Template', 'Journey',]
    }, 200);

    setTimeout(() => {
      this.acPanelConfig.showInlineSpinner = false;
    }, 5000);
  }

  onClick(event,value) {
    console.log(event,value);
  }

  onIconClick(event) {
    console.log(event);
  }

  onOptionClick(event) {
    console.log(event);
    alert();
  }

  onBlurEvnt(event) {
    console.log(event);
  }
}
