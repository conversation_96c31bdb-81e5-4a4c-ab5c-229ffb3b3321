.cif-fbc-grid-container {
    height: 100%;
  
    .headerRow {
      .folder-details {
        .selected-folder {
          color: #444444;
          font-family: <PERSON><PERSON>;
          font-size: 14px;
          letter-spacing: 0;
          line-height: 16px;
          display: inline-block;
          margin-bottom: 10px;
        }
        span.folder-label {
          color: #6d7692;
          font-family: <PERSON><PERSON>;
          font-size: 14px;
          letter-spacing: 0;
          line-height: 16px;
          margin-left: 10px;
        }
      }
  
      .title-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 30px;
        .grid-title {
          h3 {
            color: #6d7692;
            font-family: Montserrat;
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0;
            line-height: 19px;
            margin-bottom: 5px;
          }
        }
  
        .selected-item {
          width: 50%;
          display: flex;
          justify-content: end;
          p {
            margin-bottom: 5px;
            display: flex;
            width: 100%;
            justify-content: end;
            span {
              max-width: 50%;
              color: #6d7692;
              font-family: Montserrat;
              font-size: 14px;
              font-weight: 500;
              letter-spacing: 0;
              margin-left: 5px;
            }
          }
        }
      }
  
      .search-section {
        background-color: #f5f5f5;
        box-shadow: 0 2px 2px 0 rgb(0 0 0 / 25%);
        padding: 0 10px;
        margin: 0 1px 10px;
  
        .input-search-wrapper {
          width: 300px;
  
          i {
            color: #959595;
            font-size: 14px;
            cursor: pointer;
          }
  
          .mat-form-field {
            .mat-form-field-wrapper {
              padding-bottom: 0.75em;
            }
          }
        }
      }
    }
  
    .grid-section {
      height: calc(100% - 88px);
  
      .hcl-grid-container {
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.125);
        border: 1px solid rgba(0, 0, 0, 0.125);
  
        .ag-header-container,
        .ag-center-cols-container {
          width: 100%;
        }
      }
  
      &.paginated {
        .hcl-grid-container {
          height: calc(100% - 100px);
        }
      }
  
      &.withoutSearch {
        .hcl-grid-container {
          height: calc(100% - 25px);
        }
      }
    }
  }
  