export class SharedModuleConstants {
    public static get richTextOptions() {
        return {
            key: 'BWC6D-16D3E3F3H3D1A6A4wc2DBKSPJ1WKTUCQOd1OURPE1KDc1C-7J2A4D4B4C6D2A1F4G1C1==',
            charCounterCount: false,
            placeholderText: '',
            charCounterMax: -1,
            attribution: false,
            language: '',
            heightMin: 150,
            heightMax: 300,
            fileUpload: false,
            imagePaste: false,
            htmlRemoveTags: ['script', 'style', 'button', 'select'],
            toolbarButtons: {
                'moreText': {
                    'buttons': ['bold', 'italic', 'underline', 'strikeThrough',
                        'fontSize', 'textColor', 'backgroundColor'], 'buttonsVisible': 7
                },
                'moreParagraph': {
                    'buttons': ['paragraphFormat', 'formatOL', 'formatUL']
                },
                'moreRich': { 'buttons': ['insertLink', 'insertTable'] },
                'moreMisc': { 'buttons': ['undo', 'redo'], 'align': 'right', 'buttonsVisible': 4 }
            },
            colorsBackground: [
                '#001423', '#0066b3', '#69b4e1', '#b9e6fa', '#4e0817', '#eb1946',
                '#f58ca2', '#f8d8e0', '#f5821e', '#fab914', '#fcdc89',
                '#fac08e', '#fdead9', '#5a2d91', '#ac96c8', '#e3dcec', '#00afbe',
                '#7fd7de', '#d4f1f4', '#bed732', '#deeb98', '#f4f8dc', 'REMOVE'],
            colorsStep: 8,
            colorsText: ['#001423', '#0066b3', '#69b4e1', '#b9e6fa', '#4e0817',
                '#eb1946', '#f58ca2', '#f8d8e0', '#f5821e', '#fab914', '#fcdc89',
                '#fac08e', '#fdead9', '#5a2d91', '#ac96c8', '#e3dcec', '#00afbe',
                '#7fd7de', '#d4f1f4', '#bed732', '#deeb98', '#f4f8dc', 'REMOVE'],
            tableColors: ['#001423', '#0066b3', '#69b4e1', '#b9e6fa', '#4e0817',
                '#eb1946', '#f58ca2', '#f8d8e0', '#f5821e', '#fab914', '#fcdc89',
                '#fac08e', '#fdead9', '#5a2d91', '#ac96c8', '#e3dcec', '#00afbe',
                '#7fd7de', '#d4f1f4', '#bed732', '#deeb98', '#f4f8dc', 'REMOVE'],
            tableColorsStep: 8,
            linkInsertButtons: ['linkBack'],
            linkEditButtons: ['linkEdit', 'linkRemove'],
            linkAlwaysBlank: true,
            quickInsertButtons: ['table', 'ul', 'ol'],
            wordAllowedStyleProps: [
                'font-size', 'background', 'color', 'width', 'text-align', 'vertical-align', 'background-color', 'padding',
                'margin', 'height', 'margin-top', 'margin-left', 'margin-right', 'margin-bottom', 'text-decoration',
                'font-weight', 'font-style', 'text-indent', 'border', 'border-.*'],
            pasteDeniedTags: ['a', 'input', 'button', 'select'],
            editorClass: 'hcl-custom-text-editor',
            toolbarSticky: false
        };
    }
}
