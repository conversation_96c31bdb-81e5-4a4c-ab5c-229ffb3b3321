.auto-complete {
  i {
    color: #959595;
    &:hover {
      color: #f5821e;
    }
  }
}

.autocomplete-loader-style {
  position: absolute;
  left: 0;
  right: 22px;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: flex-end;
  flex-direction: column;
}

.recent-search {
  padding: 8px 18px;
  font-family: Roboto, sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.25px;
  text-align: left;
  color: grey;
}

.autocomplete-viewport {
  height: 200px;
}

.auto-complete {
  .hcl-icon-calendar {
    cursor: pointer;
  }

  .mat-form-field-infix {
    display: contents !important;
  }

  .mat-input-element {
    line-height: 2em;
    min-height: 24px;
  }
}

.mat-autocomplete-panel {
  .mat-option {
    display: block;
    height: 50px;
  }

  .mat-option.no-suggestions {
    height: 90px !important; 
  }

  .mat-option-text {
    width: 100%;
  }

  .selected-option {
    background-color: rgba(0, 0, 0, 0.04) !important;
  }

  .disabled-option {
    pointer-events: none;
    opacity: 0.4;
  }

  .opt-val-cls {
    // margin-bottom: 5px;
    color: #444444;
    font-family: Roboto;
    font-size: 14px;
    letter-spacing: 0;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
  }

  .opt-small-cls {
    color: #6d7692;
    font-family: Roboto;
    font-size: 12px;
    letter-spacing: 0.4px;
    margin: 0;
  }
}

.suffix-option-icon {
  margin-left: 8px;
  cursor: pointer;
}

.chips-container {
  height: auto;
  padding: 0px 0px 8px 18px;
  display: flex;
}

.rounded-chip {
  background-color: #e0e0e0;
  border: 2px solid #d2d6e1;
  border-radius: 14px;
  display: inline-block;
  height: 29px;
  margin: 3px;
  padding: 2px 10px;
  max-width: 216px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  text-align: center;
  align-items: center;

  &:hover {
    border-color: #f5821e;
    background-color: #ececec;
    box-shadow: 6px 6px 10px 0 rgba(0, 0, 0, 0.3);
  }

  .chip-child {
    display: flex;
    align-items: center;
  }

  .ellipsis {
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: Roboto, sans-serif;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    text-align: center;
  }

  .select-option {
    margin-left: 5px;
    margin-right: 3px;
    font-size: 14px;
    font-family: Roboto, sans-serif;
    margin-top: 1px;
  }

  i:hover {
    color: #f5821e;
  }
}

.type-class{
  font-family: 'Roboto';
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.4px;
  text-align: right;
  color: #939393;
}

.prefix-option-icon{
  cursor: pointer;
}

.clearInput{
  cursor: pointer;
}