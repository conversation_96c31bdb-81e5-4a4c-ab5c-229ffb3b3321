import {
    ILayout,
    TLayoutTypes,
    ILayoutOptions,
    ILayoutColumnOptions
  } from '../interfaces';
  import { TFields } from './Fields';
  import { defaultsDeep } from 'lodash';
  
  export const defaultColumnsOptions: ILayoutColumnOptions = {
    background: {
      color: 'transparent'
    },
    border: {
      width: 0,
      color: '#ffffff',
      radius: 0,
      style: 'solid'
    },
    verticalAlign: 'top',
    height: {
      value: 100,
      unit: 'px',
      auto: true,
      units: ['px', '%', 'cover', 'contain']
    },
    width: {
      value: 100,
      unit: 'px',
      auto: true,
      units: ['px', '%', 'cover', 'contain']
    }
  };
  
  export class TLayouts implements ILayout {
    columns = 1;
    readonly id = Date.now();
    public downloadDetails: {
      // in case the image is a lin/
      // the Id of the asset that we want to download
      assetId?: number | string,
      // the id that needs to be given to the tag
      id?: string,
      // the URL which needs to be hit to download the asset
      assetUrl?: string,
      // flag thatr tells the current status of the asset
      assetDownloaded?: boolean
    } = null
    options: ILayoutOptions = {
      border: {
        color: '#ffffff',
        style: 'solid',
        width: 0,
        radius: 0
      },
      background: {
        color: '#ffffff',
        url: '',
        repeat: 'repeat',
        size: {
          value: 100,
          unit: 'px',
          auto: true,
          units: ['px', '%', 'cover', 'contain']
        }
      },
      padding: {
        top: 4,
        right: 4,
        bottom: 4,
        left: 4
      },
      margin: {
        top: 0,
        bottom: 0
      },
      gaps: [4, 4],
      height: {
        value: 100,
        unit: 'px',
        auto: true,
        units: ['px']
      },
    };
  
    constructor(
      readonly type: TLayoutTypes = 'cols_1',
      public fields: TFields[][] = [],
      options?: ILayoutOptions
    ) {
      if (!fields.length) {
        if (['cols_2', 'cols_12', 'cols_21'].includes(type)) {
          this.columns = 2;
        } else if (type === 'cols_3') {
          this.columns = 3;
        } else if (type === 'cols_4') {
          this.columns = 4;
        }
      }
  
      const columns: ILayoutColumnOptions[] = Array.from(
        { length: this.columns },
        () => defaultColumnsOptions
      );
  
      let columnsWidth = [1];
      if (type === 'cols_21') {
        columnsWidth = [4, 6];
      } else if (type === 'cols_12') {
        columnsWidth = [6, 4];
      } else if (type === 'cols_2') {
        columnsWidth = [5, 5];
      } else if (type === 'cols_3') {
        columnsWidth = [3.33, 3.33, 3.33];
      } else if (type === 'cols_4') {
        columnsWidth = [2.5, 2.5, 2.5, 2.5];
      }
  
      this.options = defaultsDeep(options, this.options, {
        columns,
        columnsWidth
      });
    }
  }
  