import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { Role } from '../models/role';
import { Approval } from '../models/approval';
import { TranslateService } from '@ngx-translate/core';
import { ApprovalListConfig } from '../models/approval-list-config';
import { NotificationService } from 'hcl-angular-widgets-lib';

@Injectable()
export class ApprovalSharedDataService {

  private _memberRoles = new BehaviorSubject<Role[]>(null);
  private _memberRoles$ = this._memberRoles.asObservable();
  private _responseHistory = new BehaviorSubject<any[]>(null);
  private _responseHistory$ = this._responseHistory.asObservable();
  private _securityPolicyOption = new BehaviorSubject<any>(null);
  private _securityPolicyOption$ = this._securityPolicyOption.asObservable();
  private APPROVED = 'APPROVED';
  private APPROVED_WCHANGES = 'APPROVED_WCHANGES';
  private _serverUrl: string;
  private _tokenId: string;
  private _username: string;
  private loginAttempts = 0;
  private _MAX_LOGIN_ATTEMPTS: number = 2;
  private _approvalConfig: ApprovalListConfig;
  private loginTimerId: any;

  constructor(private _translate: TranslateService, private notificationService: NotificationService) { }

  public setMemberRoles(obj) {
    this._memberRoles.next(obj);
  }

  public getMemberRoles() {
    return this._memberRoles$;
  }

  public setApprovalResponseHistory(obj) {
    this._responseHistory.next(obj);
  }

  public getApprovalResponseHistory() {
    return this._responseHistory$;
  }

  public setSecurityPolicyOption(obj) {
    this._securityPolicyOption.next(obj);
  }

  public getSecurityPolicyOption() {
    return this._securityPolicyOption$;
  }

  get serverUrl(): string {
    return this._serverUrl;
  }

  set serverUrl(value: string) {
    this._serverUrl = value;
  }

  get tokenId(): string {
    return this._tokenId;
  }

  set tokenId(value: string) {
    this._tokenId = value;
  }

  get username(): string {
    return this._username;
  }

  set username(value: string) {
    this._username = value;
  }

  get approvalConfig(): ApprovalListConfig {
    return this._approvalConfig;
  }

  set approvalConfig(value: ApprovalListConfig) {
    this._approvalConfig = value;
  }

  public getTypeIconClass(filename: string, mimeType: string) {
    let fileTypeImage = 'hcl-icon-unknown-type';
    if (filename && '' !== filename) {
      const lowerCaseFileName = filename.toLowerCase();
      if (lowerCaseFileName.indexOf('.doc') > 0) {
        fileTypeImage = 'hcl-icon-ms-word-type';
      } else if (lowerCaseFileName.indexOf('.pdf') > 0) {
        fileTypeImage = 'hcl-icon-pdf-type';
      } else if (lowerCaseFileName.indexOf('.csv') > 0) {
        fileTypeImage = 'hcl-icon-csv-type';
      } else if (lowerCaseFileName.indexOf('.ppt') > 0) {
        fileTypeImage = 'hcl-icon-ms-ppt-type';
      } else if ((mimeType && (mimeType.toUpperCase() !== 'URL' && mimeType.toLowerCase() !== 'url-url')) &&
        (lowerCaseFileName.indexOf('.bmp') > 0 || lowerCaseFileName.indexOf('.jpg') > 0 || lowerCaseFileName.indexOf('.jpeg') > 0
          || lowerCaseFileName.indexOf('.gif') > 0 || lowerCaseFileName.indexOf('.png') > 0)) {
        fileTypeImage = 'hcl-icon-image-type';
      } else if (lowerCaseFileName.indexOf('.psd') > 0) {
        fileTypeImage = 'hcl-icon-psd-type';
      } else if (lowerCaseFileName.indexOf('.xls') > 0) {
        fileTypeImage = 'hcl-icon-ms-excel-type';
      } else if (lowerCaseFileName.indexOf('.txt') > 0) {
        fileTypeImage = 'hcl-icon-text-type';
      } else if (lowerCaseFileName.indexOf('.htm') > 0) {
        fileTypeImage = 'hcl-icon-html-type';
      } else if (mimeType && mimeType.toUpperCase() === 'URL' || mimeType.toLowerCase() === 'url-url') {
        fileTypeImage = 'hcl-icon-url-type';
      } else if (mimeType && mimeType.toLowerCase() === 'formtask') {
        fileTypeImage = 'hcl-icon-form-type';
      } else if (mimeType && mimeType.toLowerCase() === '-1') {
        fileTypeImage = 'hcl-icon-flowchart-type';
      }
    }
    return fileTypeImage;
  }

  public getRespCountByItem(item): number {
    let countApprove = 0;
    if (item && item.responseList) {
      item.responseList.forEach(resp => {
        if (resp.statusCode === this.APPROVED || resp.statusCode === this.APPROVED_WCHANGES) {
          countApprove++;
        }
      });
      return countApprove;
    }
    return 0;
  }

  public getApprovalStatus(data: Approval): string {
    let status: string = '';
    if (data && data.stateCode) {
      status = this._translate.instant('APPROVALPICKER.APPROVAL_STATUS_VALUES.' + data.stateCode);
    }
    return status;
  }

  /**
   * Clear interval timer when approval widget is closed
   */
  clearTimer() {
    if (this.loginTimerId) {
      clearInterval(this.loginTimerId);
    }
  }

  /**
   * Initiate login timer according to token time validity 
   */
  reloginTimer() {
    this.loginAttempts = 0;
    if (this.approvalConfig.reLogin) {
      let retryInterval = this.approvalConfig.tokenValidity;
      if (retryInterval && retryInterval >= 15) {
        retryInterval = retryInterval * 1000 - 2000;
        this.loginTimerId = setInterval(this.retrylogin.bind(this, this.loginAttempts), retryInterval);
      } else {
        this.loginTimerId = setInterval(this.retrylogin.bind(this, this.loginAttempts), 60000);
      }
    }
  }

  /**
   * Call relogin method of approval config to update the token
   */
  private retrylogin() {
    if (this.loginAttempts < this._MAX_LOGIN_ATTEMPTS) {
      this.loginAttempts++;
      this.approvalConfig.reLogin(this.reLoginSuccess());
    }
  }

  /**
   * Reset login attempts
   */
  private reLoginSuccess() {
    this.loginAttempts = 0;
  }

  /**
   * Display error notification when api is failing
   * @param error
   * @returns 
   */
  public displayErrorNotification(error): string {
    let errorMsg: string = '';

    if (error.status && error.error) {
      error = error.error;
    }
    // if the error has status & key we have a unhandled error
    if (error.status && error.key) {
      errorMsg = error.status;
    } else {
      for (const x in error) {
        // if this object has length then we have to concate it
        if (x.toLowerCase() !== 'key' && error[x].length) {
          error[x].forEach(e => errorMsg += e);
        }
      }
    }
    this.notificationService.show({
      message: errorMsg,
      type: 'error', close: true, autoHide: 6000
    });

    return errorMsg;
  }

}
