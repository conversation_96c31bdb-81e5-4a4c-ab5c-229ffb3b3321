.bar-chart {
  svg {
    .xAxis, .yAxis {
      path {
        stroke: #6D7692;
      }
      .tick {
        text {
          fill: #6D7692;
          font-family: Montserrat;
          font-size: 10px;
          font-weight: 500;
        }
        
        line {
          stroke: #6D7692;
        }
      }
    }
    .xAxisLabel, .yAxisLabel {
      fill: #6D7692;
      font-family: Montserrat;
      font-size: 11px;
      font-weight: 600;
    }
  }
}
.bar-chart-tooltip {
  z-index: 1001;
  position: absolute;
  overflow: auto;
  padding-left: 10px;
  opacity: 0.9;
  display: none;
  .bar-chart-tooltip-arrow {
    border-width: 10px;
    border-color: rgba(0,0,0,0.9);
    border-style: solid;
    border-top-color: transparent;
    border-bottom-color: transparent;
    border-left-color: transparent;
    width: 0px;
    height: 0px;
    position: relative;
    display: block;
    top: 35px;
    left: -19px;
  }
  .bar-chart-tooltip-contents {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 3px;
    .bar-chart-tooltip-container {
      padding: 5px 20px 6px 20px;
      .bar-chart-tooltip-row {
        height: 13px;
        color: white;
        font-size: 10px;
        font-family: 'Montserrat';
        .bar-chart-tooltip-label {
          height: 10px;
        }
      }
    }
  }
}