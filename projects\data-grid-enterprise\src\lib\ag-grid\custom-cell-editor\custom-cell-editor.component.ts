import { Component, <PERSON><PERSON>he<PERSON>, <PERSON>Child } from '@angular/core';
import { ICellEditorAngularComp } from 'ag-grid-angular';
import { DropDownConfig, MenuComponent, PopupMenuConfig } from 'hcl-angular-widgets-lib';

import * as _ from 'lodash';

@Component({
  selector: 'hcl-custom-cell-editor',
  templateUrl: './custom-cell-editor.component.html',
  styleUrls: ['./custom-cell-editor.component.css']
})
export class CustomCellEditorComponent implements DoCheck, ICellEditorAngularComp {
  @ViewChild('cellPopupMenu') cellPopupMenu: MenuComponent;
  configPopUp: PopupMenuConfig = {
    alwaysOpen: true
  };
  /**
   * The data that is displayed in this row
   */
  rowData: any;
  /**
   * The column definition
   */
  columnDef: any;
  /**
   * the default constructor
   */
  config: DropDownConfig;
  /**
   * The grid api for stopping edit mode programatically
   */
  api: any;
  /**
   * This variable will store new values which we will return back to grid
   */
  newData: any;
  constructor() { }
  /**
   * This function is called by the grid to update the grid data
   * returns {any}
   */
  getValue(): any {
    if (this.columnDef.cellEditorParams.type === 'dynamic') {
      if (this.columnDef.cellEditorParams.getDynamicType(this.columnDef, this.rowData) === 'text'
        || this.columnDef.cellEditorParams.getDynamicType(this.columnDef, this.rowData) === 'number') {
        return this.columnDef.cellEditorParams.conf.inputConf.formControlName.value;
      } else if (this.columnDef.cellEditorParams.getDynamicType(this.columnDef, this.rowData) === 'date') {
        if (!this.newData) {
          this.newData = this.rowData[this.columnDef.field];
        } else {
          this.newData = this.columnDef.cellEditorParams.conf.dateConfig.formControlName.value;
        }

        return this.newData;
      } else if (this.columnDef.cellEditorParams.getDynamicType(this.columnDef, this.rowData) === 'select') {
        this.newData = this.columnDef.cellEditorParams.conf.selectConf.formControl.value;
        return this.newData;
      }
    }

    if (this.columnDef.cellEditorParams.conf.inputConf) {
      // this.rowData[this.columnDef.field] = this.columnDef.cellEditorParams.conf.inputConf.formControlName.value;
      this.newData = this.columnDef.cellEditorParams.conf.inputConf.formControlName.value;
    } else if (this.columnDef.cellEditorParams.conf.dateConfig) {
      // this.rowData[this.columnDef.field] = this.columnDef.cellEditorParams.conf.dateConfig.formControlName.value;
      if (!this.newData) {
        this.newData = this.rowData[this.columnDef.field];
      } else {
        this.newData = this.columnDef.cellEditorParams.conf.dateConfig.formControlName.value;
      }
    } else if (this.columnDef.cellEditorParams.conf.selectConf && this.columnDef.cellEditorParams.conf.selectConf.formControl
      && !this.newData) {
      this.newData = this.columnDef.cellEditorParams.conf.selectConf.formControl.value;
    } else if (this.columnDef.cellEditorParams.conf.multiSelectConfig && this.columnDef.cellEditorParams.conf.multiSelectConfig.formControl
      && !this.newData) {
      this.newData = this.columnDef.mapData(this.rowData, this.columnDef,
        this.columnDef.cellEditorParams.conf.multiSelectConfig.formControl.value);
    } else if (this.columnDef.cellEditorParams.conf.timePickerConfig && !this.newData) {
      this.newData = this.rowData[this.columnDef.field];
    } else if (this.columnDef.cellEditorParams.conf.autoCompleteTreeConfig) {
      // this.newData = this.columnDef.cellEditorParams.conf.autoCompleteTreeConfig.config.value;
      this.newData = this.columnDef.mapData(this.rowData, this.columnDef,
        this.columnDef.cellEditorParams.conf.autoCompleteTreeConfig.config.value);
    }
    // return this.rowData[this.columnDef.field];
    return this.newData;
  }

  ngDoCheck() {
    /* if (this.columnDef.cellEditorParams.type === 'select') {
      this.config = {
        options: this.columnDef.cellEditorParams.conf.selectConf.options,
        disabled: false,
        placeholder: this.columnDef.cellEditorParams.conf.selectConf.placeholder ?
          this.columnDef.cellEditorParams.conf.selectConf.placeholder : '',
        selectedOption: this.rowData[this.columnDef.field],
        disableOptionCentering: true,
        defaultText: this.columnDef.cellEditorParams.conf.selectConf.placeholder ?
          this.columnDef.cellEditorParams.conf.selectConf.placeholder : '',
        name: ''
      };
    } */
  }
  /**
   * Called by the AgGrid
   * param params
   */
  agInit(params: any): void {
    // set the row data
    this.api = params.api;
    this.rowData = params.data;
    this.columnDef = params.colDef;
    if (this.columnDef.cellEditorParams.type === 'dynamic') {
      switch(this.columnDef.cellEditorParams.getDynamicType(this.columnDef, this.rowData)) {
        case 'text':
        case 'number':
          if (this.columnDef.cellEditorParams.conf.inputConf) {
            this.columnDef.cellEditorParams.conf.inputConf.formControlName.setValue(this.rowData[this.columnDef.field]);
          }
        break;
        case 'date':
          if (this.columnDef.cellEditorParams.conf.dateConfig) {
            this.columnDef.cellEditorParams.conf.dateConfig.formControlName.setValue(this.rowData[this.columnDef.field] instanceof Date ?
              this.rowData[this.columnDef.field] : (isNaN(+this.rowData[this.columnDef.field]) ? null : 
              new Date(+this.rowData[this.columnDef.field])));
          }
        break;
        case 'select':
          if (this.columnDef.cellEditorParams.conf.selectConf && this.columnDef.cellEditorParams.conf.selectConf.formControl) {
            // lets check if we have getOptions, in that case we have to get the options
            if (this.columnDef.cellEditorParams.conf.selectConf.getOptions) {
              this.columnDef.cellEditorParams.conf.selectConf.options =
                this.columnDef.cellEditorParams.conf.selectConf.getOptions(params.data, params.colDef);
            }
            this.columnDef.cellEditorParams.conf.selectConf.formControl.setValue(_.isArray(this.rowData[this.columnDef.field]) ? this.rowData[this.columnDef.field][0] : this.rowData[this.columnDef.field]);
          }
        break;
      }
    } else {
      // In case we have a input box
      if (this.columnDef.cellEditorParams.conf.inputConf) {
        this.columnDef.cellEditorParams.conf.inputConf.formControlName.setValue(this.rowData[this.columnDef.field]);
      } else if (this.columnDef.cellEditorParams.conf.dateConfig) {
        this.columnDef.cellEditorParams.conf.dateConfig.formControlName.setValue(this.rowData[this.columnDef.field] instanceof Date ?
          this.rowData[this.columnDef.field] : null);
      } else if (this.columnDef.cellEditorParams.conf.timePickerConfig) {
        this.columnDef.cellEditorParams.conf.timePickerConfig.inputConf.formControlName.setValue(this.rowData[this.columnDef.field]);
      } else if (this.columnDef.cellEditorParams.conf.selectConf && this.columnDef.cellEditorParams.conf.selectConf.formControl) {
        // lets check if we have getOptions, in that case we have to get the options
        if (this.columnDef.cellEditorParams.conf.selectConf.getOptions) {
          this.columnDef.cellEditorParams.conf.selectConf.options =
            this.columnDef.cellEditorParams.conf.selectConf.getOptions(params.data, params.colDef);
        }
        this.columnDef.cellEditorParams.conf.selectConf.formControl.setValue(this.rowData[this.columnDef.field]);
      } else if (this.columnDef.cellEditorParams.conf.autoCompleteTreeConfig) {
        this.columnDef.cellEditorParams.conf.autoCompleteTreeConfig.inputConfig.value = '';
        this.columnDef.cellEditorParams.conf.autoCompleteTreeConfig.config.value = this.rowData[this.columnDef.field];
      }
    }
    
    // if we have a init method we have to call it
    if (this.columnDef.initEdit) {
      this.columnDef.initEdit(this.rowData, this.columnDef);
    }
  }

  searchInAutoComplete(event): void {
    if (this.columnDef.cellEditorParams.conf.autoCompleteConfig.optClick) {
      this.columnDef.cellEditorParams.conf.autoCompleteConfig.optClick(event);
    }
  }

  /**
   * Do some validation before editing starts
   * returns {boolean}
   */
  isCancelBeforeStart(): boolean {
    if (this.columnDef.canEdit) {
      return !this.columnDef.canEdit(this.rowData);
    }
    return false;
  }
  /**
   * In case of select box when a value is selected this function will be called & update the value in the grid
   * param e
   */
  onSelect(e) {
    // this.rowData[this.columnDef.field] = this.columnDef.mapData(this.rowData, this.columnDef, e);
    this.newData = this.columnDef.mapData(this.rowData, this.columnDef, e);

    let valid = true;

    if (this.columnDef.cellEditorParams.validateControl) {
      valid = this.columnDef.cellEditorParams.validateControl(e, this.columnDef, this.rowData);
    }

    if (valid) {
      this.api.stopEditing(false);
    } else {
      this.cellPopupMenu.openMenu(e);

      e.srcElement.focus();
    }
  }

  inputBlured(event: any) {
    let valid = true;

    if (this.columnDef.cellEditorParams.validateControl) {
      valid = this.columnDef.cellEditorParams.validateControl(event, this.columnDef, this.rowData);
    }

    if (valid) {
      this.api.stopEditing(false);
    } else {
      this.cellPopupMenu.openMenu(event);

      event.srcElement.focus();
    }
  }

  togglePanel(e) {
    if (!e.state) { // refractor data only when multi select box is closed
      // this.rowData[this.columnDef.field] = this.columnDef.mapData(this.rowData, this.columnDef, e.value);
      this.newData = this.columnDef.mapData(this.rowData, this.columnDef, e.value);
      this.api.stopEditing(false);
    }
  }

  dateSelected(obj) {
    this.newData = this.columnDef.mapData(this.rowData, this.columnDef, obj);

    let valid = true;

    if (this.columnDef.cellEditorParams.validateControl) {
      valid = this.columnDef.cellEditorParams.validateControl(obj, this.columnDef, this.rowData);
    }

    if (valid) {
      this.api.stopEditing(false);
    } else {
      this.cellPopupMenu.openMenu(obj);
    }
  }

  treeMenuClosed(e) {
    this.api.stopEditing(false);
  }

  pickerClosed(obj) {
    if (obj.isKeyPressed) {
      if (obj.key === 13) {
        // this.rowData[this.columnDef.field] = obj.value;
        this.newData = this.columnDef.mapData(this.rowData, this.columnDef, obj.value); // obj.value;
        this.api.stopEditing(false);
      } else {
        this.api.stopEditing(true);
      }
    } else {
      // this.rowData[this.columnDef.field] = obj.value;
      this.newData = this.columnDef.mapData(this.rowData, this.columnDef, obj.value); // obj.value;
      this.api.stopEditing(false);
    }
  }

  getDynamicType() {
    return this.columnDef.cellEditorParams.getDynamicType(this.columnDef, this.rowData);
  }

  closeMenu(event) {
    let valid = true;

    if (this.columnDef.cellEditorParams.validateControl) {
      valid = this.columnDef.cellEditorParams.validateControl(event, this.columnDef, this.rowData);
    }

    if (valid) {
      this.api.stopEditing(false);
      this.cellPopupMenu.closeMenu(true);
    }
  }

  // afterGuiAttached(params?: IAfterGuiAttachedParams): void {
  //   debugger
  // }
  //
  // destroy(): void {
  //   debugger
  // }
  //
  // focusIn(): void {
  //   debugger
  // }
  //
  // focusOut(): void {
  //   debugger
  // }
  //
  // getFrameworkComponentInstance(): any {
  //   debugger
  // }
  //
  // getGui(): HTMLElement {
  //   debugger
  //   return undefined;
  // }
  //
  // init(params: ICellEditorParams): Promise<void> | void {
  //   debugger
  //   return undefined;
  // }
  //
  // isCancelAfterEnd(): boolean {
  //   debugger
  //   return false;
  // }
  //
  // isPopup(): boolean {
  //   debugger
  //   return false;
  // }
}
