<div class="cif-object-mapping">
    <div class="cif-object-mapping__header">
        <h2>{{appTitle}}</h2>
    </div>
    <div class="cif-object-mapping__content">
        <div class="metadata">
            <div class="cms-repos">
                <hcl-drop-down (select)="repositoryChanged($event)" [config]="repoConfig"> </hcl-drop-down>
            </div>

            <ng-container *ngIf="cifRepoLoaded">
                <ng-container *ngIf="isServiceCategoryFolderEnabled; else serviceCategoryDropdown">
                    <div class="category-folder-sec">
                        <div class="category-input">
                            <hcl-input [config]="serviceCategoryInputConf"> </hcl-input>
                        </div>

                        <div class="ml-4" *ngIf="!isViewMode">
                            <hcl-button (onclick)="openCategorySelection('service')"
                                [config]="serviceCategoryButtonConf">
                            </hcl-button>
                        </div>
                    </div>
                </ng-container>

                <ng-template #serviceCategoryDropdown>
                    <div class="category-dropdown">
                        <hcl-drop-down (select)="categoryChanged($event, 'service')"
                            [config]="serviceCategoryDropdownConfig">
                        </hcl-drop-down>
                    </div>
                </ng-template>

                <ng-container *ngIf="isAppicationCategoryFolderEnabled; else applicationCategoryDropdown">
                    <div class="category-folder-sec">
                        <div class="category-input">
                            <hcl-input [config]="applicationCategoryInputConf"> </hcl-input>
                        </div>

                        <div class="ml-4" *ngIf="!isViewMode">
                            <hcl-button (onclick)="openCategorySelection('application')"
                                [config]="applicationCategoryButtonConf">
                            </hcl-button>
                        </div>
                    </div>
                </ng-container>

                <ng-template #applicationCategoryDropdown>
                    <div class="category-dropdown">
                        <hcl-drop-down (select)="categoryChanged($event, 'application')"
                            [config]="applicationCategoryDropdownConfig">
                        </hcl-drop-down>
                    </div>
                </ng-template>

            </ng-container>
        </div>

        <div class="mapping-section">
            <ng-container *ngIf="cifRepoLoaded && readyTorenderMappingSec">
                <div class="titles">
                    <h4>{{config.translations.fieldMapping}}</h4>
                    <p>{{config.translations.mapFieldsToAutosync}}</p>
                </div>

                <div class="mapping-area">
                    <div class="headers">
                        <div class="attr-header">
                            <span [attr.data-position]="'bottom-top-start'"
                                hclTooltip="{{config.translations.hostAppFieldTitle}}">
                                {{config.translations.hostAppFieldTitle}}</span>
                        </div>

                        <div class="attr-header">
                            <span [attr.data-position]="'bottom-top-start'"
                                hclTooltip="{{selectedRepoLabel + ' '+ config.translations.serviceFieldTitle}}">
                                {{ selectedRepoLabel + ' '+ config.translations.serviceFieldTitle}}</span>
                        </div>
                    </div>
                    <div class="attributes-mapping">
                        <hcl-cif-om-attrs-subscription [isEditMode]="isEditMode" [config]="config"
                            (mappedData)="updateMappedData($event)">
                        </hcl-cif-om-attrs-subscription>
                    </div>
                </div>
            </ng-container>
        </div>
    </div>
    <div class="cif-object-mapping__actions">
        <hcl-button *ngIf="!isViewMode" [config]="saveButtonConfig" (onclick)="saveObjectMapping()">
        </hcl-button>
        <hcl-button [config]="cancelButtonConfig" (onclick)="cancelMapping()">
        </hcl-button>
    </div>
</div>

<hcl-side-bar *ngIf="addCategorySelectionToDom">
    <div class="cif-object-mapping-category-selection-wrapper">
        <hcl-cif-folder-base-entities [config]="cifFolderBaseEntitiesConf" (selectedCifFbc)="updateCifFbs($event)"
            (closeCifFbs)="closeCifFbsSidebar()">
        </hcl-cif-folder-base-entities>
    </div>
</hcl-side-bar>