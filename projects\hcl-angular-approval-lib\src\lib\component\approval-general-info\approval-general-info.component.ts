import { Component, OnInit, OnD<PERSON>roy, Input } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { Approval } from '../../models/approval';
import { SecurityPolicy } from '../../models/securityPolicy';
import { ApprovalSharedDataService } from '../../service/approval-shared-data.service';

@Component({
  selector: 'hcl-approval-general-info',
  templateUrl: './approval-general-info.component.html',
  styleUrls: ['./approval-general-info.component.scss']
})
export class ApprovalGeneralInfoComponent implements OnInit, OnDestroy {

  @Input() approval: Approval;
  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();

  allPolicies: SecurityPolicy[] = [];

  constructor(private _sharedDataService: ApprovalSharedDataService) { }

  ngOnInit(): void {
    this.populatePolicyData();
  }

  private populatePolicyData() {
    this.subscriptionList.push(this._sharedDataService.getSecurityPolicyOption().subscribe(
      (res) => {
        if (res) {
          res.content.forEach((ele: SecurityPolicy) => {
            this.allPolicies.push(ele);
          });
        }
      }
      ));
  }

  getPolicyDisplay() {
    let policyToDisplay = '';
    if (this.approval && this.approval.secPolicyId) {
      if (this.allPolicies && this.allPolicies.length > 0) {
        const filteredPolicy: SecurityPolicy[] = this.allPolicies.filter(p => p.secPolicyId === this.approval.secPolicyId);
        if (filteredPolicy && filteredPolicy.length > 0) {
          policyToDisplay = filteredPolicy[0].name;
        }
      } else {
        policyToDisplay = '' + this.approval.secPolicyId;
      }
    }
    return policyToDisplay;
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => {
      sub.unsubscribe();
    });
  }

}
