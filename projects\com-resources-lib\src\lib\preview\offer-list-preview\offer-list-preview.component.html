<section class="d-flex flex-column">
    <h3 class="heading" *ngIf="offerListData.type.id === 'STATIC'">{{translations.staticOfferList}}</h3>
    <h3 class="heading" *ngIf="offerListData.type.id !== 'STATIC'">{{translations.smartOfferList}}</h3>

    <section class="section">
        <h6 class="title">{{translations.summary}}</h6>
        <table>
            <tr>
                <td class="key ellipsis" hclTooltip="{{translations.name}}">{{translations.name}}</td>
                <td class="value ellipsis" hclTooltip="{{offerListData.displayName}}">{{offerListData.displayName}}</td>
            </tr>
            <!-- <tr *ngIf="offerListData.type.id !== 'STATIC'">
                <td class="key ellipsis" hclTooltip="{{translations.listLimit}}">{{translations.listLimit}}</td>
                <td class="value ellipsis" hclTooltip="{{offerListData.type.properties.size}}">{{offerListData.type.properties.size}}</td>
            </tr>
            <tr>
                <td class="key ellipsis" hclTooltip="{{translations.securityPolicy}}">{{translations.securityPolicy}}</td>
                <td class="value ellipsis" hclTooltip="{{offerListData.policyName}}">{{offerListData.policyName}}</td>
            </tr>
            <tr *ngIf="offerListData.type.id !== 'STATIC'">
                <td class="key ellipsis" hclTooltip="{{translations.orderBy}}">{{translations.orderBy}}</td>
                <td class="value ellipsis" hclTooltip="{{offerListData.displayOrderBy}}">{{offerListData.displayOrderBy}}</td>
            </tr> -->
            <tr>
                <td class="key ellipsis" hclTooltip="{{translations.description}}">{{translations.description}}</td>
                <td class="value ellipsis" hclTooltip="{{offerListData.description}}">{{offerListData.description}}</td>
            </tr>
            <tr>
                <td class="key ellipsis" hclTooltip="{{translations.status}}">{{translations.status}}</td>
                <td class="value ellipsis" hclTooltip="{{offerListData.state}}">{{offerListData.state}}</td>
            </tr>
        </table>
    </section>

    <section class="section" *ngIf="offerListData.type.id !== 'STATIC'">
        <h6 class="title">{{translations.criteria}}</h6>
        <table>
            <tr>
                <td class="key ellipsis" hclTooltip="{{translations.searchInTheFolder}}">{{translations.searchInTheFolder}}</td>
                <td class="value ellipsis" hclTooltip="{{folders}}">{{folders}}</td>
            </tr>
            <tr>
                <td *ngIf="offerListData.type.properties.includeSubFolders" class="key ellipsis w-100" hclTooltip="{{translations.subFoldersIncluded}}">{{translations.subFoldersIncluded}}</td>
                <td *ngIf="!offerListData.type.properties.includeSubFolders" class="key ellipsis w-100" hclTooltip="{{translations.subFoldersNotIncluded}}">{{translations.subFoldersNotIncluded}}</td>
            </tr>
            <tr>
                <td class="key ellipsis" hclTooltip="{{translations.conditions}}">{{translations.conditions}}</td>
                <td class="value ellipsis" hclTooltip="{{offerListData.displayOfferQuery}}">{{offerListData.displayOfferQuery}}</td>
            </tr>
        </table>
    </section>

    <section class="section">
        <h6 class="title">{{translations.offers}}</h6>
        <hcl-data-grid-v2 [config]="gridConfig" #dataGrid>
        </hcl-data-grid-v2>
    </section>
</section>