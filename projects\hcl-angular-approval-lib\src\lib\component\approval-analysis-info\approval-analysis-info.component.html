<div class="w-100 h-100 analysis-info-container">
    <div class="grid-container h-100">
        <hcl-data-grid-v2 [config]="analysisGridConf" (gridReady)="onGridReady($event)">
            <ng-template hclTemplate hclTemplateName="userCell" type="cell-renderer" let-cell>
                <span class="analysis-user-name">{{cell.row.user}}</span>
                <span class="analysis-user-name">{{cell.row.modifiedDate}}</span>
            </ng-template>
            <ng-template hclTemplate hclTemplateName="actionCell" type="cell-renderer" let-cell>
                <span hclTooltip="{{cell.row.action}}">
                    <hcl-smart-ellipse [text]="cell.row.action"></hcl-smart-ellipse>
                </span>
            </ng-template>
        </hcl-data-grid-v2>
    </div>
</div>