<hcl-donut-chart-v2 [chartConfig]="donutChartConfig"></hcl-donut-chart-v2>
<div class="component-container">
    <div class="legends">
        <div class="legend-container" *ngFor="let legend of donutChartConfig.data">
            <span class="legend-color" style="background-color: {{legend.color}};"></span>
            <span class="legend-label">{{legend.tooltipHeadingLabel}} {{legend.tooltipValueLabel}}</span>
        </div>
    </div>
    <br />
    <div class="legends">
        <div class="legend-container" *ngFor="let legend of donutChartProgressConfig.data">
            <span class="legend-color" style="background-color: {{legend.color}};"></span>
            <span class="legend-label">{{legend.tooltipHeadingLabel}} {{legend.tooltipValueLabel}}</span>
        </div>
    </div>
</div>
<hcl-donut-chart-v2 #progressD3DonutChart [chartConfig]="donutChartProgressConfig"></hcl-donut-chart-v2>
