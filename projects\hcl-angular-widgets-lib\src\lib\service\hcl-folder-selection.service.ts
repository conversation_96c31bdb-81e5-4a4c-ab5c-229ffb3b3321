import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class HclFolderSelectionService {
  private moveItems = new BehaviorSubject([]);
  private _moveFolderApiCallCount = 0;
  private _applicationBaseURL: string;
  private _headers;

  constructor(private http: HttpClient) {}

  sendItemsToMoveFolders(itemsArray: any[]) {
    this.moveItems.next([...itemsArray]);
  }

  clearItemsFromMoveFolders() {
    this.moveItems.next([]);
  }

  getItemsFromMoveFolders(): Observable<any> {
    return this.moveItems.asObservable();
  }

  get moveFolderApiCallCount(): number {
    return this._moveFolderApiCallCount;
  }

  set moveFolderApiCallCount(value: number) {
    this._moveFolderApiCallCount = value;
  }

  resetFolderData() {
    // this.listingDetails = new BehaviorSubject([]);
    // this.segmentsFolder = 3;
    this.moveItems = new BehaviorSubject([]);
    this.moveFolderApiCallCount = 0;
    this.clearItemsFromMoveFolders();
    // this.breadcrumbData = new BehaviorSubject([]);
  }

  get headers(): HttpHeaders {
    return this._headers;
  }

  set headers(value: HttpHeaders) {
    this._headers = value;
  }

  set applicationBaseURL(url: string) {
    this._applicationBaseURL = url;
  }
  get applicationBaseURL(): string {
    return this._applicationBaseURL;
  }

  public getFolders(id: number): Observable<any> {
    return this.http.get<any>(
      this.applicationBaseURL +
        "/api/segmentcentral/v1/folders/" +
        id +
        "/tree?objectType=FOLDERS",
      { headers: this.headers }
    );
  }
}
