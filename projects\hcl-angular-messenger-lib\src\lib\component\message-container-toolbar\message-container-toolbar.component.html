<div class="filter-container">
  <div class="filter-label">
    Filter
  </div>
  <div class="w-100 dropdown-container">
    <hcl-button [config]="filterButtonConfig" (onclick)="openFilterMenu($event)">
    </hcl-button>
    <hcl-menu [config]="filterMenuConfig" (menuClosed)="filterMenuClosed()"
              (itemClick)="setSelectedFilter($event)" #filterMenu>
    </hcl-menu>
  </div>
  <DIV class="search-item-container">
    <hcl-input [config]="searchBoxConfig"
               (keyup.enter)="searchOrCloseIconClick()"
               (valueEntered)="toggleIconOnChange()"
               (iconClick)="searchOrCloseIconClick()">
    </hcl-input>
  </DIV>
</div>
