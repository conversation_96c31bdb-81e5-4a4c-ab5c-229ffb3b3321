import { IpEmailBuilderService } from '../../../ip-email-builder.service';
import { RenderingClass, HtmlLayoutField, IHtmlFieldOptions } from '../interfaces';
import {
  createBorder,
  createLineHeight,
  createPadding,
  uniqueId
} from '../utils';

export class Html<PERSON>ield implements HtmlLayoutField, RenderingClass {
  constructor(public options: IHtmlFieldOptions, private _ngb: IpEmailBuilderService) { }

  render() {
    const {
      backgroundColor,
      border,
      color,
      font,
      align,
      lineHeight,
      padding,
      fullWidth,
      rules,
      id
    } = this.options;
    const borderStyle = (border.width) ? `border:"${createBorder(border)}"` : ``;
    const textId = uniqueId();
    let ruleMapId = "";
    if (rules && rules.length > 0) {
      this._ngb.setRuleMapper(textId, rules);
      ruleMapId = "ruleMap_" + textId;
    }
  const rawTemplate = `
  <mj-raw>
  <tr class="hide-on-${this.options.hideOn}">
    <td>
     <div class="ip-html-field"> 
     <!--[if mso]>
         <table role="presentation" border="0" cellpadding="0" cellspacing="0">

        <tr>
        <td style="padding: ${createPadding(padding)}; font-weight: ${font.weight};" >
    <![endif]-->
            <div class="droppable html-field-droppable ${ruleMapId} ${id ? '_ASSET_' + id : ''}">
              ${this.options.innerHtml}
            </div>
            <!--[if mso]>
        </td></tr></table>
    <![endif]-->
      </div>  
    </td>
  </tr>               
      </mj-raw> `;

    if (this.options.hideOn === 'desktop') {
      return `
        <mj-raw>
        <!--[if !mso]><!-- --></mj-raw>
        ${rawTemplate}
        <mj-raw><!--<![endif]--></mj-raw>
      `;
    } else {
      return rawTemplate;
    }
  }
}
