import { Component, OnInit } from "@angular/core";
import { weekConfig } from "projects/hcl-angular-widgets-lib/src/lib/component/week-selector/week-selector.config";

@Component({
  selector: "app-week-selector-demo",
  templateUrl: "./week-selector-demo.component.html",
  styleUrls: ["./week-selector-demo.component.scss"],
})
export class WeekSelectorDemoComponent implements OnInit {
  stringConfig: weekConfig = {
    minDays: 2,
    weekdays: [
      {
        dayId: "sunday",
        localeDay: "sun",
        disable: false,
        selected: true,
      },
      {
        dayId: "monday",
        localeDay: "mon",
        disable: false,
      },
      {
        dayId: "tuesday",
        localeDay: "tue",
        disable: false,
      },
      {
        dayId: "wednesday",
        localeDay: "wed",
        disable: false,
      },
      {
        dayId: "thursday",
        localeDay: "thru",
        disable: false,
      },
      {
        dayId: "friday",
        localeDay: "fri",
        disable: false,
      },
      {
        dayId: "saturday",
        localeDay: "sat",
        disable: true,
      },
    ],
  };
  numberConfig: weekConfig = {
    minDays: 5,
    weekdays: [
      {
        dayId: 1,
        localeDay: "mon",
        disable: false,
      },
      {
        dayId: 2,
        localeDay: "tue",
        disable: false,
      },
      {
        dayId: 3,
        localeDay: "wed",
        disable: false,
      },
      {
        dayId: 4,
        localeDay: "thru",
        disable: false,
      },
      {
        dayId: 5,
        localeDay: "fri",
        disable: false,
      },
      {
        dayId: 6,
        localeDay: "sat",
        disable: true,
      },
      {
        dayId: 7,
        localeDay: "sun",
        disable: false,
        selected: true,
      },
    ],
  };
  selectedDays1: string | number[] = [];
  selectedDays2: string | number[] = [];
  constructor() {}

  ngOnInit(): void {}

  weekDays1(event) {
    this.selectedDays1 = event;
  }
  weekDays2(event) {
    this.selectedDays2 = event;
  }
}
