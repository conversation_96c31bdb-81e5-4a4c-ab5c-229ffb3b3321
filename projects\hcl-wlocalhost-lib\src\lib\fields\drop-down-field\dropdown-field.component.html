

<div [ngStyle]="getParentStyles()" class="droppable">
    <div [ngStyle]="getAlignmentStyles()">
      <label [ngStyle]="getLabelStyles()" *ngIf="field.label" [for]="id" [ngClass]="{'required':field.options.isRequired}">{{ field.label }}</label>
      <div [ngStyle]="getInputFieldContainerStyles()">
        <select [ngStyle]="getInputFieldStyles()" [id]="id">
                <option *ngIf="field.placeholder" value="" disabled selected hidden>{{field.placeholder}}</option>
                <option  *ngFor="let opt of field.dropdownOptionList" value="{{opt.value}}" title="{{opt.label}}">
                  <span *ngIf="opt.isVisible || (ngjs.refreshEnabled && !ngjs.Email.version)">
                    {{opt.label}}
                  </span>
                </option>
        </select>
      </div>
    </div>
    <mat-error *ngIf="field.errors && field.errors.length > 0">
      <div class="error-container">
        <span class="hcl-icon-error" [matTooltip]="fetchComponentErrorMessages()" [matTooltipClass]="'multiline-tooltip'"></span>
      </div>
    </mat-error>
  </div>
