<div class="hcl-query-builder-v2">
  <!-- <div class="hcl-qb-v2-page-loader" *ngIf="inlineSpinnerConfig.isLoading">
        <hcl-progress-spinner [config]="inlineSpinnerConfig">
        </hcl-progress-spinner>
    </div> -->
  <div [formGroup]="queryBuilderForm" *ngIf="groupArray" [style.maxWidth.px]="1200"
    class="float-left w-100 h-100 position-relative" [ngClass]="{ disabled: config?.disableQueryBuilder }">
    <ng-container *ngFor="let group of groupArray; let i = index; let lastGroupItem = last">
      <ng-container [ngTemplateOutlet]="recursiveGroupTemplate" [ngTemplateOutletContext]="{
          group: group,
          groupCondition: group?.OR ? 'or' : 'and',
          gId: group.groupId,
          lastGroupItem: lastGroupItem,
          hasAdjacentGroup: groupArray[i + 1]?.OR || groupArray[i + 1]?.AND,
          firstChild: (group.OR && group.OR[0]) || (group.AND && group.AND[0]),
          parentConditionColor: group?.OR ? 'blue' : 'green'
        }">
      </ng-container>
      <ng-container *ngIf="!(group?.AND?.length || group?.OR?.length)"
        [ngTemplateOutlet]="noRulesTemplate"></ng-container>
    </ng-container>
  </div>
  <ng-container *ngIf="config.enableExpressionBuilder">
    <ng-template [ngTemplateOutlet]="expressionBuilderTemplate"></ng-template>
  </ng-container>
</div>

<ng-template #noRulesTemplate>
  <div class="no-rule-container float-left w-100">
    <span class="d-block text-center nr-h m-semibold mb-1">{{
      noRuleHeader
      }}</span>
    <span class="d-block text-center nr-b r-regular" [innerHTML]="noRuleContext"></span>
  </div>
</ng-template>

<ng-template #groupNameTemplate let-group="group">
  <span *ngIf="group.groupId !== 'group_1'" class="rule-group-name m-semibold ml-4">
    <span class="mx-1">{{ group.groupName }}</span>
    <span (click)="editGroupName(group)" class="cursor-pointer"><i class="hcl-icon-edit"></i></span>
  </span>
</ng-template>

<ng-template #recursiveGroupTemplate let-group="group" let-groupCondition="groupCondition" let-gId="gId"
  let-lastGroupItem="lastGroupItem" let-hasAdjacentGroup="hasAdjacentGroup" let-firstChild="firstChild"
  let-parentConditionColor="parentConditionColor">
  <div class="group-container {{ groupCondition }}-grp" id="{{ group.groupId }}" [ngClass]="{
      'group-container-collapsed':
        group?.groupId !== 'group_1' && expandCollapseObj[group?.groupId],
      'no-rules': !(group?.AND?.length || group?.OR?.length),
      'gc-or-condition': group.OR,
      'gc-and-condition': group.AND,
      'group-with-next-sibling': !lastGroupItem,
      'has-adjacent-group': hasAdjacentGroup,
      'has-group-as-firstchild':
        firstChild && (firstChild.OR || firstChild.AND),
      'parent-condition-green':
        group?.groupId !== 'group_1' && parentConditionColor === 'green',
      'parent-condition-blue':
        group?.groupId !== 'group_1' && parentConditionColor === 'blue'
    }">
    <ng-container *ngIf="group.AND">
      <div class="group-header" [ngClass]="{
          'no-expand-collapse': group?.groupId === 'group_1',
          'dynamic-header': groupHasDynamicRule[group?.groupId],
          'highlight-group-header': group?.groupId !== 'group_1' && checkHighlightedGroup(group)
        }">
        <div class="group-condition-symbol" *ngIf="group?.groupId !== 'group_1'">
          <div class="and-symbol">&</div>
        </div>
        <div id="{{ gId }}" class="condition-toggle r-regular">
          <span class="toggle-inner-container" *ngIf="showSwitch[group.groupId]">
            <span [ngClass]="{ 'selected-toggle': !group.toggleConfig.checked }">{{ config.translations.orLabel
              }}</span>
            <hcl-slide-toggle class="mx-2" [config]="group.toggleConfig" (toggleChange)="toggleChange($event, group)">
            </hcl-slide-toggle>
            <span [ngClass]="{ 'selected-toggle': group.toggleConfig.checked }">{{ config.translations.andLabel
              }}</span>
          </span>
          <ng-template *ngIf="config.needGroupName" [ngTemplateOutlet]="groupNameTemplate"
            [ngTemplateOutletContext]="{ group: group }"></ng-template>
          <div hclTooltip="{{ showSubgroupRuleCount }}" (mouseenter)="doCountRulesAndSubgroups(group)"
            (click)="doCollapse(group)" class="expand-collapse" *ngIf="group?.groupId !== 'group_1'" [ngClass]="{
              collapsed: expandCollapseObj[group?.groupId],
              expanded: !expandCollapseObj[group?.groupId]
            }">
            <i class="hcl-icon-add-button"></i>
            <i class="hcl-icon-expand-group"></i>
          </div>
        </div>
        <div class="action-buttons">
          <hcl-button [config]="addRuleBtnConfig" (onclick)="addRule(group)"></hcl-button>
          <hcl-button [config]="addGroupBtnConfig" (onclick)="addGroup(group)"></hcl-button>
          <hcl-button *ngIf="group.groupId !== 'group_1'" [config]="deleteGroupBtnConfig"
            (click)="deleteGroup(group)"></hcl-button>
        </div>
      </div>
      <div class="group-content">
        <ng-container *ngIf="!expandCollapseObj[group.groupId]">
          <div *ngFor="let ruleItem of group.AND; let i = index; let last = last" class="group-rules" [ngClass]="{
              'has-no-subgroup': !(ruleItem?.AND || ruleItem?.OR)
            }">
            <ng-container *ngIf="ruleItem.AND || ruleItem.OR">
              <ng-template [ngTemplateOutlet]="recursiveGroupTemplate" [ngTemplateOutletContext]="{
                  group: ruleItem,
                  groupCondition: 'and',
                  gId: group.groupId,
                  lastGroupItem: last,
                  hasAdjacentGroup:
                    group.AND[i + 1]?.OR || group.AND[i + 1]?.AND,
                  firstChild:
                    (ruleItem.OR && ruleItem.OR[0]) ||
                    (ruleItem.AND && ruleItem.AND[0]),
                  parentConditionColor: 'green'
                }">
              </ng-template>
            </ng-container>
            <ng-container *ngIf="!ruleItem.OR && !ruleItem.AND">
              <ng-template [ngTemplateOutlet]="singleQueryTemplate" [ngTemplateOutletContext]="{
                  group: group,
                  rule: ruleItem,
                  isLastItem: last
                }">
              </ng-template>
            </ng-container>
          </div>
        </ng-container>
      </div>
    </ng-container>

    <ng-container *ngIf="group.OR">
      <div class="group-header" [ngClass]="{
          'no-expand-collapse': group?.groupId === 'group_1',
          'dynamic-header': groupHasDynamicRule[group?.groupId]
        }">
        <div class="group-condition-symbol" *ngIf="group?.groupId !== 'group_1'">
          <div class="or-symbol">/</div>
        </div>
        <div id="{{ gId }}" class="condition-toggle r-regular">
          <span class="toggle-inner-container" *ngIf="showSwitch[group.groupId]">
            <span [ngClass]="{ 'selected-toggle': !group.toggleConfig.checked }">{{ config.translations.orLabel
              }}</span>
            <hcl-slide-toggle class="mx-2" [config]="group.toggleConfig" (toggleChange)="toggleChange($event, group)">
            </hcl-slide-toggle>
            <span [ngClass]="{ 'selected-toggle': group.toggleConfig.checked }">{{ config.translations.andLabel
              }}</span>
          </span>
          <ng-template *ngIf="config.needGroupName" [ngTemplateOutlet]="groupNameTemplate"
            [ngTemplateOutletContext]="{ group: group }"></ng-template>
          <div hclTooltip="{{ showSubgroupRuleCount }}" (mouseenter)="doCountRulesAndSubgroups(group)"
            (click)="doCollapse(group)" class="expand-collapse" *ngIf="group?.groupId !== 'group_1'" [ngClass]="{
              collapsed: expandCollapseObj[group?.groupId],
              expanded: !expandCollapseObj[group?.groupId]
            }">
            <i class="hcl-icon-add-button"></i>
            <i class="hcl-icon-expand-group"></i>
          </div>
        </div>
        <div class="action-buttons">
          <hcl-button [config]="addRuleBtnConfig" (onclick)="addRule(group)"></hcl-button>
          <hcl-button [config]="addGroupBtnConfig" (onclick)="addGroup(group)"></hcl-button>
          <hcl-button *ngIf="group.groupId !== 'group_1'" [config]="deleteGroupBtnConfig"
            (click)="deleteGroup(group)"></hcl-button>
        </div>
      </div>
      <div class="group-content">
        <ng-container *ngIf="!expandCollapseObj[group.groupId]">
          <div *ngFor="let ruleItem of group.OR; let i = index; let last = last" class="group-rules" [ngClass]="{
              'has-no-subgroup': !(ruleItem?.AND || ruleItem?.OR)
            }">
            <ng-container *ngIf="ruleItem.OR || ruleItem.AND">
              <ng-template [ngTemplateOutlet]="recursiveGroupTemplate" [ngTemplateOutletContext]="{
                  group: ruleItem,
                  groupCondition: 'or',
                  gId: group.groupId,
                  lastGroupItem: last,
                  hasAdjacentGroup: group.OR[i + 1]?.OR || group.OR[i + 1]?.AND,
                  firstChild:
                    (ruleItem.OR && ruleItem.OR[0]) ||
                    (ruleItem.AND && ruleItem.AND[0]),
                  parentConditionColor: 'blue'
                }">
              </ng-template>
            </ng-container>
            <ng-container *ngIf="!ruleItem.OR && !ruleItem.AND">
              <ng-template [ngTemplateOutlet]="singleQueryTemplate" [ngTemplateOutletContext]="{
                  group: group,
                  rule: ruleItem,
                  isLastItem: last
                }">
              </ng-template>
            </ng-container>
          </div>
        </ng-container>
      </div>
    </ng-container>
  </div>
</ng-template>

<ng-template #singleQueryTemplate let-group="group" let-rule="rule" let-isLastItem="isLastItem">
  <div class="single-query" id="{{ group.groupId }}" [ngClass]="{'dynamic-rule': supportDynamic?.[group?.groupId+'_rule_'+rule?.ruleId]?.isDynamic,
        'highlight-single-rule': checkHighlightedRule(group,rule), 'last-rule':isLastItem }">
    <div class="rule-condition-symbol">
      <div class="and-symbol" *ngIf="group?.AND">&</div>
      <div class="or-symbol" *ngIf="group?.OR">/</div>
    </div>
    <div class="query-fields-container d-flex">
      <div class="query-field query-column-type" *ngIf="config?.columnTypes?.length">
        <hcl-drop-down [config]="
        rulesConfigMap.get(
          group.groupId + '_rule_' + rule.ruleId + '_paramType'
        )
      " (select)="
      updateRuleParamType(
        $event,
        group.groupId + '_rule_' + rule.ruleId,
        group,
        rule
      )
    "></hcl-drop-down>
      </div>
      <div class="query-field query-column">
        <hcl-drop-down *ngIf="!rule.expressionField && !rule.selectBox && !rule.inputBox" (select)="
            updateRuleParameter(
              $event,
              rule,
              group.groupId + '_rule_' + rule.ruleId,
              group
            )
          " (panelStateChange)="resetOptions($event, group, rule)" [config]="
            rulesConfigMap.get(
              group.groupId + '_rule_' + rule.ruleId + '_param'
            )
          "
          (onIconClicked)="onIconClicked(rule)">
          >
          <ng-template hclTemplate hclTemplateName="dynamicAction">
            <hcl-input class="search-column-eqb" (keydown)="$event.stopPropagation()"
              (valueEntered)="filterOptions($event, group, rule)" (iconClick)="clearFieldNameSearch(group, rule)"
              [config]="
                rulesConfigMap.get(
                  group.groupId + '_rule_' + rule.ruleId + '_param_search'
                )
              ">
            </hcl-input>
            <ng-container *ngIf="rulesConfigMap.get(
                  group.groupId + '_rule_' + rule.ruleId + '_param'
                )?.options?.length === 0
              ">
              <span class="d-block">{{
                config.translations.noResultFoundMsg
                }}</span>
            </ng-container>
          </ng-template>
        </hcl-drop-down>
        <div *ngIf="config.enableExpressionBuilder && rule.expressionField && !rule.selectBox && !rule.inputBox"
          class="custom-expression-container">
          <div class="expression-label ellipsis" hclTooltip="{{ rule.expressionField }}">
            {{ rule.expressionField }}
          </div>
          <div class="icon-container">
            <i class="hcl-icon-edit" (click)="
                updateRuleParameter(
                  'expressionBuilder',
                  rule,
                  group.groupId + '_rule_' + rule.ruleId,
                  group
                )
              "></i>
          </div>
        </div>

        <hcl-select-box *ngIf="rule.selectBox && !rule.expressionField && !rule.inputBox" (select)="
          updateRuleParameter(
            $event,
            rule,
            group.groupId + '_rule_' + rule.ruleId,
            group
          )
        " class="float-left mb-0 w-100" [config]="
          rulesConfigMap.get(
            group.groupId + '_rule_' + rule.ruleId + '_param'
          )
        ">
        </hcl-select-box>

        <hcl-input *ngIf="!rule.selectBox && !rule.expressionField && rule.inputBox" class="float-left mb-0"
          (valueEntered)="updateRuleParameterForInput(
          $event,
          rule,
          group.groupId + '_rule_' + rule.ruleId
        )" [config]="
                    rulesConfigMap.get(
                      group.groupId + '_rule_' + rule.ruleId + '_param'
                    )
                    ">
        </hcl-input>

      </div>

      <div class="query-field query-operators" *ngIf="fieldTypeMap[rule?.param?.toLowerCase()] !== 'FOLDER'">
        <hcl-drop-down (select)="
            updateRuleOperator(
              $event,
              rule,
              rulesConfigMap.get(
                group.groupId + '_rule_' + rule.ruleId + '_value'
              ),
              group
            )
          " [config]="
            rulesConfigMap.get(group.groupId + '_rule_' + rule.ruleId + '_opt')
          ">
        </hcl-drop-down>
      </div>

      <div class="query-field query-value" *ngIf="
          !(
            rule.opt === 'null' ||
            rule.opt === 'notnull' ||
            rule.opt === 'true' ||
            rule.opt === 'false'
          )
        " [ngClass]="{
          'dynamic-value':
            supportDynamic[group.groupId + '_rule_' + rule.ruleId].isDynamic,
          'range-value': rule.opt === 'between' || rule.opt === 'notbetween'
        }">
        <ng-container *ngIf="
            supportDynamic[group.groupId + '_rule_' + rule.ruleId].isDynamic &&
            !(
              rule.opt === 'between' ||
              rule.opt === 'notbetween' ||
              rule.opt === 'in' ||
              rule.opt === 'notin'
            )
          ">
          <hcl-radio class="float-left dynamic-value-radio" (selectionChanged)="
              changeSelectionType(
                $event,
                rule,
                group.groupId,
                group.groupId + '_rule_' + rule.ruleId
              )
            " [config]="
              rulesConfigMap.get(
                group.groupId + '_rule_' + rule.ruleId + '_selectionType'
              )
            ">
            <ng-template hclTemplate hclTemplateName="staticvalueTemplate" type="content">
              <div [ngClass]="{
                  disableInput: !(
                    rulesConfigMap.get(
                      group.groupId + '_rule_' + rule.ruleId + '_selectionType'
                    ).formControl.value === 'staticvalue'
                  )
                }">
                <ng-container *ngIf="rule.datatype === 'Date' && config.showDatePicker">
                  <hcl-date-picker [localeJson]="config.dateConfigObj.dateLocaleJson"
                    class="dynamic-input-box ml-2 float-left extra-padding w-100" [config]="
                      rulesConfigMap.get(
                        group.groupId + '_rule_' + rule.ruleId + '_value'
                      )
                    " (onSubmitWithoutSelectionMode)="
                      updateRuleValue1(
                        $event,
                        rule,
                        group,
                        group.groupId + '_rule_' + rule.ruleId
                      )
                    " (onSubmitWithSelectionMode)="
                      updateRuleValue1(
                        $event,
                        rule,
                        group,
                        group.groupId + '_rule_' + rule.ruleId
                      )
                    ">
                  </hcl-date-picker>
                </ng-container>
                <ng-container *ngIf="rule.datatype === 'Date' && !config.showDatePicker">
                  <hcl-input *ngIf="
                      fieldTypeMap[rule?.param?.toLowerCase()] !== 'DROPDOWN' &&
                      fieldTypeMap[rule?.param?.toLowerCase()] !== 'FOLDER'
                    " class="dynamic-input-box ml-2 float-left" (valueEntered)="
                      updateRuleValue1(
                        $event,
                        rule,
                        group,
                        group.groupId + '_rule_' + rule.ruleId
                      )
                    " [config]="
                      rulesConfigMap.get(
                        group.groupId + '_rule_' + rule.ruleId + '_value'
                      )
                    ">
                  </hcl-input>
                </ng-container>

                <ng-container *ngIf="rule.datatype !== 'Date'">
                  <hcl-input *ngIf="
                      fieldTypeMap[rule?.param?.toLowerCase()] !== 'DROPDOWN' &&
                      fieldTypeMap[rule?.param?.toLowerCase()] !== 'FOLDER'
                    " class="dynamic-input-box ml-2 float-left" (valueEntered)="
                      updateRuleValue1(
                        $event,
                        rule,
                        group,
                        group.groupId + '_rule_' + rule.ruleId
                      )
                    " [config]="
                      rulesConfigMap.get(
                        group.groupId + '_rule_' + rule.ruleId + '_value'
                      )
                    ">
                  </hcl-input>
                  <hcl-drop-down *ngIf="
                      fieldTypeMap[rule?.param?.toLowerCase()] === 'DROPDOWN'
                    " class="dynamic-input-box ml-2 float-left" [config]="
                      rulesConfigMap.get(
                        group.groupId + '_rule_' + rule.ruleId + '_value'
                      )
                    " (panelStateChange)="resetOptions($event, group, rule)" (select)="
                      updateRuleValue1(
                        $event,
                        rule,
                        group,
                        group.groupId + '_rule_' + rule.ruleId
                      )
                    ">
                    <ng-template hclTemplate hclTemplateName="valueSearchtemp">
                      <hcl-input class="search-column-eqb" (keydown)="$event.stopPropagation()"
                        (valueEntered)="filterDropDownList($event, group, rule)"
                        (iconClick)="clearFilterDropDownList(group, rule)" [config]="
                          rulesConfigMap.get(
                            group.groupId + '_rule_' + rule.ruleId + '_value_search'
                          )
                        ">
                      </hcl-input>
                      <ng-container *ngIf="rulesConfigMap.get(
                            group.groupId + '_rule_' + rule.ruleId + '_value'
                          )?.options?.length === 0
                        ">
                        <span class="d-block">{{config.translations.noResultFoundMsg}}</span>
                      </ng-container>
                    </ng-template>
                  </hcl-drop-down>
                  <div (click)="openFolderTree(group)" class="folder-selection" *ngIf="
                      fieldTypeMap[rule?.param?.toLowerCase()] === 'FOLDER'
                    ">
                    <ng-container *ngIf="
                        rulesConfigMap.get(
                          group.groupId + '_rule_' + rule.ruleId + '_value'
                        ).formControlName?.value?.name
                      ">
                      <span class="folder-placeholder r-regular">{{
                        config.translations.folderName
                        }}</span>
                      <span hclTooltip="{{
                          rulesConfigMap.get(
                            group.groupId + '_rule_' + rule.ruleId + '_value'
                          ).formControlName?.value?.name
                        }}" class="ellipsis selected-folder r-regular pt-1">{{
                        rulesConfigMap.get(
                        group.groupId + "_rule_" + rule.ruleId + "_value"
                        ).formControlName?.value?.name
                        }}</span>
                    </ng-container>
                    <ng-container *ngIf="
                        !rulesConfigMap.get(
                          group.groupId + '_rule_' + rule.ruleId + '_value'
                        ).formControlName?.value?.name
                      ">
                      <span class="no-folder pb-1 r-regular">{{
                        config.translations.folderName
                        }}</span>
                      <span class="folder-required r-regular"><i class="hcl-icon-error"></i>{{
                        config.translations.requiredField }}</span>
                    </ng-container>
                  </div>
                </ng-container>
              </div>
            </ng-template>
            <ng-template hclTemplate hclTemplateName="dynamicvalueTemplate" type="content">
              <span class="or-span-text m-regular">{{
                config.translations.orLabel
                }}</span>
              <div [ngClass]="{
                  disableInput: !(
                    rulesConfigMap.get(
                      group.groupId + '_rule_' + rule.ruleId + '_selectionType'
                    ).formControl.value === 'dynamicvalue'
                  )
                }">
                <hcl-select-box *ngIf="
                    supportDynamic[group.groupId + '_rule_' + rule.ruleId]
                      .controlType === 'hcl-select-box'
                  " (select)="
                    updateRuleValue1(
                      $event,
                      rule,
                      group,
                      group.groupId + '_rule_' + rule.ruleId
                    )
                  " class="float-left w-75 ml-2 mb-0" [config]="
                    rulesConfigMap.get(
                      group.groupId + '_rule_' + rule.ruleId + '_dynamicValue'
                    )
                  ">
                </hcl-select-box>
                <!-- Dropdown for expression -->
                <hcl-drop-down *ngIf="
                    supportDynamic[group.groupId + '_rule_' + rule.ruleId]
                      .controlType === 'hcl-drop-down'
                  " (select)="
                    updateRuleValue1(
                      $event,
                      rule,
                      group,
                      group.groupId + '_rule_' + rule.ruleId
                    )
                  " class="float-left w-75 ml-2 mb-0" [config]="
                    rulesConfigMap.get(
                      group.groupId + '_rule_' + rule.ruleId + '_dynamicValue'
                    )
                  ">
                </hcl-drop-down>
                <!-- Normal HTML for expression -->
                <div *ngIf="
                    supportDynamic[group.groupId + '_rule_' + rule.ruleId]
                      .controlType === 'htmlDiv'
                  " class="custom-expression-container expression-value">
                  <div class="expression-label ellipsis" hclTooltip="{{ rule.dynamicValue }}">
                    {{ rule.dynamicValue }}
                  </div>
                  <div class="icon-container">
                    <i class="hcl-icon-edit" (click)="
                        updateRuleValue1(
                          'expressionBuilder',
                          rule,
                          group,
                          group.groupId + '_rule_' + rule.ruleId
                        )
                      "></i>
                  </div>
                </div>
              </div>
            </ng-template>
          </hcl-radio>
        </ng-container>
        <ng-container *ngIf="
            !supportDynamic[group.groupId + '_rule_' + rule.ruleId].isDynamic ||
            rule.opt === 'between' ||
            rule.opt === 'notbetween' ||
            rule.opt === 'in' ||
            rule.opt === 'notin'
          ">
          <ng-container *ngIf="rule.datatype === 'Date' && config.showDatePicker">
            <hcl-date-picker class="float-left mt-3 w-100" [localeJson]="config.dateConfigObj.dateLocaleJson" [config]="
                rulesConfigMap.get(
                  group.groupId + '_rule_' + rule.ruleId + '_value'
                )
              " (onSubmitWithoutSelectionMode)="
                updateRuleValue1(
                  $event,
                  rule,
                  group,
                  group.groupId + '_rule_' + rule.ruleId
                )
              " (onSubmitWithSelectionMode)="
                updateRuleValue1(
                  $event,
                  rule,
                  group,
                  group.groupId + '_rule_' + rule.ruleId
                )
              ">
            </hcl-date-picker>
          </ng-container>
          <ng-container *ngIf="rule.datatype === 'Date' && !config.showDatePicker">
            <hcl-input *ngIf="
                fieldTypeMap[rule?.param?.toLowerCase()] !== 'DROPDOWN' &&
                fieldTypeMap[rule?.param?.toLowerCase()] !== 'FOLDER'
              " (valueEntered)="
                updateRuleValue1(
                  $event,
                  rule,
                  group,
                  group.groupId + '_rule_' + rule.ruleId
                )
              " [config]="
                rulesConfigMap.get(
                  group.groupId + '_rule_' + rule.ruleId + '_value'
                )
              ">
            </hcl-input>
          </ng-container>
          <ng-container *ngIf="rule.datatype !== 'Date'">
            <hcl-input *ngIf="
                fieldTypeMap[rule?.param?.toLowerCase()] !== 'DROPDOWN' &&
                fieldTypeMap[rule?.param?.toLowerCase()] !== 'FOLDER'
              " (valueEntered)="
                updateRuleValue1(
                  $event,
                  rule,
                  group,
                  group.groupId + '_rule_' + rule.ruleId
                )
              " [config]="
                rulesConfigMap.get(
                  group.groupId + '_rule_' + rule.ruleId + '_value'
                )
              ">
            </hcl-input>
            <hcl-drop-down class="dynamic-input-box" *ngIf="fieldTypeMap[rule?.param?.toLowerCase()] === 'DROPDOWN'"
              [config]="
                rulesConfigMap.get(
                  group.groupId + '_rule_' + rule.ruleId + '_value'
                )
              " (panelStateChange)="resetOptions($event, group, rule)" (select)="
                updateRuleValue1(
                  $event,
                  rule,
                  group,
                  group.groupId + '_rule_' + rule.ruleId
                )
              ">
              <ng-template hclTemplate hclTemplateName="valueSearchtemp">
                <hcl-input class="search-column-eqb" (keydown)="$event.stopPropagation()"
                  (valueEntered)="filterDropDownList($event, group, rule)"
                  (iconClick)="clearFilterDropDownList(group, rule)" [config]="
                    rulesConfigMap.get(
                      group.groupId + '_rule_' + rule.ruleId + '_value_search'
                    )
                  ">
                </hcl-input>
                <ng-container *ngIf="rulesConfigMap.get(
                      group.groupId + '_rule_' + rule.ruleId + '_value'
                    )?.options?.length === 0
                  ">
                  <span class="d-block">{{config.translations.noResultFoundMsg}}</span>
                </ng-container>
              </ng-template>
            </hcl-drop-down>
            <div (click)="openFolderTree(group)" class="folder-selection"
              *ngIf="fieldTypeMap[rule?.param?.toLowerCase()] === 'FOLDER'">
              <ng-container *ngIf="
                  rulesConfigMap.get(
                    group.groupId + '_rule_' + rule.ruleId + '_value'
                  ).formControlName?.value?.name
                ">
                <span class="folder-placeholder r-regular">{{
                  config.translations.folderName
                  }}</span>
                <span hclTooltip="{{
                    rulesConfigMap.get(
                      group.groupId + '_rule_' + rule.ruleId + '_value'
                    ).formControlName?.value?.name
                  }}" class="ellipsis selected-folder r-regular pt-1">{{
                  rulesConfigMap.get(
                  group.groupId + "_rule_" + rule.ruleId + "_value"
                  ).formControlName?.value?.name
                  }}</span>
              </ng-container>
              <ng-container *ngIf="
                  !rulesConfigMap.get(
                    group.groupId + '_rule_' + rule.ruleId + '_value'
                  ).formControlName?.value?.name
                ">
                <span class="no-folder pb-1 r-regular">{{
                  config.translations.folderName
                  }}</span>
                <span class="folder-required r-regular"><i class="hcl-icon-error"></i>{{
                  config.translations.requiredField }}</span>
              </ng-container>
            </div>
          </ng-container>
        </ng-container>
      </div>

      <div class="sel-folder-btn-outer" *ngIf="fieldTypeMap[rule?.param?.toLowerCase()] === 'FOLDER'">
        <hcl-button [config]="changeFolderBtn" (click)="openFolderTree(group)"></hcl-button>
      </div>

      <span *ngIf="rule.opt === 'between' || rule.opt === 'notbetween'" class="to-label">{{ config.translations.toLabel
        }}</span>

      <div class="query-field range-value ml-4" *ngIf="rule.opt === 'between' || rule.opt === 'notbetween'">
        <ng-container *ngIf="rule.datatype === 'Date' && config.showDatePicker">
          <hcl-date-picker class="float-left mt-3 w-100" [localeJson]="config.dateConfigObj.dateLocaleJson" [config]="
              rulesConfigMap.get(
                group.groupId + '_rule_' + rule.ruleId + '_value2'
              )
            " (onSubmitWithoutSelectionMode)="
              updateRuleValue2(
                $event,
                rule,
                group.groupId,
                group.groupId + '_rule_' + rule.ruleId
              )
            " (onSubmitWithSelectionMode)="
              updateRuleValue2(
                $event,
                rule,
                group.groupId,
                group.groupId + '_rule_' + rule.ruleId
              )
            ">
          </hcl-date-picker>
        </ng-container>
        <ng-container *ngIf="rule.datatype === 'Date' && !config.showDatePicker">
          <hcl-input (valueEntered)="
              updateRuleValue2(
                $event,
                rule,
                group.groupId,
                group.groupId + '_rule_' + rule.ruleId
              )
            " [config]="
              rulesConfigMap.get(
                group.groupId + '_rule_' + rule.ruleId + '_value2'
              )
            ">
          </hcl-input>
        </ng-container>
        <ng-container *ngIf="rule.datatype !== 'Date'">
          <hcl-input (valueEntered)="
              updateRuleValue2(
                $event,
                rule,
                group.groupId,
                group.groupId + '_rule_' + rule.ruleId
              )
            " [config]="
              rulesConfigMap.get(
                group.groupId + '_rule_' + rule.ruleId + '_value2'
              )
            ">
          </hcl-input>
        </ng-container>
      </div>

      <div *ngIf="rule.opt === 'in' || rule.opt === 'notin'" class="in-opt-add-btn float-right mt-3">
        <hcl-button [config]="inOtpAddBtnConfig" (click)="
            addInOptValue(
              rulesConfigMap.get(
                group.groupId + '_rule_' + rule.ruleId + '_value'
              ),
              rule,
              group.groupId + '_rule_' + rule.ruleId + '_moreBtn'
            )
          ">
        </hcl-button>
      </div>
    </div>
    <div class="delete-rule" *ngIf="checkExistenceOfDeleteRuleButton()">
      <span hclTooltip="{{ config.translations.delete }}" class="hcl-icon-close"
        (click)="checkIfRuleIsExpression(group, rule)"></span>
    </div>
    <div *ngIf="rule.opt === 'in' || rule.opt === 'notin'" class="in-opt-values">
      <div class="in-opt-value" *ngFor="let val of rule.value | slice : 0 : 2; let i = index">
        <span *ngIf="rule.datatype === 'Date'" hclTooltip="{{ val | date : config.dateConfigObj.datePipeFormat }}"
          class="ellipsis">{{ val | date : config.dateConfigObj.datePipeFormat }}</span>
        <span *ngIf="rule.datatype !== 'Date'" hclTooltip="{{ val }}" class="ellipsis">{{ val }}</span>
        <span class="hcl-icon-close" (click)="
            removeInOptValue(
              i,
              rule,
              group.groupId + '_rule_' + rule.ruleId + '_moreBtn'
            )
          "></span>
      </div>
      <div class="more-values-button" *ngIf="rule.value.length > 2">
        <hcl-button [config]="
            rulesConfigMap.get(
              group.groupId + '_rule_' + rule.ruleId + '_moreBtn'
            )
          " (click)="
            openRuleValuesModal(
              rule,
              group.groupId + '_rule_' + rule.ruleId + '_moreBtn'
            )
          ">
        </hcl-button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #deleteSingleGroupDialogTemplate>
  <p>{{ config.translations.areYouSure }}</p>
  <p>{{ config.translations.deleteGroupModalMsg }}</p>
</ng-template>

<ng-template #deleteSingleGroupActionsTemplate>
  <div class="delete-nested-group">
    <hcl-button [config]="cancelModalBtnConf"
      (onclick)="modalService.closeDialog(); selectedRuleToViewInValues = null"></hcl-button>
    <hcl-button [config]="deleteBtnConf" (onclick)="
        modalService.closeDialog(); proceedToDeleteGroup(selectedGroupToDelete)
      ">
    </hcl-button>
  </div>
</ng-template>

<ng-template #deleteNestedGroupDialogTemplate>
  <p>{{ config.translations.areYouSure }}</p>
  <p>{{ config.translations.deleteNestedGroupsModalMsg }}</p>
</ng-template>

<ng-template #deleteNestedGroupActionsTemplate>
  <div class="delete-nested-group">
    <hcl-button [config]="cancelModalBtnConf"
      (onclick)="modalService.closeDialog(); selectedRuleToViewInValues = null"></hcl-button>
    <hcl-button [config]="deleteBtnConf" (onclick)="
        modalService.closeDialog(); proceedToDeleteGroup(selectedGroupToDelete)
      ">
    </hcl-button>
  </div>
</ng-template>

<ng-template #inOperatorRuleValuesActionsDialogTemplate>
  <hcl-button [config]="closeButtonConfig" (onclick)="modalService.closeDialog()">
  </hcl-button>
</ng-template>

<ng-template #inOperatorRuleValuesContentDialogTemplate>
  <div class="qb-in-notin-values-modal-container">
    <p class="values-modal-title">{{ getInOperatorPopupTitle() }}</p>
    <div class="opt-values-list">
      <div class="in-opt-value" *ngFor="let val of selectedRuleToViewInValues.rule.value; let i = index">
        <span *ngIf="selectedRuleToViewInValues.rule.datatype === 'Date'"
          hclTooltip="{{ val | date : config.dateConfigObj.datePipeFormat }}" class="ellipsis">{{ val | date :
          config.dateConfigObj.datePipeFormat }}</span>
        <span *ngIf="selectedRuleToViewInValues.rule.datatype !== 'Date'" hclTooltip="{{ val }}" class="ellipsis">{{ val
          }}</span>
        <span class="hcl-icon-close" (click)="
            removeInOptValue(
              i,
              selectedRuleToViewInValues.rule,
              selectedRuleToViewInValues.moreBtnConfigId
            )
          "></span>
      </div>
    </div>
  </div>
</ng-template>

<!-- Delete expression rule actions template -->
<ng-template #deleteExpressionRuleActionsTemplate>
  <div class="delete-nested-group">
    <hcl-button [config]="cancelModalBtnConf" (onclick)="modalService.closeDialog()"></hcl-button>
    <hcl-button [config]="deleteExpressionBtnConf" (onclick)="modalService.closeDialog(); proceedToDeleteExpression()">
    </hcl-button>
  </div>
</ng-template>

<!-- Expression builder side panel template -->
<ng-template #expressionBuilderTemplate>
  <hcl-side-bar [disableClose]="true" [hidden]="!showSideBar">
    <div class="d-flex h-100 flex-row expression-builder" *ngIf="showSideBar">
      <div class="slider-part" *ngIf="config?.expressionBuilderPanelConfig?.isChildSidePanel"
        (click)="closeSidebar(true)">
        <h3>{{ config?.expressionBuilderPanelConfig?.parentTitle }}</h3>
      </div>
      <aside class="h-100 side-panel-container overflow-hidden">
        <div class="h-100 w-100">
          <div class="w-100 header-container">
            <ng-container [ngTemplateOutlet]="
                config?.expressionBuilderPanelConfig?.headerTemplate
                  ? config.expressionBuilderPanelConfig.headerTemplate
                  : defaultHeader
              ">
            </ng-container>
          </div>
          <div class="row content-container m-0">
            <div class="col-12 custom-expression-upper-container p-0 position-relative">
              <div class="expression-input-title">
                {{ config.translations.expressionInputTitle }}
              </div>
              <div class="expression-input-help-msg">
                {{ config.translations.expressionInputHelpMsg }}
              </div>
              <div class="expression-text-area-container">
                <hcl-textarea [config]="textareaConfig" [hclSuggestions]="suggestionTextareaConfig"
                  (currentCaret)="getCursorPositionInTextareaControl($event)"></hcl-textarea>
              </div>
              <div class="check-syntax-btn-container position-absolute">
                <hcl-button [config]="checkSyntaxConfig" (onclick)="checkSyntax()"></hcl-button>
              </div>
            </div>
            <div class="col-10 expression-msg-container success-msg" *ngIf="showSyntaxIcon">
              <i class="hcl-icon-sms-delivered"></i>
              {{ config.translations.validExpression }}
            </div>
            <div class="col-10 expression-msg-container error-msg"
              *ngIf="expressionErrorMsg && expressionErrorMsg.length > 0">
              <i class="hcl-icon-error"></i>
              {{ expressionErrorMsg }}
            </div>
            <ng-container [ngTemplateOutlet]="
                config?.expressionBuilderPanelConfig?.contentTemplate
                  ? config.expressionBuilderPanelConfig.contentTemplate
                  : defaultContent
              ">
            </ng-container>
          </div>
          <div class="action-btn-container">
            <hcl-button class="label-text-button" [config]="saveConfig" (onclick)="saveExpression()">
            </hcl-button>
            <hcl-button class="label-text-button" [config]="cancelConfig" (onclick)="closeSidebar(true)">
            </hcl-button>
          </div>
        </div>
      </aside>
    </div>
  </hcl-side-bar>
</ng-template>

<!-- Default header for expression builder side panel -->
<ng-template #defaultHeader>
  <h3>{{ config.translations.expressionBuilderHeader }}</h3>
</ng-template>

<!-- Default content for expression builder side panel -->
<ng-template #defaultContent>
  <div>{{ config.translations.expressionBuilderBodyContent }}</div>
</ng-template>