import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from "@angular/core";
import { UntypedFormControl, UntypedFormGroup, Validators } from "@angular/forms";
import { QueryBuilderV2Component, QueryBuilderV2Conf } from "hcl-angular-widgets-lib";
import { HclSegmentationLibDataService } from "../../hcl-segmentation-lib-data.service";
import { HclSegmentationLibService } from "../../hcl-segmentation-lib.service";
import { PanelEntity, SidePanelDetails } from "../side-panel/side-panel-details";
import { UtilsService } from "../../utils.service";
import { DataProfileConfig } from "../../models/segment";
@Component({
  selector: "hcl-rule-builder",
  templateUrl: "./rule-builder.component.html",
  styleUrls: ["./rule-builder.component.scss"],
})
export class RuleBuilderComponent implements OnInit {
  @Input() config: QueryBuilderV2Conf;
  @Input() dpConfig: DataProfileConfig;
  @Input() applicationMode: "create" | "edit" | "createWithQbData";
  @Input() audienceInfo: { [key: string]: any };
  @Output() ruleBasedState: EventEmitter<boolean> = new EventEmitter<boolean>();
  @ViewChild('queryComp') queryComp: QueryBuilderV2Component;

  rulebasedSegmentForm: UntypedFormGroup;

  constructor(
    private dataService: HclSegmentationLibDataService,
    private hclSegmentationLibService: HclSegmentationLibService,
    private utils: UtilsService
  ) { }

  ngOnInit(): void {
    this.constructForm();
    this.rulebasedSegmentForm.valueChanges.subscribe((data) => {
      this.dataService.rulebasedSegmentForm = this.rulebasedSegmentForm;
    });


  }

  ngAfterViewInit() {
    if (this.applicationMode === "edit" || this.applicationMode === 'createWithQbData') {
      // this.queryComp.reRender(this.config.jsonData);
      this.ruleBasedState.emit(
        this.queryComp.getQbState()
      );

      this.rulebasedSegmentForm.patchValue({
        nestedCondition: this.generateConditionJson(this.config.jsonData),
      });

      this.dataService.rulebasedSegmentForm = this.rulebasedSegmentForm;
    }
  }

  constructForm() {
    this.rulebasedSegmentForm = new UntypedFormGroup({
      audienceName: new UntypedFormControl(
        this.audienceInfo.audienceName,
        Validators.required
      ),
      audienceTableId: new UntypedFormControl(
        this.audienceInfo.audienceTableId,
        Validators.required
      ),
      nestedCondition: new UntypedFormControl(null, Validators.required),
    });
  }

  queryBuilderJsonHandler(queryBuilderDetails) {
    // this.rulebasedSegmentForm.patchValue({
    //   nestedCondition: queryBuilderDetails.jsonData,
    // });
    // this.rulebasedSegmentForm.markAsDirty();
    // this.ruleBasedState.emit(
    //   this.rulebasedSegmentForm.valid &&
    //     queryBuilderDetails.jsonData &&
    //     queryBuilderDetails.qbFormIsValid
    // );

    this.rulebasedSegmentForm.patchValue({
      nestedCondition: this.generateConditionJson(queryBuilderDetails.jsonData),
    });
    this.rulebasedSegmentForm.markAsDirty();

    // this.hclSegmentationLibService.queryBuilderWidgetData = queryBuilderDetails;
    this.ruleBasedState.emit(
      this.rulebasedSegmentForm.valid &&
      queryBuilderDetails &&
      queryBuilderDetails.qbFormIsValid
    );
  }

  generateConditionJson(queryData: any) {
    return this.hclSegmentationLibService.generateQueryJson(
      queryData,
      this.audienceInfo.audienceTablePhysicalName
    );
  }

  paramDetailsIconClicked(event: any) {
    const column = this.config.columnFields.find((column) => {
      if(column.fieldValue === event.param && column.fieldDataType === event.datatype) {
        return column.fieldId;
      }
    });
    
    const detail: SidePanelDetails = {
      panel: PanelEntity.DATA_PROFILE,
      inputData: {
        fieldId: column.fieldId,
        columnName: column.fieldLabel,
        fieldDataType: column.fieldDataType,
        audienceTableId: this.audienceInfo.audienceTableId,
        dpConfig: this.dpConfig
      },
    };
    this.utils.openSidePanel(detail);
  }
}
