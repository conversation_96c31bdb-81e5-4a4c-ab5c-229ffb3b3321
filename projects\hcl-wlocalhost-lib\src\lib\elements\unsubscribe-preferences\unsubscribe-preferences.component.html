<div fxLayout [ngStyle]="getParentStyles()">
  <div [ngStyle]="getFullWidth()">
    <div [class.full-width]="block.options.fullWidth" [ngStyle]="getWebpageStyles()">
      <a *ngIf="block.isUnsubscribe" class="droppable">{{ block.unsubscribeLabel }}</a> 
      <span *ngIf="block.isUnsubscribe && block.isManagePreferences"> | </span> 
      <a *ngIf="block.isManagePreferences" class="droppable">{{ block.managePreferencesLabel }}</a>
    </div>
  </div>
</div>
<mat-error *ngIf="block.errors && block.errors.length > 0">
  <div class="error-container">
    <span class="hcl-icon-error" [matTooltip]="fetchComponentErrorMessages()" [matTooltipClass]="'multiline-tooltip'"></span>
  </div>
</mat-error>
