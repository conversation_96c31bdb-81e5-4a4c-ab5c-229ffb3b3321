import { Component, Input, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { LabelField } from '../../classes/Fields';
import {
  createFont,
  createPadding,
  createBorder,
  createLineHeight,
  createWidthHeight,
  createBackground,
  createButtonBackground
} from '../../utils';

@Component({
  selector: 'ip-label-field',
  templateUrl: './label-field.component.html',
  styleUrls: ['./label-field.component.css'],
  encapsulation: ViewEncapsulation.Emulated
})
export class LabelFieldComponent {
  @Input() field: LabelField;

  ngOnInit() {
    console.log(this.field);
  }
  constructor(private translate: TranslateService) {}

  getButtonFieldStyles() {
    const {
      align,
      color,
      font,
      lineHeight,
      fullWidth
    } = this.field.options;

    return {
      'text-align': align,
      color,
      width: fullWidth ? '100%' : 'auto',
      ...createFont(font),
      ...createLineHeight(lineHeight)
    };
  }

  getFullWidth() {
    const {
      fullWidth
    } = this.field.options;

    return {
      width: fullWidth ? '100%' : 'auto',
    };
  }

  getParentStyles() {
    const { align, border, padding } = this.field.options;

    return {
      'justify-content': (align === 'center' ? 'center' : (align === 'left' ? 'flex-start' : 'flex-end')),
      ...createBorder(border),
      ...createPadding(padding)
    };
  }
}
