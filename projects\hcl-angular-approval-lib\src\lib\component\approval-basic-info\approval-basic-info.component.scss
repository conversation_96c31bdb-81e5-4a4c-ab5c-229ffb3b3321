$title-font-color: #6d7692;
$title-font-family: Montserrat;
$value-font-color: #444444;
$value-font-family: Roboto;

.approval-basic-info-container {
    .approval-name {
        color: $title-font-color;
        font-family: $title-font-family;
        font-size: 20px;
        font-weight: bold;
        flex: 4;
    }
    .approval-status {
        width: 20%;
        text-align: right;
        font-size: 14px;
        flex: 1;
        .status-title {
            color: $title-font-color;
            font-family: $title-font-family;
        }
        .status-value {
            margin-left: 10px;
            font-weight: bold;
            color: $value-font-color;
            font-family: $value-font-family;
        }
    }
    .task-code-container span,
    .timezone-msg {
        font-size: 14px;
        color: $title-font-color;
        font-family: $title-font-family;
    }
    .text-ellipsis {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
}
