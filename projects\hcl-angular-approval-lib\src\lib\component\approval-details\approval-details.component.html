<div class="w-100 h-100 position-relative approval-details-container">
    <ng-container *ngIf="currentApproval else errorContainer">
        <div class="w-100 basic-info-wrapper">
            <hcl-approval-basic-info [approval]="currentApproval"></hcl-approval-basic-info>
        </div>
        <div class="w-100 approval-tabs-wrapper">
            <hcl-tabs [config]="approvalTabsConfig" orientation="horizontal" (selectedTabChange)="tabChanged($event)">
                <ng-template hclTemplate hclTemplateName="hTab1" type="tab-content">
                    <hcl-approval-general-info [approval]="currentApproval"
                        *ngIf="approvalTabsConfig.selectedTab === 0">
                    </hcl-approval-general-info>
                </ng-template>
                <ng-template hclTemplate hclTemplateName="hTab2" type="tab-content">
                    <hcl-approval-user-info [approval]="currentApproval" *ngIf="approvalTabsConfig.selectedTab === 1">
                    </hcl-approval-user-info>
                </ng-template>
                <ng-template hclTemplate hclTemplateName="hTab3" type="tab-content">
                    <hcl-approval-rules-info [approval]="currentApproval" *ngIf="approvalTabsConfig.selectedTab === 2">
                    </hcl-approval-rules-info>
                </ng-template>
                <ng-template hclTemplate hclTemplateName="hTab4" type="tab-content">
                    <hcl-approval-items-info [approval]="currentApproval" *ngIf="approvalTabsConfig.selectedTab === 3">
                    </hcl-approval-items-info>
                </ng-template>
                <ng-template hclTemplate hclTemplateName="hTab5" type="tab-content">
                    <hcl-approval-responder-history-info [approval]="currentApproval"
                        *ngIf="approvalTabsConfig.selectedTab === 4">
                    </hcl-approval-responder-history-info>
                </ng-template>
                <ng-template hclTemplate hclTemplateName="hTab6" type="tab-content">
                    <hcl-approval-analysis-info [approval]="currentApproval"
                        *ngIf="approvalTabsConfig.selectedTab === 5">
                    </hcl-approval-analysis-info>
                </ng-template>
                <!-- <ng-template hclTemplate hclTemplateName="hTab7" type="tab-content">
                <hcl-approval-dependency-info [approval]="currentApproval" *ngIf="approvalTabsConfig.selectedTab === 6">
                </hcl-approval-dependency-info>
            </ng-template> -->
            </hcl-tabs>
        </div>
    </ng-container>

    <ng-template #errorContainer>
        <div class="error-msg-container">
            <span *ngIf="!showErrorFlag">{{'APPROVALPICKER.TITLES.NO_RESULT_FOUND' | translate}}</span>
            <span *ngIf="showErrorFlag">{{errorMsg}}</span>
        </div>
    </ng-template>

    <div class="close-btn-container position-absolute">
        <hcl-button [config]="closeButtonConfig" (onclick)="closeInnerPanel()"></hcl-button>
    </div>
</div>