import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ApprovalGeneralInfoComponent } from './approval-general-info.component';

describe('ApprovalGeneralInfoComponent', () => {
  let component: ApprovalGeneralInfoComponent;
  let fixture: ComponentFixture<ApprovalGeneralInfoComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ApprovalGeneralInfoComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ApprovalGeneralInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
