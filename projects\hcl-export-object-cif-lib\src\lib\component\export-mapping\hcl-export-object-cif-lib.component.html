<div class="cif-export-object">
    <div *ngIf="!sourceOrTargetConfigMissing" class="cif-export-object__content-wrapper">
        <div class="cif-export-object__meta">
            <h2>{{config.translations.exportMappingAppTitle}}</h2>
            <hcl-input class="required" [config]="mappingNameInputConf" (inputBlured)="updateLabelValue($event)">
            </hcl-input>
        </div>
        <div class="cif-export-object__source-selection">
            <!-- <p class="section-title">{{config.translations.sourceSelectionTitle}}</p> -->
            <div class="source-subcategories">
                <hcl-drop-down class="required" (select)="srcSubCategoryChanged($event)"
                    [config]="srcSubCategoriesConfig">
                </hcl-drop-down>
            </div>
        </div>
        <div class="cif-export-object__target-selection">
            <p class="section-title">{{config.translations.targetSelectionTitle}}</p>
            <div class="target-category">
                <hcl-drop-down class="required" (select)="targetSystemChanged($event)"
                    [config]="targetSystemDropdownConfig">
                </hcl-drop-down>
            </div>

            <div class="category-folder-sec">
                <div class="category-input">
                    <hcl-input class="required" [config]="targetCategoryIdInputConf"> </hcl-input>
                </div>

                <div class="ml-4" *ngIf="!isViewMode">
                    <hcl-button (onclick)="openFolderCategorySelection(1)" [config]="browseButton1Conf">
                    </hcl-button>
                </div>
            </div>

            <div class="category-folder-sec">
                <div class="category-input">
                    <hcl-input class="required" [config]="targetObjectIdInputConf"> </hcl-input>
                </div>

                <div class="ml-4" *ngIf="!isViewMode">
                    <hcl-button (onclick)="openFolderCategorySelection(2)" [config]="browseButton2Conf">
                    </hcl-button>
                </div>
            </div>
        </div>
        <div class="cif-export-object__field-mappings">
            <ng-container *ngIf="readyToRenderMappingSec">
                <div class="titles">
                    <p class="section-title">{{config.translations.fieldMapping}}</p>
                    <p class="mapping-info">{{config.translations.mapFieldsToExternalSource}}</p>
                </div>

                <div class="mapping-area">
                    <div class="headers">
                        <div class="attr-header">
                            <span [attr.data-position]="'bottom-top-start'"
                                hclTooltip="{{config.translations.targetAppFieldTitle}}">
                                {{ config.translations.targetAppFieldTitle}}</span>
                        </div>
                        <div class="attr-header">
                            <span [attr.data-position]="'bottom-top-start'"
                                hclTooltip="{{config.translations.sourceAppFieldTitle}}">
                                {{config.translations.sourceAppFieldTitle}}</span>
                        </div>
                    </div>
                    <div class="attributes-mapping">
                        <hcl-export-cif-field-subscriptions [isEditMode]="isEditMode" [config]="config"
                            (mappedData)="updateMappedData($event)">
                        </hcl-export-cif-field-subscriptions>
                    </div>
                </div>
            </ng-container>
        </div>
    </div>
    <div *ngIf="sourceOrTargetConfigMissing" class="cif-export-object__config-missing">
        <p class="error-msg">{{config.translations.configurationsMissing}}</p>
    </div>
    <div class="cif-export-object__actions" [ngClass]="{'expanded': readyToRenderMappingSec}">
        <hcl-button *ngIf="!isViewMode" [config]="saveButtonConfig" (onclick)="saveExportObjectMapping()">
        </hcl-button>
        <hcl-button [config]="cancelButtonConfig" (onclick)="cancelMapping()">
        </hcl-button>
    </div>
</div>

<hcl-side-bar *ngIf="addCategorySelectionToDom">
    <div class="cif-object-mapping-category-selection-wrapper">
        <hcl-cif-folder-base-entities [config]="cifFolderBaseEntitiesConf" (selectedCifFbc)="updateCifFbs($event)"
            (closeCifFbs)="closeCifFbsSidebar(true)">
        </hcl-cif-folder-base-entities>
    </div>
</hcl-side-bar>