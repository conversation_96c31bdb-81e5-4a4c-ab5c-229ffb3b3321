export interface DataGridConfig {
  columnDefs: ColumnDefs[];
  rowData: any[];
}
export interface ColumnDefs {
  headerName: String;
  field: String;
  rowDrag?: Boolean;
  sortable?: Boolean;
  filter?: Boolean;
  checkboxSelection?: Boolean;
  filterParams?: any;
}

export interface ColumnSelectionConf {
  resetBtnLabel?: string;
  applyBtnLabel?: string;
  addRemoveLabel?: string;
  defaultColLabel?: string;
  defaultList?: { key: string; data: any }[];
  isSearchField?: boolean;
  searchLabel?: string;
}
