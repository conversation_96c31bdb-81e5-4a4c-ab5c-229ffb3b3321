<div class="donut-container" id="chartContainer">
  <div
    class="text-section"
    [ngClass]="{ 'with-count': segmentStats.segmentCount >= 0 }"
  >
    <div class="chart-message">
      <ng-container
        *ngIf="segmentStats.segmentCount < 0 && segmentType === 'RuleBased'"
      >
        {{ config.translations.buildRulesAndRunToView }}
      </ng-container>

      <ng-container *ngIf="segmentStats.segmentCount >= 0">
        <span class="count">
          {{ segmentStats.segmentCount }}
        </span>
        <span
          class="multiline-ellipsis"
          hclTooltip="{{
            translate.instant('MESSAGES.TOTAL_NUMBER_OF_RECORDS_ON_DATE', {
              value: segmentStats.totalCount,
              date:
                segmentStats.asOnTimeStamp
                | date : config.withoutTimeDatePipeFormat
            })
          }}"
        >
          {{
            translate.instant("MESSAGES.TOTAL_NUMBER_OF_RECORDS_ON_DATE", {
              value: segmentStats.totalCount,
              date:
                segmentStats.asOnTimeStamp
                | date : config.withoutTimeDatePipeFormat
            })
          }}
        </span>
      </ng-container>
    </div>
  </div>
  <div class="chart-section">
    <hcl-apache-chart [apacheChartConfig]="segmentDonutConfig"></hcl-apache-chart>
    <div class="donut-chart-text percent">
      <ng-container *ngIf="segmentStats.segmentCount >= 0">
        <span
          hclTooltip="{{ percentage }}"
          class="display-number-of-records ellipsis"
        >
          {{ percentage }}</span
        >
      </ng-container>
    </div>
    <div class="donut-chart-text">
      <ng-container *ngIf="segmentStats.segmentCount >= 0">
        <span
          class="multiline-ellipsis chart-message"
          hclTooltip="{{ config.translations.ofTotalRecords }}"
        >
          {{ config.translations.ofTotalRecords }}
        </span>
      </ng-container>

      <!-- <ng-container
        *ngIf="segmentStats.segmentCount < 0 && segmentType === 'RuleBased'"
      >
        <span
          class="multiline-ellipsis"
          hclTooltip="{{
            'MESSAGES.BUILD_RULES_AND_RUN_TO_VIEW_RESULT' | translate
          }}"
        >
          {{ "MESSAGES.BUILD_RULES_AND_RUN_TO_VIEW_RESULT" | translate }}
        </span>
      </ng-container>
  
      <ng-container
        *ngIf="segmentStats.segmentCount < 0 && segmentType === 'Composite'"
      >
        <span
          class="multiline-ellipsis"
          hclTooltip="{{
            'MESSAGES.ADD_SEGMENTS_AND_RUN_TO_VIEW_RESULT' | translate
          }}"
        >
          {{ "MESSAGES.ADD_SEGMENTS_AND_RUN_TO_VIEW_RESULT" | translate }}
        </span>
      </ng-container> -->
    </div>
  </div>
</div>
