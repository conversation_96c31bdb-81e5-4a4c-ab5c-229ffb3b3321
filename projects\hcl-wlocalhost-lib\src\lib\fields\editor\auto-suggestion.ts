import Quill from "quill";
let Inline = Quill.import("blots/inline");
export class AutoSuggestNode extends Inline {
  static blotName = "autosuggest";
  static className = "autosuggest";
  static tagName = "span";
  // the data object that we sent to this blot
  public contentData: any;

  constructor(name, value) {
    super(name, value);
    this.contentData = value;
  }

  /**
   * This function will actually create the bolt
   * param {{label: string; length: number}} value
   * returns {any}
   */
  static create(value: any) {
    let node = super.create(value);
    if (value?.label) {
      this.setNodeConfigurations(node, value);
    }
    return node;
  }

  /**
   * We will set some configurations on the node
   * param node
   * param value
   */
  static setNodeConfigurations(node, value) {
    node['innerHTML'] = value.label;
    node.setAttribute("data-text", value);
  }

  /**
   * Returns the format on the blot.
   * @param node 
   * @returns 
   */
  static formats(node) {
    return node.getAttribute("data-text");
  }
}