import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import { AfterViewInit, Component, ElementRef, EventEmitter, Input, NgZone, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { SideBarComponent, CheckboxConfig, ModalService, HclAssetPickerService } from 'hcl-angular-widgets-lib';
import { Offer, OfferListGetResponse } from '../../select-offers.model';
import { Router } from '@angular/router';
import { UntypedFormControl } from '@angular/forms';
import { filter, map, pairwise, throttleTime } from 'rxjs/operators';
import { OfferDataService } from '../../../offer-data.service';
import { TranslateService } from '@ngx-translate/core';
import { SelectOffersConstant } from '../../select-offers.constant';
import { SelectOffersService } from '../../../select-offers.service';

@Component({
  selector: 'hcl-offerlist-lp-card-list',
  templateUrl: './offerlist-lp-card-list.component.html',
  styleUrls: ['./offerlist-lp-card-list.component.scss']
})
export class OfferlistLpCardListComponent implements OnInit, AfterViewInit {

  @ViewChild(SideBarComponent) sideBarComponentRef: SideBarComponent;
  @ViewChild('scrollViewport') scrollViewport: CdkVirtualScrollViewport;

  @Input() offerlistResponseData: any;
  @Input() chunkSize: number;
  @Input() isDataLoading: boolean;
  @Output() loadNewOfferlists: EventEmitter<number> = new EventEmitter();
  @Output() offerListSelectionUpdate: EventEmitter<any> = new EventEmitter<any>();

  subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();


  loaderItems = [];
  addSideBarToDom = false;
  offerlistInfo: { offerlistName: string, offerlistId: number, count: number };


  constructor(
    private zone: NgZone,
    public router: Router,
    private offerDataService: OfferDataService,
    public modalService: ModalService,
    public translate: TranslateService,
    private selectOffersService: SelectOffersService,
    private hclAssetPickerService: HclAssetPickerService,
  ) { }

  ngAfterViewInit(): void {
    this.scrollViewport.elementScrolled().pipe(
      map(() => this.scrollViewport.measureScrollOffset('bottom')),
      pairwise(),
      filter(([y1, y2]) => (y2 < y1 && y2 < 100)),
      throttleTime(500)
    ).subscribe(() => {
      this.zone.run(() => {
        this.loadMoreOfferlists();
      });
    }
    );
  }

  ngOnInit(): void {
    if (this.selectOffersService.selectedOlData && this.selectOffersService.selectedOlData.length > 0) {
      this.offerListSelectionUpdate.emit();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['chunkSize'] && changes['chunkSize'].currentValue) {
      this.setLoaderItems();
    }
  }

  setLoaderItems() {
    this.loaderItems = [];
    for (let i = 0; i < 4; i++) {
      const dummyItems = '1'.repeat(this.chunkSize).split('');
      this.loaderItems.push(dummyItems);
    }
  }


  isSelectedItem(item) {
    return this.selectOffersService.selectedOlData &&
      this.selectOffersService.selectedOlData.findIndex(offerlistData => offerlistData.offerListId === item.id) !== -1;
  }

  // set checkbox config of current asset
  public setAssetCheckboxConf(item: any): CheckboxConfig {
    const checkboxConf: CheckboxConfig = {
      type: 'single',
      formControl: new UntypedFormControl(),
      singleCheckboxData: {
        value: item,
        name: 'offerlistSelection',
        color: 'primary',
        checked: item.isSelected,
        disabled: false
      }
    };

    checkboxConf.formControl.setValue(item.isSelected);
    return checkboxConf;
  }


  /**
 * Thumbnail Image loaded callback event
 * @param event
 * @param offer
 * @param imageElement
 */
  updateOfferThumbnailState(event: string, offer: Offer, imageElement: ElementRef) {
    if (event === 'error' && !offer.isImageLoaded) {
      // check if thumbnail is asset picker item and all properties has value 
      const { url, applicationId, objectType, objectId } = offer.thumbnailProperties;
      this.subscriptionList.push(this.offerDataService.getAssetPickerRepositories().subscribe((repos) => {
        if (repos) {
          const selectedRepoForPreview = repos.find(repo => repo.identifier === applicationId);
          const isAnonymousContent = selectedRepoForPreview && !selectedRepoForPreview.anonymousContent;
          if (isAnonymousContent && url && applicationId && objectType && objectId) {
            const resourceUrl = this.hclAssetPickerService.baseUrl + '/' + applicationId + '/download?resourceId=' + objectId +
              '&resource=' + url;
            this.offerDataService.downloadAssetPickerAnonymousContent(resourceUrl).then(data => {
              offer.isImageLoaded = true;
              imageElement['src'] = data;
            }, error => {
              offer.brokenThumbnail = true;
              offer.isImageLoaded = true;
            });
          } else {
            offer.brokenThumbnail = true;
            offer.isImageLoaded = true;
          }
        } else {
          this.offerDataService.getAndSetAssetPickerInstances(true);
        }
      }));
    } else {
      offer.isImageLoaded = true;
    }
  }

  getOfferCodeWithDelimiter(offerCodeArray) {
    return offerCodeArray.join(this.offerDataService.offerApplicationConfig.offerCodeDelimiter);
  }

  getStatusClass(offer: Offer) {
    const stateClass = offer.state === "PUBLISHED" ? 'published' : offer.state === "RETIRED" ? 'retired' : 'draft';
    return stateClass;
  }

  getStatusCopy(offer: Offer) {
    const stateClass = offer.state === "PUBLISHED" ? this.translate.instant('TITLES.PUBLISHED') :
      offer.state === "RETIRED" ? this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.HEADERS.RETIRED') : this.translate.instant('TITLES.DRAFT');
    return stateClass;
  }

  returnOfferListType(data) {
    if (data.id === SelectOffersConstant.staticOfferListCapital) {
      return this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.STATIC');
    } else {
      return this.translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.SMART');
    }
  }

  /*
   Check if user scroll has reached to bottom and data available in DB and trigger next page content
 */
  loadMoreOfferlists(): void {
    if (!this.isDataLoading) {
      if ((this.offerlistResponseData.page.pageNumber + 1) < this.offerlistResponseData.page.totalPages) {
        this.loadNewOfferlists.emit(this.offerlistResponseData.page.pageNumber + 1);
      }
    }
  }

  // card actions below
  offerlistClicked(item) {
    if (item.id) {
      this.selectOffersService.viewRoute = 'listOfferLists';
      this.selectOffersService.offerListId = +item.id;
      this.selectOffersService.sendViewOfferListClicked(+item.id);
    }
  }

  offerlistCheckboxClicked(event, offerlist) {
    if (event.checked) {
      offerlist.isSelected = true;
      this.cardSelected({ data: offerlist });
    } else {
      offerlist.isSelected = false;
      this.cardUnSelected({ data: offerlist });
    }
  }

  cardSelected(data: any) {
    if (data.data && !this.selectOffersService.selectedOlData.some(ol => ol.offerListId === data.data.id)) {
      let offerListData;

      offerListData = {
        offerListId: +data.data.id,
        offerListDisplayName: data.data.displayName,
        type: data.data.type.id,
        state: data.data.state
      };

      if (this.selectOffersService.rowSelectMode === 'single') {
        this.selectOffersService.selectedOlData = [offerListData];
      } else {
        this.selectOffersService.selectedOlData.push(offerListData);
      }
      this.offerListSelectionUpdate.emit();
    }
  }

  cardUnSelected(data: any) {
    if (data.data) {
      this.selectOffersService.selectedOlData.some((selectedData, index) => {
        if (+selectedData.offerListId === +data.data.id) {
          this.selectOffersService.selectedOlData.splice(index, 1);
          return;
        }
      });
      this.offerListSelectionUpdate.emit();
    }
  }


  openOffersDetailPane(offerlist: any) {
    this.offerlistInfo = { offerlistName: offerlist.displayName, offerlistId: offerlist.id, count: offerlist.count };
    this.addSideBarToDom = true;
    setTimeout(() => {
      this.sideBarComponentRef.openSideBar();
    }, 100);
  }

  closeSidebar() {
    this.addSideBarToDom = false;
    this.offerlistInfo = null;
  }

}
