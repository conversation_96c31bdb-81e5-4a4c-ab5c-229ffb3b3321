/*Examples for  https://echarts.apache.org/examples/en/index.html */

import { Component, OnInit, ViewChild } from '@angular/core';
import { ApacheColumnChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/apache-chart/apache-chart';
import { ApacheChartComponent } from 'projects/hcl-angular-charts-lib/src/public-api';

@Component({
  selector: 'app-apache-column-chart-demo',
  templateUrl: './apache-column-chart-demo.component.html',
  styleUrls: ['./apache-column-chart-demo.component.scss']
})
export class ApacheColumnChartDemoComponent implements OnInit {

  @ViewChild('apacheColumnChart1') apacheColumnChart1:ApacheChartComponent;
  @ViewChild('apacheColumnChart2') apacheColumnChart2:ApacheChartComponent;

  apacheColumnChartConfig1: ApacheColumnChartConfig =  {
    backgroundColor: 'transparent',
    legend: {
       show:true  // clicking on legend will show/hide the data
    },
    title:{
      id:"chart1",
      left: 50,
      text: ''
    },
    grid:  {
      left: 50,
      right: 50,
      top: 50,
      bottom: 50,
      containLabel: true
    }
   ,
    yAxis: {
        type: 'value',
        splitLine: { 
          show: true, 
          lineStyle: {
               type: 'dashed', // Type of the grid lines (solid, dashed, dotted)
               color: 'gray' // Color of the grid lines
          }
       },
    },
    dataZoom: [
        {
        show:true,
        xAxisIndex: Array[0],
        start: 15,   // starts showing data from {start}% of the data
        end : 63,    // ends showing data at {end}% of the data
        type:'inside',  // drag to view remaining data   
        zoomLock: true
        },
    ],
    tooltip: {
      showContent: true,
    },
    xAxis: {
        boundaryGap: true,
        axisTick: {
            interval: 0
        },
        axisLabel:{
            interval: 0,
            rotate: 90,
            margin: 8,
            fontSize: 12,
            fontFamily: 'Segoe UI',
            fontColor: '#444444'
        },
        type: 'category',
        data:[
          "Jan 2020",
          "Feb 2020",
          "Mar 2020",
          "Apr 2020",
          "May 2020",
          "Jun 2020",
          "Jul 2020",
          "Aug 2020",
          "Sep 2020",
          "Oct 2020",
          "Nov 2020",
          "Dec 2020",
          "Jan 2021",
          "Feb 2021",
          "Mar 2021",
          "Apr 2021",
          "May 2021",
          "Jun 2021",
          "Jul 2021",
          "Dec 2022",
          "Jan 2023",
          "Feb 2023",
          "Mar 2023",
          "Apr 2023",
          "May 2023",
          "Jun 2023",
          "Jul 2023",
          "Aug 2023",
          "Sep 2023",
          "Oct 2023",
          "Nov 2023",
          "Dec 2023",
          "Jan 2024"
        ]
 
    },
    series : [
    {
      name: "Sales Target achieved",
      type: "bar",
      stack : 'true',
      barMaxWidth: '200',
      itemStyle: {
          color:'#008000'
      },
      data:[
        76, 148, 111, 88, 57, 174, 95, 179, 123, 45, 67, 183, 157, 121, 166, 83, 
        195, 102, 69, 139, 178, 91, 36, 65, 146, 160, 171, 38, 77, 143, 134, 199,89
      ]
      
    },
    {
      name: "Sales Target remaining",
       type: "bar",
      stack: 'true',
      barMaxWidth: '200',
      itemStyle: {
          color: '#4169E1'
      },
      data:[
        164, 92, 58, 137, 109, 121, 75, 91, 185, 76, 47, 168, 172, 158, 178, 100, 
        155, 143, 189, 45, 60, 87, 167, 123, 98, 54, 191, 176, 135, 81, 174, 63,45
      ]
      
    }
  ]
   };


   apacheColumnChartConfig2: ApacheColumnChartConfig =  {
    legend: {
       show:true  // clicking on legend will show/hide the data
    },
    title:{
      id:"chart2",
      left: 50,
      text: ''
    },
    grid:  {
      left: 50,
      right: 50,
      top: 50,
      bottom: 50,
      containLabel: true
    }
   ,
    yAxis: {
        type: 'value',
        splitLine: { 
          show: true, 
          lineStyle: {
               type: 'dashed', // Type of the grid lines (solid, dashed, dotted)
               color: 'gray' // Color of the grid lines
          }
       },
    },
    dataZoom: [
        {
        show:true,
        xAxisIndex: Array[0],
        start: 80,   // starts showing data from {start}% of the data
        end : 100,    // ends showing data at {end}% of the data
        type:'slider',  // drag to view remaining data   
        },
    ],
    tooltip: {
      showContent: true,
    },
    xAxis: {
        boundaryGap: true,
        axisTick: {
            interval: 0
        },
        axisLabel:{
            interval: 0,
            rotate: 90,
            margin: 8,
            fontSize: 12,
            fontFamily: 'Segoe UI',
            fontColor: '#444444'
        },
        type: 'category',
        data:[
          "Jan 2020",
          "Feb 2020",
          "Mar 2020",
          "Apr 2020",
          "May 2020",
          "Jun 2020",
          "Jul 2020",
          "Aug 2020",
          "Sep 2020",
          "Oct 2020",
          "Nov 2020",
          "Dec 2020",
          "Jan 2021",
          "Feb 2021",
          "Mar 2021",
          "Apr 2021",
          "May 2021",
          "Jun 2021",
          "Jul 2021",
          "Dec 2022",
          "Jan 2023",
          "Feb 2023",
          "Mar 2023",
          "Apr 2023",
          "May 2023",
          "Jun 2023",
          "Jul 2023",
          "Aug 2023",
          "Sep 2023",
          "Oct 2023",
          "Nov 2023",
          "Dec 2023",
          "Jan 2024"
        ]
 
    },
    series : [
    {
      name: "Sales Target achieved",
      type: "bar",
      stack : 'true',
      barMaxWidth: '200',
      itemStyle: {
          color:'#DAA520'
      },
      data:[
        195, 102, 69, 139, 178, 91, 36, 65, 146, 160, 171, 38, 77, 143, 134, 199,89,
        76, 148, 111, 88, 57, 174, 95, 179, 123, 45, 67, 183, 157, 121, 166, 83,
      ]
      
    },
    {
      name: "Sales Target remaining",
       type: "bar",
      stack: 'true',
      barMaxWidth: '200',
      itemStyle: {
          color: '#6A5ACD'
      },
      data:[
        155, 143, 189, 45, 60, 87, 167, 123, 98, 54, 191, 176, 135, 81, 174, 63,45,
        164, 92, 58, 137, 109, 121, 75, 91, 185, 76, 47, 168, 172, 158, 178, 100
      ]
      
    }
  ]
   };

  
ngAfterViewInit(): void {
  window.setTimeout(() => {
  this.apacheColumnChartConfig1.title['text']= 'Sales Graph for 2020-2024' ;
  this.apacheColumnChart1.updateChart();
  }, 1000)
}


 generate() {
  console.log(this.apacheColumnChart1.generateImageUrl());
 }
  constructor() { }

  ngOnInit(): void {
  }

}
