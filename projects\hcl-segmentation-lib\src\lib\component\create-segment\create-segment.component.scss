@use '../../assets/scss/variables' as *;
.create-segment {
  background-color: $card-background;
  padding: 30px;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .loader-sec {
    display: flex;
    height: 100%;
    justify-content: center;
    align-items: center;
  }

  &__title-n-meta {
    height: 110px;
    padding-top: 15px;

    .page-title {
      color: #6d7692;
      font-family: $font-primary;
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 24px;
    }

    .audience-meta {
      padding-top: 20px;
      display: flex;

      .meta-label {
        color: #6d7692;
        font-family: $font-secondary;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        min-width: 100px;
      }

      .meta-value {
        color: #444444;
        font-family: $font-secondary;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 16px;
        padding-left: 15px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .audience-level {
        min-width: 30%;
        max-width: 50%;
        display: flex;
      }

      .audience-table {
        flex: 1;
        display: flex;
        padding-left: 15px;
      }
    }
  }

  &__content {
    height: calc(100% - 170px);
    display: flex;

    .rule-builder {
      width: calc(100% - 510px);
      height: 100%;
      background-color: $card-background;
      border: 1px solid $tango-border-color;
      border-radius: $border-radius-8;
      padding: 20px;

      .rb-title {
        color: #6d7692;
        font-family: $font-primary;
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 0;
        line-height: 19px;
      }

      .rule-builder-container {
        padding-top: 5px;
        height: calc(100% - 25px);
      }
    }

    .segment-meta-section {
      width: 500px;
      height: 100%;
      margin-left: 10px;

      .donut-chart {
        width: 100%;
        height: 200px;
        overflow: hidden;
        margin-bottom: 10px;
        background-color: $card-background;
        border: 1px solid $tango-border-color;
        border-radius: $border-radius-8;
        position: relative;

        .chart-title {
          position: absolute;
          z-index: 1;
          top: 20px;
          left: 20px;
          color: #6d7692;
          font-family: $font-primary;
          font-size: 16px;
          font-weight: 600;
          letter-spacing: 0;
          line-height: 19px;
        }
      }

      .segment-metadata {
        width: 100%;
        height: calc(100% - 210px);
        padding: 20px;
        overflow: auto;
        background-color: $card-background;
        border: 1px solid $tango-border-color;
        border-radius: $border-radius-8;
      }
    }
  }

  &__actions {
    height: 60px;
    padding-top: 20px;
    display: flex;
    flex-flow: row-reverse;
    align-items: flex-end;

    hcl-button {
      margin-left: 20px;
    }
  }
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
