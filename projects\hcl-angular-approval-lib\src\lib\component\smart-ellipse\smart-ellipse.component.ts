import { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';

@Component({
  selector: 'hcl-smart-ellipse',
  templateUrl: './smart-ellipse.component.html',
  styleUrls: ['./smart-ellipse.component.scss'],
  inputs: ['text'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SmartEllipseComponent implements OnInit {

  public fullText: string;
  public leftText: string;
  public rightText: string;

  constructor() {
    this.leftText = '';
    this.rightText = '';
  }

  set text(value: string) {
    const splitIndex = Math.round(value.length * 0.75);

    this.fullText = value;
    this.leftText = value.slice(0, splitIndex);
    this.rightText = value.slice(splitIndex);

  }

  ngOnInit(): void {
  }

}
