.popoverContainer {
    z-index: 1001;
    position: relative;
    background: none;
    width: 308px;
    max-height: 310px;
    left: -50px;
    top: 15px;
}
.popoverContent {
    background-color: #ffffff;
    box-shadow: 0 1px 10px 0 rgba(0,0,0,0.16);
}
.triangle {
    width: 0;
    height: 0;
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-bottom: 11px solid #ffffff;
    position: relative;
    left: 13%;
}
.popoverHeader {
    height: 40px;
    border-bottom: 1px solid #D9D9D9;
}
.popoverHeader li {
    height: 100%;
    border-bottom: 2px solid #0066B3;	
    color: #686868;	
    font-family: Roboto-regular,Roboto, sans-serif;
    font-size: 14px;	
    font-weight: 500;	
    line-height: 17px;	
    text-align: center;
    list-style: none;
    width: 60px;
    margin-right: 30px;
    margin-top: 2px;
    padding: 17px 13px 5px 13px;
}
.popoverHeader > ul {
    height: 100%;
    width: 100%;
    margin: 0px 15px;
    padding: 0px;
    display: flex;
}
.popoverButton {
    height: 36px;
    border-top: 1px solid #D9D9D9;
    padding: 10px 0px;
}
.filterBtn {
    height: 16px;	
    width: 50%;	
    color: #0066B3;	
    font-size: 13px;	
    font-weight: bold;	
    line-height: 16px;	
    text-align: left;
}
.filterBtn.apply {
    text-align: right;
}
.popoverContentDetails {
    padding: 10px;
    overflow: auto;
    height: 160px;
    max-height: 223px;
}
.searchContact {
    width: 100%;
}