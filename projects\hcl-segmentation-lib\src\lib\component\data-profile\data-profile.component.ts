import { HttpHeaders } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ButtonConf, NotificationService, ProgressSpinner } from 'hcl-angular-widgets-lib';
import { HclSegmentationLibService } from '../../hcl-segmentation-lib.service';
import { AudienceApplicationConfig } from '../../models/segment';

@Component({
  selector: 'hcl-data-profile',
  templateUrl: './data-profile.component.html',
  styleUrls: ['./data-profile.component.scss']
})
export class DataProfileComponent implements OnInit {
  @Input() dPData: any;
  @Output() sidebarClosed: EventEmitter<any> = new EventEmitter<any>();
  closeConfig: ButtonConf;
  private loginAttempts = 0;
  private audLoginAttempts = 0;
  private MAX_LOGIN_ATTEMPTS = 2;
  errorResponse: any;
  errorMessage: string = '';

  dataProfileSpinner: ProgressSpinner = {
    isInline: true,
    isLoading: false,
    color: 'primary',
    mode: 'indeterminate',
    value: 50,
    diameter: 50,
    strokeWidth: 3
  }


  constructor(
    private hclSegmentationLibService: HclSegmentationLibService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.dataProfileSpinner.isLoading = true;
    if (this.dPData.dpConfig.dpApplicationMode === 'dataProfileFromCreateSegment') {
      this.hclSegmentationLibService.segmentBaseUrl = this.dPData.dpConfig.segmentBaseUrl;
      this.updateAudienceToken();
    } else {
      this.dataProfileSpinner.isLoading = false;
    }
    this.setConfiguration();
  }
  setConfiguration() {
    this.closeConfig = {
      name: 'close',
      value: this.dPData.dpConfig.translations.close,
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      styleClass: 'large-btn',
      borderRadius: 5,
    };
  }

  updateAudienceToken(callback?) {
    this.hclSegmentationLibService
      .getAudienceToken()
      .subscribe((audConfig: AudienceApplicationConfig) => {
        this.loginAttempts = 0;
        const audHeaders = new HttpHeaders()
          .set("m_user_name", this.dPData.dpConfig.applicationUser)
          .set("m_tokenId", audConfig.token)
          .set("api_auth_mode", "manager")
          .set("401", "ignore")
          .set("403", "ignore");

        this.hclSegmentationLibService.audienceHeaders = audHeaders;
        this.hclSegmentationLibService.audienceBaseUrl = audConfig.serverURL;
        if (callback) {
          callback();
        }
        this.dataProfileSpinner.isLoading = false;
      }, this.handleServerError.bind(this, this.updateAudienceToken.bind(this), "seg"));
  }

  /**
  * In case error from server this function will be called
  * param error
  */
  private handleServerError(callback, context, err) {
    if (err.status === 401) {
      if (context === "seg" && this.loginAttempts < this.MAX_LOGIN_ATTEMPTS) {
        // we have to a login again
        this.reLogin(callback);
      } else if (
        context === "aud" &&
        this.audLoginAttempts < this.MAX_LOGIN_ATTEMPTS
      ) {
        this.audLoginAttempts++;
        this.updateAudienceToken(callback);
      } else {
        this.notificationService.show({
          message: err.message
            ? err.message
            : this.dPData.dpConfig.translations.unableToFetchData,
          type: "error",
          close: true,
          autoHide: 6000,
        });
      }
    } else if (err.status === 403) {
      this.notificationService.show({
        message: err.error["403"][0]
          ? err.error["403"][0]
          : this.dPData.dpConfig.translations.unableToFetchData,
        type: "error",
        close: true,
        autoHide: 6000,
      });
    } else if (err.status === 400) {
      // bad request error
      const error: any = err.error,
        errArray: { filed: string; messages: string[] }[] = [];

      for (const x in error) {
        if (error.hasOwnProperty(x)) {
          const field: string =
            x.indexOf('[') > -1
              ? x.substring(0, x.lastIndexOf('['))
              : x,
            errorMessages: string[] = [];

          error[x].forEach((s: any) => {
            errorMessages.push(s);
          });
          errArray.push({
            filed: field,
            messages: errorMessages,
          });
        }
      }

      errArray.forEach((element) => {
        this.notificationService.show({
          message: `${element.messages.join(", ")}`,
          type: "error",
          close: true,
          autoHide: 6000,
        });
      });
    } else {

      if (
        err.error &&
        err.error[err.status] &&
        Array.isArray(err.error[err.status])
      ) {
        if (err.error[err.status] && err.error[err.status][0]) {
          if (err.error[422]) {
            this.errorResponse = err.error;
            this.notificationService.show({
              message: err.error[err.status].toString(),
              type: 'warning',
              close: true,
              autoHide: 6000,
            });
          } else {
            // errorShown = true;
            err.error[err.status].forEach((element: any) => {
              this.notificationService.show({
                message: element,
                type: 'error',
                close: true,
                autoHide: 6000,
              });
            });
          }

        }
      } else if (err.message) {
        this.notificationService.show({
          message: err.message,
          type: "error",
          close: true,
          autoHide: 6000,
        });
      }
    }
  }

  /**
   * In case there is a un auth error we can do a  relogin to get the new token
   */
  private reLogin(callbackFunction: any): void {
    this.loginAttempts++;
    // check if we have a relogin method
    if (this.dPData.dpConfig.reLogin) {
      this.dPData.dpConfig.reLogin(this.reLoginSuccess.bind(this, callbackFunction));
    } else {
      this.notificationService.show({
        message: this.dPData.dpConfig.translations.unableToFetchData,
        type: "error",
        close: true,
        autoHide: 6000,
      });
    }
  }

  /**
   * called when the relogin is successful from the caller
   */
  public reLoginSuccess(callback): void {
    if (callback) {
      callback();
    }
  }

  handleErrorMessage(err: any) {
    if (err && err[400]) {
      this.errorMessage = err[400];
    }
  }

  close() {
    this.sidebarClosed.emit();
  }

}
