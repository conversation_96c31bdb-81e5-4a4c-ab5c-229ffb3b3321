import { Component, OnInit, ViewChild } from '@angular/core';
import { ButtonConf, QueryBuilderComponent, QueryBuilderConf } from 'projects/hcl-angular-widgets-lib/src/public_api';

@Component({
  selector: 'app-query-builder-demo',
  templateUrl: './query-builder-demo.component.html',
  styleUrls: ['./query-builder-demo.component.scss']
})
export class QueryBuilderDemoComponent implements OnInit {

  // { 'param': 'LastName', 'opt': 'endswith', 'value': 'aa', 'datatype': 'STRING' },
  //     {
  //       'OR': [{ 'param': 'Email', 'opt': 'contains', 'value': '.com', 'datatype': 'STRING', 'dateFormat': '' },
  //       { 'param': 'ID', 'opt': 'gt', 'value': '00', 'datatype': 'NUMBER', 'dateFormat': '' },
  //       {
  //         'AND': [{ 'datatype': "NUMBER", 'dateFormat': "", 'opt': "in", 'param': "ID", 'value': ['12', '14'] },
  //         { 'datatype': "DATE", 'dateFormat': "dd/mm/yyyy", 'opt': "lt", 'param': "date", 'value': '1580495400000' }]
  //       }
  //       ]
  //     }
  config: QueryBuilderConf = {
    jsonData: {
      'OR': [
        { 'param': 'Email', 'opt': 'contains', 'value': '.com', 'datatype': 'STRING', 'dateFormat': '' },
        { 'param': 'ID', 'opt': 'gt', 'value': '00', 'datatype': 'NUMBER', 'dateFormat': '' },
        {
          'AND': [{ 'datatype': "NUMBER", 'dateFormat': "", 'opt': "in", 'param': "ID", 'value': ['12', '14'] },
          { 'datatype': "DATE", 'dateFormat': "dd/mm/yyyy", 'opt': "lt", 'param': "date", 'value': '1580495400000' }]
        }
      ]
    },
    deleteRuleIcon: 'hcl-icon-close',
    columnFields: [
      { fieldName: "FirstName", dateFormat: "", fieldDataType: "String", fieldLength: 1000, regex: null },
      { fieldName: "LastName", regex: null, fieldLength: 1000, fieldDataType: "String", dateFormat: "" },
      { fieldName: "Email", regex: "EMAIL_ID", fieldDataType: "String", dateFormat: "", fieldLength: 1000 },
      { fieldName: "ID", regex: '[^[0-9]*\.?[0-9]*]', fieldDataType: "Numeric", dateFormat: "", fieldLength: 1000 },
      { fieldName: "date", regex: null, fieldDataType: "Date", dateFormat: "dd/mm/yyyy", fieldLength: 1000 }
    ],
    conditionConfigStringType: [{ label: 'is equal to', value: 'eq' },
    { label: 'not equal to', value: 'neq' },
    { label: 'begins with', value: 'beginswith' },
    { label: 'ends with', value: 'endswith' },
    { label: 'contains', value: 'contains' },
    { label: 'in', value: 'in' },
    { label: 'is null', value: 'null' },
    { label: 'is not null', value: 'notnull' }],
    conditionConfigNumberType: [{ label: 'is equal to', value: 'eq' },
    { label: 'not equal to', value: 'neq' },
    { label: 'is null', value: 'null' },
    { label: 'is not null', value: 'notnull' },
    { label: 'is greater than', value: 'gt' },
    { label: 'is greater than or equal to', value: 'gte' },
    { label: 'is less than', value: 'lt' },
    { label: 'is less than or equal to', value: 'lte' },
    { label: 'is between', value: 'between' },
    { label: 'is not in between', value: 'notbetween' },
    { label: 'in', value: 'in' },
    { label: 'not in', value: 'notin' }],
    conditionConfigDateType: [{ label: 'is equal to', value: 'eq' },
    { label: 'not equal to', value: 'neq' },
    { label: 'after', value: 'gt' },
    { label: 'after or equal to', value: 'gte' },
    { label: 'before', value: 'lt' },
    { label: 'before or equal to', value: 'lte' },
    { label: 'is between', value: 'between' },
    { label: 'is not in between', value: 'notbetween' }],
    conditionConfigDefault: [{ label: 'IS_EQUAL', value: 'eq' },
    { label: 'NOT_EQUAL', value: 'neq' },
    { label: 'IS_NULL', value: 'null' },
    { label: 'IS_NOT_NULL', value: 'notnull' },
    { label: 'IS_GREATER', value: 'gt' },
    { label: 'IS_GREATER_OR_EQUAL', value: 'gte' },
    { label: 'IS_LESS', value: 'lt' },
    { label: 'IS_LESS_OR_EQUAL', value: 'lte' },
    { label: 'IS_BETWEEN', value: 'between' },
    { label: 'IN', value: 'in' },
    { label: 'NOT_IN', value: 'notin' },
    { label: 'BEGINS_WITH', value: 'beginswith' },
    { label: 'ENDS_WITH', value: 'endswith' },
    { label: 'CONTAINS', value: 'contains' }],
    timeZone: 'Asia/Calcutta',
    maxGrpLevel: 5,
    readOnlyMode: false,
    showSplitEquation: true,
    translations: {
      cancelModalBtnLabel: 'Cancel',
      deleteGroupModalBtnLabel: 'Delete Group',
      deleteGroupModalMsg: 'Deleting this group will delete all the nested groups',
      dateFormatUnmatchError: 'Please enter date as per format',
      incorrectDateErrorMsg: 'Please enter the correct date',
      completeOrDeleteRuleErrorMsg: 'Please complete/delete the rule.',
      atleastOneRuleRequiredErrorMsg: 'At least one rule is required to save the changes.',
      addRuleLabel: 'Add Rule',
      addGroupLabel: 'Add Group',
      deleteGroupLabel: 'Delete Group',
      staticAndOrLabel: 'AND',
      andLabel: 'AND',
      orLabel: 'OR',
      noResultFoundMsg: '',
      filterFieldsPlaceholder: 'Type to search',
    },
    hideConditionToggle: false,
    fieldNamePlaceHolder: 'Field',
    conditionPlaceHolder: 'Condition',
    conditionValue1PlaceHolder: 'Value1',
    conditionValue2PlaceHolder: 'Value2'
  };

  saveQueryBtnConf: ButtonConf = {
    value: 'Save Query',
    buttonType: 'flat',
    borderRadius: 5,
    color: 'accent',
    name: 'basicButton'
  };

  resetBtnConf: ButtonConf = {
    value: 'Reset',
    buttonType: 'flat',
    borderRadius: 5,
    color: 'accent',
    name: 'basicButton'
  };

  isError: any;
  finalQuery: any;

  @ViewChild('queryComp') queryComp: QueryBuilderComponent;

  constructor() { }

  ngOnInit() {
    console.log('config demo', this.config);
  }

  saveQueryJson() {
    // call the widget method'
    this.queryComp.saveQueryBuilderJson();
    if (!this.isError) {
      this.finalQuery = this.queryComp.finalQuery;
      console.log('final condition demo', this.finalQuery);
    } else {
      this.finalQuery = '';
    }
  }

  isErrorInQuery(eve) {
    this.isError = eve
    console.log('is error?', this.isError);
  }

  resetQuerybuilder() {
    this.queryComp.reset();
  }

}
