.offerlist-card-view-container {
  height: calc(100% - 2px);
  overflow-y: auto;
  display: block;

  &.summary-page {
    height: calc(100% - 5px);
    box-shadow: none;
    margin: 0;

    .scroll-container {
      background-color: transparent;
      padding: 30px 0 4px 30px;
      height: calc(100% - 65px);
      margin: 2px;
      box-shadow: none;
    }
  }

  .metadata-container {
    height: 30px;
    background-color: transparent;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .offer-count {
      color: #6d7692;
      font-family: Roboto;
      font-size: 12px;
      letter-spacing: 0.4px;
      line-height: 20px;
    }

    .manage-selections {
      position: static;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 380px;
      background-color: transparent;

      .selected-offers {
        width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 0 0 0 5px;
        font-size: 14px;
      }
      .manage-selections-button {
        width: 150px;
      }
    }
  }

  .scroll-container {
    background-color: #f5f5f5;
    box-shadow: 0 2px 2px 0 rgb(0 0 0 / 25%), 0 0px 2px 0 rgb(0 0 0 / 25%);
    padding: 30px 0 4px 30px;
    height: calc(100% - 95px);
    margin: 2px;

    .no-records {
      display: flex;
      flex-flow: column;
      align-items: center;
      height: 90%;
      justify-content: center;
      .icon {
        .hcl-icon-search-noresult {
          font-size: 50px;
          cursor: default;
        }
      }
      .title {
        color: #444444;
        font-family: Roboto;
        font-size: 13px;
        letter-spacing: 0;
        line-height: 16px;
        text-align: center;
      }
      .sub-copy {
        color: #444444;
        font-family: Roboto;
        font-size: 19px;
        letter-spacing: 0;
        line-height: 23px;
      }
    }
  }

  .cursor-default {
    cursor: default;
  }
}

button.mat-menu-item {
  line-height: 35px;
}
