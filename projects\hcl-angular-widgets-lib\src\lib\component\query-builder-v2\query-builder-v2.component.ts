import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
} from "@angular/core";
import {
  AbstractControl,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  ValidatorFn,
  Validators,
} from "@angular/forms";

import { Subscription } from "rxjs";
import { ModalService } from "../../service/modal.service";
import { NotificationService } from "../../service/notification.service";
import { ButtonConf } from "../button/button-config";
import { DropDownConfig, DropDownElement } from "../drop-down/drop-down-conf";
import { ModalConfig } from "../modal/modal.config";
import { QueryBuilderV2Conf } from "./query-builder-v2.conf";
import * as _ from "lodash";
import { SideBarComponent } from "../side-bar/side-bar.component";
import { TextareaConfig } from "../textarea/textarea.config";
import { SuggestionDirectiveConfig } from "../../directive/hcl-suggestion-config";
import moment from 'moment/moment';

@Component({
  selector: "hcl-query-builder-v2",
  templateUrl: "./query-builder-v2.component.html",
  styleUrls: ["./query-builder-v2.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class QueryBuilderV2Component
  implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild("deleteNestedGroupActionsTemplate")
  deleteNestedGroupActionsTemplate: TemplateRef<ElementRef>;
  @ViewChild("deleteNestedGroupDialogTemplate")
  deleteNestedGroupDialogTemplate: TemplateRef<ElementRef>;
  @ViewChild("deleteSingleGroupActionsTemplate")
  deleteSingleGroupActionsTemplate: TemplateRef<ElementRef>;
  @ViewChild("deleteSingleGroupDialogTemplate")
  deleteSingleGroupDialogTemplate: TemplateRef<ElementRef>;
  @ViewChild("inOperatorRuleValuesActionsDialogTemplate")
  inOperatorRuleValuesActionsDialogTemplate: TemplateRef<ElementRef>;
  @ViewChild("inOperatorRuleValuesContentDialogTemplate")
  inOperatorRuleValuesContentDialogTemplate: TemplateRef<ElementRef>;
  @ViewChild(SideBarComponent) sideBarComponentRef: SideBarComponent;
  @ViewChild("deleteExpressionRuleActionsTemplate")
  deleteExpressionRuleActionsTemplate: TemplateRef<ElementRef>;

  @Input() config: QueryBuilderV2Conf;
  @Output() queryBuilderStateStream = new EventEmitter<any>();
  @Output() folderSelect = new EventEmitter<any>();
  @Output() expressionBuilderValue = new EventEmitter<any>();
  @Output() emptyGroup = new EventEmitter<any>();
  @Output() paramDetailsIconClicked = new EventEmitter<any>();

  formSubscription: Subscription;
  addRuleBtnConfig: ButtonConf;
  addGroupBtnConfig: ButtonConf;
  deleteGroupBtnConfig: ButtonConf;
  fieldNameConfig: DropDownConfig;
  fieldValueDropDownConfig: DropDownConfig;
  fieldNameBaseOptions: DropDownElement[] = [];
  columnTypeConfig: DropDownConfig;
  columnTypeOptions: DropDownElement[] = [];
  fieldNameBaseObject: any[] = [];
  conditionConfig: DropDownConfig;
  // inlineSpinnerConfig: ProgressSpinner;
  deleteNestedGroupDialogConfig: ModalConfig;
  deleteSingleGroupDialogConfig: ModalConfig;
  deleteExpressionRuleDialogConfig: ModalConfig;
  cancelModalBtnConf: ButtonConf;
  deleteBtnConf: ButtonConf;
  deleteExpressionBtnConf: ButtonConf;
  inOtpAddBtnConfig: ButtonConf;
  plusMoreBtnConfig: ButtonConf;
  closeButtonConfig: ButtonConf;
  changeFolderBtn: ButtonConf;
  inOperatorRuleValuesDialogConfig: ModalConfig;
  showSubgroupRuleCount = "";
  rulesConfigMap = new Map<string, any>();
  inOperatorValuesMap = new Map<string, any>();
  selectedGroupToDelete = null;
  selectedRuleToViewInValues = null;
  supportDynamic = {};
  expandCollapseObj = {};
  groupHasDynamicRule = {};
  dynamicRuleIndex = {};
  colorArray = [
    "#0078d8",
    "#f5821e",
    "#c4d056",
    "#7d5aa6",
    "#ddbb4d",
    "#444444",
  ];
  isColumnType = false;
  currentGroupIndex = 0;
  currentRuleIndex = 0;
  totalRuleCount = 0;

  groupArray = [];

  queryBuilderForm: UntypedFormGroup;
  showSwitch = {};
  dynamicAttrOfType = [];
  fieldTypeMap = {};
  showSideBar: boolean = false;
  textareaConfig: TextareaConfig;
  suggestionTextareaConfig: SuggestionDirectiveConfig = null;
  saveConfig: ButtonConf;
  cancelConfig: ButtonConf;
  currentExpressionObject: {
    rule: any;
    ruleIndex: any;
    group: any;
    isParamField: boolean;
    oldRule?: any;
  } = null;
  expressionErrorMsg: string = null;
  suggestions: any[] = [];
  cursorPosition: number = 0;
  checkSyntaxConfig: ButtonConf;
  showSyntaxIcon: boolean = false;
  noRuleHeader: string = "";
  noRuleContext: string = "";
  valueAsDropdownList = {};

  @Output() expressionBuilderPanelClosed = new EventEmitter<any>();
  @Output() componentReady = new EventEmitter<any>();

  constructor(
    private fb: UntypedFormBuilder,
    public modalService: ModalService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.isColumnType = Boolean(this.config?.columnTypes?.length);
    this.fieldTypeMap = {};
    this.setConfiguration();
    this.queryBuilderForm = this.fb.group({});

    if (
      this.config.jsonData &&
      (this.config.jsonData["AND"] || this.config.jsonData["OR"])
    ) {
      this.groupArray.push(
        this.generateGroupArrayAndControlsFromJson(this.config.jsonData, null)
      );
    } else {
      this.config.jsonData = {
        AND: [],
      };
      this.groupArray.push(
        this.generateGroupArrayAndControlsFromJson(this.config.jsonData, null)
      );
    }
    setTimeout(() => {
      this.checkAndEmitJson();
      this.createNoRulesTemplate();
    }, 0);

    this.config.maxRuleCount =
      this.config.maxRuleCount || Number.MAX_SAFE_INTEGER;
  }

  ngAfterViewInit() {
    this.deleteSingleGroupDialogConfig = {
      disableClose: false,
      backdropClass: "",
      hasBackdrop: true,
      contentTemplate: this.deleteSingleGroupDialogTemplate,
      actionsTemplate: this.deleteSingleGroupActionsTemplate,
      width: "400px",
      title: this.config.translations.deleteGroupLabel,
    };

    this.deleteNestedGroupDialogConfig = {
      disableClose: false,
      backdropClass: "",
      hasBackdrop: true,
      contentTemplate: this.deleteNestedGroupDialogTemplate,
      actionsTemplate: this.deleteNestedGroupActionsTemplate,
      width: "400px",
      title: this.config.translations.deleteGroupsLabel,
    };

    this.inOperatorRuleValuesDialogConfig = {
      disableClose: false,
      hasBackdrop: true,
      title: "",
      height: "auto",
      width: "400px",
      contentTemplate: this.inOperatorRuleValuesContentDialogTemplate,
      actionsTemplate: this.inOperatorRuleValuesActionsDialogTemplate,
    };

    if (this.config.enableExpressionBuilder) {
      this.deleteExpressionRuleDialogConfig = {
        disableClose: false,
        backdropClass: "",
        hasBackdrop: true,
        content: this.config.translations.deleteExpressionModalMsg,
        actionsTemplate: this.deleteExpressionRuleActionsTemplate,
        width: "400px",
        title: this.config.translations.deleteExpressionLabel,
      };
    }
    this.componentReady.emit(this);
  }

  checkAndEmitJson() {
    this.formSubscription = this.queryBuilderForm.valueChanges.subscribe(() => {
      setTimeout(() => {
        const data = this.getQueryJson();
        const state = this.getQbState();
        this.addRuleBtnConfig.disabled = !this.queryBuilderForm.valid || (this.totalRuleCount === this.config.maxRuleCount);
        this.addGroupBtnConfig.disabled = !this.queryBuilderForm.valid;
        if (data && this.config.emitQueryJsonOnFormChange) {
          this.queryBuilderStateStream.emit({
            jsonData: data,
            qbFormIsValid: state,
          });
        }
      }, 0);
    });
  }

  setConfiguration() {
    this.addRuleBtnConfig = {
      value: this.config.translations.addRuleLabel,
      buttonType: "mat",
      color: "accent",
      name: "addRuleBtn",
      styleClass: "custom-icon",
      icon: "hcl-icon-add-rule",
      type: "button",
    };

    this.addGroupBtnConfig = {
      value: this.config.translations.addGroupLabel,
      buttonType: "mat",
      color: "accent",
      name: "addRuleBtn",
      styleClass: "custom-icon",
      icon: "hcl-icon-add-group",
      type: "button",
    };

    this.deleteGroupBtnConfig = {
      value: this.config.translations.deleteGroupLabel,
      buttonType: "mat",
      color: "accent",
      name: "addRuleBtn",
      styleClass: "custom-icon",
      icon: "hcl-icon-delete",
      type: "button",
    };

    this.fieldNameConfig = {
      options: [],
      disabled: false,
      placeholder: this.config.translations.fieldNamePlaceHolder,
      disableOptionCentering: true,
      name: "fieldname",
      isToolTip: true,
      iconTooltip: this.config.translations.viewProfile,
      errorList: [
        {
          errorCondition: "required",
          errorMsg: this.config.translations.requiredField,
        },
      ],
      dynamicAction: { templateName: "dynamicAction", template: null },
    };

    this.fieldValueDropDownConfig = {
      options: [],
      disabled: false,
      placeholder: this.config.translations.conditionValuePlaceHolder,
      disableOptionCentering: true,
      name: "dropdownTypeValue",
      errorList: [
        {
          errorCondition: "required",
          errorMsg: this.config.translations.requiredField,
        },
      ],
      dynamicAction: { templateName: "valueSearchtemp", template: null },
    };

    if (this.isColumnType) {
      this.columnTypeConfig = {
        options: [],
        disabled: false,
        placeholder: this.config.translations.columntypePlaceHolder,
        disableOptionCentering: true,
        name: "paramType",
        isToolTip: true,
        errorList: [
          {
            errorCondition: "required",
            errorMsg: this.config.translations.requiredField,
          },
        ],
      };

      this.config.columnTypes.forEach((element) => {
        this.columnTypeOptions.push({
          label: element.fieldLabel,
          value: element.fieldValue,
          toolTipHTML: `<span>${element.fieldLabel}</span>`,
        });
      });
    } else {
      this.config.columnFields.forEach((element) => {
        this.fieldNameBaseOptions.push({
          label: element.fieldLabel,
          value: element.fieldValue,
          toolTipHTML: `<span>${element.fieldLabel}</span>`,
          icon: element.profile ? "hcl-icon-user" : null,
        });
      });
    }

    this.conditionConfig = {
      options: [],
      disabled: false,
      placeholder: this.config.translations.conditionPlaceHolder,
      disableOptionCentering: true,
      name: "condition",
      formControl: new UntypedFormControl(),
      isToolTip: true,
      errorList: [
        {
          errorCondition: "required",
          errorMsg: this.config.translations.requiredField,
        },
      ],
    };

    this.cancelModalBtnConf = {
      value: this.config.translations.cancelModalBtnLabel,
      buttonType: "stroked",
      borderRadius: 5,
      color: "accent",
      name: "cancelModalButton",
    };

    this.deleteBtnConf = {
      value: this.config.translations.delete,
      buttonType: "raised",
      color: "accent",
      borderRadius: 5,
      name: "DeleteGroupModalButton",
    };

    this.inOtpAddBtnConfig = {
      value: this.config.translations.add,
      buttonType: "stroked",
      borderRadius: 5,
      color: "accent",
      styleClass: "hcl-sm-button",
      icon: "hcl-icon-add-button",
      name: "cancelModalButton",
      disabled: false,
    };

    this.plusMoreBtnConfig = {
      value: this.config.translations.more,
      buttonType: "mat",
      color: "accent",
      name: "addRuleBtn",
      styleClass: "custom-icon",
      icon: "hcl-icon-add-button",
      type: "button",
    };

    this.closeButtonConfig = {
      value: this.config.translations.close,
      buttonType: "stroked",
      borderRadius: 5,
      color: "accent",
      name: "cancelModalButton",
      disabled: false,
    };

    this.changeFolderBtn = {
      value: this.config.translations.selectFolderPlaceHolder,
      buttonType: "mat",
      color: "accent",
      borderRadius: 5,
      name: "changeFolder",
      type: "button",
    };

    if (this.config.enableExpressionBuilder) {
      this.textareaConfig = {
        name: "expressionTextArea",
        placeholder: "",
        errorList: [],
        formControlName: new UntypedFormControl(""),
        endHintMsg: "",
        disabled: false,
        rows: 5,
        cols: 5,
        isShowLimit: false,
        autosize: false,
        enableCaretPosition: true,
      };
      this.suggestionTextareaConfig = {
        ...this.config.expressionBuilderPanelConfig.suggestionTextareaConfig,
      };
      this.suggestionTextareaConfig.formControl =
        this.textareaConfig.formControlName;
      this.saveConfig = {
        name: "saveExpression",
        value: this.config.translations.save,
        color: "accent",
        buttonType: "flat",
        type: "submit",
        styleClass: "large-btn",
        borderRadius: 5,
        disabled: true,
      };
      this.cancelConfig = {
        name: "cancelExpression",
        value: this.config.translations.cancelModalBtnLabel,
        color: "accent",
        buttonType: "stroked",
        type: "button",
        styleClass: "large-btn",
        borderRadius: 5,
      };
      this.deleteExpressionBtnConf = {
        value: this.config.translations.delete,
        buttonType: "raised",
        color: "accent",
        borderRadius: 5,
        name: "deleteExpressionModalButton",
      };
      this.checkSyntaxConfig = {
        name: "checkSyntax",
        value: this.config.translations.checkSyntaxBtnLabel
          ? this.config.translations.checkSyntaxBtnLabel
          : "Check syntax",
        color: "accent",
        buttonType: "stroked",
        type: "button",
        styleClass: "hcl-md-button",
        borderRadius: 5,
        disabled: true,
      };

      this.textareaConfig.formControlName.valueChanges.subscribe((val) => {
        if (
          this.textareaConfig.formControlName.value &&
          this.textareaConfig.formControlName.value.length > 0
        ) {
          this.saveConfig.disabled = false;
          this.checkSyntaxConfig.disabled = false;
        } else {
          this.saveConfig.disabled = true;
          this.checkSyntaxConfig.disabled = true;
        }
      });
    }
  }

  createNoRulesTemplate() {
    const addRule = `"<span class="add-link-inline add-rule-inline">${this.config.translations.addRuleLabel}</span>"`;
    const addGroup = `"<span class="add-link-inline add-group-inline">${this.config.translations.addGroupLabel}</span>"`;

    this.noRuleHeader =
      this.config.translations.noRuleHeader || "No New Rule to Show";
    this.noRuleContext = (
      this.config.translations.noRuleContext ||
      "You have not created any rule yet. Click on #rule# or #group# to create"
    )
      .replace("#rule#", addRule)
      .replace("#group#", addGroup);

    setTimeout(() => {
      document.addEventListener("click", (e: any) => {
        const addRule = e.target.closest(".add-rule-inline");
        const addGrp = e.target.closest(".add-group-inline");
        if (addRule) {
          this.addRule(this.groupArray[0]);
        }
        if (addGrp) {
          this.addGroup(this.groupArray[0]);
        }
      });
    });
  }

  generateGroupArrayAndControlsFromJson(
    groupData: any,
    nestedGroupContainer: any
  ) {
    const logicalOperator = nestedGroupContainer
      ? nestedGroupContainer.AND
        ? "AND"
        : "OR"
      : groupData.AND
        ? "AND"
        : "OR";
    const groupIndex = "group_" + ++this.currentGroupIndex;
    this.queryBuilderForm.addControl(
      groupIndex,
      this.generateNewGroupControls(groupIndex)
    );

    const groupArrayItem = nestedGroupContainer ? nestedGroupContainer : {};
    groupArrayItem.groupId = groupIndex;
    groupArrayItem.groupName = `Group ${this.currentGroupIndex}`;
    groupArrayItem.toggleConfig = {
      color: "primary",
      disabled: true,
      name: "toggle",
      checked: logicalOperator === "AND" ? true : false,
    };

    groupArrayItem.nestedGroupIndex = !nestedGroupContainer
      ? 0
      : nestedGroupContainer.nestedGroupIndex;
    if (!nestedGroupContainer) {
      groupArrayItem[logicalOperator] = [];
    }
    const groupRulesArray = Array.isArray(groupData)
      ? groupData
      : groupData[logicalOperator];
    this.showSwitch[groupArrayItem.groupId] = true;
    if (groupRulesArray && groupRulesArray.length > 1) {
      groupArrayItem.toggleConfig.disabled = false;
    } else {
      groupArrayItem.toggleConfig.disabled = true;
      if (this.config.isHideSwitch) {
        this.showSwitch[groupArrayItem.groupId] = false;
      }
    }
    if (groupRulesArray) {
      for (let i = 0; i < groupRulesArray.length; i++) {
        if (groupRulesArray[i]["AND"] || groupRulesArray[i]["OR"]) {
          const nestedLogicalOperator = groupRulesArray[i].AND ? "AND" : "OR";
          const nestedGroupItem = {
            nestedGroupIndex:
              groupArrayItem.nestedGroupIndex >= this.colorArray.length - 1
                ? 0
                : groupArrayItem.nestedGroupIndex + 1,
          };
          nestedGroupItem[nestedLogicalOperator] = [];
          const newGroupArrayItem = this.generateGroupArrayAndControlsFromJson(
            groupRulesArray[i]["AND"] || groupRulesArray[i]["OR"],
            nestedGroupItem
          );
          groupArrayItem[logicalOperator].push(newGroupArrayItem);
        } else {
          groupRulesArray[i].ruleId = ++this.currentRuleIndex;
          ++this.totalRuleCount;
          this.queryBuilderForm.controls[groupIndex][
            "controls"
          ].rules.addControl(
            `${groupIndex}_rule_${groupRulesArray[i].ruleId}`,
            this.generateNewRuleControls(
              groupIndex,
              groupRulesArray[i].ruleId,
              groupRulesArray[i]
            )
          );
          groupArrayItem[logicalOperator].push(groupRulesArray[i]);
          if (
            this.supportDynamic[
              `${groupIndex}_rule_${groupRulesArray[i].ruleId}`
            ].isDynamic
          ) {
            this.changeSelectionType(
              "EDIT_CASE",
              groupRulesArray[i],
              groupIndex,
              `${groupIndex}_rule_${groupRulesArray[i].ruleId}`
            );
          }
        }
      }
    } else {
      groupData.ruleId = ++this.currentRuleIndex;
      ++this.totalRuleCount;
      this.queryBuilderForm.controls[groupIndex]["controls"].rules.addControl(
        `${groupIndex}_rule_${groupData.ruleId}`,
        this.generateNewRuleControls(groupIndex, groupData.ruleId, groupData)
      );
      groupArrayItem[logicalOperator].push(groupData);
      if (
        this.supportDynamic[`${groupIndex}_rule_${groupData.ruleId}`].isDynamic
      ) {
        this.changeSelectionType(
          "EDIT_CASE",
          groupData,
          groupIndex,
          `${groupIndex}_rule_${groupData.ruleId}`
        );
      }
    }
    this.addRuleBtnConfig.disabled = this.totalRuleCount === this.config.maxRuleCount;
    return groupArrayItem;
  }

  generateNewGroupControls(groupIndex) {
    const groupConfig = this.fb.group({
      logicalOp: new UntypedFormControl("AND"),
      rules: this.fb.group({}),
    });

    this.rulesConfigMap.set(`${groupIndex}`, groupConfig);
    return groupConfig;
  }

  getRuleValue(data: any) {
    let value;
    if (data && data?.datatype) {
      if (data?.datatype?.toLowerCase() === "Date".toLowerCase()) {
        if (!this.config.showDatePicker) {
          const format = data.dateFormat.toUpperCase();

          if (Array.isArray(data.value)) {
            let date: any = new Date(0);
            const sec = parseInt(data.value[0], 10);
            date = moment(sec).format(format);
            data.value[0] = date.toString();

            let date2: any = new Date(0);
            const sec2 = parseInt(data.value[1], 10);
            date2 = moment(sec2).format(format);
            data.value[1] = date2.toString();
            value = data.value;
          } else {
            let date: any = new Date(0);
            const sec = parseInt(data.value, 10);
            date = moment(sec).format(format);
            value = date.toString();
          }
        } else {
          value = new Date(data.value);
        }
      } else {
        value = data.value === 0 ? 0 : data.value;
      }
      return value;
    } else {
      return null;
    }
  }

  generateNewRuleControls(groupIndex, ruleIndex, data?: any) {
    const firstColumnDataType =
      (this.config.columnFields[0] &&
        this.config.columnFields[0].fieldDataType) ||
      "String";
    let ruleConfig;
    const dc = this.config.columnFields.filter(
      (d) => data?.param === d.fieldValue
    );
    const paramObj = this.config.columnFields.find(
      (el) => el.fieldValue === data?.param
    );
    const isDynamicRule: boolean =
      dc && dc[0] ? (dc[0].isDynamic ? true : false) : false;
    this.supportDynamic[`${groupIndex}_rule_${ruleIndex}`] = {
      isDynamic: isDynamicRule,
      controlType: dc[0]?.controlType ? dc[0]?.controlType : null,
    };
    this.dynamicRuleIndex[`${groupIndex}~rule_${ruleIndex}`] = isDynamicRule;
    this.groupHasDynamicRule = this.createDynamicGroupObj();
    if (this.supportDynamic[`${groupIndex}_rule_${ruleIndex}`].isDynamic) {
      if (this.isColumnType) {
        ruleConfig = new UntypedFormGroup({
          paramType: new UntypedFormControl(
            (data && data.paramType) || null,
            Validators.required
          ),
          param: new UntypedFormControl(
            (data && data.param) || null,
            Validators.required
          ),
          opt: new UntypedFormControl(
            (data && data.opt) || null,
            Validators.required
          ),
          value: new UntypedFormControl(
            this.getRuleValue(data),
            data &&
              (data.opt === "between" || data.opt === "notbetween") &&
              (data.datatype.toLowerCase() === "Numeric".toLowerCase() ||
                data.datatype.toLowerCase() === "Integer".toLowerCase() ||
                data.datatype.toLowerCase() === "Long".toLowerCase() ||
                data.datatype.toLowerCase() === "Double".toLowerCase() ||
                data.datatype.toLowerCase() === "Float".toLowerCase())
              ? [
                Validators.required,
                this.validateDigitsInRange(data.value, "from"),
              ]
              : data &&
                (data.opt === "between" || data.opt === "notbetween") &&
                data.datatype.toLowerCase() === "Date".toLowerCase() &&
                !this.config.showDatePicker
                ? [
                  Validators.required,
                  this.validateDateFormat(
                    data.value,
                    paramObj.dateFormat ? paramObj.dateFormat : "dd MM yyyy"
                  ),
                ]
                : [Validators.required]
          ),
          dynamicValue: new UntypedFormControl(
            (data && data.dynamicValue) || null,
            data.opt === "between" ||
              data.opt === "notbetween" ||
              data.opt === "in" ||
              data.opt === "notin" || data.opt === "true" || data.opt === "false" || data.opt === "null" || data.opt === "notnull"
              ? []
              : [Validators.required]
          ),
          selectionType: new UntypedFormControl(
            (data && data.selectionType) || null,
            data.opt === "between" ||
              data.opt === "notbetween" ||
              data.opt === "in" ||
              data.opt === "notin" || data.opt === "true" || data.opt === "false" || data.opt === "null" || data.opt === "notnull"
              ? []
              : [Validators.required]
          ),
        });
      } else {
        ruleConfig = new UntypedFormGroup({
          param: new UntypedFormControl(
            (data && data.param) || null,
            Validators.required
          ),
          opt: new UntypedFormControl(
            (data && data.opt) || null,
            Validators.required
          ),
          value: new UntypedFormControl(
            this.getRuleValue(data),
            data &&
              (data.opt === "between" || data.opt === "notbetween") &&
              (data.datatype.toLowerCase() === "Numeric".toLowerCase() ||
                data.datatype.toLowerCase() === "Integer".toLowerCase() ||
                data.datatype.toLowerCase() === "Long".toLowerCase() ||
                data.datatype.toLowerCase() === "Double".toLowerCase() ||
                data.datatype.toLowerCase() === "Float".toLowerCase())
              ? [
                Validators.required,
                this.validateDigitsInRange(data.value, "from"),
              ]
              : data &&
                (data.opt === "between" || data.opt === "notbetween") &&
                data.datatype.toLowerCase() === "Date".toLowerCase() &&
                !this.config.showDatePicker
                ? [
                  Validators.required,
                  this.validateDateFormat(
                    data.value,
                    paramObj.dateFormat ? paramObj.dateFormat : "dd MM yyyy"
                  ),
                ]
                : [Validators.required]
          ),
          dynamicValue: new UntypedFormControl(
            (data && data.dynamicValue) || null,
            data.opt === "between" ||
              data.opt === "notbetween" ||
              data.opt === "in" ||
              data.opt === "notin" || data.opt === "true" || data.opt === "false" || data.opt === "null" || data.opt === "notnull"
              ? []
              : [Validators.required]
          ),
          selectionType: new UntypedFormControl(
            (data && data.selectionType) || null,
            data.opt === "between" ||
              data.opt === "notbetween" ||
              data.opt === "in" ||
              data.opt === "notin" || data.opt === "true" || data.opt === "false" || data.opt === "null" || data.opt === "notnull"
              ? []
              : [Validators.required]
          ),
        });
      }
    } else {
      if (this.isColumnType) {
        ruleConfig = new UntypedFormGroup({
          paramType: new UntypedFormControl(
            (data && data.paramType) || null,
            Validators.required
          ),
          param: new UntypedFormControl(
            (data && data.param) || null,
            Validators.required
          ),
          opt: new UntypedFormControl(
            (data && data.opt) || null,
            Validators.required
          ),
          value: new UntypedFormControl(
            this.getRuleValue(data),
            data &&
              (data.opt === "between" || data.opt === "notbetween") &&
              (data.datatype.toLowerCase() === "Numeric".toLowerCase() ||
                data.datatype.toLowerCase() === "Integer".toLowerCase() ||
                data.datatype.toLowerCase() === "Long".toLowerCase() ||
                data.datatype.toLowerCase() === "Double".toLowerCase() ||
                data.datatype.toLowerCase() === "Float".toLowerCase())
              ? [
                Validators.required,
                this.validateDigitsInRange(data.value, "from"),
              ]
              : data &&
                (data.opt === "between" || data.opt === "notbetween") &&
                data.datatype.toLowerCase() === "Date".toLowerCase() &&
                !this.config.showDatePicker
                ? [
                  Validators.required,
                  this.validateDateFormat(
                    data.value,
                    paramObj.dateFormat ? paramObj.dateFormat : "dd MM yyyy"
                  ),
                ]
                : [Validators.required]
          ),
        });
      } else {
        ruleConfig = new UntypedFormGroup({
          param: new UntypedFormControl(
            (data && data.param) || null,
            Validators.required
          ),
          opt: new UntypedFormControl(
            (data && data.opt) || null,
            Validators.required
          ),
          value: new UntypedFormControl(
            this.getRuleValue(data),
            data &&
              (data.opt === "between" || data.opt === "notbetween") &&
              (data.datatype.toLowerCase() === "Numeric".toLowerCase() ||
                data.datatype.toLowerCase() === "Integer".toLowerCase() ||
                data.datatype.toLowerCase() === "Long".toLowerCase() ||
                data.datatype.toLowerCase() === "Double".toLowerCase() ||
                data.datatype.toLowerCase() === "Float".toLowerCase())
              ? [
                Validators.required,
                this.validateDigitsInRange(data.value, "from"),
              ]
              : data &&
                (data.opt === "between" || data.opt === "notbetween") &&
                data.datatype.toLowerCase() === "Date".toLowerCase() &&
                !this.config.showDatePicker
                ? [
                  Validators.required,
                  this.validateDateFormat(
                    data.value,
                    paramObj.dateFormat ? paramObj.dateFormat : "dd MM yyyy"
                  ),
                ]
                : [Validators.required]
          ),
        });
      }
    }

    let value1Config;
    let value2Config;

    let fieldsConfig = this.getFieldsElementDropdownConfig(
      ruleConfig.controls.param
    );

    this.rulesConfigMap.set(
      `${groupIndex}_rule_${ruleIndex}_param`,
      fieldsConfig
    );

    if (data) {
      if (data.opt === "true" || data.opt === "false" || data.opt === "null" || data.opt === "notnull") {
        ruleConfig.controls.value.setValue(null);
        ruleConfig.controls.value.clearValidators();
        ruleConfig.controls.value.updateValueAndValidity();
      }
      if (data.opt === "in" || data.opt === "notin") {
        ruleConfig.controls.value.setValue(null);
        ruleConfig.controls.value.clearValidators();
        ruleConfig.controls.value.updateValueAndValidity();
        this.inOperatorValuesMap.set(
          `${groupIndex}_rule_${ruleIndex}`,
          data.value
        );
        // this.rulesConfigMap.set(`${groupIndex}_rule_${ruleIndex}_addBtn`, { ...this.inOtpAddBtnConfig });
        this.rulesConfigMap.set(`${groupIndex}_rule_${ruleIndex}_moreBtn`, {
          ...this.plusMoreBtnConfig,
          value: `  ${data.value.length - 2} ${this.config.translations.more}`,
        });
      }
      if (data.opt === "between" || data.opt === "notbetween") {
        ruleConfig.controls.value.setValue(
          data.datatype.toLowerCase() === "Date".toLowerCase() &&
            this.config.showDatePicker
            ? new Date(data.value[0])
            : data.value[0]
        );
        ruleConfig.addControl(
          "value2",
          new UntypedFormControl(
            data.datatype.toLowerCase() === "Date".toLowerCase() &&
              this.config.showDatePicker
              ? new Date(data.value[1])
              : data.value[1],
            data.datatype.toLowerCase() === "Numeric".toLowerCase() ||
              data.datatype.toLowerCase() === "Integer".toLowerCase() ||
              data.datatype.toLowerCase() === "Long".toLowerCase() ||
              data.datatype.toLowerCase() === "Double".toLowerCase() ||
              data.datatype.toLowerCase() === "Float".toLowerCase()
              ? [
                Validators.required,
                this.validateDigitsInRange(data.value, "to"),
              ]
              : data.datatype.toLowerCase() === "Date".toLowerCase() &&
                !this.config.showDatePicker
                ? [
                  Validators.required,
                  this.validateDateFormat(
                    data.value,
                    paramObj.dateFormat ? paramObj.dateFormat : "dd MM yyyy"
                  ),
                ]
                : [Validators.required]
          )
        );

        if (data.datatype.toLowerCase() === "Date".toLowerCase()) {
          value2Config = this.getDateFieldConfig(ruleConfig.controls.value2);
        } else {
          value2Config = this.getInputFieldConfig(
            ruleConfig.controls.value2,
            data && data.datatype,
            `${groupIndex}_rule_${ruleIndex}`
          );
        }

        this.rulesConfigMap.set(
          `${groupIndex}_rule_${ruleIndex}_value2`,
          value2Config
        );
      }

      if (
        data?.datatype &&
        data?.datatype?.toLowerCase() === "Date".toLowerCase() &&
        this.config.showDatePicker
      ) {
        value1Config = this.getDateFieldConfig(ruleConfig.controls.value);
        if (data.opt === "between" || data.opt === "notbetween") {
          value2Config.minDate = new Date(data.value[0]);
          value1Config.maxDate = new Date(data.value[1]);
        }
      } else {
        value1Config = this.getInputFieldConfig(
          ruleConfig.controls.value,
          data && data.datatype,
          `${groupIndex}_rule_${ruleIndex}`
        );
      }
    } else {
      value1Config = this.getInputFieldConfig(
        ruleConfig.controls.value,
        (data && data.datatype) || firstColumnDataType,
        `${groupIndex}_rule_${ruleIndex}`
      );
    }

    const paramSearchConfig = this.getDropdownSearchInputConfig();
    const valueSearchConfig = this.getDropdownSearchInputConfig();

    const conditionsConfig = this.getConditionsDropdownConfig(
      ruleConfig.controls.opt,
      (data && data.datatype) || firstColumnDataType,
      ruleConfig
    );

    this.rulesConfigMap.set(
      `${groupIndex}_rule_${ruleIndex}_param_search`,
      paramSearchConfig
    );

    this.rulesConfigMap.set(
      `${groupIndex}_rule_${ruleIndex}_value_search`,
      valueSearchConfig
    );

    this.rulesConfigMap.set(
      `${groupIndex}_rule_${ruleIndex}_opt`,
      conditionsConfig
    );
    this.rulesConfigMap.set(
      `${groupIndex}_rule_${ruleIndex}_value`,
      value1Config
    );

    if (
      this.config.autoSingleValueSelect &&
      this.config.columnFields.length == 1
    ) {
      this.rulesConfigMap.get(
        `${groupIndex}_rule_${ruleIndex}_param`
      ).disabled = true;
    }

    if (this.supportDynamic[`${groupIndex}_rule_${ruleIndex}`].isDynamic) {
      const dynamicValueConfig = this.getDynamicValueFieldConfig(
        ruleConfig.controls.dynamicValue,
        data && data.datatype
      );
      const valueSelectionConfig = this.getSelectionTypeConfig(
        ruleConfig.controls.selectionType
      );
      this.rulesConfigMap.set(
        `${groupIndex}_rule_${ruleIndex}_dynamicValue`,
        dynamicValueConfig
      );
      this.rulesConfigMap.set(
        `${groupIndex}_rule_${ruleIndex}_selectionType`,
        valueSelectionConfig
      );
    }

    if (this.isColumnType) {
      const paramType = this.getFieldsElementForparamType(
        ruleConfig.controls.paramType
      );
      this.rulesConfigMap.set(
        `${groupIndex}_rule_${ruleIndex}_paramType`,
        paramType
      );

      if (data) {
        let columnSelectedArr =
          this.config.dynamicColumnFields[data?.paramType],
          fieldsConfig;
        if (data.selectBox) {
          fieldsConfig = this.getParamSelectBoxConfig(
            ruleConfig.controls.param
          );
          this.fieldNameBaseObject[`${groupIndex}_rule_${ruleIndex}_param`] =
            columnSelectedArr;
          this.rulesConfigMap.set(
            `${groupIndex}_rule_${ruleIndex}_param`,
            fieldsConfig
          );
          this.rulesConfigMap.get(
            `${groupIndex}_rule_${ruleIndex}_param`
          ).options = [...columnSelectedArr];
          for (let optlist of this.rulesConfigMap.get(
            `${groupIndex}_rule_${ruleIndex}_param`
          ).options) {
            for (let opt of optlist.optList) {
              if (data.param === opt.value.name) {
                this.rulesConfigMap
                  .get(`${groupIndex}_rule_${ruleIndex}_param`)
                  .formControl.setValue(opt.value);
              }
            }
          }
        } else if (data.inputBox) {
          fieldsConfig = this.getParamInputBoxConfig(ruleConfig.controls.param);
          this.rulesConfigMap.set(
            `${groupIndex}_rule_${ruleIndex}_param`,
            fieldsConfig
          );
          this.rulesConfigMap.get(
            `${groupIndex}_rule_${ruleIndex}_opt`
          ).options = this.config.conditionConfigAllType;
        } else {
          fieldsConfig = this.getFieldsElementDropdownConfig(
            ruleConfig.controls.param
          );
          this.fieldNameBaseObject[`${groupIndex}_rule_${ruleIndex}_param`] =
            columnSelectedArr?.map((ele) => {
              return {
                label: ele?.label,
                value: ele?.value,
                toolTipHTML: `<span>${ele?.label}</span>`,
                icon: ele?.profile ? "hcl-icon-user" : null,
              };
            });
          this.rulesConfigMap.set(
            `${groupIndex}_rule_${ruleIndex}_param`,
            fieldsConfig
          );
          if (
            this.fieldNameBaseObject[`${groupIndex}_rule_${ruleIndex}_param`]
          ) {
            this.rulesConfigMap.get(
              `${groupIndex}_rule_${ruleIndex}_param`
            ).options = [
                ...this.fieldNameBaseObject[
                `${groupIndex}_rule_${ruleIndex}_param`
                ],
              ];
          }
          for (let opt of this.rulesConfigMap.get(
            `${groupIndex}_rule_${ruleIndex}_param`
          ).options) {
            if (opt.value.name === data.param) {
              this.rulesConfigMap
                .get(`${groupIndex}_rule_${ruleIndex}_param`)
                .formControl.setValue(opt.value);
            }
          }
        }
      }
    }

    /**check for the option which need to active on 1st group */
    if (groupIndex !== "group_1") {
      let firstLevelOpts = this.config.columnFields.filter(
        (ele) => ele?.isFirstLevel
      )?.[0];
      if (firstLevelOpts) {
        let paramOpts = this.rulesConfigMap.get(
          `${groupIndex}_rule_${ruleIndex}_param`
        );
        paramOpts.options.forEach((ele) => {
          if (ele.value === firstLevelOpts?.fieldValue) {
            ele.disabled = true;
            ele.toolTipHTML =
              "<span>" +
              (this.config?.translations?.firstLevelWarning ||
                "This is available in 1st level only") +
              "</span>";
          }
        });
      }
    }

    this.rulesConfigMap.set(`${groupIndex}_rule_${ruleIndex}`, ruleConfig);
    return ruleConfig;
  }

  getFieldsElementForparamType(newFormControl) {
    const options = [...this.columnTypeOptions];
    if (options.length === 0) {
      options.push({
        label: this.config.translations.fieldOptionsDidNotLoad,
        value: "",
      });
    }
    const config = {
      ...this.columnTypeConfig,
      options,
      formControl: newFormControl,
    };
    return config;
  }

  getFieldsElementDropdownConfig(newFormControl) {
    let options = [];
    if (!this.isColumnType) {
      options = [...this.fieldNameBaseOptions];
    }
    if (options.length === 0) {
      options.push({
        label: this.config.translations.fieldOptionsDidNotLoad,
        value: "",
      });
    }
    const config = {
      ...this.fieldNameConfig,
      options,
      formControl: newFormControl,
      dynamicAction: { templateName: "dynamicAction", template: null }
    };
    return config;
  }

  checkForFieldType(reqFields, paramSelected, type) {
    const returnField =
      reqFields &&
      reqFields.filter((field) => {
        let fieldName =
          paramSelected?.formControl?.value?.name ||
          paramSelected?.formControl?.value ||
          paramSelected?.value?.param;
        return field?.name?.toLowerCase() === fieldName?.toLowerCase();
      });
    returnField?.length && (this.fieldTypeMap[returnField[0].name] = type);

    return returnField;
  }

  getInputFieldConfig(newFormControl, dataType, paramSelected?, dateFormat?) {
    let returnedFieldConfig,
      paramMap = this.rulesConfigMap.get(`${paramSelected}_param`);
    const dropDownFields = this.checkForFieldType(
      this.config.dropdownColumns,
      paramMap || paramSelected,
      "DROPDOWN"
    );
    if (dropDownFields && dropDownFields.length) {
      this.valueAsDropdownList[paramSelected] = dropDownFields[0].dropdownValue;
      returnedFieldConfig = {
        ...this.fieldValueDropDownConfig,
        options: dropDownFields[0].dropdownValue,
        formControl: newFormControl,
        dynamicAction: { templateName: "valueSearchtemp", template: null },
      };
    } else {
      returnedFieldConfig = {
        name: "ruleValue",
        placeholder: this.config.translations.conditionValuePlaceHolder,
        formControlName: newFormControl,
        type:
          dataType &&
            (dataType?.toLowerCase() === "String".toLowerCase() ||
              dataType?.toLowerCase() === "Date".toLowerCase())
            ? "text"
            : dataType &&
              (dataType?.toLowerCase() === "Integer".toLowerCase() ||
                dataType?.toLowerCase() === "Bit".toLowerCase())
              ? "integer"
              : "number",
        ...(dataType &&
          dataType?.toLowerCase() === "Bit".toLowerCase() && {
          min: 0,
          max: 1,
        }),
        maximumLength: 256,
        errorList: [
          {
            errorCondition: "required",
            errorMsg: this.config.translations.requiredField,
          },
          {
            errorCondition: "invalidDigitsRange",
            errorMsg: this.config.translations.invalidDigitsRangeMsg,
          },
          {
            errorCondition: "invalidDateFormat",
            errorMsg: this.config.translations.invalidDateFormat,
          },
        ],
      };
    }

    return returnedFieldConfig;
  }

  getSelectionTypeConfig(newFormControl) {
    return {
      value: "",
      name: "selectionType",
      elements: [
        {
          label: "",
          checked: false,
          value: "staticvalue",
          contentTemplateName: "staticvalueTemplate",
        },
        {
          label: "",
          checked: false,
          value: "dynamicvalue",
          contentTemplateName: "dynamicvalueTemplate",
        },
      ],
      horizontal: true,
      formControl: newFormControl,
    };
  }

  getDynamicValueFieldConfig(newFormControl, type?) {
    this.checkDynamicAttrTypes(type);
    return {
      options: this.dynamicAttrOfType,
      disabled: false,
      placeholder: this.config.translations.dynamicPlaceholder,
      errorList: [
        {
          errorCondition: "required",
          errorMsg: this.config.translations.requiredField,
        },
      ],
      disableOptionCentering: true,
      name: "dynamicValues",
      formControl: newFormControl,
    };
  }

  checkDynamicAttrTypes(type) {
    switch (type) {
      case "String":
        this.dynamicAttrOfType =
          (this.config.dynamicValues.length > 0 &&
            this.config.dynamicValues[0][1]) ||
          this.config.dynamicValues;
        break;
      case "Numeric":
      case "Integer":
      case "Long":
      case "Double":
      case "Float":
      case "Bit":
        this.dynamicAttrOfType =
          (this.config.dynamicValues.length > 0 &&
            this.config.dynamicValues[0][2]) ||
          this.config.dynamicValues;
        break;
      case "Date":
        this.dynamicAttrOfType =
          (this.config.dynamicValues.length > 0 &&
            this.config.dynamicValues[0][3]) ||
          this.config.dynamicValues;
        break;
      default:
        this.dynamicAttrOfType =
          (this.config.dynamicValues.length > 0 &&
            this.config.dynamicValues[0][1]) ||
          this.config.dynamicValues;
    }
  }

  getDropdownSearchInputConfig() {
    return {
      name: "fieldSearch",
      placeholder: this.config.translations.filterFieldsPlaceholder,
      formControlName: new UntypedFormControl(),
      icon: "hcl-icon-close",
      type: "text",
    };
  }

  getDateFieldConfig(newFormControl) {
    const config = _.cloneDeep(this.config.dateConfigObj.dateConfig);
    config.formControlName = newFormControl;
    return config;
  }

  getParamSelectBoxConfig(newFormControl) {
    return {
      options: [],
      disabled: false,
      placeholder: this.config.translations.fieldNamePlaceHolder,
      errorList: [
        {
          errorCondition: "required",
          errorMsg: this.config.translations.requiredField,
        },
      ],
      disableOptionCentering: true,
      name: "paramSelectBox",
      formControl: newFormControl,
    };
  }

  getParamInputBoxConfig(newFormControl) {
    return {
      name: "paramTextBox",
      placeholder: this.config.translations.fieldNamePlaceHolder,
      formControlName: newFormControl,
      type: "text",
      maximumLength: 256,
      errorList: [
        {
          errorCondition: "required",
          errorMsg: this.config.translations.requiredField,
        },
      ],
    };
  }

  getGroupParentAndIndex(group) {
    const result = { group: null, index: null, logicalOperator: null };
    let found = false;

    function recurse(groupToFind, parentGroup) {
      const logicalOperator = parentGroup.AND ? "AND" : "OR";
      for (let i = 0; i < parentGroup[logicalOperator].length; i++) {
        if (
          parentGroup[logicalOperator][i].groupId &&
          parentGroup[logicalOperator][i].groupId === groupToFind.groupId
        ) {
          result.group = parentGroup;
          result.index = i;
          result.logicalOperator = logicalOperator;
          found = true;
          break;
        }
      }

      if (!found) {
        for (let i = 0; i < parentGroup[logicalOperator].length; i++) {
          if (found) {
            break;
          }
          if (
            parentGroup[logicalOperator][i].AND ||
            parentGroup[logicalOperator][i].OR
          ) {
            recurse(groupToFind, parentGroup[logicalOperator][i]);
          }
        }
      }
    }

    recurse(group, this.groupArray[0]);
    return result;
  }

  // Group actions events
  toggleChange(event, group) {
    if (event.checked) {
      group.toggleConfig.checked = true;
      delete Object.assign(group, { ["AND"]: group["OR"] })["OR"];
    } else {
      group.toggleConfig.checked = false;
      delete Object.assign(group, { ["OR"]: group["AND"] })["AND"];
    }

    if (this.config.emitQueryJsonOnFormChange) {
      setTimeout(() => {
        const data = this.getQueryJson();
        const state = this.getQbState();
        if (data) {
          this.queryBuilderStateStream.emit({
            jsonData: data,
            qbFormIsValid: state,
          });
        }
      }, 0);
    }
  }

  addRule(group) {
    const logicalOperator = group.AND ? "AND" : "OR";
    const groupIndex = group.groupId;
    if (this.totalRuleCount < this.config.maxRuleCount) {
      const newRuleIndex = ++this.currentRuleIndex;
      ++this.totalRuleCount;
      this.queryBuilderForm.controls[groupIndex]["controls"].rules.addControl(
        `${groupIndex}_rule_${newRuleIndex}`,
        this.generateNewRuleControls(groupIndex, newRuleIndex)
      );
      const newRule = {
        datatype: "",
        dateFormat: "",
        opt: "",
        param: "",
        paramLabel: "",
        value: "",
        ruleId: newRuleIndex,
      };
      this.showSwitch[group.groupId] = true;
      group[logicalOperator].push(newRule);
      if (group[logicalOperator].length > 1) {
        group.toggleConfig.disabled = false;
      } else {
        if (this.config.isHideSwitch) {
          this.showSwitch[group.groupId] = false;
        }
      }

      if (
        this.config.autoSingleValueSelect &&
        this.config.columnFields.length == 1
      ) {
        this.rulesConfigMap
          .get(`${groupIndex}_rule_${newRuleIndex}_param`)
          .formControl.setValue(this.config.columnFields[0].fieldValue);
        this.updateRuleParameter(
          this.config.columnFields[0].fieldValue,
          newRule,
          `${groupIndex}_rule_${newRuleIndex}`,
          group
        );
        this.rulesConfigMap.get(
          `${groupIndex}_rule_${newRuleIndex}_param`
        ).disabled = true;
      }

      this.addRuleBtnConfig.disabled = this.totalRuleCount === this.config.maxRuleCount;
    } else {
      this.addRuleBtnConfig.disabled = true;
    }
  }

  addGroup(group) {
    const logicalOperator = group.AND ? "AND" : "OR";
    const newGroupIndex = "group_" + ++this.currentGroupIndex;
    this.queryBuilderForm.addControl(
      newGroupIndex,
      this.generateNewGroupControls(newGroupIndex)
    );
    const newGroupItem = {
      groupId: newGroupIndex,
      groupName: `Group ${this.currentGroupIndex}`,
      toggleConfig: {
        color: "primary",
        disabled: true,
        name: "toggle",
        checked: true,
      },
      AND: [],
      nestedGroupIndex:
        group.nestedGroupIndex >= this.colorArray.length - 1
          ? 0
          : group.nestedGroupIndex + 1,
    };
    this.showSwitch[group.groupId] = true;
    this.addRule(newGroupItem);
    group[logicalOperator].push(newGroupItem);
    if (group[logicalOperator].length > 1) {
      group.toggleConfig.disabled = false;
    } else {
      if (this.config.isHideSwitch) {
        this.showSwitch[group.groupId] = false;
      }
    }
  }

  deleteGroup(group) {
    this.selectedGroupToDelete = group;
    if (this.isNestedGroup(group)) {
      this.modalService.openDialog(this.deleteNestedGroupDialogConfig);
    } else {
      this.modalService.openDialog(this.deleteSingleGroupDialogConfig);
    }
  }

  proceedToDeleteGroup(group) {
    this.deleteGroupConfig(group);
    this.queryBuilderForm.removeControl(group.groupId);
    this.rulesConfigMap.delete(group.groupId);
    const parentGroupDetails = this.getGroupParentAndIndex(group);
    parentGroupDetails.group[parentGroupDetails.logicalOperator].splice(
      parentGroupDetails.index,
      1
    );
    this.selectedGroupToDelete = null;
    this.showSwitch[parentGroupDetails.group.groupId] = true;
    if (
      parentGroupDetails.group[parentGroupDetails.logicalOperator].length < 2
    ) {
      parentGroupDetails.group.toggleConfig.disabled = true;
      if (this.config.isHideSwitch) {
        this.showSwitch[parentGroupDetails.group.groupId] = false;
      }
    }
  }

  isNestedGroup(group) {
    const logicalOP = group.AND ? "AND" : "OR";
    return group[logicalOP].some((el) => el.AND || el.OR);
  }

  deleteGroupConfig(group) {
    const logicalOperator = group.AND ? "AND" : "OR";
    const tempGroup = JSON.parse(JSON.stringify(group));

    tempGroup[logicalOperator].forEach((g) => {
      if (g[logicalOperator]) {
        this.deleteGroupConfig(g);
        this.queryBuilderForm.controls[g.groupId][
          "controls"
        ].rules.removeControl(g[logicalOperator].groupId);
        this.rulesConfigMap.delete(g[logicalOperator].groupId);
      } else {
        this.deleteRule(group, g.ruleId);
      }
    });
  }

  // Rule events
  filterOptions(searchString, group, rule) {
    const dropDownConfig = this.rulesConfigMap.get(
      group.groupId + "_rule_" + rule.ruleId + "_param"
    );
    if (this.isColumnType) {
      if (searchString) {
        dropDownConfig.options = this.fieldNameBaseObject[
          group.groupId + "_rule_" + rule.ruleId
        ].filter((element) =>
          element.label?.toUpperCase().includes(searchString.toUpperCase())
        );
      } else {
        dropDownConfig.options = [
          ...this.fieldNameBaseObject[group.groupId + "_rule_" + rule.ruleId],
        ];
      }
    } else {
      if (searchString) {
        dropDownConfig.options = this.fieldNameBaseOptions.filter((element) =>
          element.label?.toUpperCase().includes(searchString.toUpperCase())
        );
      } else {
        dropDownConfig.options = [...this.fieldNameBaseOptions];
      }
    }
  }

  resetOptions(event, group, rule) {
    if (!event) {
      const dropDownConfig = this.rulesConfigMap.get(
        group.groupId + "_rule_" + rule.ruleId + "_param"
      ),
        searchInputConfig = this.rulesConfigMap.get(
          group.groupId + "_rule_" + rule.ruleId + "_param_search"
        ),
        valueSearchInputConfig = this.rulesConfigMap.get(
          group.groupId + "_rule_" + rule.ruleId + "_value_search"
        ),
        valuedropDownConfig = this.rulesConfigMap.get(
          group.groupId + "_rule_" + rule.ruleId + "_value"
        );
      if (this.isColumnType) {
        dropDownConfig.options = [
          ...this.fieldNameBaseObject[group.groupId + "_rule_" + rule.ruleId],
        ];
      } else {
        dropDownConfig.options = [...this.fieldNameBaseOptions];
      }

      if ("options" in valuedropDownConfig) {
        valuedropDownConfig.options = [
          ...this.valueAsDropdownList[group.groupId + "_rule_" + rule.ruleId],
        ];
      }
      searchInputConfig?.formControlName?.setValue("");
      valueSearchInputConfig?.formControlName?.setValue("");
    }
  }

  clearFieldNameSearch(group, rule) {
    const dropDownConfig = this.rulesConfigMap.get(
      group.groupId + "_rule_" + rule.ruleId + "_param"
    ),
      searchInputConfig = this.rulesConfigMap.get(
        group.groupId + "_rule_" + rule.ruleId + "_param_search"
      );

    searchInputConfig.formControlName.reset();
    if (this.isColumnType) {
      dropDownConfig.options = [
        ...this.fieldNameBaseObject[group.groupId + "_rule_" + rule.ruleId],
      ];
    } else {
      dropDownConfig.options = [...this.fieldNameBaseOptions];
    }
  }

  getOperators(paramDataType, paramSelected) {
    let filteredOptions;
    const dropDownFields = this.checkForFieldType(
      this.config.dropdownColumns,
      paramSelected,
      "DROPDOWN"
    );
    const folderFields = this.checkForFieldType(
      this.config.folderColumns,
      paramSelected,
      "FOLDER"
    );
    if (paramDataType.toLowerCase() === "String".toLowerCase()) {
      filteredOptions = this.config.conditionConfigStringType;
    } else if (
      paramDataType.toLowerCase() === "Numeric".toLowerCase() ||
      paramDataType.toLowerCase() === "Integer".toLowerCase() ||
      paramDataType.toLowerCase() === "Long".toLowerCase() ||
      paramDataType.toLowerCase() === "Double".toLowerCase() ||
      paramDataType.toLowerCase() === "Float".toLowerCase()
    ) {
      filteredOptions = this.config.conditionConfigNumberType;
    } else if (paramDataType.toLowerCase() === "Bit".toLowerCase()) {
      filteredOptions = this.config.conditionConfigBitType;
    } else if (paramDataType.toLowerCase() === "Expression".toLowerCase()) {
      filteredOptions = this.config.conditionConfigExpressionType;
    } else if (paramDataType.toLowerCase() === "Boolean".toLowerCase()) {
      filteredOptions = this.config.conditionConfigBooleanType;
    } else {
      filteredOptions = this.config.conditionConfigDateType;
    }

    if (
      (dropDownFields && dropDownFields.length) ||
      (folderFields && folderFields.length)
    ) {
      filteredOptions = this.config.conditionConfigBitType;
      if (
        paramSelected?.formControl?.value?.type?.toLowerCase() === "segment" ||
        paramSelected?.value?.param?.toLowerCase() === "segment"
      ) {
        filteredOptions = this.config.conditionConfigSegmentType;
      }
    }

    return filteredOptions;
  }

  getConditionsDropdownConfig(newFormControl, fieldDatatype, ruleConfig) {
    let filteredOptions = this.getOperators(fieldDatatype, ruleConfig);
    return {
      ...this.conditionConfig,
      formControl: newFormControl,
      options: filteredOptions,
    };
  }

  updateRuleParamType(paramType, ruleIndex, group, rule) {
    let columnSelectedArr = this.config.dynamicColumnFields[paramType],
      fc =
        this.rulesConfigMap.get(ruleIndex + "_param").formControl ||
        this.rulesConfigMap.get(ruleIndex + "_param").formControlName;
    if (!columnSelectedArr) {
      this.rulesConfigMap.set(
        ruleIndex + "_param",
        this.getParamInputBoxConfig(fc)
      );
      this.rulesConfigMap.get(ruleIndex + "_opt").options =
        this.config.conditionConfigAllType;
      this.rulesConfigMap
        .get(ruleIndex + "_opt")
        .formControl.setValue(
          this.rulesConfigMap.get(ruleIndex + "_opt").options[0].value
        );
      rule.selectBox = false;
      rule.inputBox = true;
    } else if (columnSelectedArr?.[0]?.optList) {
      this.rulesConfigMap.set(
        ruleIndex + "_param",
        this.getParamSelectBoxConfig(fc)
      );
      this.fieldNameBaseObject[ruleIndex] = columnSelectedArr;
      this.rulesConfigMap.get(ruleIndex + "_param").options = [
        ...columnSelectedArr,
      ];
      rule.selectBox = true;
      rule.inputBox = false;
    } else {
      rule.inputBox = true;
      rule.selectBox = false;
      this.fieldNameBaseObject[ruleIndex] = columnSelectedArr?.map((ele) => {
        return {
          label: ele?.label,
          value: ele?.value,
          toolTipHTML: `<span>${ele?.label}</span>`,
        };
      });
      this.rulesConfigMap.set(
        ruleIndex + "_param",
        this.getFieldsElementDropdownConfig(fc)
      );
      this.rulesConfigMap.get(ruleIndex + "_param").options = [
        ...this.fieldNameBaseObject[ruleIndex],
      ];
      setTimeout(() => {
        rule.selectBox = false;
        rule.inputBox = false;
      })
    }

    this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
      "controls"
    ][ruleIndex].reset();
    this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
      "controls"
    ][ruleIndex]["controls"]["paramType"].setValue(paramType);
    rule.paramType = paramType;
    this.queryBuilderForm.updateValueAndValidity({
      onlySelf: false,
      emitEvent: true,
    });
  }

  updateRuleParameterForInput(param, rule, ruleIndex) {
    rule.param = param;
    rule.datatype = "string";
    const value1Config = this.prepareValueFieldConfig(
      "string",
      rule,
      ruleIndex,
      { dateFormat: "dd MM yyyy" }
    );
    this.rulesConfigMap.set(ruleIndex + "_value", value1Config);
  }

  updateRuleParameter(param, rule, ruleIndex, group) {
    let paramObj;
    if (this.isColumnType) {
      if (this.fieldNameBaseObject[ruleIndex]?.[0]?.optList?.length) {
        for (let opt of this.fieldNameBaseObject[ruleIndex]) {
          for (let optlist of opt.optList) {
            if (optlist?.value?.name === param?.name) {
              paramObj = { label: optlist.viewValue, value: optlist.value };
              break;
            }
          }
        }
      } else {
        paramObj = this.fieldNameBaseObject?.[ruleIndex]?.find(
          (el) => el.value.name === (param?.value?.name || param?.name)
        );
      }
    } else {
      paramObj = this.config.columnFields?.find(
        (el) => el.fieldValue === param
      );
    }

    const isDynamicRule: boolean = paramObj
      ? paramObj.isDynamic
        ? true
        : false
      : false;
    this.supportDynamic[ruleIndex] = {
      isDynamic: isDynamicRule,
      controlType: paramObj.controlType ? paramObj.controlType : null,
    };
    this.dynamicRuleIndex[`${group.groupId}~rule_${rule.ruleId}`] =
      isDynamicRule;
    this.groupHasDynamicRule = this.createDynamicGroupObj();
    let paramDataType = paramObj.fieldDataType;
    const oldRule = { ...rule };
    rule.param = param;
    rule.paramLabel = paramObj.fieldLabel;
    if (this.isColumnType) {
      paramDataType =
        paramObj?.value?.type?.toLowerCase() === "segment"
          ? "NUMERIC"
          : paramObj?.value?.type;
      rule.paramLabel = paramObj?.label;
      rule.param = param?.name;
    }
    if (
      rule.datatype.toLowerCase() !== paramDataType.toLowerCase() ||
      this.config.dropdownColumns?.length ||
      this.config.folderColumns?.length
    ) {
      if (
        paramDataType.toLowerCase() === "Expression".toLowerCase() &&
        rule.datatype
      ) {
        paramDataType = rule.datatype;
      } else {
        rule.datatype = paramDataType;
      }
      const conditionsConfig = this.rulesConfigMap.get(ruleIndex + "_opt");
      let filteredOptions = this.getOperators(
        paramDataType,
        this.rulesConfigMap.get(ruleIndex + "_param")
      );
      conditionsConfig.options = filteredOptions;
      conditionsConfig.formControl.reset();

      rule.opt = filteredOptions?.[0]?.value;
      conditionsConfig.formControl.setValue(filteredOptions?.[0]?.value);
      this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
        "controls"
      ][ruleIndex]["controls"]["opt"].setValue(filteredOptions?.[0]?.value);
      this.queryBuilderForm.updateValueAndValidity({
        onlySelf: false,
        emitEvent: true,
      });

      const value1Config = this.prepareValueFieldConfig(
        paramDataType,
        rule,
        ruleIndex,
        paramObj
      );
      this.rulesConfigMap.set(ruleIndex + "_value", value1Config);

      if (this.supportDynamic[ruleIndex].isDynamic) {
        if (!this.rulesConfigMap.get(ruleIndex).controls.dynamicValue) {
          this.rulesConfigMap
            .get(ruleIndex)
            .addControl(
              "dynamicValue",
              new UntypedFormControl(null, [Validators.required])
            );
        }
        if (!this.rulesConfigMap.get(ruleIndex).controls.selectionType) {
          this.rulesConfigMap
            .get(ruleIndex)
            .addControl(
              "selectionType",
              new UntypedFormControl(null, [Validators.required])
            );
        }
        const dynamicValueConfig = this.getDynamicValueFieldConfig(
          this.rulesConfigMap.get(ruleIndex).controls.dynamicValue,
          paramDataType
        );
        const valueSelectionConfig = this.getSelectionTypeConfig(
          this.rulesConfigMap.get(ruleIndex).controls.selectionType
        );
        this.rulesConfigMap.set(
          `${ruleIndex}_dynamicValue`,
          dynamicValueConfig
        );
        this.rulesConfigMap.set(
          `${ruleIndex}_selectionType`,
          valueSelectionConfig
        );

        this.checkDynamicAttrTypes(paramDataType);
        this.rulesConfigMap
          .get(ruleIndex + "_dynamicValue")
          .formControl.reset();
        this.rulesConfigMap
          .get(ruleIndex + "_selectionType")
          .formControl.reset();
        this.rulesConfigMap.get(ruleIndex + "_dynamicValue").options =
          this.dynamicAttrOfType;
      }

      if (rule.opt === "between" || rule.opt === "notbetween") {
        this.rulesConfigMap.get(ruleIndex + "_value2").formControlName
          ? this.rulesConfigMap
            .get(ruleIndex + "_value2")
            .formControlName.reset()
          : this.rulesConfigMap.get(ruleIndex + "_value2").formControl.reset();
      }
      rule.value = Array.isArray(rule.value) ? [] : "";
    }
    const paramMap = this.rulesConfigMap.get(`${ruleIndex}_param`);
    const folderFields = this.checkForFieldType(
      this.config.folderColumns,
      paramMap || ruleIndex,
      "FOLDER"
    );
    if (folderFields && folderFields.length) {
      this.openFolderTree(group);
    }
    // open expression builder side panel
    if (
      paramObj?.fieldDataType?.toLowerCase() === "Expression".toLowerCase() &&
      this.config.enableExpressionBuilder
    ) {
      this.currentExpressionObject = {
        rule: rule,
        ruleIndex: ruleIndex,
        group: group,
        isParamField: true,
        oldRule: oldRule,
      };
      this.textareaConfig.formControlName.setValue(
        rule.expressionField ? rule.expressionField : null
      );
      this.openSidePanelForExpressionBuilder();
    }
  }

  prepareValueFieldConfig(paramDataType, rule, ruleIndex, paramObj) {
    const value1Config =
      paramDataType.toLowerCase() === "Date".toLowerCase() &&
        this.config.showDatePicker
        ? this.getDateFieldConfig(
          (this.rulesConfigMap.get(ruleIndex + "_value").formControlName || this.rulesConfigMap.get(ruleIndex + "_value").formControl)
        )
        : this.getInputFieldConfig(
          this.rulesConfigMap.get(ruleIndex + "_value").formControl ||
          this.rulesConfigMap.get(ruleIndex + "_value").formControlName,
          paramDataType,
          ruleIndex,
          paramObj.dateFormat
        );

    value1Config.formControlName
      ? value1Config.formControlName?.reset()
      : value1Config.formControl?.reset();

    if (
      paramDataType.toLowerCase() === "Date".toLowerCase() &&
      !this.config.showDatePicker
    ) {
      value1Config.formControlName.clearValidators();
      value1Config.formControlName.setValidators([
        Validators.required,
        this.validateDateFormat(
          rule.value,
          paramObj.dateFormat
            ? paramObj.dateFormat
            : rule.dateFormat
              ? rule.dateFormat
              : "dd MM yyyy"
        ),
      ]);

      let errorObj = _.find(value1Config.errorList, {
        errorCondition: "invalidDateFormat",
      });

      const dateFormat = paramObj.dateFormat
        ? paramObj.dateFormat
        : rule.dateFormat;
      errorObj.errorMsg =
        this.config.translations.invalidDateFormat + " " + dateFormat;
    } else {
      value1Config?.formControlName?.clearValidators();
      value1Config?.formControlName?.setValidators([Validators.required]);
      value1Config?.formControl?.clearValidators();
      value1Config?.formControl?.setValidators([Validators.required]);
    }
    return value1Config;
  }

  createDynamicGroupObj() {
    let dynamicGroupArr = {};
    for (const [key, value] of this.getKeyValOfObj(this.dynamicRuleIndex)) {
      if (value) {
        dynamicGroupArr[key.split("~")[0]] = value;
      }
    }
    return dynamicGroupArr;
  }

  getKeyValOfObj(obj) {
    const ownProps = Object.keys(obj);
    let i = ownProps.length,
      resArray = new Array(i);
    while (i--) resArray[i] = [ownProps[i], obj[ownProps[i]]];
    return resArray;
  }

  updateRuleOperator(opt, rule, value1Config, group) {
    rule.opt = opt;
    const paramObj = this.config.columnFields.find(
      (el) => el.fieldValue === rule.param
    );
    value1Config.formControlName
      ? value1Config.formControlName.reset()
      : value1Config.formControl.reset();
    if (opt === "between" || opt === "notbetween") {
      rule.value = [];
      if (
        rule.datatype.toLowerCase() === "Date".toLowerCase() &&
        !this.config.showDatePicker
      ) {
        this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
          "controls"
        ][`${group.groupId}_rule_${rule.ruleId}`].addControl(
          "value2",
          new UntypedFormControl(null, [
            Validators.required,
            this.validateDateFormat(
              rule.value,
              paramObj.dateFormat ? paramObj.dateFormat : "dd MM yyyy"
            ),
          ])
        );
      } else {
        this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
          "controls"
        ][`${group.groupId}_rule_${rule.ruleId}`].addControl(
          "value2",
          new UntypedFormControl(
            null,
            rule.datatype.toLowerCase() === "Numeric".toLowerCase() ||
              rule.datatype.toLowerCase() === "Integer".toLowerCase() ||
              rule.datatype.toLowerCase() === "Long".toLowerCase() ||
              rule.datatype.toLowerCase() === "Double".toLowerCase() ||
              rule.datatype.toLowerCase() === "Float".toLowerCase()
              ? [
                Validators.required,
                this.validateDigitsInRange(rule.value, "to"),
              ]
              : [Validators.required]
          )
        );
      }

      const value2FormControl =
        this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
          "controls"
        ][`${group.groupId}_rule_${rule.ruleId}`].get("value2");
      value2FormControl.setValue(null);
      let value2Config;
      if (
        rule.datatype.toLowerCase() === "Date".toLowerCase() &&
        this.config.showDatePicker
      ) {
        value2Config = this.getDateFieldConfig(value2FormControl);
      } else {
        value2Config = this.getInputFieldConfig(
          value2FormControl,
          rule.datatype,
          `${group.groupId}_rule_${rule.ruleId}`
        );
      }
      this.rulesConfigMap.set(
        `${`${group.groupId}_rule_${rule.ruleId}`}_value2`,
        value2Config
      );

      if (
        rule.datatype.toLowerCase() === "Numeric".toLowerCase() ||
        rule.datatype.toLowerCase() === "Integer".toLowerCase() ||
        rule.datatype.toLowerCase() === "Long".toLowerCase() ||
        rule.datatype.toLowerCase() === "Double".toLowerCase() ||
        rule.datatype.toLowerCase() === "Float".toLowerCase()
      ) {
        const value1FomrControl =
          this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
            "controls"
          ][`${group.groupId}_rule_${rule.ruleId}`].get("value");
        value1FomrControl.clearValidators();
        value1FomrControl.setValidators([
          Validators.required,
          this.validateDigitsInRange(rule.value, "from"),
        ]);
      }

      if (
        rule.datatype.toLowerCase() === "Date".toLowerCase() &&
        !this.config.showDatePicker
      ) {
        const value1FomrControl =
          this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
            "controls"
          ][`${group.groupId}_rule_${rule.ruleId}`].get("value");
        value1FomrControl.clearValidators();
        value1FomrControl.setValidators([
          Validators.required,
          this.validateDateFormat(
            rule.value,
            paramObj.dateFormat ? paramObj.dateFormat : "dd MM yyyy"
          ),
        ]);
      }

      if (
        this.supportDynamic[`${group.groupId}_rule_${rule.ruleId}`].isDynamic
      ) {
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.dynamicValue.setValidators(null);
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.dynamicValue.updateValueAndValidity();
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.selectionType.setValidators(null);
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.selectionType.updateValueAndValidity();
      }
    } else if (opt === "in" || opt === "notin") {
      rule.value = [];
      value1Config.formControlName
        ? value1Config.formControlName.clearValidators()
        : value1Config.formControl.clearValidators();
      value1Config.formControlName
        ? value1Config.formControlName.updateValueAndValidity()
        : value1Config.formControl.updateValueAndValidity();
      this.inOperatorValuesMap.set(
        `${group.groupId}_rule_${rule.ruleId}`,
        rule.value
      );
      // this.rulesConfigMap.set(`${group.groupId}_rule_${rule.ruleId}_addBtn`, { ...this.inOtpAddBtnConfig });
      this.rulesConfigMap.set(`${group.groupId}_rule_${rule.ruleId}_moreBtn`, {
        ...this.plusMoreBtnConfig,
        value: `  ${rule.value.length - 2} ${this.config.translations.more}`,
      });

      if (rule.datatype.toLowerCase() === "Date".toLowerCase()) {
        const fromDateConfig = this.rulesConfigMap.get(
          `${group.groupId}_rule_${rule.ruleId}_value`
        );
        fromDateConfig.maxDate = null;
      }
      this.rulesConfigMap.delete(`${group.groupId}_rule_${rule.ruleId}_value2`);
      this.queryBuilderForm.controls[group.groupId]["controls"].rules[
        "controls"
      ][`${group.groupId}_rule_${rule.ruleId}`].removeControl("value2");
      const value1FomrControl =
        this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
          "controls"
        ][`${group.groupId}_rule_${rule.ruleId}`].get("value");
      value1FomrControl.clearValidators();
      value1FomrControl.setValidators(null);
      if (
        this.supportDynamic[`${group.groupId}_rule_${rule.ruleId}`].isDynamic
      ) {
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.dynamicValue.setValidators(null);
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.dynamicValue.updateValueAndValidity();
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.selectionType.setValidators(null);
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.selectionType.updateValueAndValidity();
      }
    } else if (
      opt === "null" ||
      opt === "notnull" ||
      opt === "true" ||
      opt === "false"
    ) {
      rule.value = undefined;
      value1Config.formControlName.setValidators(null);
      value1Config.formControlName.updateValueAndValidity();
      if (
        this.inOperatorValuesMap.has(`${group.groupId}_rule_${rule.ruleId}`)
      ) {
        this.inOperatorValuesMap.delete(`${group.groupId}_rule_${rule.ruleId}`);
      }
      if (
        this.supportDynamic[`${group.groupId}_rule_${rule.ruleId}`].isDynamic
      ) {
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.dynamicValue.setValidators(null);
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.dynamicValue.updateValueAndValidity();
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.selectionType.setValidators(null);
        this.queryBuilderForm.controls[group.groupId]["controls"].rules[
          "controls"
        ][
          `${group.groupId}_rule_${rule.ruleId}`
        ].controls.selectionType.updateValueAndValidity();
      }
    } else {
      rule.value = "";
      if (
        !this.supportDynamic[`${group.groupId}_rule_${rule.ruleId}`].isDynamic
      ) {
        value1Config.formControlName
          ? value1Config.formControlName.setValidators([Validators.required])
          : value1Config.formControl.setValidators([Validators.required]);
        value1Config.formControlName
          ? value1Config.formControlName.updateValueAndValidity()
          : value1Config.formControl.updateValueAndValidity();
      } else {
        if (
          !this.queryBuilderForm.controls[group.groupId]["controls"].rules[
            "controls"
          ][`${group.groupId}_rule_${rule.ruleId}`].controls.selectionType
            .validators
        ) {
          this.queryBuilderForm.controls[group.groupId]["controls"].rules[
            "controls"
          ][
            `${group.groupId}_rule_${rule.ruleId}`
          ].controls.selectionType.setValidators([Validators.required]);
          this.queryBuilderForm.controls[group.groupId]["controls"].rules[
            "controls"
          ][
            `${group.groupId}_rule_${rule.ruleId}`
          ].controls.selectionType.updateValueAndValidity();
        }
        this.changeSelectionType(
          "EDIT_CASE",
          rule,
          group.groupId,
          `${group.groupId}_rule_${rule.ruleId}`
        );
      }
      this.inOperatorValuesMap.delete(`${group.groupId}_rule_${rule.ruleId}`);
      if (rule.datatype.toLowerCase() === "Date".toLowerCase()) {
        const fromDateConfig = this.rulesConfigMap.get(
          `${group.groupId}_rule_${rule.ruleId}_value`
        );
        fromDateConfig.maxDate = null;
      }

      this.rulesConfigMap.delete(`${group.groupId}_rule_${rule.ruleId}_value2`);
      this.queryBuilderForm.controls[group.groupId]["controls"].rules[
        "controls"
      ][`${group.groupId}_rule_${rule.ruleId}`].removeControl("value2");
      const value1FomrControl =
        this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
          "controls"
        ][`${group.groupId}_rule_${rule.ruleId}`].get("value");
      if (
        !this.supportDynamic[`${group.groupId}_rule_${rule.ruleId}`].isDynamic
      ) {
        if (
          rule.datatype.toLowerCase() === "Date".toLowerCase() &&
          !this.config.showDatePicker
        ) {
          value1FomrControl.clearValidators();
          value1FomrControl.setValidators([
            Validators.required,
            this.validateDateFormat(
              rule.value,
              paramObj.dateFormat ? paramObj.dateFormat : "dd MM yyyy"
            ),
          ]);
        } else {
          value1FomrControl.clearValidators();
          value1FomrControl.setValidators([Validators.required]);
        }
      }
    }
    this.queryBuilderForm.updateValueAndValidity({
      onlySelf: false,
      emitEvent: true,
    });
  }

  updateRuleValue1(value, rule, group, ruleIndex: string) {
    const formControl =
      this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
      "controls"
      ][ruleIndex]["controls"];
    if (!value && (rule.opt === "null" || rule.opt === "notnull")) {
      formControl["value"].setValue(null);
    }
    if (rule.opt === "between" || rule.opt === "notbetween") {
      if (rule.datatype.toLowerCase() === "Date".toLowerCase()) {
        if (this.config.showDatePicker) {
          rule.value[0] = value;
          const toDateConfig = this.rulesConfigMap.get(ruleIndex + "_value2");
          toDateConfig.minDate = value;
        } else {
          rule.value[0] = value;
          formControl["value2"].updateValueAndValidity({
            onlySelf: false,
            emitEvent: true,
          });
        }
      } else {
        rule.value[0] = value;
        formControl["value2"].updateValueAndValidity({
          onlySelf: false,
          emitEvent: true,
        });
      }
    } else if (rule.opt === "in" || rule.opt === "notin") {
      // don't do anything
    } else if (rule.opt !== "in" && rule.opt !== "notin") {
      if (!this.supportDynamic[ruleIndex].isDynamic) {
        rule.value = value;
      } else {
        if (formControl["selectionType"].value === "staticvalue") {
          rule.selectionType = "staticvalue";
          rule.value = value;
        } else {
          rule.selectionType = "dynamicvalue";
          rule.dynamicValue = value;

          // open side panel to build expression for current rule dynamic value
          const paramObj = this.config.dynamicValues.find(
            (el) => el.value === value
          );
          if (
            paramObj?.type === "Expression" &&
            this.config.enableExpressionBuilder
          ) {
            this.currentExpressionObject = {
              rule: rule,
              ruleIndex: ruleIndex,
              group: group,
              isParamField: false,
            };
            this.supportDynamic[
              this.currentExpressionObject.ruleIndex
            ].controlType = "htmlDiv";
            this.openSidePanelForExpressionBuilder();
          }
        }
      }
    }
  }

  updateRuleValue2(value, rule, groupIndex, ruleIndex: string) {
    const formControl =
      this.queryBuilderForm.controls[groupIndex]["controls"]["rules"][
      "controls"
      ][ruleIndex]["controls"];
    if (!value && (rule.opt === "null" || rule.opt === "notnull")) {
      formControl["value2"].setValue(null);
    }
    if (rule.datatype.toLowerCase() === "Date".toLowerCase()) {
      if (this.config.showDatePicker) {
        rule.value[1] = value;
        const fromDateConfig = this.rulesConfigMap.get(ruleIndex + "_value");
        fromDateConfig.maxDate = value;
      } else {
        rule.value[1] = value;
        this.queryBuilderForm.controls[groupIndex]["controls"]["rules"][
          "controls"
        ][ruleIndex]["controls"]["value"].updateValueAndValidity({
          onlySelf: false,
          emitEvent: true,
        });
      }
    } else {
      rule.value[1] = value;
      this.queryBuilderForm.controls[groupIndex]["controls"]["rules"][
        "controls"
      ][ruleIndex]["controls"]["value"].updateValueAndValidity({
        onlySelf: false,
        emitEvent: true,
      });
    }
  }

  addInOptValue(inputConfig, rule, moreBtnConfigId) {
    if (inputConfig.formControlName.value) {
      if (
        rule.value.some((el) =>
          rule.datatype.toLowerCase() === "Date".toLowerCase()
            ? Date.parse(el) === Date.parse(inputConfig.formControlName.value)
            : el == inputConfig.formControlName.value
        )
      ) {
        this.notificationService.show({
          message: this.config.translations.valueAlreadyPresent,
          type: "error",
          close: true,
          autoHide: 6000,
        });
      } else {
        rule.value.push(inputConfig.formControlName.value);
        inputConfig.formControlName.reset();

        const buttonConfig = this.rulesConfigMap.get(moreBtnConfigId);
        buttonConfig.value = `  ${rule.value.length - 2} ${this.config.translations.more
          }`;
      }
    }
  }

  getInOperatorPopupTitle() {
    if (this.selectedRuleToViewInValues) {
      const paramDataType = this.selectedRuleToViewInValues.rule.datatype;
      let operatorLabel;

      if (paramDataType.toLowerCase() === "String".toLowerCase()) {
        operatorLabel = this.config.conditionConfigStringType.find(
          (el) => el.value === this.selectedRuleToViewInValues.rule.opt
        ).label;
      } else if (
        paramDataType.toLowerCase() === "Numeric".toLowerCase() ||
        paramDataType.toLowerCase() === "Integer".toLowerCase() ||
        paramDataType.toLowerCase() === "Long".toLowerCase() ||
        paramDataType.toLowerCase() === "Double".toLowerCase() ||
        paramDataType.toLowerCase() === "Float".toLowerCase()
      ) {
        operatorLabel = this.config.conditionConfigNumberType.find(
          (el) => el.value === this.selectedRuleToViewInValues.rule.opt
        ).label;
      } else if (paramDataType.toLowerCase() === "Bit".toLowerCase()) {
        operatorLabel = this.config.conditionConfigBitType.find(
          (el) => el.value === this.selectedRuleToViewInValues.rule.opt
        ).label;
      } else {
        operatorLabel = this.config.conditionConfigDateType.find(
          (el) => el.value === this.selectedRuleToViewInValues.rule.opt
        ).label;
      }
      return this.selectedRuleToViewInValues.rule.param + " " + operatorLabel;
    }
  }

  validateDigitsInRange(
    dateValues: number[],
    dateRangeType: string
  ): ValidatorFn {
    return (control: AbstractControl): { [key: string]: boolean } | null => {
      if (control.value) {
        if (dateRangeType === "from" && control.value >= +dateValues[1]) {
          return { invalidDigitsRange: true };
        } else if (dateRangeType === "to" && control.value <= +dateValues[0]) {
          return { invalidDigitsRange: true };
        }
      }
      return null;
    };
  }

  validateDateFormat(dateValue: string, dateFormat: string): ValidatorFn {
    return (control: AbstractControl): { [key: string]: boolean } | null => {
      if (control.value && !this.config.showDatePicker) {
        // converting utc to epoch
        let value1 = moment(control.value, dateFormat.toUpperCase(), true);
        if (!value1.isValid()) {
          return { invalidDateFormat: true };
        }
      }
      return null;
    };
  }

  removeInOptValue(index, rule, moreBtnConfigId) {
    rule.value.splice(index, 1);
    this.queryBuilderForm.updateValueAndValidity({
      onlySelf: false,
      emitEvent: true,
    });

    const buttonConfig = this.rulesConfigMap.get(moreBtnConfigId);
    buttonConfig.value = `  ${rule.value.length - 2} ${this.config.translations.more
      }`;
  }

  openRuleValuesModal(rule, moreBtnConfigId) {
    this.selectedRuleToViewInValues = { rule, moreBtnConfigId };
    this.modalService.openDialog(this.inOperatorRuleValuesDialogConfig);
  }

  deleteRule(group, ruleIndex) {
    const logicalOperator = group.AND ? "AND" : "OR";
    const groupIndex = group.groupId;
    const ruleArrayIndex = group[logicalOperator].findIndex(
      (el) => el.ruleId === ruleIndex
    );
    this.queryBuilderForm.controls[groupIndex]["controls"].rules.removeControl(
      `${group.groupId}_rule_${ruleIndex}`
    );
    group[logicalOperator].splice(ruleArrayIndex, 1);
    this.showSwitch[group.groupId] = true;
    if (group[logicalOperator].length < 2) {
      group.toggleConfig.disabled = true;
      if (this.config.isHideSwitch) {
        this.showSwitch[group.groupId] = false;
      }
    }
    this.removeRuleConfig(`${group.groupId}_rule_${ruleIndex}`);

    delete this.dynamicRuleIndex[`${group.groupId}~rule_${ruleIndex}`];
    this.groupHasDynamicRule = this.createDynamicGroupObj();

    if (this.inOperatorValuesMap.has(`${group.groupId}_rule_${ruleIndex}`)) {
      this.inOperatorValuesMap.delete(`${group.groupId}_rule_${ruleIndex}`);
    }
    --this.totalRuleCount;
    this.addRuleBtnConfig.disabled = !this.queryBuilderForm.valid;
  }

  removeRuleConfig(groupAndRuleIndex) {
    this.rulesConfigMap.delete(groupAndRuleIndex + "_param");
    this.rulesConfigMap.delete(groupAndRuleIndex + "_param_search");
    this.rulesConfigMap.delete(groupAndRuleIndex + "_opt");
    this.rulesConfigMap.delete(groupAndRuleIndex + "_value");
    this.rulesConfigMap.delete(groupAndRuleIndex + "_value2");
    this.rulesConfigMap.delete(groupAndRuleIndex);
    // this.rulesConfigMap.delete(groupAndRuleIndex + '_addBtn');
    this.rulesConfigMap.delete(groupAndRuleIndex + "_moreBtn");
    if (this.supportDynamic[groupAndRuleIndex].isDynamic) {
      this.rulesConfigMap.delete(groupAndRuleIndex + "_dynamicValue");
      this.rulesConfigMap.delete(groupAndRuleIndex + "_selectionType");
    }
    if (this.isColumnType) {
      this.rulesConfigMap.delete(groupAndRuleIndex + "_paramType");
    }
    this.rulesConfigMap.delete(groupAndRuleIndex + "_value_search");
  }

  print(data) {
    //.log(data);
  }

  getParamData(paramdata) {
    const paramObj = this.config.columnFields.find(
      (el) => el.fieldValue === paramdata.param
    );
    return paramObj;
  }

  changeCentury(inputDate, format) {
    let momentDate: any = moment(inputDate, format, true);
    let date = new Date(momentDate); //parse the date initially
    const dateArray = inputDate?.split(/[/ .-]/); //regex to look for / or - delimited dates
    const index = format.split(/[/ .-]/).indexOf("YY");
    if (index >= 0 && dateArray && dateArray[index].length === 2) {
      //if the input has a 2 digit year
      const fullDateyear = date.getFullYear();
      if (fullDateyear < 2000)
        //and the parser decided it's before 1950
        date.setFullYear(fullDateyear + 100); //add a century
    }

    return date;
  }

  // Public methods
  public getQueryJson(
    groupArrayData = _.cloneDeep(this.groupArray[0]),
    updateRuleData?: boolean
  ) {
    if (groupArrayData) {
      const jsonItemData: any = {};
      const logicalOp = groupArrayData.AND ? "AND" : "OR";
      jsonItemData[logicalOp] = [];

      for (let i = 0; i < groupArrayData[logicalOp].length; i++) {
        if (
          groupArrayData[logicalOp][i].AND ||
          groupArrayData[logicalOp][i].OR
        ) {
          const nestedJsonData = this.getQueryJson(
            groupArrayData[logicalOp][i]
          );
          jsonItemData[logicalOp].push(nestedJsonData);
        } else {
          // we need to modify json to be sent to API in case of expression builder
          if (updateRuleData && groupArrayData[logicalOp][i].expressionField) {
            groupArrayData[logicalOp][i].param =
              groupArrayData[logicalOp][i].expressionField;
            if (groupArrayData[logicalOp][i].selectionType === "dynamicvalue") {
              groupArrayData[logicalOp][i].value =
                groupArrayData[logicalOp][i].dynamicValue;
            }
          }

          jsonItemData[logicalOp].push(groupArrayData[logicalOp][i]);
        }
      }
      if (!(jsonItemData.AND?.length || jsonItemData.OR?.length)) {
        if (groupArrayData.groupId !== "group_1") {
          this.emptyGroup.emit(true);
        } else {
          this.emptyGroup.emit(false);
        }
      }
      return jsonItemData;
    }
  }

  public reset(data?: any) {
    // this.inlineSpinnerConfig.isLoading = true;
    this.currentGroupIndex = 0;
    this.currentRuleIndex = 0;
    this.totalRuleCount = 0;
    this.groupArray = [];
    this.rulesConfigMap.clear();
    if (this.formSubscription) {
      this.formSubscription.unsubscribe();
    }
    this.queryBuilderForm = this.fb.group({});

    this.config.jsonData = data
      ? data
      : {
        AND: [],
      };

    if (this.isColumnType) {
      this.columnTypeOptions = [];
      this.columnTypeConfig.options = [];

      this.config.columnTypes.forEach((element) => {
        this.columnTypeOptions.push({
          label: element.fieldLabel,
          value: element.fieldValue,
          toolTipHTML: `<span>${element.fieldLabel}</span>`,
        });
      });
      this.columnTypeConfig.options = [...this.columnTypeOptions];
    } else {
      this.fieldNameBaseOptions = [];
      this.fieldNameConfig.options = [];

      this.config.columnFields.forEach((element) => {
        this.fieldNameBaseOptions.push({
          label: element.fieldLabel,
          value: element.fieldValue,
          toolTipHTML: `<span>${element.fieldLabel}</span>`,
          icon: element.profile ? 'hcl-icon-user': null
        });
      });

      this.fieldNameConfig.options = [...this.fieldNameBaseOptions];
    }

    this.groupArray.push(
      this.generateGroupArrayAndControlsFromJson(this.config.jsonData, null)
    );
    this.addRuleBtnConfig.disabled = !this.queryBuilderForm.valid || (this.totalRuleCount === this.config.maxRuleCount);
    this.addGroupBtnConfig.disabled = !this.queryBuilderForm.valid;
    this.checkAndEmitJson();
  }

  public reRender(jsonData) {
    this.reset(jsonData);
  }

  public getQbState() {
    let allInOperatorValuesAvailable = true;
    if (this.inOperatorValuesMap.size) {
      const mapIterator = this.inOperatorValuesMap.values();
      for (let i = 0; i < this.inOperatorValuesMap.size; i++) {
        const valueArray = mapIterator.next().value;
        if (!valueArray.length) {
          allInOperatorValuesAvailable = false;
          break;
        }
      }
    }

    return (
      this.totalRuleCount &&
      this.queryBuilderForm.valid &&
      allInOperatorValuesAvailable
    );
  }

  /**
   * changeSelectionType action on radio to change selection fro static or dynamic value.
   */
  changeSelectionType(value, rule, groupIndex, ruleIndex: string) {
    if (value?.value || value === "EDIT_CASE") {
      let operatorsMap = this.rulesConfigMap.get(ruleIndex + "_opt");
      const paramObj = this.config.columnFields.find(
        (el) => el.fieldValue === rule.param
      );
      const formControl =
        this.queryBuilderForm.controls[groupIndex]["controls"]["rules"][
        "controls"
        ][ruleIndex]["controls"];
      if (
        value?.value === "staticvalue" ||
        (value === "EDIT_CASE" && rule.selectionType === "staticvalue")
      ) {
        rule.dynamicValue = "";
        formControl["dynamicValue"].reset();
        formControl["dynamicValue"].clearValidators();
        if (rule.datatype.toLowerCase() === "Date".toLowerCase()) {
          formControl["value"].setValidators([
            Validators.required,
            this.validateDateFormat(
              rule.value,
              paramObj.dateFormat ? paramObj.dateFormat : "dd MM yyyy"
            ),
          ]);
        } else {
          formControl["value"].setValidators(Validators.required);
        }
        if (rule.datatype.toLowerCase() === "String".toLowerCase()) {
          operatorsMap.options = this.config.conditionConfigStringType;
        }
        const isDynamicRule: boolean = paramObj
          ? paramObj.isDynamic
            ? true
            : false
          : false;
        this.supportDynamic[ruleIndex] = {
          isDynamic: isDynamicRule,
          controlType: paramObj.controlType ? paramObj.controlType : null,
        };
      } else if (
        value?.value === "dynamicvalue" ||
        (value === "EDIT_CASE" && rule.selectionType === "dynamicvalue")
      ) {
        if (
          rule.param === "expressionBuilder" &&
          rule.dynamicValue &&
          rule.dynamicValue.startsWith("#")
        ) {
          this.supportDynamic[ruleIndex].controlType = "htmlDiv";
        }
        if (
          !(
            rule.opt === "between" ||
            rule.opt === "notbetween" ||
            rule.opt === "in" ||
            rule.opt === "notin"
          )
        ) {
          rule.value = "";
          formControl["value"].reset();
          formControl["value"].clearValidators();
          formControl["dynamicValue"].setValidators(Validators.required);
        }
      }
      if (
        rule.datatype.toLowerCase() === "String".toLowerCase() &&
        value !== "EDIT_CASE"
      ) {
        formControl["value"].reset();
        formControl["dynamicValue"].reset();
      }
      this.queryBuilderForm.controls[groupIndex]["controls"]["rules"][
        "controls"
      ][ruleIndex]["controls"]["value"].updateValueAndValidity({
        onlySelf: false,
        emitEvent: true,
      });

      this.queryBuilderForm.controls[groupIndex]["controls"]["rules"][
        "controls"
      ][ruleIndex]["controls"]["dynamicValue"].updateValueAndValidity({
        onlySelf: false,
        emitEvent: true,
      });
    }
  }

  openFolderTree(group) {
    this.folderSelect.emit(group);
  }

  /**
   * This method opens side panel for expression builder
   */
  private openSidePanelForExpressionBuilder() {
    this.showSideBar = true;
    this.assignSuggestions();
    this.sideBarComponentRef.openSideBar();
  }

  /**
   * This method closes side panel for expression builder
   */
  closeSidebar(isCancelClicked: boolean) {
    this.showSideBar = false;
    if (
      this.currentExpressionObject.isParamField &&
      this.currentExpressionObject.rule.param === "expressionBuilder" &&
      !this.currentExpressionObject.rule.expressionField
    ) {
      // We need to reset variables in query builder ir order to reset UI controls
      this.rulesConfigMap
        .get(this.currentExpressionObject.ruleIndex + "_param")
        .formControl.setValue(null);
      this.rulesConfigMap
        .get(this.currentExpressionObject.ruleIndex)
        .removeControl("dynamicValue");
      this.rulesConfigMap
        .get(this.currentExpressionObject.ruleIndex)
        .removeControl("selectionType");
      this.supportDynamic[this.currentExpressionObject.ruleIndex] = {
        isDynamic: false,
        controlType: null,
      };
      this.resetRuleObject(this.groupArray[0]);
    }
    if (isCancelClicked) {
      // we need to reset operator and value back to it's original state
      this.rulesConfigMap
        .get(this.currentExpressionObject.ruleIndex + "_opt")
        .formControl.setValue(this.currentExpressionObject.oldRule.opt);
      this.resetRuleObject(
        this.groupArray[0],
        this.currentExpressionObject.oldRule
      );
      if (this.currentExpressionObject.oldRule.selectionType) {
        this.rulesConfigMap
          .get(this.currentExpressionObject.ruleIndex + "_selectionType")
          .formControl.setValue(
            this.currentExpressionObject.oldRule.selectionType
          );
        if (
          this.currentExpressionObject.oldRule.selectionType === "staticvalue"
        ) {
          this.rulesConfigMap.get(
            this.currentExpressionObject.ruleIndex + "_value"
          ).formControlName
            ? this.rulesConfigMap
              .get(this.currentExpressionObject.ruleIndex + "_value")
              .formControlName.setValue(
                this.currentExpressionObject.oldRule.value
              )
            : this.rulesConfigMap
              .get(this.currentExpressionObject.ruleIndex + "_value")
              .formControl.setValue(
                this.currentExpressionObject.oldRule.value
              );
        } else {
          this.rulesConfigMap
            .get(this.currentExpressionObject.ruleIndex + "_dynamicValue")
            .formControl.setValue(
              this.currentExpressionObject.oldRule.dynamicValue
            );
        }
      }
    }
    this.currentExpressionObject = null;
    this.textareaConfig.formControlName.setValue(null);
    this.expressionErrorMsg = null;
    this.showSyntaxIcon = false;
    this.sideBarComponentRef.close("close");
    this.expressionBuilderPanelClosed.emit();
  }

  /**
   * This method checks whether delete button for rule should be present or not
   * @returns boolean
   */
  checkExistenceOfDeleteRuleButton(): boolean {
    return !this.config.keepOneRulePresent || this.totalRuleCount > 1;
  }

  /**
   * Callback for save button in expression builder side panel
   */
  saveExpression() {
    this.expressionBuilderValue.emit({
      value: this.textareaConfig.formControlName.value,
      isSaveClicked: true,
    });
  }

  /**
   * Callback for check syntax button in expression builder side panel
   */
  checkSyntax() {
    this.expressionBuilderValue.emit({
      value: this.textareaConfig.formControlName.value,
      isSaveClicked: false,
    });
  }

  /**
   * This method validates the custom expression written in side panel
   * call updateQueryBuilderRuleObject() if expression is valid
   * @param data
   */
  public validateCustomExpression(data: {
    valid: boolean;
    returnType?: string;
    isSaveClicked?: boolean;
    error?: string;
  }) {
    if (data.valid && data.isSaveClicked) {
      this.expressionErrorMsg = null;
      this.updateQueryBuilderRuleObject(this.groupArray[0], data.returnType);
      this.closeSidebar(false);
      if (this.config.emitQueryJsonOnFormChange) {
        setTimeout(() => {
          const d = this.getQueryJson();
          const state = this.getQbState();
          if (d) {
            this.queryBuilderStateStream.emit({
              jsonData: d,
              qbFormIsValid: state,
            });
          }
        }, 0);
      }
    } else if (data.valid && !data.isSaveClicked) {
      this.showSyntaxIcon = true;
      this.expressionErrorMsg = null;
      setTimeout(() => {
        this.showSyntaxIcon = false;
      }, 5000);
    } else {
      this.expressionErrorMsg = data.error;
      this.showSyntaxIcon = false;
    }
  }

  /**
   * Update current rule object to accomodate expression related properties
   * @param group
   * @param type
   */
  private updateQueryBuilderRuleObject(group, type) {
    const operator = group.AND ? "AND" : "OR";
    if (this.currentExpressionObject.group.groupId === group.groupId) {
      // locate and update rule
      const index = group[operator].findIndex(
        (d) => d.ruleId === this.currentExpressionObject.rule.ruleId
      );
      if (index !== -1) {
        if (this.currentExpressionObject.isParamField) {
          this.populateRuleOperatorAndValueConfig(type);
          this.currentExpressionObject.rule.expressionField =
            this.textareaConfig.formControlName.value;
          this.currentExpressionObject.rule.dynamicValue = "";
          this.currentExpressionObject.rule.value = "";
        } else {
          this.currentExpressionObject.rule.dynamicValue =
            this.textareaConfig.formControlName.value;
        }
        group[operator][index] = this.currentExpressionObject.rule;
      }
    } else {
      // traverse inner group
      if (group[operator]) {
        for (let i = 0; i < group[operator].length; i++) {
          if (group[operator][i].groupId) {
            this.updateQueryBuilderRuleObject(group[operator][i], type);
          }
        }
      }
    }
  }

  /**
   * Populate operator and value related config according to type of expression created
   * @param paramDataType
   */
  private populateRuleOperatorAndValueConfig(paramDataType) {
    this.currentExpressionObject.rule.datatype = paramDataType;
    const conditionsConfig = this.rulesConfigMap.get(
      this.currentExpressionObject.ruleIndex + "_opt"
    );
    let filteredOptions = this.getOperators(
      paramDataType,
      this.rulesConfigMap.get(this.currentExpressionObject.ruleIndex + "_param")
    );
    conditionsConfig.options = filteredOptions;
    conditionsConfig.formControl.reset();

    const value1Config =
      paramDataType.toLowerCase() === "Date".toLowerCase() &&
        this.config.showDatePicker
        ? this.getDateFieldConfig(
          (this.rulesConfigMap.get(
            this.currentExpressionObject.ruleIndex + "_value"
          ).formControlName ||
            this.rulesConfigMap.get(
              this.currentExpressionObject.ruleIndex + "_value"
            ).formControl)
        )
        : this.getInputFieldConfig(
          this.rulesConfigMap.get(
            this.currentExpressionObject.ruleIndex + "_value"
          ).formControl ||
          this.rulesConfigMap.get(
            this.currentExpressionObject.ruleIndex + "_value"
          ).formControlName,
          paramDataType,
          this.currentExpressionObject.ruleIndex
        );

    value1Config.formControlName
      ? value1Config.formControlName?.reset()
      : value1Config.formControl?.reset();

    this.rulesConfigMap.set(
      this.currentExpressionObject.ruleIndex + "_value",
      value1Config
    );
    if (this.supportDynamic[this.currentExpressionObject.ruleIndex].isDynamic) {
      this.checkDynamicAttrTypes(paramDataType);
      if (
        paramDataType.toLowerCase() === "Date".toLowerCase() &&
        !this.config.showDatePicker
      ) {
        value1Config.formControlName.clearValidators();
        value1Config.formControlName.setValidators([
          Validators.required,
          this.validateDateFormat(
            value1Config.formControlName.value,
            "dd MM yyyy"
          ),
        ]);
        let errorObj = _.find(value1Config.errorList, {
          errorCondition: "invalidDateFormat",
        });
        errorObj.errorMsg =
          this.config.translations.invalidDateFormat + " " + "dd MM yyyy";
      }
      this.rulesConfigMap
        .get(this.currentExpressionObject.ruleIndex + "_dynamicValue")
        .formControl.reset();
      this.rulesConfigMap
        .get(this.currentExpressionObject.ruleIndex + "_selectionType")
        .formControl.reset();
      this.rulesConfigMap.get(
        this.currentExpressionObject.ruleIndex + "_dynamicValue"
      ).options = this.dynamicAttrOfType;
    }
    if (
      this.currentExpressionObject.rule.opt === "between" ||
      this.currentExpressionObject.rule.opt === "notbetween"
    ) {
      this.rulesConfigMap.get(
        this.currentExpressionObject.ruleIndex + "_value2"
      ).formControlName
        ? this.rulesConfigMap
          .get(this.currentExpressionObject.ruleIndex + "_value2")
          .formControlName.reset()
        : this.rulesConfigMap
          .get(this.currentExpressionObject.ruleIndex + "_value2")
          .formControl.reset();
    }
    this.currentExpressionObject.rule.value = Array.isArray(
      this.currentExpressionObject.rule.value
    )
      ? []
      : "";
    this.currentExpressionObject.rule.opt = "";
  }

  /**
   * This method checks if current rule is expression or not
   * @param group
   * @param rule
   */
  checkIfRuleIsExpression(group, rule) {
    const isExpression: boolean = rule.param === "expressionBuilder";
    if (isExpression) {
      this.deleteExpressionRuleDialogConfig.data = { group: group, rule: rule };
      this.modalService.openDialog(this.deleteExpressionRuleDialogConfig);
    } else {
      this.deleteRule(group, rule.ruleId);
    }
  }

  /**
   * Delete the expression rule
   */
  proceedToDeleteExpression() {
    const data: any = this.deleteExpressionRuleDialogConfig.data;
    this.deleteRule(data.group, data.rule.ruleId);
    this.deleteExpressionRuleDialogConfig.data = null;
  }

  /**
   * Set query json as per default properties
   * @param groupArrayData
   * @param updateRuleData
   * @returns
   */
  public setQueryJson(groupArrayData, updateRuleData?: boolean) {
    if (groupArrayData) {
      const jsonItemData = {};
      const logicalOp = groupArrayData.AND ? "AND" : "OR";
      jsonItemData[logicalOp] = [];

      for (let i = 0; i < groupArrayData[logicalOp].length; i++) {
        if (
          groupArrayData[logicalOp][i].AND ||
          groupArrayData[logicalOp][i].OR
        ) {
          const nestedJsonData = this.setQueryJson(
            groupArrayData[logicalOp][i]
          );
          jsonItemData[logicalOp].push(nestedJsonData);
        } else {
          // we need to modify json to be sent to query builder in case of expression builder
          if (updateRuleData && groupArrayData[logicalOp][i].expressionField) {
            groupArrayData[logicalOp][i].param = "expressionBuilder";
          }
          jsonItemData[logicalOp].push(groupArrayData[logicalOp][i]);
        }
      }
      return jsonItemData;
    }
  }

  /**
   * perform expand collapse
   */
  doCollapse(group) {
    this.expandCollapseObj[group.groupId] =
      !this.expandCollapseObj[group.groupId];
  }

  /**
   * This function will populate the suggestion list for expression builder
   */
  public populateSuggestions(data: any[]) {
    this.suggestions = [...data];
  }

  private assignSuggestions() {
    if (this.suggestionTextareaConfig.collection) {
      this.suggestionTextareaConfig.collection.map((c: any) => {
        const filteredList = this.suggestions.filter(
          (s) => s.triggerValue === c.trigger
        );
        c.values = filteredList ? filteredList : [];
      });
    }
  }

  /**
   * This function will reset properties of current rule object if it was expression
   * @param group
   */
  private resetRuleObject(group, oldRule?: any) {
    const operator = group.AND ? "AND" : "OR";
    if (this.currentExpressionObject.group.groupId === group.groupId) {
      // locate and update rule
      const index = group[operator].findIndex(
        (d) => d.ruleId === this.currentExpressionObject.rule.ruleId
      );
      if (index !== -1) {
        if (oldRule) {
          // group[operator][index].opt = oldRule.opt;
          this.updateRuleOperator(
            oldRule.opt,
            group[operator][index],
            this.rulesConfigMap.get(
              this.currentExpressionObject.group.groupId +
              "_rule_" +
              this.currentExpressionObject.rule.ruleId +
              "_value"
            ),
            this.currentExpressionObject.group
          );
          if (oldRule.dynamicValue) {
            group[operator][index].dynamicValue = oldRule.dynamicValue;
          }
          if (oldRule.value) {
            group[operator][index].value = oldRule.value;
          }
          if (oldRule.opt === "in" || oldRule.opt === "notin") {
            this.inOperatorValuesMap.set(
              `${this.currentExpressionObject.group.groupId}_rule_${this.currentExpressionObject.rule.ruleId}`,
              oldRule.value
            );
            this.rulesConfigMap.set(
              `${this.currentExpressionObject.group.groupId}_rule_${this.currentExpressionObject.rule.ruleId}_moreBtn`,
              {
                ...this.plusMoreBtnConfig,
                value: `  ${oldRule.value.length - 2} ${this.config.translations.more
                  }`,
              }
            );
          }
        } else {
          group[operator][index].datatype = "";
          group[operator][index].param = "";
          group[operator][index].paramLabel = "";
        }
      }
    } else {
      // traverse inner group
      if (group[operator]) {
        for (let i = 0; i < group[operator].length; i++) {
          if (group[operator][i].groupId) {
            this.resetRuleObject(group[operator][i], oldRule);
          }
        }
      }
    }
  }

  /**
   * get field with error from group for highlighting purpose...
   */
  checkHighlightedRule(group, rule) {
    return !this.queryBuilderForm.controls[group.groupId]["controls"]["rules"][
      "controls"
    ][`${group.groupId}_rule_${rule.ruleId}`].valid;
  }

  /**
   * get field with error from group for highlighting purpose...
   */
  checkHighlightedGroup(group) {
    return !(group?.AND?.length || group?.OR?.length);
  }

  /**
   * This method will add macro / dd field to text area input control
   * @param fName
   */
  public addMacroToExpressionBuilder(fName: string) {
    let expressionValue = this.textareaConfig.formControlName.value;
    if (expressionValue) {
      expressionValue =
        expressionValue.substr(0, this.cursorPosition) +
        fName +
        expressionValue.substr(this.cursorPosition);
    } else {
      expressionValue = fName;
    }
    this.textareaConfig.formControlName.setValue(expressionValue);
  }

  /**
   * Get current caret position for textarea input control
   * @param cursorPosition
   */
  getCursorPositionInTextareaControl(cursorPosition: number) {
    this.cursorPosition = cursorPosition;
  }

  editGroupName(group) {
    //do edit groupNAme here...
  }

  /**calculate sub group and rule count */
  doCountRulesAndSubgroups(group) {
    this.showSubgroupRuleCount = "";
    const totalItemsInGroup = group.AND || group.OR;
    let subGroupCount = 0;
    for (let itemInGroup of totalItemsInGroup) {
      if (itemInGroup.groupId) {
        subGroupCount++;
      }
    }
    this.showSubgroupRuleCount = `${this.config.translations.subgroupLabel || "subgroup(s)"
      }: ${subGroupCount}<br>${this.config.translations.ruleLabel || "rule(s)"
      }: ${totalItemsInGroup.length - subGroupCount}`;
  }

  filterDropDownList(searchString, group, rule) {
    const valueDropDownConfig = this.rulesConfigMap.get(
      group.groupId + "_rule_" + rule.ruleId + "_value"
    );
    if (searchString) {
      valueDropDownConfig.options = this.valueAsDropdownList[
        group.groupId + "_rule_" + rule.ruleId
      ].filter((element) =>
        element.label?.toUpperCase().includes(searchString.toUpperCase())
      );
    } else {
      valueDropDownConfig.options = [
        ...this.valueAsDropdownList[group.groupId + "_rule_" + rule.ruleId],
      ];
    }
  }

  clearFilterDropDownList(group, rule) {
    const valueDropDownConfig = this.rulesConfigMap.get(
      group.groupId + "_rule_" + rule.ruleId + "_value"
    ),
      searchInputConfig = this.rulesConfigMap.get(
        group.groupId + "_rule_" + rule.ruleId + "_value_search"
      );
    searchInputConfig?.formControlName?.reset();
    valueDropDownConfig.options = [
      ...this.valueAsDropdownList[group.groupId + "_rule_" + rule.ruleId],
    ];
  }

  onIconClicked(rule: any) {
    this.paramDetailsIconClicked.emit(rule);
  }


  ngOnDestroy(): void {
    this.fieldTypeMap = {};
    this.supportDynamic = {};
    if (this.formSubscription) {
      this.formSubscription.unsubscribe();
    }
  }
}
