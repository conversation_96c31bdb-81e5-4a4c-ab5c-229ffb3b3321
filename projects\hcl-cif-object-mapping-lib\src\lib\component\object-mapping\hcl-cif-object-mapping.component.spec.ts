import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HclCifObjectMappingComponent } from './hcl-cif-object-mapping.component';

describe('HclCifObjectMappingComponent', () => {
  let component: HclCifObjectMappingComponent;
  let fixture: ComponentFixture<HclCifObjectMappingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [HclCifObjectMappingComponent]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HclCifObjectMappingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
