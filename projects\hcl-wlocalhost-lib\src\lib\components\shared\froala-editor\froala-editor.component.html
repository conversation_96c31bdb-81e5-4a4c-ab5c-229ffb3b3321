<div class="quick-view-container" id="{{froalaId}}">
    <div class="froala-editor">
        <div class="d-flex justify-content-center" [froalaEditor]="editorOptions" (froalaInit)="initialize($event)"></div>
    </div>
  </div>
<!-- <hcl-popup-menu [config]="maxAIRewritePopupMenuOptions" popupMenuTemplate="maxAIMenuTemplate" #maxAIPopupMenu>
    <ng-template hclTemplate hclTemplateName="maxAIMenuTemplate" type="maxAIMenuTemplate">
        <div (click)="$event.stopPropagation();" class="outerPadding">
            <div class="header" hclTooltip="{{ 'TEXT_IMAGE_GENERATION.REWRITE_WITH_MAX_AI' | translate }}">
                {{ 'TEXT_IMAGE_GENERATION.REWRITE_WITH_MAX_AI' | translate }}
            </div>
            <div class="body">
                <div class="menuOption" *ngFor="let item of rewriteOptions" (click)="menuOptionMaxAIClicked(item)" hclTooltip="{{ 'TEXT_IMAGE_GENERATION.' + item | uppercase | translate }}">
                    <span class="menuOptionLabel">
                      {{ 'TEXT_IMAGE_GENERATION.' + item | uppercase | translate }}
                    </span>
                </div>
            </div>
        </div>
    </ng-template>
</hcl-popup-menu> -->