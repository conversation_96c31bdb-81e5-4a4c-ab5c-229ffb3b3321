.ap-item-details-container {
    max-width: 450px;
    height: auto;
    max-height: 540px;
    position: relative;
    background: rgba(0, 0, 0, 0.8);
    overflow: hidden;
    margin: auto;

    &.no-preview-available {
        display: inline-block;
        padding: 12px 12px 30px;
        background: transparent;
    }

    .close-pop-over {
        position: absolute;
        top: 7px;
        right: 7px;

        span {
            color: #f5f5f5;
        }
    }

    .ap-item-scroll-container {
        padding: 30px 0;
        width: 450px;
        .ap-item-details {
            display: block;
            width: 100%;
            padding: 0 30px 0;
            position: relative;
            color: #f5f5f5;
            font-family: "Helvetica";
            font-size: 14px;
            letter-spacing: 0.17px;
            line-height: 21px;
            height: auto;
            max-height: 480px;
            overflow-y: auto;

            .thumbnail {
                margin-bottom: 25px;
                display: flex;
                background-color: #d8d8d8;
                justify-content: center;
                padding: 10px;
                img {
                    max-width: 100%;
                    display: block;
                    margin: 0 auto;
                    max-height: 300px;
                }
                [class^="hcl-icon"] {
                    font-size: 225px;
                    color: #959595;
                    cursor: auto;
                }
            }

            .metadata {
                position: relative;
                // margin-bottom: 20px;

                .fileName {
                    align-items: center;
                    font-size: 16px;
                    display: flex;
                    a {
                        color: #f5f5f5;
                        margin-right: 5px;
                        &:hover {
                            text-decoration: none;
                        }
                    }
                }

                .size {
                    p {
                        margin-bottom: 3px;
                    }
                }

                .mime-type {
                    display: inline-block;
                    bottom: 0;
                    right: 0;
                    border-radius: 7.5px;
                    background-color: #d8d8d8;
                    padding: 3px 10px;
                    max-width: 390px;
                    margin-top: 10px;

                    span {
                        color: #6d7692;
                        font-family: Roboto;
                        font-size: 12px;
                        letter-spacing: 0.4px;
                        line-height: 14px;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        display: block;
                    }
                }
            }

            .description {
                margin-top: 10px;
            }
        }
    }
    .error-container {
        background: rgba(0, 0, 0, 0.7);
        .ap-no-preview {
            // background-color: rgba(0, 0, 0, 0.7);
            display: inline-block;
            padding: 10px 12px;
            color: #f5f5f5;
            font-family: Roboto;
            font-size: 14px;
            letter-spacing: 0;
            line-height: 18px;
            position: relative;
            max-width: 600px;

            &.left-arrow {
                &::after {
                    content: "";
                    border-left: 0px solid transparent;
                    border-right: 12px solid transparent;
                    border-top: 20px solid rgba(0, 0, 0, 0.7);
                    position: absolute;
                    bottom: -20px;
                    left: 0;
                }
            }
            &.right-arrow {
                &::after {
                    content: "";
                    border-left: 12px solid transparent;
                    border-right: 0px solid transparent;
                    border-top: 20px solid rgba(0, 0, 0, 0.7);
                    position: absolute;
                    bottom: -20px;
                    right: 0;
                }
            }
        }
    }
}
