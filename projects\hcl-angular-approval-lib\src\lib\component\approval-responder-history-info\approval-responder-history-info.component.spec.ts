import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ApprovalResponderHistoryInfoComponent } from './approval-responder-history-info.component';

describe('ApprovalResponderHistoryInfoComponent', () => {
  let component: ApprovalResponderHistoryInfoComponent;
  let fixture: ComponentFixture<ApprovalResponderHistoryInfoComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ApprovalResponderHistoryInfoComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ApprovalResponderHistoryInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
