import { Component, ElementRef, OnInit, ViewEncapsulation } from '@angular/core';
import { ITooltipAngularComp } from 'ag-grid-angular';
import { IAfterGuiAttachedParams, ITooltipParams, RowNode } from 'ag-grid-community';
import { Overlay, OverlayPositionBuilder } from '@angular/cdk/overlay';

@Component({
  selector: 'hcl-custom-tooltip',
  templateUrl: './custom-tooltip.component.html',
  styleUrls: ['./custom-tooltip.component.scss']
})
export class CustomTooltipComponent implements OnInit, ITooltipAngularComp {
  private classString: string = '';
  text: any = '';

  constructor(private overlay: Overlay,
    private overlayPositionBuilder: OverlayPositionBuilder,
    private elementRef: ElementRef) { }

  ngOnInit() { }

  afterGuiAttached(params?: IAfterGuiAttachedParams): void {
  }

  agInit(params: any): void {
    const tooltipParams: any = params.column.colDef.tooltipComponentParams,
      node = params.api.getDisplayedRowAtIndex(params.rowIndex);

    if (node && node.data) {
      this.text = tooltipParams.getTooltip ? tooltipParams.getTooltip(node.data)
        : node.data[params.column.colDef.field];
    }
  }
}
