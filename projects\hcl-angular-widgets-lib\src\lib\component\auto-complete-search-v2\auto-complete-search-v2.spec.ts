import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AutoCompleteSearchV2Component } from './auto-complete-search-v2';

describe('AutoCompleteSearchV2Component', () => {
  let component: AutoCompleteSearchV2Component;
  let fixture: ComponentFixture<AutoCompleteSearchV2Component>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AutoCompleteSearchV2Component ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AutoCompleteSearchV2Component);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
