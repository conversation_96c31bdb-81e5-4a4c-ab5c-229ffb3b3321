.custom-header {
    font-weight: bold;
    margin-bottom: 15px;
    color: #6d7692;
    font-family: <PERSON><PERSON><PERSON>;
    font-size: 20px;
}

.info-msg {
    color: #15161c;
    font-family: <PERSON><PERSON>;
    font-size: 14px;
}

.lower-section-title {
    color: #6d7692;
    font-family: Montser<PERSON>;
    font-size: 14px;
    margin-top: 30px;
}

.accordion-data-container {
    height: auto;
    display: flex;
    max-height: 350px;
    margin-top: 10px;

    .data-child-container {
        flex: 1;
        background-color: #f5f5f5;
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);

        .data-child-title {
            height: 60px;
            padding: 20px 15px;
            border-bottom: 1px solid #e0e0e0;
            color: #6d7692;
            font-family: Montserrat;
            font-size: 16px;
            font-weight: 600;
        }
        .row-data-container {
            overflow-y: auto;
            height: calc(100% - 60px);
            .row-data {
                height: 50px;
                color: #444444;
                font-family: <PERSON><PERSON>;
                font-size: 14px;
                border-bottom: 1px solid #e0e0e0;
                padding: 0px 15px;
                line-height: 50px;
                display: flex;
                cursor: pointer;

                .function-name {
                    flex: 1;
                }
            }

            .mat-expansion-panel-body {
                padding: 0px;
                .row-data {
                    padding: 0px 30px;
                }
            }

            .selected {
                background-color: #cce4f7;
                box-shadow: 6px 6px 10px 0 rgba(0, 0, 0, 0.3);
            }
        }
    }
    .data-child-container:first-child {
        margin-right: 15px;
    }
}

.msg-note-container {
    color: #6d7692;
    font-family: Roboto;
    font-size: 12px;
    letter-spacing: 0.4px;
    margin-top: 10px;
}

.supported-expression-info-container {
    margin-top: 20px;
    color: #6d7692;

    .expression-name {
        font-family: Montserrat;
        font-size: 16px;
        font-weight: 600;
    }

    .expression-description {
        font-family: Roboto;
        font-size: 12px;
        letter-spacing: 0.4px;
        margin-top: 10px;
        height: auto;
        // max-height: 100px;
        overflow-y: auto;
    }
}

@media screen and (max-width: 1600px) {
    .expression-description {
        max-height: 50px;
    }
}

@media screen and (min-width: 1920px) and (max-width: 2560px) {
    .expression-description {
        max-height: 100px;
    }
}
