<div class="offers-container">
    <div class="folders-pane"
        [ngClass]="{'folder-panel-minimized': folderPanelState == 1, 'folder-panel-normal': folderPanelState == 2, 'folder-panel-maximized': folderPanelState == 3}">
        <div class="folders-logo">
            <span class="ml-3">
                {{'OFFER_PICKER.TITLES.SELECT_OFFERS'| translate}}
            </span>
            <div class="max-min-icon-container">
                <div class="expand-collapse-icon-container">
                    <div class="expand-collapse-seperator">
                    </div>
                    <div class="max-min-button-container">
                        <div class="min-button-container" *ngIf="folderPanelState == 2 || folderPanelState == 3">
                            <hcl-button [config]="minimizeFolderIconButton" (onclick)="minimizeFolderList($event)">
                            </hcl-button>
                        </div>
                        <div class="max-button-container" *ngIf="folderPanelState == 2 || folderPanelState == 1">
                            <hcl-button [config]="maxFolderIconButton" (onclick)="maximizeFolderList($event)">
                            </hcl-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="side-pane" #foldersPane>
            <div class="vertical-label" *ngIf="folderPanelState == 1">
                <span [ngClass]="{'reset-transform-rotate': verticalLocale()}">{{'OFFER_PICKER.TITLES.SELECT_OFFERS'|
                    translate}}</span>
            </div>
            <hcl-offer-folders class="folders-container" [config]="config.foldersConfig"
                (folderSelect)="folderSelected($event)">
            </hcl-offer-folders>
        </div>
    </div>
    <div #listingContainer class="header-and-grid-container"
        [ngClass]="{'offer-grid-panel-minimized': folderPanelState == 3, 'offer-grid-panel-normal': folderPanelState == 2, 'offer-grid-panel-maximized': folderPanelState == 1}">
        <!--The container that will be displayed when the offer list is docked-->
        <div class="h-100" *ngIf="folderPanelState == 3">
            <div class="max-container-offer-list h-100">
                <div class="max-button-offer-container">
                    <hcl-button [config]="minimizeFolderIconButton" (onclick)="minimizeFolderList($event)"></hcl-button>
                </div>
                <div class="max-text-offer-container vertical-label">
                    <span [ngClass]="{'reset-transform-rotate': verticalLocale()}">{{
                        'LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS' | translate }}</span>
                </div>
            </div>
        </div>
        <div *ngIf="!showGlobalResults" class="list-conatiner-header breadcrumb-container-non-clickable">
            <span class="selected-folder-text mr-1"
                hclTooltip="{{'FOLDERS.TITLES.VIEW_FOLDER_OFFER_OFFERLISTS'| translate}}">{{'FOLDERS.TITLES.VIEW_FOLDER_OFFER_OFFERLISTS'|
                translate}}</span>
            <span class="selected-folder">
                <hcl-breadcrumb #breadCrumb>
                    <ng-template *ngFor="let breadCrum of breadCrumbData" hclTemplate hclTemplateName="breadCrumbData"
                        [hclTemplateConfig]="{label: breadCrum.folder.displayName, value: breadCrum}">
                        <div class="breadcrumb-item" hclTooltip="{{breadCrum.folder.displayName}}">
                            {{breadCrum.folder.displayName}}
                        </div>
                    </ng-template>
                </hcl-breadcrumb>
            </span>
        </div>
        <div *ngIf="showGlobalResults" class="list-conatiner-header">
            <span class="mr-1">{{'LIST_OFFERS_AND_OFFER_LISTS.TITLES.GLOBAL_SEARCH_RESULTS'| translate}}</span>
        </div>
        <hcl-tabs [config]="tabHorizontalConfig" orientation="horizontal" (selectedTabChange)="tabSelectChange($event)">
            <ng-template hclTemplate hclTemplateName="offers" type="tab-content">
                <div class="parent-tab-container">
                    <div class="offer-header-row mb-2 p-1">
                        <div class="view-controls">
                            <i class="hcl-icon-grid-view" hclTooltip="{{'LABELS.CARD_VIEW' | translate}}"
                                [ngClass]="{'active-view':!offerGridViewActive}"
                                (click)="changeOfferListingView('Card view')"></i>
                            <i class="hcl-icon-list-view" hclTooltip="{{'LABELS.GRID_VIEW' | translate}}"
                                [ngClass]="{'active-view':offerGridViewActive}"
                                (click)="changeOfferListingView('Grid view')"></i>
                        </div>
                        <div class="divider"></div>
                        <div class="search-container-offer">
                            <hcl-input class="offers-search mx-3" (iconClick)="searchOffersIconClick()"
                                (keyup.enter)="searchOffers()" (keydown)="enterSearchOffer($event)"
                                [config]="searchOffersConfig"></hcl-input>
                            <span class="d-none">
                                <hcl-button [config]="filterOffersButtonConf" class="mx-10px"></hcl-button>
                            </span>
                        </div>
                        <div class="action-container mr-2">
                            <div *ngIf="!offerGridViewActive" class="sort-container">
                                <hcl-menu class="offers-menu" [config]="cvOffersSortMenuConf"
                                    (itemClick)="cvOffersSortMenuItemClicked($event)">
                                    <ng-template hclTemplate hclTemplateName="offerFilterMenutrigger" let-item="item">
                                        <hcl-button [config]="cvOffersSortBtnConf" class="offer-sort-btn mx-10px">
                                        </hcl-button>
                                    </ng-template>
                                    <ng-template hclTemplate hclTemplateName="menuHeader" let-item="item">
                                        <div class="action-menu-header" (click)="$event.stopPropagation()" *ngIf="item">
                                            {{ item.label }}
                                        </div>
                                    </ng-template>
                                    <ng-template hclTemplate hclTemplateName="menuItem" let-item="item">
                                        <div class="action-menu-item" *ngIf="item">
                                            <hcl-radio [config]="getSortOptionRadioConf(item)"></hcl-radio> {{
                                            item.label }}
                                        </div>
                                    </ng-template>
                                </hcl-menu>
                            </div>

                            <div class="ml-auto">
                                <hcl-button [config]="refreshOffersButtonConf" (onclick)="onOffersRefreshClick($event)"
                                    class="refresh-btn">
                                </hcl-button>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="offerGridViewActive && tabHorizontalConfig.selectedTab === 0" class="offers-grid-container">
                        <hcl-offer-listing #offergrid (offerSelectionUpdate)="offerSelectionUpdate($event)">
                        </hcl-offer-listing>
                    </div>
                    <ng-container *ngIf="!offerGridViewActive && tabHorizontalConfig.selectedTab === 0 && loadCardView">
                        <hcl-offer-card-view  [folderPanelState]="folderPanelState"  (offerSelectionUpdate)="offerSelectionUpdate($event)"
                            [cvOfferSortState]="cvOfferSortState">
                        </hcl-offer-card-view>
                    </ng-container>
                </div>
            </ng-template>
            <ng-template hclTemplate hclTemplateName="offerLists" type="tab-content">
                <div class="parent-tab-container">
                    <div class="offer-header-row mb-2 p-1">
                        <div class="view-controls">
                            <i class="hcl-icon-grid-view" hclTooltip="{{'LABELS.CARD_VIEW' | translate}}"
                                [ngClass]="{'active-view':!offerlistGridViewActive}"
                                (click)="changeOfferlistListingView('Card view')"></i>
                            <i class="hcl-icon-list-view" hclTooltip="{{'LABELS.GRID_VIEW' | translate}}"
                                [ngClass]="{'active-view':offerlistGridViewActive}"
                                (click)="changeOfferlistListingView('Grid view')"></i>
                        </div>
                        <div class="divider"></div>
                        <div class="action-container reset-position mr-">
                            <div class="search-container-offer">
                                <hcl-input class="offers-search mx-3" (iconClick)="searchOfferListIconClick()"
                                    (keyup.enter)="searchOfferList()" (keydown)="enterSearchOfferList($event)"
                                    [config]="searchOfferListConfig"></hcl-input>
                                <span class="d-none">
                                    <hcl-button [config]="filterOffersButtonConf" class="mx-10px"></hcl-button>
                                </span>
                            </div>

                            <div *ngIf="!offerlistGridViewActive" class="sort-container">
                                <hcl-menu class="offers-menu" [config]="cvOfferlistSortMenuConf"
                                    (itemClick)="cvOfferlistSortMenuItemClicked($event)">
                                    <ng-template hclTemplate hclTemplateName="offerlistSortMenutrigger" let-item="item">
                                        <hcl-button [config]="cvOffersSortBtnConf" class="offer-sort-btn mx-10px">
                                        </hcl-button>
                                    </ng-template>
                                    <ng-template hclTemplate hclTemplateName="menuHeader" let-item="item">
                                        <div class="action-menu-header" (click)="$event.stopPropagation()" *ngIf="item">
                                            {{ item.label }}
                                        </div>
                                    </ng-template>
                                    <ng-template hclTemplate hclTemplateName="menuItem" let-item="item">
                                        <div class="action-menu-item" *ngIf="item">
                                            <hcl-radio [config]="getOlSortOptionRadioConf(item)"></hcl-radio> {{
                                            item.label }}
                                        </div>
                                    </ng-template>
                                </hcl-menu>
                            </div>

                            <div class="ml-auto">
                                <hcl-button [config]="refreshOffersButtonConf" (onclick)="onOfferListRefreshClick()"
                                    class="refresh-btn">
                                </hcl-button>

                                <div class="filter-container">
                                    <hcl-menu class="offer-filter" [config]="offerListFilterMenuConf"
                                        (itemClick)="offerListFilterMenuItemClicked($event)">
                                        <ng-template hclTemplate hclTemplateName="offerFilterMenutrigger"
                                            let-item="item">
                                            <span class="hcl-icon-filter"></span>
                                        </ng-template>
                                        <ng-template hclTemplate hclTemplateName="menuHeader" let-item="item">
                                            <div class="action-menu-header" (click)="$event.stopPropagation()"
                                                *ngIf="item">{{ item.label }}
                                            </div>
                                        </ng-template>
                                        <ng-template hclTemplate hclTemplateName="menuItem" let-item="item">
                                            <div class="action-menu-item" *ngIf="item">
                                                <hcl-radio [config]="getOLFilterOptionRadioConf(item)"></hcl-radio> {{
                                                item.label }}
                                            </div>
                                        </ng-template>
                                    </hcl-menu>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="offerlistGridViewActive && tabHorizontalConfig.selectedTab === 1" class="offers-grid-container">
                        <ng-container>
                            <hcl-offer-list-listing #offerlistgrid [offerListType]="olType"
                                (offerListSelectionUpdate)="offerListSelectionUpdate($event)">
                            </hcl-offer-list-listing>
                        </ng-container>
                    </div>
                    <ng-container *ngIf="!offerlistGridViewActive && tabHorizontalConfig.selectedTab === 1">
                            <hcl-offerlist-card-view [folderPanelState]="folderPanelState" [offerlistSortState]="offerlistSortState" [isSummaryPage]="false"
                                (offerListSelectionUpdate)="offerListSelectionUpdate($event)">
                            </hcl-offerlist-card-view>
                    </ng-container>
                </div>
            </ng-template>
        </hcl-tabs>
    </div>
</div>
<div class="offer-actions">
    <hcl-button [config]="cancelActionsConf" (onclick)="close($event)" class="action-button"></hcl-button>
    <hcl-button [config]="selectOffersActionsConf" (onclick)="getSelectedOffers($event)" class="action-button">
    </hcl-button>
</div>