@use '../../assets/scss/variables' as *;

.data-profile {
    width: 90dvw;

    .loader-sec {
        width: 100%;
        height: 95dvh;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    h1 {
        font-family: $font-primary;
        font-size: 20px;
        font-weight: 700;
        line-height: 24.38px;
        color: #6D7692;
    }

    .data-profile-content {
        display: flex;
        gap: 10px;
        width: 100%;
        margin-top: 20px;
        height: 80dvh;
        overflow: hidden;
        position: relative;

        .padding-container {
            padding: 20px;
            background-color: $card-background;
            border: 1px solid $tango-border-color;
            border-radius: $border-radius-8;
            height: 99.5%;
            position: relative;
        }

        .w-20 {
            width: 20%;
        }

        .w-80 {
            width: 80%;
        }
    }

    .error-message-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 80dvh;
        overflow: hidden;

        .err-msg {
            padding-top: 20px;
            max-width: 600px;
            color: #444;
            text-align: center;
            font-family: $font-secondary;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
        }

        .hcl-icon-error {
            color: $message-error;
            font-size: 48px;
        }
    }


    .action-btn-container {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
    }
}