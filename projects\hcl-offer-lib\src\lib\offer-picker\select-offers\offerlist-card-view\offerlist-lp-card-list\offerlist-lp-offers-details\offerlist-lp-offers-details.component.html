<div class="offerlist-lp-offers-details">
    <div class="header">
        <div class="w-50">
            <div class="ol-name" hclTooltip="{{offerlistInfo.offerlistName}}">{{offerlistInfo.offerlistName}}</div>
            <div class="offer-count" *ngIf="offerData">
                <ng-container *ngIf="offerData.length > 1">
                    <span
                        hclTooltip="{{offerData.length}} {{translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')}}">
                        {{offerData.length}} {{translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')}}
                    </span>
                </ng-container>

                <ng-container *ngIf="offerData.length < 2">
                    <span hclTooltip="{{translate.instant('LABELS.OFFER')}}">
                        {{offerData.length}} {{translate.instant('LABELS.OFFER')}} </span>
                </ng-container>
            </div>
        </div>

        <div class="search-container">
            <hcl-input class="offer-search" [ngClass]="{'search-on': isSearchOn}" (iconClick)="searchIconClick()"
                (keyup.enter)="search()" [config]="searchBoxConfig"></hcl-input>
        </div>

    </div>
    <div class="offer-details" *ngIf="offerData">
        <div class="offer-item" *ngFor="let offer of offerData">
            <div class="thumbnail">
                <hcl-skeleton-loader
                    [config]="{ contentLoading: offer.thumbnailProperties?.url && !offer.isImageLoaded, animation:'progress', theme:{ 'border-radius': '0', height: '75px', 'margin-bottom':'0', display:'block' } }">
                </hcl-skeleton-loader>
                <img *ngIf="offer.thumbnailProperties?.url" #offerThumbnail [src]="offer.thumbnailProperties.url"
                    (imageLoaded)="updateOfferThumbnailState($event, offer, offerThumbnail)" default="" title=""
                    titleText="" />
                <ng-container *ngIf="offer.brokenThumbnail">
                    <svg width="100" height="75" xmlns="http://www.w3.org/2000/svg">
                        <g fill="none" fill-rule="evenodd">
                            <path fill="#D8D8D8" d="M0 0h100v75H0z" />
                            <path
                                d="m55.245 43.245-3.37-3.37-1.681-1.681-5.069-5.069-.881-.881-.49-.49a.623.623 0 0 0-.883.881l1.004 1.009v8.231c0 .688.563 1.25 1.25 1.25h8.231l1.002 1.002c.244.244.64.245.886.001a.623.623 0 0 0 .001-.883Zm-10.12-1.37v-6.981l4.275 4.275-.525.656-1.25-1.7-1.875 2.5h5.106l1.25 1.25h-6.981Zm1.769-8.75-1.25-1.25h8.231c.688 0 1.25.563 1.25 1.25v8.231l-1.25-1.25v-6.981h-6.981Z"
                                fill="#959595" />
                        </g>
                    </svg>
                </ng-container>

                <ng-container *ngIf="!offer.thumbnailProperties?.url">
                    <svg width="100" height="75" xmlns="http://www.w3.org/2000/svg">
                        <g fill="none" fill-rule="evenodd">
                            <path fill="#6C7794" d="M0-2h111v77H0z" />
                            <path
                                d="M48.564 16.854c.779-1.139 2.509-1.139 3.287 0l1.655 2.42c.566.828 1.695 1.097 2.595.618l2.63-1.4c1.237-.66 2.769.117 2.91 1.475l.301 2.886c.103.987.974 1.732 2 1.711l3.003-.06c1.413-.027 2.395 1.348 1.868 2.613l-1.123 2.69c-.384.92.029 1.97.947 2.413l2.688 1.296c1.264.609 1.473 2.267.396 3.15l-2.288 1.88a1.857 1.857 0 0 0-.322 2.56l1.756 2.354c.827 1.107.213 2.669-1.165 2.968l-2.93.636c-1.003.218-1.663 1.142-1.519 2.124l.424 2.872c.199 1.35-1.096 2.458-2.46 2.105l-2.902-.752c-.992-.257-2.021.265-2.366 1.199l-1.007 2.733c-.474 1.285-2.153 1.685-3.191.76l-2.207-1.968a2.021 2.021 0 0 0-2.672 0l-2.207 1.968c-1.038.925-2.718.525-3.191-.76l-1.008-2.733c-.344-.934-1.374-1.456-2.366-1.2l-2.9.753c-1.365.353-2.66-.754-2.46-2.105l.422-2.872c.145-.982-.516-1.906-1.517-2.124l-2.931-.636c-1.378-.3-1.992-1.861-1.166-2.968l1.757-2.353c.6-.804.46-1.92-.322-2.562l-2.289-1.878c-1.076-.884-.868-2.542.396-3.151l2.688-1.296c.919-.442 1.331-1.492.948-2.412l-1.123-2.691c-.528-1.265.455-2.64 1.867-2.612l3.003.06c1.027.02 1.897-.725 2-1.712l.301-2.886c.142-1.358 1.673-2.134 2.91-1.475l2.63 1.4c.9.479 2.03.21 2.595-.617l1.655-2.421Z"
                                fill="#FFF" fill-rule="nonzero" />
                            <path
                                d="m42.472 37.664 7.2-7.2A1.59 1.59 0 0 1 50.8 30h5.6c.88 0 1.6.72 1.6 1.6v5.6c0 .44-.176.84-.472 1.136l-7.2 7.2A1.59 1.59 0 0 1 49.2 46c-.44 0-.84-.176-1.128-.472l-5.6-5.6A1.564 1.564 0 0 1 42 38.8c0-.44.184-.848.472-1.136Zm12.861-4.108a.888.888 0 1 0 0-1.778.888.888 0 1 0 0 1.778Z"
                                fill="#6C7794" fill-rule="nonzero" />
                            <g>
                                <path d="M104-28H74V2h30z" />
                            </g>
                        </g>
                    </svg>
                </ng-container>
            </div>
            <div class="offer-meta">
                <div class="offer-name" hclTooltip="{{offer.displayName}}">{{offer.displayName}}</div>
                <div class="offer-codes">
                    <p class="ellipsis" hclTooltip="{{'CREATE_OFFER.LABELS.OFFER_CODE' | translate}}:">
                        {{'CREATE_OFFER.LABELS.OFFER_CODE' | translate}}:</p>
                    <p class="ellipsis" hclTooltip="{{offer.offerCodes}}">
                        {{offer.offerCodes}}</p>
                </div>
            </div>
        </div>
    </div>
    <div class="actions">
        <hcl-button [config]="cancelButtonConfig" (onclick)="cancel()"></hcl-button>
    </div>
</div>