
<div class="data-profile">
    <div class="loader-sec" *ngIf="dataProfileSpinner.isLoading">
        <hcl-progress-spinner [config]="dataProfileSpinner">
        </hcl-progress-spinner>
    </div>
    <ng-container *ngIf="!dataProfileSpinner.isLoading">
        <h1>
            {{dPData.dpConfig.translations.dataProfileTitle}}: {{dPData.columnName}}
        </h1>
        <div class="data-profile-content" *ngIf="!errorMessage">
            <div class="w-20 padding-container">
                <hcl-data-profile-statistics 
                    [config]="dPData">
                </hcl-data-profile-statistics>
            </div>
            <div class="w-80 padding-container">
                <hcl-data-profile-details 
                    (errorMessage)="handleErrorMessage($event)" 
                    [config]="dPData">
                </hcl-data-profile-details>
            </div>
        </div>
        <div class="error-message-container" *ngIf="errorMessage">
            <i class="hcl-icon-error"></i>
            <p class="err-msg">
                {{ errorMessage }}
            </p>
        </div>
        <div class="action-btn-container">
            <hcl-button [config]="closeConfig" (onclick)="close()"></hcl-button>
        </div>
    </ng-container>
</div>