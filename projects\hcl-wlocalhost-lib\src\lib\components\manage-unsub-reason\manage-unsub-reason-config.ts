import { UntypedFormControl } from '@angular/forms';
import { ButtonConf, InputConfig, RadioConfig, SlideToggleConf } from 'hcl-angular-widgets-lib';

export class ManageUnsubReasonConfig {

    public static reasonMaxLength: number = 256;

    public static cancelConfig: ButtonConf = {
        name: 'no',
        value: 'cancel',
        color: 'accent',
        buttonType: 'stroked',
        type: 'button',
        styleClass: 'large-btn',
        borderRadius: 5,
    };

    public static addReason: ButtonConf = {
        name: 'addreason',
        value: '',
        color: 'accent',
        buttonType: 'flat',
        type: 'submit',
        styleClass: 'large-btn',
        borderRadius: 5,
    };

    public static addReasonDisable: ButtonConf = {
        name: 'addreasondisable',
        value: '',
        color: 'accent',
        buttonType: 'flat',
        type: 'submit',
        styleClass: 'large-btn',
        borderRadius: 5,
        disabled: true
    };

    public static saveConfig: ButtonConf = {
        name: 'save',
        value: '',
        color: 'accent',
        buttonType: 'flat',
        type: 'submit',
        styleClass: 'large-btn',
        borderRadius: 5,
    };

    public static addReasonInputConfig: InputConfig = {
        name: 'addnewreason',
        value: '',
        placeholder: '',
        type: '',
        disabled: false,
        maximumLength: ManageUnsubReasonConfig.reasonMaxLength,
        isShowLimit: true,
        formControlName: new UntypedFormControl(),
      };

      public static otherconfig: SlideToggleConf = {
        name: 'othertoggle',
        color: 'primary',
        disabled: false,
        value: '',
        formControl: new UntypedFormControl()
      };
}