<div class="auto-complete" *ngIf="!config.isLazyLoad || (config.suggestions && config.suggestions.length < 5)">
  <mat-form-field class="w-100" appearance="fill" floatLabel="never">
    <input *ngIf="config?.formControl" [disabled]="config.disabled" aria-label="search"
      [type]="config.inputType ? config.inputType : 'text' " [name]="config.name" [placeholder]="config.placeholder"
      matInput [formControl]="config?.formControl" autocomplete="off" (blur)="onBlurEvent($event, false)"
      (keypress)="onBlurEvent($event, true); onKeyPressEvent($event)" (focus)="onInputFocused($event)"
      [matAutocomplete]="config.autoComplete === 'auto' ? auto : config.autoComplete === 'staticOptions' ? staticOptions : none">

    <input *ngIf="!config.formControl" [disabled]="config.disabled" aria-label="search"
      [type]="config.inputType ? config.inputType : 'text' " [name]="config.name" [placeholder]="config.placeholder"
      matInput [(ngModel)]="config.value" autocomplete="off" (blur)="onBlurEvent($event, false)"
      (keypress)="onBlurEvent($event, true); onKeyPressEvent($event)" (focus)="onInputFocused($event)"
      [matAutocomplete]="config.autoComplete === 'auto' ? auto : config.autoComplete === 'staticOptions' ? staticOptions : none">
      <i matPrefix class="pr-2" [class]="config.prefixIconClass" (click)="onIconClick($event, config.value, 'prefixIcon')"></i>
        <mat-autocomplete #auto="matAutocomplete" (optionSelected)="onOptionClick($event, '')" class="auto-complete-overlay">
          <ng-container *ngIf="config.formControl && config.canFilter">
            <mat-option *ngFor="let option of filteredOptions | async" class="border border-bottom"
              [value]="isOptionObjectType(option) ? option.label : option" (mouseenter)="onOptionHover(option)"
              (mouseleave)="onOptionLeave(option)">
              <ng-container *ngTemplateOutlet="optionTemplate; context: {option: option, config: config, hoverFlag: hoverFlag}"></ng-container>
            </mat-option>
          </ng-container>
          <ng-container
            [ngTemplateOutlet]="config.formControl && !config.canFilter ? !config.showInlineSpinner? formControl : spinner : ''">
          </ng-container>
          <ng-template #formControl>
            <mat-option *ngFor="let option of config.suggestions" class="border border-bottom"
              [value]="isOptionObjectType(option) ? option.label : option" (mouseenter)="onOptionHover(option)"
              (mouseleave)="onOptionLeave(option)">
              <ng-container *ngTemplateOutlet="optionTemplate; context: {option: option, config: config, hoverFlag: hoverFlag}"></ng-container>
            </mat-option>
          </ng-template>
          <ng-template #spinner>
            <mat-option class="border border-bottom">
              {{ loadingText }}
              <div class="autocomplete-loader-style">
                <hcl-progress-spinner [config]="inlineSpinnerConfig">
                </hcl-progress-spinner>
              </div>
            </mat-option>
          </ng-template>
          <ng-container *ngIf="(!config?.formControl?.value || config?.formControl?.value.trim() === '') && config.showRecentSearch && config.recentSearch?.length > 0">
            <mat-option  *ngIf="!(filteredOptions | async)?.length" class="border border-bottom no-suggestions">
              <div class="autocomplete-loader-style">
                <span class="recent-search">{{config.recentSearchText}}</span>
                <div class="chips-container">
                  <div *ngFor="let tag of config.recentSearch" class="rounded-chip" (click)="onOptionClick($event, tag)">
                    <span class="ellipsis" hclTooltip="{{ tag }}">{{ tag }}</span>
                  </div>
                </div>
              </div>
            </mat-option>
          </ng-container>
          <ng-container *ngIf="!config.formControl">
            <mat-option *ngFor="let option of config.suggestions" class="border border-bottom" [value]="config.value"
              (mouseenter)="onOptionHover(option)" (mouseleave)="onOptionLeave(option)">
              <ng-container *ngTemplateOutlet="optionTemplate; context: {option: option, config: config, hoverFlag: hoverFlag}"></ng-container>
            </mat-option>
          </ng-container>
        </mat-autocomplete>
      
        <mat-autocomplete #staticOptions="matAutocomplete" (optionSelected)="onOptionClick($event, config.value)"
          class="auto-complete-overlay">
          <mat-option *ngFor="let option of config.suggestions"
            [ngClass]="{'selected-option': config.selectedOption === option || config.selectedOption === option.value, 'disabled-option': config.diabledStaticOptions || option.disabled}"
            class="border border-bottom" [value]="config.value" (mouseenter)="onOptionHover(option)"
            (mouseleave)="onOptionLeave(option)">
            <div class="d-flex">
              <p class="opt-small-cls">{{ config.optionsPrefix || ''}} &nbsp;</p>
              <p *ngIf="!isOptionObjectType(option)" hclTooltip="{{option}}" class="opt-val-cls">{{option}}</p>
              <ng-container *ngIf="isOptionObjectType(option)">
                <span class="mr-2 prefix-option-icon" *ngIf="option ">
                  <i [ngClass]="option.iconClass"></i>
                </span>
                <p class="opt-val-cls" hclTooltip="{{option.label}}">{{option.label}}</p>
                <span class="suffix-option-icon" *ngIf="hoverFlag === option && option && option.type === config.iconLoadOnOptionSuffix">
                  <i [ngClass]="option.iconClsAfterString" (click)="onOptionIconClick($event, option)"></i>
                </span>
                <p class="type-class ml-auto" hclTooltip="{{option.typeData}}">{{option.typeData}}</p>
              </ng-container>
            </div>
          </mat-option>
        </mat-autocomplete>
      
        <mat-autocomplete #none></mat-autocomplete>
        <mat-hint *ngIf="errorMessage" class="d-flex align-items-center">
          <span *ngIf="!config.errorInfo" class="hcl-icon-error"></span>
          <i *ngIf="config.errorInfo" class="hcl-icon-info errorClr" hclTooltip="{{config.errorInfo}}">
          </i> <span class="mat-error">{{getErrorMessage()}}</span>
        </mat-hint>
        <i matSuffix *ngIf="config.formControl?.value || config.value" class="clearInput pr-2 hcl-icon-close-x" (click)="clearInput()"></i>
        <i matSuffix class="pr-2" [class]="config.suffixIconClass" (click)="onIconClick($event, config.value, 'suffixIcon1')"></i>
        <i matSuffix class="pr-2" [class]="config.maxIconClass" (click)="onIconClick($event, config.value, 'suffixIcon2')"></i>
  </mat-form-field>
</div>

<div class="auto-complete" *ngIf="config.isLazyLoad && (config.suggestions && config.suggestions.length > 4)">
  <mat-form-field class="w-100" appearance="fill" floatLabel="never">
    <input *ngIf="config?.formControl" [disabled]="config.disabled" aria-label="search"
      [type]="config.inputType ? config.inputType : 'text' " [name]="config.name" [placeholder]="config.placeholder"
      matInput [formControl]="config?.formControl" autocomplete="off" (blur)="onBlurEvent($event, false)"
      (keypress)="onBlurEvent($event, true); onKeyPressEvent($event)" (focus)="onInputFocused($event)"
      [matAutocomplete]="config.autoComplete === 'auto' ? auto : config.autoComplete === 'staticOptions' ? staticOptions : none">

    <input *ngIf="!config.formControl" [disabled]="config.disabled" aria-label="search"
      [type]="config.inputType ? config.inputType : 'text' " [name]="config.name" [placeholder]="config.placeholder"
      matInput [(ngModel)]="config.value" autocomplete="off" (blur)="onBlurEvent($event, false)"
      (keypress)="onBlurEvent($event, true); onKeyPressEvent($event)" (focus)="onInputFocused($event)"
      [matAutocomplete]="config.autoComplete === 'auto' ? auto : config.autoComplete === 'staticOptions' ? staticOptions : none">      
    <i matPrefix class="pr-2" [class]="config.prefixIconClass" (click)="onIconClick($event, config.value, 'prefixIcon')"></i>
    <mat-autocomplete #auto="matAutocomplete" (optionSelected)="onOptionClick($event, '')"
      class="auto-complete-overlay">
      <cdk-virtual-scroll-viewport *ngIf="!config.showInlineSpinner" class="autocomplete-viewport"
        [itemSize]="config.itemSize || 42" [minBufferPx]="config.minBufferPx || 84"
        [maxBufferPx]="config.maxBufferPx || 420">
        <ng-container *ngIf="config.formControl && config.canFilter">
          <mat-option *cdkVirtualFor="let option of filteredOptions | async" class="border border-bottom"
            [value]="isOptionObjectType(option) ? option.label : option" (mouseenter)="onOptionHover(option)"
            (mouseleave)="onOptionLeave(option)">
            <ng-container *ngTemplateOutlet="optionTemplate; context: {option: option, config: config, hoverFlag: hoverFlag}"></ng-container>
          </mat-option>
        </ng-container>
        <ng-container *ngIf="config.formControl && !config.canFilter">
          <mat-option *cdkVirtualFor="let option of config.suggestions; templateCacheSize: 0; trackBy: trackByIndex"
            class="border border-bottom" [value]="isOptionObjectType(option) ? option.label : option"
            (mouseenter)="onOptionHover(option)" (mouseleave)="onOptionLeave(option)">
            <ng-container *ngTemplateOutlet="optionTemplate; context: {option: option, config: config, hoverFlag: hoverFlag}"></ng-container>
          </mat-option>
        </ng-container>
        <ng-container *ngIf="!config.formControl">
          <mat-option *cdkVirtualFor="let option of config.suggestions; templateCacheSize: 0; trackBy: trackByIndex"
            class="border border-bottom" [value]="config.value" (mouseenter)="onOptionHover(option)"
            (mouseleave)="onOptionLeave(option)">
            <ng-container *ngTemplateOutlet="optionTemplate; context: {option: option, config: config, hoverFlag: hoverFlag}"></ng-container>
          </mat-option>
        </ng-container>
      </cdk-virtual-scroll-viewport>
      <ng-container *ngIf="(!config?.formControl?.value || config?.formControl?.value.trim() === '') && config.showRecentSearch && config.recentSearch?.length > 0">
        <mat-option  *ngIf="!(filteredOptions | async)?.length" class="border border-bottom no-suggestions">
          <div class="autocomplete-loader-style">
            <span class="recent-search">{{config.recentSearchText}}</span>
            <div class="chips-container">
              <div *ngFor="let tag of config.recentSearch" class="rounded-chip" (click)="onOptionClick($event, tag)">
                <span class="ellipsis" hclTooltip="{{ tag }}">{{ tag }}</span>
              </div>
            </div>
          </div>
        </mat-option>
      </ng-container>
      <ng-container *ngIf="config.formControl && !config.canFilter && config.showInlineSpinner">
        <mat-option class="border border-bottom">
          {{ loadingText }}
          <div class="autocomplete-loader-style">
            <hcl-progress-spinner [config]="inlineSpinnerConfig">
            </hcl-progress-spinner>
          </div>
        </mat-option>
      </ng-container>
    </mat-autocomplete>

    <mat-autocomplete #staticOptions="matAutocomplete" (optionSelected)="onOptionClick($event, config.value)"
      class="auto-complete-overlay">
      <cdk-virtual-scroll-viewport class="autocomplete-viewport" [itemSize]="config.itemSize || 42"
        [minBufferPx]="config.minBufferPx || 84" [maxBufferPx]="config.maxBufferPx || 420">
        <mat-option *ngFor="let option of config.suggestions"
          [ngClass]="{'selected-option': config.selectedOption === option || config.selectedOption === option.value, 'disabled-option': config.diabledStaticOptions || option.disabled}"
          class="border border-bottom" [value]="config.value">
          <div class="d-flex">
            <p class="opt-small-cls">{{ config.optionsPrefix || ''}} &nbsp;</p>
            <p *ngIf="!isOptionObjectType(option)" hclTooltip="{{option}}" class="opt-val-cls">{{option}}</p>
            <ng-container *ngIf="isOptionObjectType(option)">
              <span class="mr-2 prefix-option-icon" *ngIf="option ">
                <i [ngClass]="option.iconClass"></i>
              </span>
              <p class="opt-val-cls" hclTooltip="{{option.label}}">{{option.label}}</p>
              <span class="suffix-option-icon" *ngIf="hoverFlag === option && option && option.type === config.iconLoadOnOptionSuffix">
                <i [ngClass]="option.iconClsAfterString" (click)="onOptionIconClick($event, option)"></i>
              </span>
              <p class="type-class ml-auto" hclTooltip="{{option.typeData}}">{{option.typeData}}</p>
            </ng-container>
          </div>
        </mat-option>
      </cdk-virtual-scroll-viewport>
    </mat-autocomplete>

    <mat-autocomplete #none></mat-autocomplete>
    <mat-hint *ngIf="errorMessage" class="d-flex align-items-center">
      <span *ngIf="!config.errorInfo" class="hcl-icon-error"></span>
      <i *ngIf="config.errorInfo" class="hcl-icon-info errorClr" hclTooltip="{{config.errorInfo}}">
      </i> <span class="mat-error">{{getErrorMessage()}}</span>
    </mat-hint>
    <i matSuffix *ngIf="config.formControl?.value || config.value" class="clearInput pr-2 hcl-icon-close-x" (click)="clearInput()"></i>
    <i matSuffix class="pr-2" [class]="config.suffixIconClass" (click)="onIconClick($event, config.value, 'suffixIcon1')"></i>
    <i matSuffix class="pr-2" [class]="config.maxIconClass" (click)="onIconClick($event, config.value, 'suffixIcon2')"></i>
  </mat-form-field>
</div>

<ng-template #optionTemplate let-option="option" let-config="config" let-hoverFlag="hoverFlag">
  <div class="d-flex">
    <span *ngIf="!isOptionObjectType(option)" hclTooltip="{{option}}"
      data-position='left-bottom-start'>{{option}}</span>
    <ng-container *ngIf="isOptionObjectType(option)">
      <span class="mr-2 prefix-option-icon" *ngIf="option ">
        <i [ngClass]="option.iconClass"></i>
      </span>
      <p class="opt-val-cls" hclTooltip="{{option.label}}">{{option.label}}</p>
      <span class="suffix-option-icon" *ngIf="hoverFlag === option && option && option.type === config.iconLoadOnOptionSuffix">
        <i [ngClass]="option.iconClsAfterString" (click)="onOptionIconClick($event, option)"></i>
      </span>
      <p class="type-class ml-auto" hclTooltip="{{option.typeData}}">{{option.typeData}}</p>
    </ng-container>
  </div>
</ng-template>