import { HttpHeaders } from "@angular/common/http";
import {
  FolderSelectionConf,
  QueryBuilderV2Conf,
} from "hcl-angular-widgets-lib";
import {
  DataProfileConfig,
  DonutChartConfig,
  SegmentMetadatConfig,
  SegmentRunStats,
} from "./models/segment";

export interface SegmentationConfig {
  applicationMode: "create" | "edit" | "createWithQbData";
  applicationUser: string;
  segmentToken: string;
  segmentBaseUrl: string;
  // audienceToken: string;
  // audienceBaseUrl: string;
  // segmentHeaders: HttpHeaders;
  audienceName: string;
  audienceTableName: string;
  audienceTablePhysicalName: string;
  audienceTableId: number;
  audienceDataSource: string;
  segRootFolderId: number;
  platformBaseUrl: string;
  reLogin?: (callback) => void;
  translations: { [key: string]: any };
  queryBuilderConfig: QueryBuilderV2Conf;
  donutChartConfig: DonutChartConfig;
  segmentMetadatConfig: SegmentMetadatConfig;
  folderSelectionConf: FolderSelectionConf;
  dataProfileConfig?: DataProfileConfig;
}
