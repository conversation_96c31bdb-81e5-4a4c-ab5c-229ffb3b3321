import {AfterViewInit, Component, ElementRef, Input, OnChanges, OnInit, Renderer2, SimpleChanges, ViewEncapsulation} from '@angular/core';
import {ChartConfig} from '../../config/chart-config';
import {BaseChartRenderer} from '../../renderer/BaseChartRenderer';
import {CHART_TYPE, RendererFactory} from '../../renderer/RendererFactory';
import * as d3 from 'd3';
import { LinearGaugeChartConfig } from './linear-gauge-chart';

@Component({
  selector: 'hcl-linear-gauge-chart-v2',
  templateUrl: './linear-gauge-chart.component.html',
  styleUrls: ['./linear-gauge-chart.component.scss'],
  encapsulation : ViewEncapsulation.None
})
export class LinearGaugeChartComponent implements OnInit, AfterViewInit {
  /**
   * The default chart renderer
   */
  private chartRenderer: BaseChartRenderer;
  xScale: any;
  xScaleLinearGauge: any;
  yScale: any;
  linearGuage: any;
  

  parentGroupElem: any

  /**
   * The configuration of the chart
   */
  @Input() config : LinearGaugeChartConfig
  /**
   * The default constructor
   * renderer: we need to play with the DOM so we need the Renderer
   * element: we will add the Charting to the current element
   */
  constructor(private renderer: Renderer2,
              private element: ElementRef) { }

  /**
   * We need to create the renderer
   */
  ngOnInit() {

  }

  // ngOnChanges(changes: SimpleChanges): void {
  //   console.log(changes.data)
  //   if (changes.data.currentValue && Array.isArray(changes.data.currentValue) && changes.data.currentValue.length > 0) {
  //     window.setTimeout(() => {
  //       if (d3.select('#svg_linear_gauge_chart_' + this.config.chartContainerId)) {
  //         d3.select('#svg_linear_gauge_chart_' + this.config.chartContainerId).remove()
  //       }
  //       this.createParentSVG();
  //       this.createLabel();
  //       this.createLinearGuage();
  //       this.createXAxis();
  //     }, 0)
  //   }
  // }

  /**
   * After the initialization we need to render the chart
   */
  ngAfterViewInit(): void {
    this.createParentSVG();
    this.createLabel();
    this.createLinearGuage();
    this.createXAxis();
  }

  createParentSVG() {
    d3.select('.linear-gauge-chart')
      .append(`svg`)
      .attr('id', 'svg_linear_gauge_chart_' + this.config.chartContainerId)
      .attr(`width`, this.config.width)
      .attr(`height`, this.config.height)
    this.parentGroupElem = d3.select('#svg_linear_gauge_chart_' + this.config.chartContainerId).append(`g`);
  }

  createLabel() {
    this.parentGroupElem.append("g")
    .append(`text`)
    .attr(`transform`, `translate(10, 30)`)
    .attr(`class`, `xAxisLabel`)
    .text(`${this.config.title}`)
  }

  createLinearGuage() {
    this.linearGuage = this.parentGroupElem.append(`svg:defs`)
      .append(`svg:linearGradient`)
      .attr(`id`, `gradient`)
      .attr(`x1`, `0%`)
      .attr(`y1`, `0%`)
      .attr(`x2`, `100%`)
      .attr(`y2`, `0%`)
      .attr(`spreadMethod`, `pad`);

    this.config.percentageColorStops.forEach((item, index) => {
      this.linearGuage.append(`svg:stop`)
      .attr(`offset`, `${item.percentage}%`)
      .attr(`stop-color`, `${item.color}`)
      .attr(`stop-opacity`, 1);
    })

    this.parentGroupElem.append(`g`)
      .append(`rect`)
      .attr(`x`, 10 )
      .attr(`y`, 45 )
      .attr(`width`, `${this.config.width - 30}` )
      .attr(`height`, 6 )
      .style(`fill`, `url(#gradient)`);

  }

  createXAxis() {
      var scale = d3.scaleBand<any>()
                    .domain(this.config.percentageColorStops.map((item) => {return item.label}))
                    .range([10, this.config.width-21])
                    .paddingInner(1)
                    .paddingOuter(0)

      var x_axis = d3.axisBottom(scale)
                     .scale(scale).tickSizeOuter(0);
  
      this.parentGroupElem.append("g")
         .call(x_axis.ticks(this.config.ticks))
         .attr(`transform`, `translate(0, 51)`)
  }
}