/**
 * The default abstract class for the Renderer
 */
import {ChartConfig, ChartSeries, Dimension} from '../config/chart-config';
import {ElementRef} from '@angular/core';
import {AutoDimensionHelper} from './d3/helper/AutoDimensionHelper';
import { Observable, Subject } from 'rxjs';

export abstract class BaseChartRenderer {
  /**
   * The default chart configuration
   */
  protected chartConfig: ChartConfig;
  /**
   * A subject that will be invoked when the position of the data node is changed
   * type {Subject<any>}
   */
  protected dataNodePositionUpdateSubject: Subject<{ series: ChartSeries, data: any, value: { min: number, max: number } }>
    = new Subject<{ series: ChartSeries, data: any, value: { min: number, max: number } }>();
  /**
   * A subject that will be invoked when the dependency is deleted
   * type {Subject<any>}
   */
  protected dependencyDeletedSubject: Subject<{series: ChartSeries,  child: any, parent: any }>
    = new Subject<{series: ChartSeries,  child: any, parent: any }>();
  /**
   * A subject that will be invoked when the dependency is Added
   * type {Subject<any>}
   */
  protected dependencyAddedSubject: Subject<{ series: ChartSeries, child: any, parent: any }>
    = new Subject<{ series: ChartSeries, child: any, parent: any }>();

  /**
   * The default constructor
   * param {ChartConfig} chartConfig: we need the chart config
   */
  init(chartConfig: ChartConfig) {
    this.chartConfig = chartConfig;
  }

  /**
   * this function will validate and if necessary update the chart config
   */
  protected validateAndUpdateConfig(element: any): void {
    if (this.chartConfig) {
      // if we have dimensions ignore, else set a default dimensions
      if (!this.chartConfig.dimension) {
        // we have to update the SVG dimensions depending on the series that we have
        const dim: Dimension = AutoDimensionHelper.getDimensions(this.chartConfig);
        this.chartConfig.dimension = this.getDefaultChartDimensions(element);
        if (this.chartConfig.dimension.width < dim.width) {
          this.chartConfig.dimension.width = dim.width;
        }
      }
    } else {
      console.error('Chart configuration not specified');
    }
  }

  /**
   * this function will get the element details & set the default dimensions
   * returns {Dimension}
   */
  private getDefaultChartDimensions(element: any): Dimension {
    if (!this.chartConfig.dimension) {
      // get the dim of element or use default dimensions
      return {
        width: element.offsetWidth ? element.offsetWidth : 500,
        height: element.offsetHeight ? element.offsetHeight : 400
      };
    }
    return this.chartConfig.dimension;
  }

  /**
   * this will attach a listener for the data node position update
   * param {() => void} dataNodePositionUpdate
   */
  onDataNodePositionChange(): Observable<{ series: ChartSeries, data: any, value: { min: number, max: number } }> {
    return this.dataNodePositionUpdateSubject.asObservable();
  }

  /**
   * this will attach a listener for the data dependency is deleted
   * param {() => void} dataNodePositionUpdate
   */
  onDependencyDeleted(): Observable<{series: ChartSeries, child: any, parent: any}> {
    return this.dependencyDeletedSubject.asObservable();
  }

  /**
   * this will attach a listener for the data dependency is Added
   * param {() => void} dataNodePositionUpdate
   */
  onDependencyAdded(): Observable<{ series: ChartSeries, child: any, parent: any }> {
    return this.dependencyAddedSubject.asObservable();
  }

  /**
   * When a node portion is updated this function can be invoked
   */
  public dataNodePositionUpdated(series: ChartSeries, data: any, value: { min: number, max: number }): void {
    this.dataNodePositionUpdateSubject.next({
      series: series,
      data: data,
      value: value
    });
  }

  /**
   * Generate the delete dep event
   * param dataObject
   * param parentId
   */
  public generateDeleteDependencyEvent(series: ChartSeries, parent: any, child: any): void {
    this.dependencyDeletedSubject.next({
      series: series,
      child: child,
      parent: parent
    });
  }

  /**
   * Generate the delete dep event
   * param dataObject
   * param parentId
   */
  public generateAddDependencyEvent(series: ChartSeries, parent: any, child: any): void {
    this.dependencyAddedSubject.next({
      series: series,
      child: child,
      parent: parent
    });
  }

  /**
   * this function will actually render the chart
   * param {element} the element inside which the chart needs to be rendered
   */
  public abstract render(element: ElementRef): void;

  /**
   * This function will change the scale
   * param scale
   */
  public abstract changeAxisScale(series: ChartSeries, displayIn: any): void;

  /**
   * Refresh the data at index
   * param {number} index
   */
  public abstract refreshDataForIndex(index: number): void;

  public abstract hideDataAtIndex(index: number[]): void;

  /**
   * This function will re-render the chart
   */
  public abstract reRender(): void;
}

