/*
 *
*/
.message-container {
  display: flex;
  margin: 16px 100px 10px 16px;
  margin-right: 100px;
  position: relative;
}
.message-user {
  height: 30px;
  width: 30px;
  border-radius: 3px;
  background-color: #D8D8D8;
  margin:0px 16px 0px 0px;
  padding: 4px;
  color: #959595;
}
.message-body {
  border-radius: 4px;
  background-color: #ECECEC;
  padding: 6px 10px;
  max-width: 500px;
  position: relative;
}
.message-user-details {
  color: #444444;
  font-family: Roboto;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 16px;
  font-weight: 600;
}
.message-user-details > span {
  color: #6D7692;
  font-family: Roboto;
  font-size: 12px;
  letter-spacing: 0.4px;
  line-height: 14px;
  font-weight: 100;
}
.message {
  color: #444444;
  font-family: Roboto;
  font-size: 14px;
  /*letter-spacing: 0;*/
  /*line-height: 16px;*/
  margin: 8px 0px;
}
.messaged-edited {
  color: #F5821E!important;
}
.message-toolbar {
  height: 26px;
  width: 118px;
  border-radius: 2px;
  background-color: #F5F5F5;
  box-shadow: 0 0 6px 0 rgb(0 0 0 / 25%);
  margin-top: -20px;
  z-index: 10;
  position: absolute;
  right: 0;
  top: -5px;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s, opacity 0.5s linear;
  padding: 2px;

  .element-icon {
    color: #959595;
    height: 16px;
    width: 16px;
    padding: 3px;
    cursor: pointer;
  }
}
.show-toolbar {
  visibility: visible;
  opacity: 1;
}
.message-status {
  .element-icon {
    position: absolute;
    color: #959595;
    bottom: 0;
    right: -30px;
  }
  .delivered-and-read {
    color: #C4D056;
  }
}
/*Below CSS will be applied for the current user*/
.current-user {
  margin-right: 16px;
  margin-left: 100px;
  text-align: right;
  justify-content: flex-end;
}
.current-user > .message-body {
  background-color: #CCE4F7;
}
