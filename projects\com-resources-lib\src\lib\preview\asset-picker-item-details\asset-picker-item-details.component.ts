import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { HclAssetPickerDataService, HclAssetPickerService } from 'hcl-angular-widgets-lib';

@Component({
  selector: 'com-asset-picker-item-details',
  templateUrl: './asset-picker-item-details.component.html',
  styleUrls: ['./asset-picker-item-details.component.scss']
})
export class AssetPickerItemDetailsComponent implements OnInit {
  @Input() apOverlayInputs;
  @Input() apItemDetails;
  @Input() apItemDetailsState;
  @Input() selectedRepoForPreview;
  @Input() translations;

  @Output() closePopover = new EventEmitter();

  imageMimeTypes: string[];
  documents = new Map([
    ['application/msword', 'ms-word'],
    ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'ms-word'],
    ['application/vnd.oasis.opendocument.text', 'ms-word'],
    ['application/pdf', 'pdf'],
    ['text/html', 'html'],
    ['text/rtf', 'txt'],
    ['text/plain', 'txt'],
    ['text/csv', 'csv'],
    ['application/vnd.oasis.opendocument.spreadsheet', 'ms-excel'],
    ['application/vnd.ms-excel', 'ms-excel'],
    ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'ms-excel'],
    ['application/vnd.oasis.opendocument.presentation', 'ms-ppt'],
    ['application/vnd.ms-powerpoint', 'ms-ppt'],
    ['application/vnd.openxmlformats-officedocument.presentationml.presentation', 'ms-ppt'],
    ['application/x-indesign', 'unknown-type'],
    ['application/postscript', 'unknown-type'],
    ['application/pdf', 'pdf'],
    ['application.vnd.quark.quarkxpress', 'unknown-type'],
    ['audio/x-aac', 'audio'],
    ['audio/midi', 'audio'],
    ['audio/3gpp', 'audio'],
    ['audio/mp3', 'audio'],
    ['audio/mp4', 'audio'],
    ['audio/mpeg', 'audio'],
    ['audio/ogg', 'audio'],
    ['audio/vorbis', 'audio'],
    ['audio/vnd.rn-realaudio', 'audio'],
    ['audio/x-wav', 'audio'],
    ['audio/x-ms-wma', 'audio'],
    ['application/x-dvi', 'unknown-type'],
    ['video/x-flv', 'video'],
    ['video/mp4', 'video'],
    ['video/mpeg', 'video'],
    ['video/ogg', 'video'],
    ['video/quicktime', 'video'],
    ['video/x-ms-wmv', 'video'],
    ['application/x-shockwave-flash', 'video'],
    ['application/vnd.adobe.air-application-installer-package+zip', 'zip'],
    ['application/x-tar-gz', 'zip'],
    ['application/java-archive', 'zip'],
    ['application/x-rar-compressed', 'zip'],
    ['application/x-tar', 'zip'],
    ['application/zip', 'zip'],
  ]);

  constructor(private hclAssetPickerService: HclAssetPickerService) { 
    this.imageMimeTypes = [...this.hclAssetPickerService.imageMimeTypes];
  }

  ngOnInit(): void {
  }

  closeApItemDetails() {
    this.closePopover.emit();
  }
}
