import { Component, Input, OnInit } from '@angular/core';
import { DataGridConf } from 'hcl-data-grid-lib';

@Component({
  selector: 'com-offer-list-preview',
  templateUrl: './offer-list-preview.component.html',
  styleUrls: ['./offer-list-preview.component.scss']
})
export class OfferListPreviewComponent implements OnInit {
  @Input() offerListData = null;
  @Input() translations;
  folders = [];

  gridConfig: DataGridConf;
  constructor() { }

  ngOnInit(): void {
    if (this.offerListData?.type?.properties?.folderDTOs) {
      this.folders = this.offerListData?.type?.properties?.folderDTOs.map((folder) => folder.displayName);
    }

    this.gridConfig = {
      columns: [{
        field: 'displayName',
        header: this.translations.offerName,
        colId: 'displayName',
        minWidth: 200,
        sortable: true,
        useDefaultRenderer: true
      },
      {
        field: 'concatOfferCodes',
        header: this.translations.offerCode,
        colId: 'concatOfferCodes',
        minWidth: 200,
        sortable: true,
        useDefaultRenderer: true
      },
      {
        field: 'state',
        header: this.translations.status,
        colId: 'state',
        minWidth: 200,
        sortable: true,
        useDefaultRenderer: true
      }],
      data: this.offerListData.offers,
      scrollHeight: 330,
      isClientSideRowModel: true,
      noRowsTemplate: this.translations.noRowsMessage
    };
  }
}
