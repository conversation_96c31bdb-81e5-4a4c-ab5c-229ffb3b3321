import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin } from 'rxjs';
import { OfferDataService } from '../offer-data.service';
import { SelectOffersService } from '../select-offers.service';
import { SelectOffersConfig } from '../select-offers/select-offers.config';
import { OfferApplicationConfig, PlatformConfig } from '../select-offers/select-offers.model';

@Component({
  selector: 'hcl-select-offers-app',
  templateUrl: './select-offers-app.component.html',
  styleUrls: ['./select-offers-app.component.scss']
})
export class SelectOffersAppComponent implements OnInit, OnDestroy {

  loadSelectOfferListing: boolean = true;
  loadManageOffers: boolean = false;
  loadViewOffer: boolean = false;
  loadViewOfferList: boolean = false;

  selectedOffer: number;
  selectedOfferList: number;
  load1: boolean = false;
  load2: boolean = false;

  @Output() closeOffersApp = new EventEmitter();
  @Output() offerData = new EventEmitter();

  @Input() config: SelectOffersConfig;


  constructor(
    private offerDataService: OfferDataService,
    private translate: TranslateService,
    private selectOffersService: SelectOffersService
  ) { }

  ngOnInit() {
    if (this.config) {
      if (this.config.url) {
        this.offerDataService.url = this.config.url;
        this.offerDataService.tokenId = this.config.tokenId;
        this.offerDataService.userName = this.config.userName;
        this.offerDataService.isOfferWidget = true;
        // Share config of widget
        this.selectOffersService.pickerConfig = this.config;
        // set called config for all; set timer to retrieve token   
        this.selectOffersService.reloginTimer();
      }
      if (this.config.loadOfferOrOfferLists &&
        (this.config.loadOfferOrOfferLists === 'offers'
          || this.config.loadOfferOrOfferLists === 'offerLists'
          || this.config.loadOfferOrOfferLists === 'both')) {
        this.selectOffersService.loadOfferOrOfferLists = this.config.loadOfferOrOfferLists;
      } else {
        this.selectOffersService.loadOfferOrOfferLists = 'both';
      }
      if (this.config.rowSelectMode && (this.config.rowSelectMode === 'single' || this.config.rowSelectMode === 'multiple')) {
        this.selectOffersService.rowSelectMode = this.config.rowSelectMode;
      } else {
        this.selectOffersService.rowSelectMode = 'multiple';
      }
      if (this.config.offerAndOfferListState) {
        if (this.config.offerAndOfferListState === 'both') {
          this.selectOffersService.offerAndOfferListState = 'DRAFT,PUBLISHED';
        } else if (this.config.offerAndOfferListState === 'published') {
          this.selectOffersService.offerAndOfferListState = 'PUBLISHED';
        } else if (this.config.offerAndOfferListState === 'draft') {
          this.selectOffersService.offerAndOfferListState = 'DRAFT';
        }
      } else {
        this.selectOffersService.offerAndOfferListState = 'DRAFT,PUBLISHED';
      }
      if (this.config.disableVariants) {
        this.selectOffersService.disableVariants = true;
      }
    }

    this.setConfiguration();
    this.selectOffersService.getViewOfferClicked().subscribe(offerData => {
      if (offerData.length && offerData[0]) {
        this.selectedOffer = offerData[0];
        this.selectOffersService.offerId = offerData[0];
        this.selectOffersService.loadVariationType = 'viewMaster';
        this.loadSelectOfferListing = false;
        this.loadManageOffers = false;
        this.loadViewOffer = true;
        this.loadViewOfferList = false;
      }
    });

    this.selectOffersService.getViewOfferListClicked().subscribe(offerListData => {
      if (offerListData.length && offerListData[0]) {
        this.selectedOfferList = offerListData[0];
        this.selectOffersService.offerId = offerListData[0];
        this.loadSelectOfferListing = false;
        this.loadManageOffers = false;
        this.loadViewOffer = false;
        this.loadViewOfferList = true;
      }
    });

    this.selectOffersService.getloadNext().subscribe((loadItemArray) => {
      if (loadItemArray && loadItemArray[0]) {
        if (loadItemArray[0] === 'manageOffers') {
          this.loadSelectOfferListing = false;
          this.loadManageOffers = true;
          this.loadViewOffer = false;
          this.loadViewOfferList = false;
        }
      }
    });
  }

  setConfiguration() {
    this.loadApis();
  }

  loadApis(): Promise<boolean> {
    const urls = [this.offerDataService.getPlatformConfig(), this.offerDataService.getOfferApplicationConfig()];

    if (this.config.offersData && (this.selectOffersService.loadOfferOrOfferLists === 'offers'
      || this.selectOffersService.loadOfferOrOfferLists === 'both')) {

      const offers = [];
      this.selectOffersService.selectedOlData = [];
      if (this.selectOffersService.disableVariants) {
        this.config.offersData.forEach(offer => {
          if (offer.offerId) {
            offers.push({
              offerId: +offer.offerId,
            });
          } else {
            this.selectOffersService.selectedOlData.push(offer);
          }
        });
      } else {
        this.config.offersData.forEach(offer => {
          if (offer.offerId) {
            if (offer.variantId) {
              offers.push({
                offerId: +offer.offerId,
                variantIds: [+offer.variantId]
              });
            } else {
              offers.push({
                offerId: +offer.offerId,
              });
            }
          } else {
            this.selectOffersService.selectedOlData.push(offer);
          }
        });
      }
      urls.push(this.selectOffersService.getBulkOffers(offers));
    }

    forkJoin(urls).subscribe(configList => {
      this.offerDataService.platformConfig = configList[0] as PlatformConfig;
      this.offerDataService.offerApplicationConfig = configList[1] as OfferApplicationConfig;
      // this.selectOffersService.selectedOlData = [];
      if ((configList[0] as PlatformConfig).isAssetPickerAppInstalled) {
        this.offerDataService.setAssetPickerRepoes();
      }
      if (configList[2]) {
        this.selectOffersService.selectedOffersData = this.setOffers(configList[2]);
      }
      this.loadInitialization();
      this.selectOffersService.foldersCacheData = [];
      this.load1 = true;
    });

    return new Promise((resolve) => {
      this.offerDataService.getUserConfig().subscribe(userConf => {
        this.load2 = true;
        this.offerDataService.userConfig = userConf;
        const langToSet = userConf.locale || 'en_US';
        this.translate.use(langToSet).subscribe(() => {
          console.log(`Successfully initialized '${langToSet}' language.'`);
          resolve(true);
        }, err => {
          console.log(`Problem with '${langToSet}' language initialization.'`);
        });
      });
    });
  }

  setOffers(offers) {

    const offerData = [];

    offers.forEach(offer => {
      const offerObj = {
        offerId: offer.id,
        offerDisplayName: offer.displayName,
        variantId: offer.variantId ? offer.variantId : null,
        variantDisplayName: offer.variantDisplayName ? offer.variantDisplayName : null,
        offerCode: this.returnOfferCodes(offer.offerCodes),
        variantAttributes: offer.variantId ? this.returnVariantAttributes(offer) : null,
        offerAttributes: offer.variantId ? null : this.returnVariantAttributes(offer),
        state: this.config && this.config.notLastPublishedState ? offer.state : offer.offerCurrentState
      };
      offerData.push(offerObj);
    });
    return offerData;
  }

  returnOfferCodes(offerCodes) {
    let codeString = '';
    if (offerCodes.length > 0) {
      offerCodes.forEach((offerCode, index) => {
        if ((offerCodes.length - 1) !== index) {
          codeString += (offerCode + this.offerDataService.offerApplicationConfig.offerCodeDelimiter);
        } else {
          codeString += offerCode;
        }
      });
      return codeString;
    }
  }

  returnVariantAttributes(offer) {
    const allAttributes = [...offer.parameterizedAttributes, ...offer.staticAttributes];
    const requiredAttributes = [];

    let counter = 0;

    allAttributes.some((attribute) => {
      if ((attribute.id === 1 || attribute.id === 10) && counter < 2) {
        requiredAttributes.push(attribute);
        counter++;
      } else {
        return;
      }
    });

    return requiredAttributes;
  }


  close() {
    this.closeOffersApp.emit();
    this.selectOffersService.clearTimer();
  }

  ngOnDestroy() { 
    this.selectOffersService.clearTimer();
    this.selectOffersService.resetOfferData();
    this.selectOffersService.clearData();   
  }

  loadOffersListing(event) {
    if (event === 'manageOffers') {
      this.loadSelectOfferListing = false;
      this.loadManageOffers = true;
      this.loadViewOffer = false;
      this.loadViewOfferList = false;
    } else {
      this.loadSelectOfferListing = true;
      this.loadManageOffers = false;
      this.loadViewOffer = false;
      this.loadViewOfferList = false;
    }
  }

  loadOffersListsListing(event) {
    if (event === 'manageOfferLists') {
      this.loadSelectOfferListing = false;
      this.loadManageOffers = true;
      this.loadViewOffer = false;
      this.loadViewOfferList = false;
    } else {
      this.loadSelectOfferListing = true;
      this.loadManageOffers = false;
      this.loadViewOffer = false;
      this.loadViewOfferList = false;
    }
  }

  selectedOffersData(data) {
    this.offerData.emit(data);
  }

  loadInitialization() {
    if (this.config.loadType && this.config.loadType.type) {
      if (this.config.loadType.type === 'viewOffer' && this.config.loadType.viewOfferData) {
        this.selectOffersService.offerId = this.config.loadType.viewOfferData.offerId;
        this.selectOffersService.loadVariationType = 'viewMaster';
        this.selectOffersService.selectedVariantId = null;
        this.loadSelectOfferListing = false;
        this.loadManageOffers = false;
        this.loadViewOffer = true;
        this.loadViewOfferList = false;
        this.selectOffersService.directPathAccess = true;
      } else if (this.config.loadType.type === 'viewOfferVariant' && this.config.loadType.viewOfferVariantData) {
        this.selectOffersService.offerId = this.config.loadType.viewOfferVariantData.offerId;
        this.selectOffersService.selectedVariantId = this.config.loadType.viewOfferVariantData.variantId;
        this.selectOffersService.loadVariationType = 'viewVariation';
        this.loadSelectOfferListing = false;
        this.loadManageOffers = false;
        this.loadViewOffer = true;
        this.loadViewOfferList = false;
        this.selectOffersService.directPathAccess = true;
      } else if (this.config.loadType.type === 'viewOfferList' && this.config.loadType.viewOfferListData) {
        this.selectOffersService.offerListId = this.config.loadType.viewOfferListData.offerListId;
        this.loadSelectOfferListing = false;
        this.loadManageOffers = false;
        this.loadViewOffer = false;
        this.loadViewOfferList = true;
        this.selectOffersService.directPathAccess = true;
      } else if (this.config.loadType.type === 'viewManageSelection') {
        this.loadSelectOfferListing = false;
        this.loadManageOffers = true;
        this.loadViewOffer = false;
        this.loadViewOfferList = false;
      }
    } else {
      this.loadSelectOfferListing = true;
      this.loadManageOffers = false;
      this.loadViewOffer = false;
      this.loadViewOfferList = false;
    }
  }
}
