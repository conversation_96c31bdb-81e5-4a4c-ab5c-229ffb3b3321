.hcl-bar-node {
  -moz-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}
.hcl-bar-node:hover{
  fill: #446696;
}
.hcl-bar-node {
  fill: steelblue;
}
.donut-chart-tooltip {
  opacity: 0.9;
  position: absolute;
  overflow: auto;
  padding-left: 10px;
  z-index: 2;
  .donut-chart-tooltip-arrow {
    border-width: 10px;
    border-color: rgba(0,0,0,0.9);
    border-style: solid;
    border-top-color: transparent;
    border-bottom-color: transparent;
    border-left-color: transparent;
    width: 0px;
    height: 0px;
    position: relative;
    display: block;
    top: 35px;
    left: -19px;
  }
  .donut-chart-tooltip-contents {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 3px;
    width: 260px;
    .donut-chart-tooltip-container {
      padding: 5px 10px 6px 10px;
      text-align: center;
      .donut-chart-tooltip-row {
        &.heading {
          padding-top: 10px;
          font-size: 16px;
        }
        &.value {
          padding: 10px 0px 5px 0px;
          border-bottom: 1px solid white;
          font-size: 16px;
        }
        &.description {
          padding: 10px 0px;
          font-size: 12px;
        }
        color: white;
        font-family: 'Montserrat';
      }
    }
  }
}
