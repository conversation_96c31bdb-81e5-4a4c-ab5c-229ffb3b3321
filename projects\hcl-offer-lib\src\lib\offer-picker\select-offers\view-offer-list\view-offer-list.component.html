<div *ngIf="loadTabs" class="d-flex flex-column h-100">
    <div class="offer-list-info-container" *ngIf="readyToRender">
        <section class="list-view static-list" [ngClass]="{'w-100': !isSmartOfferList()}">
            <section class="heading">
                <span class="title">
                    {{'CREATE_OFFER_LIST.TITLES.SUMMARY' | translate}}
                </span>
                <div class="view-controls">
                    <i class="hcl-icon-grid-view" hclTooltip="{{'LABELS.CARD_VIEW' | translate}}"
                        [ngClass]="{'active-view':!offerListGridViewActive}"
                        (click)="changeListingView('Card view')"></i>
                    <i class="hcl-icon-list-view" hclTooltip="{{'LABELS.GRID_VIEW' | translate}}"
                        [ngClass]="{'active-view':offerListGridViewActive}"
                        (click)="changeListingView('Grid view')"></i>
                </div>
                <div class="d-flex ml-auto mr-2">
                    <div class="offer-state" *ngIf="state">
                        <span class="bullet-round" [ngClass]="{'green-bullet': stateClass ==='PUBLISHED', 
                        'grey-bullet': stateClass ==='RETIRED','orange-bullet': stateClass ==='DRAFT'}"></span>
                        <span class="state-label ellipsis" hclTooltip="{{state}}">{{ state }}</span>
                    </div>
                </div>
            </section>
            <section class="content">
                <div class="basic-options">
                    <div class="field">
                        <span class="field-title" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{'CREATE_OFFER_LIST.LABELS.NAME'| translate}}">{{'CREATE_OFFER_LIST.LABELS.NAME'|
                            translate}}
                        </span>
                        <span class="field-value" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{displayName}}">{{displayName}}</span>
                    </div>
                    <div class="field">
                        <span class="field-title" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{'CREATE_OFFER_LIST.LABELS.SECURITY_POLICY' | translate}}">{{'CREATE_OFFER_LIST.LABELS.SECURITY_POLICY'
                            | translate}}
                        </span>
                        <span class="field-value" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{securityPolicy}}">{{securityPolicy}}</span>
                    </div>
                    <div class="field">
                        <span class="field-title" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{'CREATE_OFFER_LIST.LABELS.DESCRIPTION' | translate}}">{{'CREATE_OFFER_LIST.LABELS.DESCRIPTION'
                            | translate}}
                        </span>
                        <span class="field-value" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{description}}">{{description || ''}}</span>
                    </div>
                </div>
                <div class="basic-options py-0">
                    <div class="field" *ngIf="isSmartOfferList()">
                        <span class="field-title" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{'CREATE_OFFER_LIST.TITLES.LIST_LIMIT' | translate}}">{{'CREATE_OFFER_LIST.TITLES.LIST_LIMIT'
                            | translate}}
                        </span>
                        <span class="field-value" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{limit || translate.instant('CREATE_OFFER_LIST.TITLES.NONE')}}">
                            {{limit || translate.instant('CREATE_OFFER_LIST.TITLES.NONE')}}
                        </span>
                    </div>
                    <div class="field" *ngIf="isSmartOfferList()">
                        <span class="field-title" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{'CREATE_OFFER_LIST.TITLES.ORDER_BY' | translate}}">{{'CREATE_OFFER_LIST.TITLES.ORDER_BY'
                            | translate}}:
                        </span>
                        <span class="field-value" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{orderRef || translate.instant('CREATE_OFFER_LIST.TITLES.NONE')}}">
                            {{orderRef || translate.instant('CREATE_OFFER_LIST.TITLES.NONE')}}</span>
                    </div>
                    <div class="field">
                        <span class="field-title" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{'CREATE_OFFER_LIST.LABELS.TYPE' | translate}}">{{'CREATE_OFFER_LIST.LABELS.TYPE'
                            | translate}}
                        </span>
                        <span class="field-value" [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{type}}">{{type}}</span>
                    </div>
                </div>
                <div *ngIf="offerListGridViewActive" class="grid-container">
                    <hcl-data-grid-v2 #offerListsGrid *ngIf="gridConfig" [config]="gridConfig"
                        (gridReady)="gridReady($event)">
                        <ng-template hclTemplate hclTemplateName="displayName" type="cell-renderer" let-cell>
                            <div *ngIf="cell && cell.row" class="ellipsis" (click)="onCellClicked(cell)">
                                <span *ngIf="cell.row.state" class="bullet-round mr-2"
                                    [ngClass]="{'green-bullet': cell.row.state ==='PUBLISHED',
                                'grey-bullet': cell.row.state ==='RETIRED','orange-bullet': cell.row.state ==='DRAFT'}"></span>
                                <span class="link">{{cell.row && cell.row[cell.col.field]}}</span>
                            </div>
                        </ng-template>
                    </hcl-data-grid-v2>
                </div>
                <ng-container *ngIf="!offerListGridViewActive">
                    <hcl-offerlist-card-view [isSummaryPage]="true"> </hcl-offerlist-card-view>
                </ng-container>
            </section>
        </section>
        <section *ngIf="isSmartOfferList()" [style.width]="expanded ? null : '45px'" class="list-view criteria">
            <section class="heading pl-0" [style.padding-left]="expanded ? null : '10px'">
                <div class="button-container" [style.border]="expanded ? null : 'none'" (click)="toggleWidth()">
                    <i class="hcl-icon-right-open" *ngIf="expanded"></i>
                    <i class="hcl-icon-left-open" *ngIf="!expanded"></i>
                </div>
                <span class="title ml-4" *ngIf="expanded">
                    {{'CREATE_OFFER_LIST.TITLES.CRITERIA' | translate}}
                </span>
            </section>
            <section class="content p-2" *ngIf="expanded">
                <div class="chips-container mb-4">
                    <div class="deleted-folders" *ngIf="deletedFolderIdsMessage">
                        <i class="hcl-icon-warning"></i>{{deletedFolderIdsMessage}}
                    </div>
                    <div class="field-title-folder">
                        <div class="sub-title1 ellipsis" hclTooltip="{{'CREATE_OFFER_LIST.TITLES.SEARCH_IN_THE_FOLDER' |
                                translate}}">
                            {{'CREATE_OFFER_LIST.TITLES.SEARCH_IN_THE_FOLDER' |
                            translate}}
                        </div>
                        <div *ngIf="!subfoldersIncluded" class="sub-title2 ellipsis" hclTooltip="{{'CREATE_OFFER_LIST.TITLES.SUBFOLDERS_NOT_INCLUDED' |
                                translate}}">{{
                            'CREATE_OFFER_LIST.TITLES.SUBFOLDERS_NOT_INCLUDED' |
                            translate}}
                        </div>
                        <div *ngIf="subfoldersIncluded" class="sub-title2 ellipsis" hclTooltip="{{'CREATE_OFFER_LIST.TITLES.SUBFOLDERS_INCLUDED' |
                                translate}}">{{
                            'CREATE_OFFER_LIST.TITLES.SUBFOLDERS_INCLUDED' |
                            translate}}
                        </div>
                    </div>
                    <ng-container *ngFor="let folder of folders; let i=index">
                        <div class="rounded-chip">
                            <div class="chip-child">
                                <div *ngIf="folder.id !== 4" class="select-option" hclTooltip="{{folder.displayName}}">
                                    {{folder.displayName}}</div>
                                <div *ngIf="folder.id === 4" class="select-option"
                                    hclTooltip="{{'FOLDERS.TITLES.ALL_OFFERS' | translate}}">
                                    {{'FOLDERS.TITLES.ALL_OFFERS' | translate}}</div>
                            </div>
                        </div>
                    </ng-container>
                </div>
                <div class="queryView">
                    <h6 class="field-title">{{'CREATE_OFFER_LIST.TITLES.CONDITIONS' | translate}}</h6>
                    <div class="query" [innerHTML]="queryView"></div>
                </div>
            </section>

            <div class="content vertical-label" *ngIf="!expanded">
                <span [ngClass]="{'reset-transform-rotate': verticalLocale()}">{{'CREATE_OFFER_LIST.TITLES.CRITERIA'
                    |
                    translate}}</span>
            </div>
        </section>
    </div>
    <div class="ol-action-container" [ngClass]="{'pr-4': !isSmartOfferList()}" *ngIf="readyToRender">
        <hcl-button [config]="noActionConfig" (onclick)="cancel()" [ngClass]="{'mr-0': isSmartOfferList()}"
            class="mx-10px">
        </hcl-button>
    </div>
</div>