@mixin two-column-layout {
  display: flex;
  flex-wrap: wrap;
  align-items: end;
  & > *:not(.hcl-full-width) {
    flex: 0 1 48%;
    margin-bottom: 10px;
    overflow: hidden;

    &:nth-child(even) {
      margin-left: 4%;
    }
  }
}

.metadata { 
  @include two-column-layout;

  .form-field {
    font-family: "Roboto-regular" !important;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 16px;
    display: flex;
  
    .ro-label {
      color: #b8b7b7;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 8px 10px 8px 0;
      width: 25%;
    }
  
    .ro-value {
      color: #15161c;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 8px 18px 8px 0;
      width: 75%;
  
      .hcl-icon-view {
        color: #959595;
      }
    }
  }
  
  .form-field {
    // width: 33%;
    min-height: 30px;
    .ro-label {
      flex: none;
      padding: 0;
      width: 25%;
    }
    .ro-value {
      padding: 0;
      width: 75%;
      padding-right: 10px;
    }
  }
  
  .description {
    font-family: "Roboto-regular" !important;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 16px;
  
    .ro-label {
      color: #b8b7b7;
      padding-right: 10px;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 25%;
      position: relative;
      &.multi-line {
        top: -15px;
      }
    }
  
    .ro-value {
      color: #15161c;
      display: inline-block;
      overflow: hidden;
      width: 75%;
      padding-right: 10px;
      word-break: break-all;
      position: relative;
  
      &.desc-ellipsis {
        height: 33px;
        &::after {
          position: absolute;
          content: "...";
          bottom: 0;
          right: 0;
        }
      }
    }
  }
}