export interface QueryBuilderConf {
    columnFields: ColumnField[];
    maxGrpLevel: Number; // 5 max (0-5) (upto 6th level),
    deleteRuleIcon: any;
    conditionConfigStringType?: Option[];
    conditionConfigNumberType?: Option[];
    conditionConfigDateType?: Option[];
    conditionConfigDefault: Option[];
    readOnlyMode?: Boolean;
    showSplitEquation?: Boolean;
    jsonData?: {};
    timeZone: any; // for date conversion to epoch
    hideConditionToggle?: boolean;
    translations: {
        cancelModalBtnLabel?: string;
        deleteGroupModalBtnLabel?: any;
        deleteGroupModalMsg?: any;
        deleteGroupLabel?: any;
        addRuleLabel?: any;
        addGroupLabel?: any;
        dateFormatUnmatchError?: any;
        incorrectDateErrorMsg?: any;
        completeOrDeleteRuleErrorMsg?: any;
        atleastOneRuleRequiredErrorMsg?: any;
        andLabel?: string;
        orLabel?: string;
        staticAndOrLabel?: string;
        splitEquationLabel?: string;
        noResultFoundMsg?: string;
        filterFieldsPlaceholder?: string
    };
    fieldNamePlaceHolder?: any,
    conditionPlaceHolder?: any,
    conditionValue1PlaceHolder?: any,
    conditionValue2PlaceHolder?: any
}

export interface ColumnField {
    fieldDataType: 'String' | 'Numeric' | 'Date'; // Date is in epoch
    timeZone?: any;
    dateFormat?: any;
    fieldName: any;
    regex?: any;
    fieldLength?: any;
}

export interface Option {
    label: any;
    value: any;
}

// sample jsonData
// {
//     'AND': [{ 'param': 'FirstName', 'opt': 'eq', 'value': 'aa', 'datatype': 'STRING' },
//     { 'param': 'LastName', 'opt': 'endswith', 'value': 'aa', 'datatype': 'STRING' },
//     {
//         'OR': [{ 'param': 'Email', 'opt': 'contains', 'value': '.com', 'datatype': 'STRING', 'dateFormat': '' },
//         { 'param': 'ID', 'opt': 'gt', 'value': '00', 'datatype': 'NUMBER', 'dateFormat': '' },
//         {
//             'AND': [{ 'datatype': "NUMBER", 'dateFormat': "", 'opt': "in", 'param': "ID", 'value': ['12', '14'] },
//             { 'datatype': "DATE", 'dateFormat': "dd/mm/yyyy", 'opt': "lt", 'param': "date", 'value': '-62164562008000' }]
//         }
//         ]
//     }
//     ]
// }
