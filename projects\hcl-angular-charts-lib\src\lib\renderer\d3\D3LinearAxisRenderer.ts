import * as d3 from 'd3';
import {ChartConfig, ChartSeries} from '../../config/chart-config';
import {D3<PERSON>hart<PERSON>enderer} from './D3ChartRenderer';

/**
 * this is the renderer that will render the linear Axis on the chart
 */
export class D3LinearAxisRenderer {
  /**
   * this is the scale object it can be time or a linear scale
   */
  private scale: any;

  /**
   * The actual element in the esvg
   */
  private scaleObject: any;
  /**
   * the chart object
   */
  private chartObject: any;

  private axis: any;
  /**
   * In case we have the floating Aixs this variable will be set
   */
  public floatingAxis: any;
  /**
   * The default constructor
   * param {ChartConfig} chartConfig
   */
  constructor(private chartConfig: ChartConfig, private series: ChartSeries, protected chartRenderer: D3ChartRenderer) {
    this.createScale();
  }

  private createScale(): void {
    const minMaxRange: {min: number, max: number} = this.getMinAndMaxRange();
    this.scale = d3.scaleLinear();
    // in case the series is of time we need a time scale
    if (this.series.timeSeries) {
      this.scale = d3.scaleTime();
    }
    const minMaxValue: {min: number, max: number} = this.getAxisMinAndMaxValue();
    // set the min & max
    this.scale.domain([
      // either use the min that is specified in the series or get the min from the array
      minMaxValue.min,
      // either use the max that is specified in the series or get the min from the array
      minMaxValue.max
    ]).range([minMaxRange.min, minMaxRange.max]);
  }

  /**
   * Tells if the aixs is fixed
   * returns {boolean}
   */
  public hasFixedAxis(): boolean {
    return this.floatingAxis ? true : false;
  }

  /**
   * This function will calculate or get the min & max value for te axis
   * returns {{min: number; max: number}}
   */
  private getAxisMinAndMaxValue(): {min: number, max: number} {
    // get the max value
    let minValue = 0;
    if (this.series.min && this.series.max) {
      return {
        min: this.series.min,
        max: this.series.max
      };
    }
    if (this.series.min) {
      minValue = this.series.min;
    } else {
      minValue = this.series.min ? this.series.min : d3.min(this.series.data, (d, i) => {
        // If we have a function that can get us the value call it, else return the object assuming the object is a number
        return this.series.getMinValue ? this.series.getMinValue(d, i) : d;
      });
    }
    // get the maxvalue
    let maxValue = 0;
    if (this.series.max) {
      maxValue = this.series.max;
    } else {
      maxValue = this.series.max ? this.series.max : d3.max(this.series.data, (d, i) => {
        // If we have a function that can get us the value call it, else return the object assuming the object is a number
        return this.series.getMaxValue ? this.series.getMaxValue(d, i) : d;
      });
    }
    // In case this is a time series its better that we have a bit of buffer
    if (this.series.timeSeries) {
      // Note 1 second = 1000ms
      const timeDiff: number = (maxValue - minValue) / 6;
      minValue = this.roundDate(minValue - timeDiff).getTime();
      maxValue = this.roundDate(maxValue + timeDiff).getTime();
    } else {
      const diff: number = (maxValue - minValue) / 6;
      maxValue += diff;
    }
    return {
      min: minValue,
      max: maxValue
    };
  }

  private roundDate(milliSeconds: number): Date{
    const dt: Date = new Date(milliSeconds);
    dt.setHours(0);
    dt.setMinutes(0)
    dt.setSeconds(0)
    return dt;
  }

  // in case the diff less than 1 min we wil have to display scale in  seconds
  // in case the diff is between 1min to 60 min we will display the scale in hours
  // in case the diff is between 1 hr to 1 day we need to show the hour scale
  // in case the diff between min & max is between 1 day to 7 days we will display days scale
  // in case the diff more then 7 days & less then 30 days then we show weeks scale
  /**
   * This function will get the min & max range value
   * returns {{min: number; max: number}}
   */
  public getMinAndMaxRange(): {min: number, max: number} {
    let minRange: number = this.chartConfig.margin.left;
    let maxRange: number = this.chartConfig.dimension.width - this.chartConfig.margin.right;
    switch (this.series.position) {
      case 'right':
      case 'left':
        minRange = this.chartConfig.margin.top;
        maxRange = this.chartConfig.dimension.height - this.chartConfig.margin.bottom;
        break;
    }
    if (!this.series.isReverse) {
      return {
        min: minRange,
        max: maxRange
      };
    } else {
      return {
        min: maxRange,
        max: minRange
      };
    }
  }

  /**
   * this function will make the axis fixed ath the top
   */
  makeAxisFixed(element: any): void {
    const d: any = document.createElement('div');
    d.style.width = '78%';
    d.style.overflow = 'hidden';
    d.id = 'floating-linear-axis-header';
    d.setAttribute('class', 'floating-linear-axis-header')
    element.appendChild(d);
    /// now in this we have to rerender the axis
    this.floatingAxis = d3.select(d)
      .append('svg')
      .attr('width', this.chartConfig.dimension.width)
      .attr('height', 56)
      .attr('transform', 'translate(0,0)');
    this.renderAxis(this.floatingAxis);
  }

  /**
   * this function will render the Aixs
   * param {ElementRef} element
   */
  renderAxis(chartObject: any): void {
    this.chartObject = chartObject;
    this.scaleObject = chartObject.append('g');
    // We have to make sure that the Axis is positioned properly
    switch (this.series.position) {
      case 'bottom':
        this.scaleObject
          .call(this.renderGridLines(d3.axisBottom(this.scale)))
          .attr('transform', 'translate(0,' + (this.chartConfig.dimension.height - this.chartConfig.margin.bottom) + ')');
        break;
      case 'right':
        this.scaleObject
          .call(this.renderGridLines(d3.axisRight(this.scale)))
          .attr('transform', 'translate(' + (this.chartConfig.dimension.width - this.chartConfig.margin.right) + ',0)');
        break;
      case 'top':
        this.scaleObject
          .call(this.renderGridLines(d3.axisTop(this.scale)))
          .attr('transform', 'translate(0,' + this.chartConfig.margin.top + ')');
        break;
      case 'left':
        this.scaleObject
          .call(this.renderGridLines(d3.axisLeft(this.scale)))
          .attr('transform', 'translate(' + this.chartConfig.margin.left + ',0)');
        break;
    }
    // if we have grid lines we will have to set the styling
    if (this.series.gridLines) {
      // get all tick lines
      this.scaleObject
        .selectAll('.tick line')
        .attr('class', this.series.gridLines.class
                        ? ('hcl-linear-axis ' + this.series.gridLines.class)
                        : 'hcl-linear-axis');

    }
  }
  /**
   * Call when we want to display grid lines
   * returns {any}
   */
  private renderGridLines(axis: any): any {
    this.axis = axis;
    // if we have grid lines object
    if (this.series.gridLines) {
      // we need the cat axis range as the grid dimension will depend on that
      const catAxisRange = this.chartRenderer.getCategoryAxisMinAndMaxRange();
      // get the tick formatting
      const tickFormat: any = this.series.tickFormat ? this.series.tickFormat : '';
      if (this.series.timeSeries) {
        return axis
          .ticks(this.series.timeSeries._timeInterval)
          .tickFormat(tickFormat)
          // the size will be the width or the height of the cat axis
          .tickSizeInner(-(catAxisRange.max - catAxisRange.min));
      }
      return axis
        .tickFormat(tickFormat)
        // the size will be the width or the height of the cat axis
        .tickSizeInner(-(catAxisRange.max - catAxisRange.min));
    }
    // no grid lines
    return axis;
  }

  /**
   * this function will change the scale of the linear axis
   * param {string} displayIn
   */
  public updateScale(displayIn: any) {
    if (this.series.timeSeries) {
      // this is a time series now change the value
      this.createScale();

      this.scaleObject.transition()
        .duration(800)
        .call(this.renderGridLines(d3.axisTop(this.scale)));
      if (this.series.gridLines) {
        // get all tick lines
        this.scaleObject
          .selectAll('.tick line')
          .attr('class', this.series.gridLines.class
            ? ('hcl-linear-axis ' + this.series.gridLines.class)
            : 'hcl-linear-axis');

      }
    }
  }

  /**
   * Get the placement position of the linear axis
   * returns {"top" | "bottom" | "right" | "left"}
   */
  public getPlacementPosition(): 'top' | 'bottom' | 'right' | 'left' {
    return this.series.position;
  }

  /**
   * Get the margin
   * returns {number}
   */
  public getMargin(): number {
    switch (this.series.position) {
      case 'top' :
        return this.chartConfig.margin.top;
      case 'bottom' :
        return this.chartConfig.margin.bottom;
      case 'right' :
        return this.chartConfig.margin.right;
      case 'left' :
        return this.chartConfig.margin.left;
    }
    return 0;
  }
  /**
   * Get the position of the current category
   * param {string} category
   * returns {any}
   */
  public getPosition(value: number): any {
    return this.scale(value);
  }

  /**
   * The max co-oridnate of the scale
   * returns {any}
   */
  public getMaxCoordinate(): number {
    return this.scale.range()[1];
  }

  /**
   * Get the invert position of the current category
   * param {string} category
   * returns {any}
   */
  public getInvertPosition(value: number): any {
    return this.scale.invert(value);
  }
}
