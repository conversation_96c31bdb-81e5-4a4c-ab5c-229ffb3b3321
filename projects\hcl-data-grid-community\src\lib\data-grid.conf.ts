import { HttpHeaders } from "@angular/common/http";
import { TemplateRef } from "@angular/core";
import { AbstractControl } from "@angular/forms";
import { SafeHtml } from "@angular/platform-browser";
import { ProcessCellForExportParams } from "ag-grid-community";
import {
  DatePickerConfig,
  InputConfig,
  LocaleSettings,
  MultiselectConfig,
  TreeConfig,
} from "hcl-angular-widgets-lib";
import { TimePickerConfig } from "hcl-angular-widgets-lib/lib/component/time-picker/time-picker.config";

export interface DataGridInfiniteScrollConf {
  cacheBlockSize?: number;
  rowBuffer?: number;
  // callback to create the URL in case of infinite scrolling
  urlParams?: (
    url: string,
    params: { startRow: number; endRow: number }
  ) => string;
}

export interface InfiniteDatasource {
  getRows(params: InfiniteGetRowsParams): void; // to fetch rows from the server. See below for params.
  destroy?(): void; // optional destroy method, if your datasource has state it needs to clean up
}

export interface InfiniteGetRowsParams {
  startRow: number; // The first row index to get.
  endRow: number; // The first row index to NOT get.
  sortModel: any; // If doing Server-side sorting, contains the sort model
  filterModel: any; // If doing Server-side filtering, contains the filter model
  context: any; // The grid context object
  successCallback(rowsThisBlock: any[], lastRow?: number): void; // Callback to call when the request is successful.
  failCallback(): void; // Callback to call when the request fails.
}

export interface DataGridConf {
  // If the columns are high & on h-scroll u see that few cells turn blank & then render
  // this is due to visualization of column, set this flag to true if u dont want that, default it is false
  suppressColumnVirtualisation?: boolean;
  scrollHeight?: number;
  rowBuffer?: number;
  disableRowAnimation?: boolean;
  columns: DataGridColumnConf[];
  data?: {}[];
  massageData?: (data: any[]) => any[];
  pagination?: DataGridPagination;
  isClientSideRowModel?: boolean;
  infiniteScroll?: DataGridInfiniteScrollConf;
  autoGroupColumnDef?: any;
  treeData?: boolean;
  getDataPath?: Function;
  dataUrl?: string;
  queryParams?: {
    [key: string]: any; // To allow any other query parameters
  };
  globalFilterOn?: boolean;
  rowExpansionAvailable?: boolean;
  dataKey?: string;
  reorderColumns?: boolean;
  rowReorderAvailable?: boolean;
  rowSelectMode?: "single" | "multiple";
  hideSelectionCheckBox?: boolean;
  scrollable?: boolean;
  resizableColumns?: boolean;
  noDataRetOnHttpResTemp?: string;
  noRowsTemplate?: string;
  loadingTemplate?: string;
  isFullWidthCell?: any;
  noDataFlag?: boolean;
  actions?: Actions;
  // by default it will be false
  hideActions?: boolean;
  topRowData?: any;
  fullWidthRow?: any;
  dropObj?: any;
  gridHeader?: GridHeader;
  suppressRowClickSelection?: boolean;
  suppressDragLeaveHidesColumns?: boolean;
  suppressRowTransform?: boolean;
  dragAndDrop?: boolean;
  multipleSort?: boolean;
  rowClassRules?: any;
  rowHeight?: number;
  rowGroupType?: any;
  enableRangeSelection?: boolean;
  isExternalFilterPresent?: Function;
  doesExternalFilterPass?: Function;
  isRowSelectable?: Function;
  processCellForClipboard?: (params: ProcessCellForExportParams) => any;
  suppressPasteForAllColumns?: boolean;
  infiniteInitialRowCount?: number;
  suppressKeyboardEvent?: Boolean;
}
export interface DataGridPagination {
  totalRecords?: number;
  total?: string; // for 'Total' string translations
  selected?: string; // for 'Selected' string translations
  currentPageIndex?: number;
  rowsPerPage?: number;
  pageSizeArray?: number[];
  rowsPerPageSuffix?: string; // Display no of rows drop-down suffix
  lazy?: boolean;
  pagination?: boolean;
  onLazyLoad?: (totalRecordTillNow: number, numberOfRows: number) => void;
  firstLabelString?: string;
  lastLabelString?: string;
  nextLabelString?: string;
  prevLabelString?: string;
  optionLabels?: string[];
  isCustomHandler?: boolean;
  previousPageIndex?: number;
  previousRowsPerPage?: number;
}
export interface DataGridColumnConf {
  suppressPaste?: boolean;
  // If set to true the data-grid will use the default rendere, that will help
  // to render the grid a bit faster
  useDefaultHeaderRenderer?: boolean;
  autoResizeToFit?: boolean;
  comparator?: Function;
  minWidth?: number;
  colId?: string;
  field: string;
  header: string;
  rowSpan?: (rowspan) => any;
  dataFormatter?: (rowData) => SafeHtml;
  checkboxSelection?: any;
  getQuickFilterText?: any;
  width?: number;
  resizable?: boolean;
  customLoadingRenderer?: {
    defaultRenderer?: boolean;
    infiniteLoadingTemplate?: string;
  };
  suppressColumnReorder?: boolean;
  rendererTemplateName?: string;
  _templateRef?: TemplateRef<any>;
  editable?: boolean;
  editableCellConf?: DataGridEditableCellConf;
  editableCombo?: boolean;
  editableOptions?: {}[];
  required?: boolean;
  sortable?: boolean;
  useDefaultRenderer?: boolean;
  headerRendererTemplateName?: string;
  _headerTemplateRef?: TemplateRef<any>;
  headerCheckboxSelection?: boolean;
  isHidden?: boolean;
  _popoverTemplateRef?: TemplateRef<any>;
  popoverTemplateName?: string;
  _sorting?: "ASC" | "DESC" | "NONE";
  disable?: boolean;
  headerCheckboxSelectionFilteredOnly?: boolean;
  tooltip?: {
    getTooltip?: (data) => string;
  };
  actions?: {
    actionFormatter?: (rowData, action) => SafeHtml;
    name: string;
    label?: string;
  }[];
  pinned?: string;
  lockPinned?: boolean;
  cellClass?: string[];
  cellClassRules?: {
    [key: string]: (param) => boolean;
  };
  mapData?: (rowData, columnDef, value) => any; // this method is provided as a callback to restructure data before exiting edit mode
}

/**
 * The configuration that is required for a editable cell
 */
export interface DataGridEditableCellConf {
  type:
    | "text"
    | "number"
    | "select"
    | "date"
    | "multiSelect"
    | "time"
    | "autoCompleteTree"
    | "autoComplete"
    | "dynamic";
  getDynamicType?: (def?: any, data?: any) => string;
  canEdit?: (data: any) => boolean;
  validateControl?: (event?: any, def?: any, rowData?: any) => boolean;
  // do some processing before edit
  initEdit?: (data: any, column: any) => void;
  conf?: {
    // In case we have a select box the conf should be specified here
    selectConf?: {
      // A place holder for the filed
      placeHolder?: string;
      // the hint message
      hintMsg?: string;
      // In case of select this is the list of options
      options?: { value?: string; label?: string }[];
      // In case we don't know the options when we set the conf we can use this function
      getOptions?: (rowData, colDef) => { value?: string; label?: string }[];
      name?: string;
      formControl?: AbstractControl;
    };
    // The input config
    inputConf?: InputConfig;
    dateConfig?: DatePickerConfig;
    localeConfig?: LocaleSettings;
    multiSelectConfig?: MultiselectConfig;
    timePickerConfig?: TimePickerConfig;
    autoCompleteConfig?: {
      suggestions?: any[];
      placeholder?: string;
      value?: string;
      autoComplete: "staticOptions" | "auto" | "none";
      suffixIconClass?: string;
      name: string;
      isBlurOrEnter?: boolean;
      disabled?: boolean;
      inputType?: string;
      formControl?: AbstractControl;
      optClick?: () => void;
    };
    autoCompleteTreeConfig?: {
      config: any;
      treeConfig: TreeConfig;
      inputConfig: any;
    };
  };
}
// Actions interface for adding hover actions on the row of grid
export interface Actions {
  hoverIcons: HoverIcon[];
}

// Interface for hover item specification
export interface HoverIcon {
  iconClickHandler?: (rowData?: any) => any;
  tooltip?: string;
  icon?: string;
  name?: string;
  disableIconHandler?: (rowData?: any) => boolean;
}

/**
 * In case user wants custome headrs to be used while we request
 * for data from the server the user has to create a class that implements
 * this interface & the getHeaers will be called by the grid
 */
export interface GridHeader {
  getHeaders(): HttpHeaders;
}
