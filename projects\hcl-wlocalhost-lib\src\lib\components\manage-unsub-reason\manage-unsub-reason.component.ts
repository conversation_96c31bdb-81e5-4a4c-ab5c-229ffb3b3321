import { Component, EventEmitter, HostListener, OnInit, Output, Renderer2, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ButtonConf,InputConfig, SideBarComponent, SlideToggleConf, NotificationService } from 'hcl-angular-widgets-lib';
import { IDropdownOption } from '../../interfaces';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import { ManageUnsubReasonConfig } from './manage-unsub-reason-config';

@Component({
  selector: 'hcl-manage-unsub-reason',
  templateUrl: './manage-unsub-reason.component.html',
  styleUrls: ['./manage-unsub-reason.component.scss']
})
export class ManageUnsubReasonComponent implements OnInit {

  // notify side bar is closed.
  @Output() sidebarClosed: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild(SideBarComponent) sideBarComponentRef: SideBarComponent =
  new SideBarComponent();

  managereason: UntypedFormGroup;
  otherconfig: SlideToggleConf = ManageUnsubReasonConfig.otherconfig;
  addReasonInputConfig: InputConfig = ManageUnsubReasonConfig.addReasonInputConfig;
  addReason: ButtonConf = ManageUnsubReasonConfig.addReason;
  addReasonDisable: ButtonConf = ManageUnsubReasonConfig.addReasonDisable;
  cancelConfig: ButtonConf = ManageUnsubReasonConfig.cancelConfig;
  saveConfig: ButtonConf =  ManageUnsubReasonConfig.saveConfig;
  list: IDropdownOption[];
  // Keeps track of highlighted section when modal is opened
  highlightedSection: HTMLElement;
  origList: {}[];
  addReasonControl;
  currClickedElement:HTMLElement;
  
  constructor( private _ngb: IpEmailBuilderService, private translate: TranslateService,
    private notificationService: NotificationService,
    private fb: UntypedFormBuilder,
    private renderer: Renderer2) { 
    // i18n for all the labels/text/buttons used in template.
    let btn: { [key: string]: string } = this.translate.instant("HYPERLINK");
    this.otherconfig.value = this.translate.instant("messages.show-unsub-reason-textarea");
    this.addReasonInputConfig.placeholder = this.translate.instant("messages.add-unsub-reason");
    this.addReason.value = this.translate.instant("settings.add");
    this.addReasonDisable.value = this.translate.instant("settings.add");
    this.cancelConfig.value = btn.CANCEL_HYPERLINK;
    this.saveConfig.value = btn.SAVE_HYPERLINK;
  }

  @HostListener('document:click', ['$event']) documentClickEvent($event) {
    
    const targetElement = $event?.target;
    let spanEditElement : HTMLElement = targetElement;

    // click on edit icon, get parent span
    if (targetElement.classList.contains('hcl-icon-edit')) {
      spanEditElement = targetElement.parentNode;
    }

    // when click on list item, do nothing
    if (targetElement.getAttribute('contenteditable')) {
      return;
    }

    // if no click on edit icon container, make list item non-editable
    if (!spanEditElement.classList.contains('edit-container')) {
      this.currClickedElement?.setAttribute('contentEditable', 'false');
    }
  }

  ngOnInit(): void {
    this.getListItems();
    this.groupFormControls();
    this.updateForm();
  }

  /**
   * Gets list of drop-down values.
  */
  getListItems(): void {
    const currentElement: any = this._ngb.currentEditingField$.getValue();
    this.list = currentElement?.dropdownOptionList;
    this.addOtherOption();
    this.origList = [...this.list];
    // weed out other option, donot show in slider
    this.list = this.list.filter(x => !x.isOtherOption);
  }

  /**
   * Add other option if it doesnot exist, a fallback case.
   */

  addOtherOption(): void {
    const dropdownList = this._ngb.unSubReasonDropDownList.dropdownOptionList;
    const otherOptionAvailable = item => item.isOtherOption === true;
    const isOtherOptionAvailable = dropdownList.some(otherOptionAvailable);
    if (isOtherOptionAvailable) {
      return;
    }
    const otherOption  = {
      "label": "Other",
      "value": "Other",
      "isOtherOption": true,
      "isVisible": true   
     };
     let otherOptionArr = [];
     otherOptionArr.push(otherOption);
     this._ngb.unSubReasonDropDownList.dropdownOptionList = this.mergeArray(this._ngb.unSubReasonDropDownList.dropdownOptionList, otherOptionArr);
  }

  /**
   * Group form controls to form group
   */

  groupFormControls(): void {

    this.managereason = this.fb.group({
      other: this.otherconfig.formControl,
      addreason: this.addReasonInputConfig.formControlName
    });
    this.addReasonControl = this.managereason.controls['addreason'];
    this.addReasonControl.setValidators([
      Validators.required
    ]);
  }

  /**
   * Updated form with values on initial launch.
   */
  updateForm() {
    this.setAddReasonValue('');
    let obj = this._ngb.unSubReasonDropDownList.dropdownOptionList;
    for (let i in obj) {
      if (obj[i].isOtherOption && obj[i].isVisible === true) {
          this.otherconfig.formControl.setValue(true);
      } else {
        this.otherconfig.formControl.setValue(false);
      }
    }
  }

  setAddReasonValue(val: string) : void {
    this.managereason.get('addreason')?.patchValue(val);
  }

  /**
   * Opens the sidebar.
   */
  openSidebar() {
    this.highlightedSection =  document.querySelector('.active');
    this.sideBarComponentRef.openSideBar();
    this.highlightedSection.classList.remove('active');
  }

  /**
   * When other toggle is changed
   */

   toggleChange(e) {
    console.log('toggle demo', e);
  }

  /**
   * Add a reason  
   */

   addReasonToList(event: any) {
     const reasonModel = {
      isOtherOption: false,
      isVisible: true,
      label: "",
      value: ""
     }
     // update the list model
     const newAddedReasonText = this.managereason.controls['addreason'].value;
     if (!newAddedReasonText) {
       return;
     }
     reasonModel.label = newAddedReasonText;
     reasonModel.value = newAddedReasonText;
     const itemNodes = document.querySelectorAll('.item-label');
     const itemExist = Array.prototype.slice.call(itemNodes).some(item => item.innerText === reasonModel.label);
     // donot add duplicate items
     if (itemExist) {
      this.notificationService.show({
        type: 'error',
        message: this.translate.instant('settings.Value_Exists'),
        autoHide: 3000,
        close: true
      });
      return;
     }
     this.list.push(reasonModel);
     // update drop down list in template with other option
     const dropDownObj =  this.mergeArray(this.list, this._ngb.unSubReasonDropDownList.dropdownOptionList);
     this.syncDropDownObject(dropDownObj);
     this.setAddReasonValue('');
   }

   /**
    * Places the cursor at the end of container
    * @param clickedElement, clicked item
    */

   placeCursorAtEnd(clickedElement: HTMLElement) {
      let range = document.createRange();
      range.selectNodeContents(clickedElement);
      range.collapse(false);
      let selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
   }

  /**
   * Edits the reason
  */
  editReason(event: any, index ) {
    const clickedElement:any = document.querySelector('div[data-index="' + index + '"' + "]");
    this.makeElementEditable(clickedElement);
  }

  /**
   * deletes the reason
  */
  deleteReason(event: any, i: number) {
    if (this.list.length === 1) {
      this.notificationService.show({
        type: 'error',
        message: this.translate.instant('settings.One_List_Item'),
        autoHide: 3000,
        close: true
      });
      return;
    }
    this.list.splice(i, 1);
    this._ngb.unSubReasonDropDownList.dropdownOptionList.splice(i, 1);
    const dropDownObj =  this.mergeArray(this.list, this._ngb.unSubReasonDropDownList.dropdownOptionList);
    // update drop down list 
    this.syncDropDownObject(dropDownObj);
  }

  /**
   * merges two arrays
   * @param arr1 
   * @param arr2 
   * @returns 
   */
  mergeArray(arr1, arr2): {}[] {
    return [...new Set([ ...arr1, ...arr2])];
  }

  /**
   * Makes an element editable
   * @param element 
   */
  makeElementEditable(element) : void {
    setTimeout(() => {
      const editableElements = document.querySelectorAll('[contentEditable="true"]');
      editableElements.forEach((el: HTMLElement) => {
        el.setAttribute('contentEditable', 'false');
      });

      // Set the current element to contentEditable true
      this.currClickedElement = element;
      element.setAttribute('contentEditable', 'true');
      element.focus();
      this.placeCursorAtEnd(element);

      // Add blur event listener to handle cursor out
      this.renderer.listen(element, 'blur', () => {
        element.setAttribute('contentEditable', 'false');
      });      
    }, 0);
   }

  /**
   * Save callback.
   */
  postSave() {
    // handle other toggle callback.
    const otherToogleValue = this.managereason.get('other').value;
    // reference to dropdown object.
    let obj = this._ngb.unSubReasonDropDownList.dropdownOptionList;
    let index = 0;
    for (let i in obj) {
      if (obj[i].isOtherOption) {
        if (otherToogleValue) { // toogle checked
          obj[i].isVisible = true;
          this.otherconfig.formControl.setValue(true);
        } else { // toogle unchecked
          obj[i].isVisible = false;
          this.otherconfig.formControl.setValue(false);
        }
      }
      this.updateListItemObj(obj, i, index++);
  }
    this.syncDropDownObject(obj);
  }

  /**
   * Updates drop down list item values on save
  */
  updateListItemObj(obj, i , index)  {
    const curElem:HTMLElement =  document.querySelector('div[data-index="' + index + '"' + "]");
    // skip other option
    if (obj[i].isOtherOption) {
      return;
    }
    obj[i].label = curElem?.innerText;
    obj[i].value = curElem?.innerText;
    delete obj[i].info;
  }

  /**
   * syns template dropdown obj with slider dropdown object.
   * this._ngb.unSubReasonDropDownList.dropdownOptionList refers template dropdown onjects
   * this.list refers slider drop down objects.
   */

  syncDropDownObject(dropDownObj): void {
    this._ngb.unSubReasonDropDownList.dropdownOptionList = dropDownObj;
  }

  /**
   * list item key down handler
   */
  keyDownHandler(event, item) {
    const listItem = event.target;
    item.info = listItem.innerText;
    // handle max length limit.
    if (listItem.innerText.length >= ManageUnsubReasonConfig.reasonMaxLength) {
      const selection = window.getSelection();
      const range = selection.getRangeAt(0);
      range.setStart(listItem.firstChild, ManageUnsubReasonConfig.reasonMaxLength);
      range.setEnd(listItem.firstChild, ManageUnsubReasonConfig.reasonMaxLength + 1);
      selection.removeAllRanges();
      selection.addRange(range);
      selection.deleteFromDocument();
    }
  }

  /**
   * Pre save, Validates the reason list
   */

   validateReasonList() {
    const itemNodes = document.querySelectorAll('.item-label');
    const itemNodesArr = Array.prototype.slice.call(itemNodes);
    const reasonList = [];
    itemNodesArr.forEach(item => reasonList.push(item.innerText));
    const duplicateItem = new Set(reasonList).size !== reasonList.length
  
     // If duplicate item exists, show error.
     if (duplicateItem) {
      this.notificationService.show({
        type: 'error',
        message: this.translate.instant('settings.Duplicate_Reason'),
        autoHide: 3000,
        close: true
      });
      return false;
     }
     return true;
   }

  /**
   * On sidebar, save btn click
   */
   onSave() {
     if (!this.validateReasonList()) {
       return;
     }
     this.postSave();
     this.closeSideBar();
     this.sidebarClosed.emit(false); // telling email page that sidebar is closed 
    }
  
    cancelAction() {
      this.syncDropDownObject(this.origList);
      this.closeSideBar();
      this.sidebarClosed.emit(false); // telling parent that sidebar is closed
      // emit event later on 
    }

    // closes slider
    closeSideBar() {
      this.sideBarComponentRef.close('close'); 
      this.highlightedSection.classList.add('active');
    }

  ngAfterViewInit() {
    this.openSidebar();
  }
}
