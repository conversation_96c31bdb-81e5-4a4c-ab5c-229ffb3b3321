import { Component, Input, OnInit, Output, EventEmitter, OnChang<PERSON>, ChangeDetector<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewEncapsulation } from '@angular/core';
import { HtmlBlock } from '../classes/Elements';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import html2canvas from 'html2canvas';
import { createBorder, createLineHeight, createPadding } from '../utils';
import { IpEmailBuilderService } from '../ip-email-builder.service';
import { HtmlField } from '../classes/Fields';


@Component({
  selector: 'ip-snippet-upload',
  styles: [
    `
      :host {
        display: block;
      }
      .snippet-info{
        background-color: rgb(204, 204, 204);
        border-radius:3px;
      }
      .choosed-snippet {
        width: 100%;
        max-height: 60px;
        overflow: hidden;
        border-radius: 3px;
        line-height: 0;
        display: flex;
        justify-content: center;
      }
      .choosed-snippet img {
        width: 100%;
        height: auto;
        object-fit: cover;
        object-position: center;
      }
      .buttonlabel {
        max-width: 70px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .size {
        font-size: 80%;
        color: #ccc;
      }
      .change-me-container {
        margin-top: -15px;
        display: inline-block;
        & > div {
          background: #cccccc;
          color: #b0afb0;
          font-size: 7px;
          text-align: center;
          width: 117px;
          height: 89px;
          line-height: 89px;
        }
      }
      #hclConvertToImage {
        width: 350px;
        height: 275px;
        background: transparent;
        position: fixed;
        overflow: hidden;
        /* left: -100px; */
        /* visibility: hidden; */
      }

      #hclConvertToImage > div img {
        max-width: 100%;
      }
    `
  ],
  template: `
  <div fxLayout fxLayoutAlign="space-between center">
    <div
      class="snippet-info"
      [fxFlex]="checkinnerHtml() ? '30%' : '40%'"
      fxLayout
      fxLayoutGap="0.5em"
      fxLayoutAlign="center center"
      >
      <div class="choosed-snippet">
          <img *ngIf="readyToRender"  [src] = "previewSrc" />
          <div *ngIf="!previewSrc"
            style = "background: #cccccc;
            color: #b0afb0;
            width: 100%;
            height: 60px;
            line-height: 60px;
            font-size: 10px;
            text-align: center; ">
            {{ 'snippet.snippet-preview' | translate }}
          </div>
        </div>
          
    </div>
    <button
    *ngIf="showEdit" 
      type="button"
      (click)="edit()"
      [disabled]="browsing | async"
      color="accent"
      mat-stroked-button
      hclTooltip ="{{ 'snippet.edit-html' | translate }}"
      >
      <div class="buttonlabel">
      {{ 'snippet.edit-html' | translate }} </div>
      </button>
      <button
    *ngIf="!showEdit" 
      type="button"
      (click)="edit()"
      [disabled]="browsing | async"
      color="accent"
      mat-stroked-button
      hclTooltip ="{{ 'snippet.html' | translate }}"
      >
      <div class="buttonlabel"> {{ 'snippet.html' | translate }} </div>
     
      </button>
      <button
      type="button"
      (click)="browse()"
      [disabled]="browsing | async"
      color="accent"
      mat-stroked-button
      hclTooltip ="{{ 'image.browse' | translate }}"
      >
      <div style = "max-width: 48px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;">{{ 'image.browse' | translate }} </div>
      </button>
  </div>
    <mat-form-field appearance="legacy" style="width: 100%" class="pt-2">
      <mat-label>{{ 'snippet.filename' | translate }}</mat-label>
      <input
        matInput
        (input)="onInput($event.target.value)"
        [(ngModel)]="fileName"
        type="text" disabled
      />
    </mat-form-field>
  `,
  encapsulation: ViewEncapsulation.None
  // changeDetection: ChangeDetectionStrategy.OnPush
})
export class SnippetUploadComponent implements OnInit, OnChanges, OnDestroy {
  @Input() block: HtmlBlock | HtmlField;
  @Input() fileName: string;
  @Input() reloadPreview: Observable<void>;
  @Output() uploadSnippet: EventEmitter<any> = new EventEmitter();
  @Output() editHtml: EventEmitter<any> = new EventEmitter();
  readyToRender = false;
  previewSrc: string;
  browsing = new BehaviorSubject(false);
  private reloadSubscription: Subscription;
  showEdit: boolean = true;

  constructor(private _ngb: IpEmailBuilderService, private chRef: ChangeDetectorRef) {
    this._ngb.currentEditingSnippetContent$.subscribe((content) => {
      if (content) {
        this.showEdit = true;
      }
      else {
        this.showEdit = false;
      }
      window.setTimeout(() => {
        this.chRef.detectChanges();
      }, 0)
    });
    this._ngb.currentEditingBlock$.subscribe((block) => {
      if (block && block.type === 'html') {
        if (this.removeHTMLSnippetContainerWithStyles(block?.options && block?.options.innerHtml)) {
          this.showEdit = true;
        } else {
          this.showEdit = false;
        }
        window.setTimeout(() => {
          this.chRef.detectChanges();
        }, 0)
      }
    })
    this._ngb.currentEditingField$.subscribe((field) => {
      if (field && field.type === 'html-field') {
        if (this.removeHTMLSnippetContainerWithStyles(field?.options && field?.options.innerHtml)) {
          this.showEdit = true;
        } else {
          this.showEdit = false;
        }
        window.setTimeout(() => {
          this.chRef.detectChanges();
        }, 0)
      }
    })
    this._ngb.currentEditingBlockFocusSnippet$.subscribe((content) => {
      if (content) {
        this.showEdit = true;
      }
      else {
        this.showEdit = false;
      }
      window.setTimeout(() => {
        this.chRef.detectChanges();
      }, 0)
    })
  }

  setPreviewSrc() {
    if (this.removeHTMLSnippetContainerWithStyles(this.block.options.innerHtml) && !document.querySelector(`._${this.block.options.id}`)) {
      this.readyToRender = false;
      const HTMLDOMParser = new DOMParser();
      const htmlStringForUpdate = HTMLDOMParser.parseFromString(this.block.options.innerHtml, "text/html");
      const preInnerHtml = htmlStringForUpdate.documentElement.querySelector('body').innerHTML;
      htmlStringForUpdate.documentElement.querySelector('body').innerHTML = `<div id="hclConvertToImage"  class="_${this.block.options.id}">${preInnerHtml}</div>`;
      document.body.appendChild(htmlStringForUpdate.documentElement.querySelector(`._${this.block.options.id}`));

      const {
        backgroundColor,
        border,
        color,
        font,
        align,
        lineHeight,
        padding
      } = this.block.options;
      const tempNodeStyles: any = [
        { 'background-color': backgroundColor },
        { 'color': color },
        { 'align': align },
        { 'line-height': `${createLineHeight(lineHeight)}` },
        { 'padding': `${createPadding(padding)}` },
        { 'font-family': `${font.family}, ${font.fallback}` },
        { 'font-size': `${font.size} px` },
        { 'font-style': `${font.style}` },
        { 'font-weight': `${font.weight}` },
      ]
      if (border.width) {
        const borderObj = createBorder(border);
        tempNodeStyles.push({ 'border': borderObj.border });
        tempNodeStyles.push({ 'border-radius': borderObj.borderRadius });
      }
      const tempNode = document.querySelector(`._${this.block.options.id}`) as HTMLElement;
      tempNodeStyles.forEach((styleItem) => {
        const propName = Object.keys(styleItem)[0];
        const propValue = styleItem[propName];
        tempNode.style[propName] = propValue;
      })

      html2canvas(tempNode, { allowTaint: true, useCORS: true }).then((canvas) => {
        const data = canvas.toDataURL('image/jpeg', 0.9);
        this.previewSrc = encodeURI(data);
        this.block.options.preview = this.previewSrc
        tempNode.parentElement.removeChild(tempNode);
        this.readyToRender = true;
        this.chRef.detectChanges();
      })
    } else {
      this.previewSrc = '';
      this.readyToRender = false;
    }
  }

  removeHTMLSnippetContainerWithStyles(html: string) {
    const originalSnippetCode: any = new DOMParser().parseFromString(html, 'text/html');
    const allHTMLSnippetContainers: any = originalSnippetCode.querySelectorAll('div#hcl-snippet-container');
    if (allHTMLSnippetContainers.length > 0) {
      html = allHTMLSnippetContainers[0].innerHTML;
    }
    return html;
  }

  browse() {
    // this.block.options.initialLoad = true;
    this.uploadSnippet.emit();
  }

  edit() {
    this.editHtml.emit();
  }

  checkinnerHtml() {
    if (this.removeHTMLSnippetContainerWithStyles(this.block.options.innerHtml)) {
      return true;
    }
    else {
      return false;
    }
  }

  ngOnChanges(changes: any): void {
    if (changes.fileName && changes.fileName.currentValue && changes.fileName.currentValue !== changes.fileName.previousValue) {

      if (this.fileName.substring(this.fileName.length - 10) !== '.snip.html') {
        this.fileName = this.fileName + '.snip.html';
      }
      this.setPreviewSrc();
    }

    if ((changes.block && (changes.block.currentValue?.options.innerHtml !== changes.block.previousValue?.options.innerHtml))) {
      if (this.removeHTMLSnippetContainerWithStyles(this.block.options.innerHtml) && !document.querySelector(`._${this.block.options.id}`)) {
        if (this.block.options.preview) {
          this.previewSrc = this.block.options.preview
          this.readyToRender = true;
        }
        else {
          this.setPreviewSrc();
        }
      }
      else {
        this.previewSrc = '';
        this.readyToRender = false;
      }

    }
  }

  ngOnInit() {
    this.reloadSubscription = this.reloadPreview.subscribe(() => {
      this.setPreviewSrc();
    })

    if (this.removeHTMLSnippetContainerWithStyles(this.block?.options && this.block?.options.innerHtml)) {
      this.setPreviewSrc();
    }
    if (this.fileName && this.fileName.substring(this.fileName.length - 10) !== '.snip.html') {
      this.fileName = this.fileName + '.snip.html';
    }

    if (this.removeHTMLSnippetContainerWithStyles(this.block?.options && this.block?.options.innerHtml)) {
      this.showEdit = true;
    } else {
      this.showEdit = false;
    }
    window.setTimeout(() => {
      this.chRef.detectChanges();
    }, 0)
  }

  onInput(value: string) {
    if (value) {
      // this.setPreviewSrc();
    }
  }

  ngOnDestroy() {
    this.reloadSubscription.unsubscribe();
  }
}
