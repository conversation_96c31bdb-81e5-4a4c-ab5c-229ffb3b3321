<div class="segment-metadata-container">
  <div [formGroup]="segmentMetadataForm">
    <div class="w-300">
      <hcl-input class="required" [config]="segmentNameConfig"></hcl-input>
    </div>
    <div>
      <hcl-textarea [config]="segmentDescriptionConfig"></hcl-textarea>
    </div>
    <div class="browse-folder">
      <hcl-input class="required" [config]="folderNameConfig"></hcl-input>
      <hcl-button
        (onclick)="openFolderSelection()"
        [config]="browseButtonConfig"
      >
      </hcl-button>
    </div>
    <div class="w-300">
      <hcl-drop-down
        [config]="securityPolicyConfig"
        class="required"
        (select)="updateSegmentSecurityPolicy()"
      >
      </hcl-drop-down>
    </div>
    <div class="mt-20">
      <div class="d-flex">
        <hcl-auto-complete
          class="flex-grow-1"
          [config]="usageCategoryConfig"
          (optClick)="addTag($event)"
          (blur)="onBlurEvnt($event)"
        >
        </hcl-auto-complete>
        <hcl-button
          class="ml-3"
          (onclick)="addTag(usageCategoryConfig.formControl.value)"
          [config]="addTagBtnConfig"
        ></hcl-button>
      </div>
      <!-- <div class="w-300"> -->
      <div class="chips-container">
        <ng-container *ngFor="let tag of usageCategoryTags">
          <div class="rounded-chip">
            <div class="chip-child">
              <div
                class="ellipsis"
                data-position="left-bottom-start"
                hclTooltip="{{ tag }}"
              >
                {{ tag }}
              </div>
              <i class="hcl-icon-close" (click)="deleteTag(tag)"></i>
            </div>
          </div>
        </ng-container>
      </div>
      <!-- </div> -->
    </div>
  </div>
</div>

<hcl-side-bar [disableClose]="true">
  <aside class="sidebarContainer" *ngIf="showSidebar">
    <hcl-folder-selection
      [config]="folderConfig"
      (cancleAction)="closeFolderSelection()"
      (folderSelected)="folderSelected($event)"
    ></hcl-folder-selection>
  </aside>
</hcl-side-bar>
