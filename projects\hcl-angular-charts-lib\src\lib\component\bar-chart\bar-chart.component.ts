import {AfterViewInit, Component, ElementRef, Input, OnChanges, OnInit, Renderer2, SimpleChanges, ViewEncapsulation} from '@angular/core';
import {ChartConfig} from '../../config/chart-config';
import {BaseChartRenderer} from '../../renderer/BaseChartRenderer';
import {CHART_TYPE, RendererFactory} from '../../renderer/RendererFactory';
import * as d3 from 'd3';
import { BarChartConfig } from './bar-chart';

@Component({
  selector: 'hcl-bar-chart-v2',
  templateUrl: './bar-chart.component.html',
  styleUrls: ['./bar-chart.component.scss'],
  encapsulation : ViewEncapsulation.None
})
export class BarChartComponent implements OnInit, AfterViewInit, OnChanges {
  /**
   * The default chart renderer
   */
  private chartRenderer: BaseChartRenderer;
  xScale: any;
  xScaleLinearGauge: any;
  yScale: any;
  linearGuage: any;

  barMouseOver: Function = null;
  barMouseLeave: Function = null;

  @Input() data = [];

  parentGroupElem: any

  /**
   * The configuration of the chart
   */
  @Input() config : BarChartConfig = {
    chartContainerId: `bar_chart1`,
    height : 500,
    width: 1100,
    tooltipHTML: (data) => {
      return `<div class="bar-chart-tooltip-container">
                  <div class="bar-chart-tooltip-row">
                      <span class="bar-chart-tooltip-label">Average sent: </span>
                      <span class="bar-chart-tooltip-xAxis">${data.xAxis}</span>
                  </div>
                  <div class="bar-chart-tooltip-row">
                      <span class="bar-chart-tooltip-label">Domain count: </span>
                      <span class="bar-chart-tooltip-yAxis">${data.yAxis}</span>
                  </div>
                  <div class="bar-chart-tooltip-row">
                      <span class="bar-chart-tooltip-label">Unresponsive color: </span>
                      <span class="bar-chart-tooltip-color">${data.color}%</span>
                  </div>
              </div>`;
    }
  }
  /**
   * The default constructor
   * renderer: we need to play with the DOM so we need the Renderer
   * element: we will add the Charting to the current element
   */
  constructor(private renderer: Renderer2,
              private element: ElementRef) { }
  ngOnChanges(changes: SimpleChanges): void {
    console.log(changes.data)
    if (changes.data.currentValue && Array.isArray(changes.data.currentValue) && changes.data.currentValue.length > 0) {
      window.setTimeout(() => {
        if (d3.select('#svg_bar_chart_' + this.config.chartContainerId)) {
          d3.select('#svg_bar_chart_' + this.config.chartContainerId).remove()
        }
        this.createParentSVG();
        this.setupBarMouseEvents();
        this.createXAxis();
        this.createYAxis();
        this.createBars();
      }, 0)
    }
  }

  /**
   * We need to create the renderer
   */
  ngOnInit() {

  }

  /**
   * After the initialization we need to render the chart
   */
  ngAfterViewInit(): void {

  }


  createParentSVG() {
    d3.select('.bar-chart')
      .append(`svg`)
      .attr('id', 'svg_bar_chart_' + this.config.chartContainerId)
      .attr(`width`, this.config.width)
      .attr(`height`, this.config.height)
    this.parentGroupElem = d3.select('#svg_bar_chart_' + this.config.chartContainerId).append(`g`);
  }

  createXAxis() {
    const width = this.config.width - 100;
    const height = this.config.height - 100;
    this.xScale = d3.scaleBand().range([0, width]).padding(0.3);
    this.xScale.domain(this.data.map(function(dataItem: any) { return dataItem.xAxis; }));
    this.parentGroupElem.append(`g`)
      .attr(`transform`, `translate(70,` + (height + 50) + `)`)
      .call(d3.axisBottom(this.xScale).tickSizeOuter(0))
      .attr(`class`, `xAxis`)
      .append(`text`)
      .attr(`transform`, `translate(${width / 2.25}, 0)`)
      .attr(`dy`, `3.71em`)
      .attr(`class`, `xAxisLabel`)
      .text(this.config.xAxisLabel ? this.config.xAxisLabel : '')
    
  }

  createYAxis() {
    const height = this.config.height - 100;
    this.yScale = d3.scaleLinear().range([height, 0]);
    this.yScale.domain([0, d3.max(this.data, function(d: any) { return d.yAxis; })]);
    this.parentGroupElem.append(`g`)
      .attr(`transform`, `translate(70, 50)`)
      .call(d3.axisLeft(this.yScale).tickFormat(function(d){
          return `` + d;
      }).ticks(10).tickSizeOuter(0))
      .attr(`class`, `yAxis`)
      .append(`text`)
      .attr(`transform`, `translate(-60, ${height / 2.25}), rotate(-90)`)
      .attr(`dy`, `0.71em`)
      .attr(`class`, `yAxisLabel`)
      .text(this.config.yAxisLabel ? this.config.yAxisLabel : '');
    this.parentGroupElem.selectAll(`.tick`)
    .filter(function (d) { return d === 0;  })
    .remove();
  }

  createBars() {
    let myColor;
    if (this.config.percentageColorStops) {
      myColor = d3.scaleLinear<string>().domain(this.config.percentageColorStops.map(stop => stop.percentage))
      .range(this.config.percentageColorStops.map(stop => stop.color))
    }  
    const height = this.config.height - 100;
    const sortedData = this.data.sort((first, second) =>  Number(second['color']) - Number(first['color'])
    );
    let maxColorValue;
    if (sortedData.length > 0) {
      maxColorValue = sortedData[0].color
    }
    this.parentGroupElem.selectAll(`.bar`)
      .data(this.data)
      .enter().append(`rect`)
      .attr(`transform`, `translate(70, 50)`)
      .attr(`class`, `bar`)
      .attr(`x`, (d) => { return this.xScale(d.xAxis); })
      .attr(`y`, (d) => { return this.yScale(d.yAxis); })
      .attr(`width`, this.xScale.bandwidth())
      .attr(`height`, (d) => { return height - this.yScale(d.yAxis); })
      .on('mouseenter', (data, index, node) => {this.barMouseOver(data, index, node);})
      .on('mouseout', (data, index, node) => {this.barMouseLeave(data, index, node)})
      .style(`fill`, (this.config.percentageColorStops ? function(d){return myColor(Math.ceil((d.color / maxColorValue) * 100))} : this.config.barColor) )

  }

  setupBarMouseEvents() {
    const tooltipContainer = document.createElement("div");
    tooltipContainer.className = "bar-chart-tooltip";
    const tooltipArrowElement = document.createElement("div");
    tooltipArrowElement.className = "bar-chart-tooltip-arrow";
    const tooltipContentsElement = document.createElement("div");
    tooltipContentsElement.className = "bar-chart-tooltip-contents";
    tooltipContainer.appendChild(tooltipArrowElement);
    tooltipContainer.appendChild(tooltipContentsElement);
    document.body.appendChild(tooltipContainer);  


    const tooltip = d3.select( ".bar-chart-tooltip" );
    const tooltipContents = d3.select( ".bar-chart-tooltip .bar-chart-tooltip-contents" );
    this.barMouseOver = (data, index, node) => {      
      tooltip
      .style('display', 'block')
      .style('left', `${d3.event.clientX}px`)
      .style('top', `${(d3.event.clientY - 48)}px`);
      tooltipContents.html(this.config.tooltipHTML(data))
      node[index].style.stroke = "#f5821e";
      node[index].style["stroke-width"] = "3"
    };
  
    this.barMouseLeave = (data, index, node) => {
        tooltip.style('display', 'none');
        node[index].style.stroke = "transparent";
        node[index].style["stroke-width"] = "3"
    };
  }



}