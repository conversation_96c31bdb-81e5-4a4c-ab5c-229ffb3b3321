<div class="app-container" *ngIf="load1">
    <hcl-select-offers *ngIf="loadSelectOfferListing && load2" (closeOffers)="close()"
        (selectedItems)="selectedOffersData($event)" [config]="config">
    </hcl-select-offers>


    <hcl-offer-variation *ngIf="loadViewOffer" (loadOffers)="loadOffersListing($event)" (closePane)="close()">
    </hcl-offer-variation>


    <hcl-offer-selection *ngIf="loadManageOffers" (cancel)="loadOffersListing($event)"></hcl-offer-selection>

    <hcl-view-offer-list *ngIf="loadViewOfferList" (loadOfferLists)="loadOffersListsListing($event)"
        (closePane)="close()">
    </hcl-view-offer-list>
</div>