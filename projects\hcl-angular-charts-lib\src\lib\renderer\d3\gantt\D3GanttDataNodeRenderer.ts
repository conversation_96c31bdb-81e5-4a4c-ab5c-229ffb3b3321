import * as d3 from 'd3';
import {D3LinearAxisRenderer} from '../D3LinearAxisRenderer';
import {D3CategoryAxisRenderer} from '../D3CategoryAxisRenderer';
import {ChartSeries, Interactive} from '../../../config/chart-config';
import {D3Drag<PERSON>enderer} from '../D3DragRenderer';
import {D3ChartRenderer} from '../D3ChartRenderer';
import {D3DraggableNode} from '../interface/D3DraggableNode';

export class D3GanttDataNodeRenderer implements D3DraggableNode {
  /**
   * Flag if set to true will not plot the node
   * type {boolean}
   */
  private doNotPlot: boolean = false;
  private hoverRectangle: any;
  /**
   * The actual data Node
   */
  private dataRectangle: any;
  /**
   * the dependency circle Node
   */
  private depCircle: any;
  /**
   * The drag renderer that will be responsible for dragging of the node
   */
  private dragRenderer: D3DragRenderer;
  /**
   * The list of custom elements
   */
  private customElements: {svgElement: any, config: any}[] = [];
  /**
   * The default constructor
   * param {D3LinearAxisRenderer} linearAxisRenderer
   * param {D3CategoryAxisRenderer} categoryAxisRenderer
   * param {ChartSeries} series
   * param dataObject
   * param globalDom
   */
  constructor(protected linearAxisRenderer: D3LinearAxisRenderer,
              protected categoryAxisRenderer: D3CategoryAxisRenderer,
              protected series: ChartSeries,
              protected dataObject: any,
              protected chartRenderer: D3ChartRenderer,
              protected globalDom: any) {
    this.globalDom = d3.select(this.globalDom);
  }

  public setDoNotPlot(flag: boolean): void {
    this.doNotPlot = flag;
  }

  /**
   * Calling this function will actually render the node in the canvas
   */
  public render(): void {
    // render a rectangle that will give a shade in case of hoever & select
    this.renderBackgroundRectangle();
    this.renderRectangle();
    this.renderDependencyStartNode();
    // we need to render other elements as well on the bar
    this.renderCustomElements();
    // in case the chart is interactive we need to attach events
    this.updateInteractiveMode(this.series.interactive);
  }

  /**
   * Remove the node from the SVG
   */
  public removeNode(): void {
    this.globalDom.selectAll('*').remove();
    this.dataRectangle = null;
  }

  /**
   * This will hide the node
   */
  public hideNode(): void {
    this.globalDom.style('visibility', 'hidden');
    // this.globalDom.style('display', 'none');
    // this.globalDom.style('opacity', 0);
  }

  /**
   * The background rect that will highligt on select
   */
  private renderBackgroundRectangle(): void {
    const height: number = this.categoryAxisRenderer.getBandWidth() +
                           (this.categoryAxisRenderer.getBandWidth() * this.categoryAxisRenderer.getPadding());
    const yCorrection: number = (this.categoryAxisRenderer.getBandWidth() * this.categoryAxisRenderer.getPadding()) / 2;
    this.hoverRectangle = this.globalDom.append('rect')
      .attr('class', 'hcl-gantt-node-background')
      .attr('x', 2)
      .attr('y', (d, i): any => {
        return this.categoryAxisRenderer.getPosition( this.series.getCategory(d, i)) - yCorrection;
      })
      // we will add a curved at the edges
      // the height will be equal to bandwidth
      .attr('height', height)
      // width needs to be calculated
      .attr('width', this.linearAxisRenderer.getMaxCoordinate());
  }

  /**
   * Let user add custom elements on the bar as well
   */
  private renderCustomElements(): void {
    if (this.series.customElements) {
      this.customElements = [];
      this.series.customElements.forEach(element => {
        let cElement: any = null;
        if (element.type === 'rect') {
          cElement = this.globalDom.append(element.type)
            .attr('class', 'hcl-gantt-custom-element ' + (element.getClass ? element.getClass(this.dataObject) : ''))
            .attr('width', element.width)
            .attr('height', element.height)
            .attr('x', (d, i): any => {
              return this.linearAxisRenderer.getPosition(this.series.getMinValue(d, i) + element.getXOffSet());
            })
            .attr('y', (d, i): any => {
              return this.categoryAxisRenderer.getPosition(this.series.getCategory(d, i)) + element.getYOffSet();
            });
        } else if (element.type === 'path') {
          cElement = this.globalDom.append(element.type)
            .attr('class', 'hcl-gantt-custom-element ' + (element.getClass ? element.getClass(this.dataObject) : ''))
            .attr('d', (d, i) => {
              const x: number = this.linearAxisRenderer.getPosition(this.series.getMinValue(d, i)) + element.getXOffSet();
              const y: number = this.categoryAxisRenderer.getPosition(this.series.getCategory(d, i)) + element.getYOffSet();
              return element.path.getPath(d, x, y);
            });
        } else if (element.type === 'text') {
          cElement = this.globalDom.append(element.type)
            .attr('class', 'hcl-gantt-custom-element-text ' + (element.getClass ? element.getClass(this.dataObject) : ''))
            .attr('x', (d, i): any => {
              return this.linearAxisRenderer.getPosition(this.series.getMinValue(d, i)) + element.getXOffSet();
            })
            .attr('y', (d, i): any => {
              return this.categoryAxisRenderer.getPosition(this.series.getCategory(d, i)) + element.getYOffSet();
            })
            .text(element.text(this.dataObject));
        }
        this.customElements.push({
          svgElement: cElement,
          config : element
        });
      });
    }
  }

  /**
   * refresh the parent global object css
   */
  public refreshParentCss(): void {
    this.globalDom
      .attr('class', (d) => {
        return 'taskbar ' +
          (this.series.getParentCssClass ? this.series.getParentCssClass(d) : '');
      });
  }

  public reRenderNodes(): void {
    if (this.doNotPlot) {
      return;
    }
    this.refreshParentCss();
    if (!this.categoryAxisRenderer.getPosition(this.series.getCategory(this.dataObject, -1))) {
      // we need to hide this object
      this.removeNode();
    } else {
      if (!this.dataRectangle) {
        this.render();
      } else {
        this.dataRectangle
          .transition()
          .duration(800)
          .attr('x', (d, i): any => {
            return this.linearAxisRenderer.getPosition(this.series.getMinValue(d, i));
          })
          .attr('y', (d, i): any => {
            return this.categoryAxisRenderer.getPosition(this.series.getCategory(d, i));
          })
          .attr('width', (d, i): any => {
            if (this.series.getMaxValue(d, i)) {
              return this.linearAxisRenderer.getPosition(this.series.getMaxValue(d, i))
                - this.linearAxisRenderer.getPosition(this.series.getMinValue(d, i));
            }
        });
        this.depCircle
          .transition()
          .duration(800)
          .attr('cx', (d, i): any => {
            return this.linearAxisRenderer.getPosition(this.series.getMaxValue(d, i));
          })
          .attr('cy', (d, i): any => {
            return this.categoryAxisRenderer.getPosition(this.series.getCategory(d, i))
              + (this.categoryAxisRenderer.getBandWidth() / 2);
          })
          .attr('r', (d, i): any => {
            if (this.series.getMaxValue(d, i)) {
              return 4;
            }
        });
        // we have to re-render the custom elements as well
        this.customElements.forEach(e => {
            const c = e.config;
            if (c.type === 'path') {
              e.svgElement.transition()
                .duration(800)
                .attr('d', (d, i) => {
                  const x: number = this.linearAxisRenderer.getPosition(this.series.getMinValue(d, i)) + c.getXOffSet();
                  const y: number = this.categoryAxisRenderer.getPosition(this.series.getCategory(d, i)) + c.getYOffSet();
                  return c.path.getPath(d, x, y);
              });
            } else if (c.type === 'text') {
              e.svgElement.transition()
                .duration(800)
                .attr('x', (d, i): any => {
                  return this.linearAxisRenderer.getPosition(this.series.getMinValue(d, i)) + c.getXOffSet();
                })
                .attr('y', (d, i): any => {
                  return this.categoryAxisRenderer.getPosition(this.series.getCategory(d, i)) + c.getYOffSet();
                })
                .text(c.text(this.dataObject));
            }
        });
      }
    }
  }

  /**
   * In case you want to make the element interactive we can call this function.
   * This will be called by default from render
   * param {Interactive} interactive
   */
  public updateInteractiveMode(interactive: Interactive): void {
    if (interactive.draggable) {
      this.dragRenderer = new D3DragRenderer(this, this.chartRenderer);
      this.dataRectangle.call(this.dragRenderer.drag());
    }
  }

  /**
   * This function will render the dependency circle
   */
  private renderDependencyStartNode(): void {
    this.depCircle = this.globalDom.append('circle')
      .attr('class', 'hcl-gantt-node-circle')
      .attr('cx', (d, i): any => {
        return this.linearAxisRenderer.getPosition( this.series.getMaxValue(d, i));
      })
      .attr('cy', (d, i): any => {
        return this.categoryAxisRenderer.getPosition( this.series.getCategory(d, i))
          + (this.categoryAxisRenderer.getBandWidth() / 2);
      })
      .attr('r', (d, i): any => {
        if (this.series.getMaxValue(d, i)) {
          return 4;
        }
    });
  }

  /**
   * this function will render the data rectangle in the canvas
   */
  private renderRectangle(): void {
    // get the min value
    this.dataRectangle = this.globalDom
      .append('rect')
      // let user customize the rectangle by their own Css
      .attr('class', (d, i): string => {
        let css: string = 'hcl-gantt-node';
        // if we have the dataCss we append that
        if (this.series.dataCss) {
          css += ' ' + this.series.dataCss;
        }
        // if we have a function we have to call that as well
        if (this.series.getCssClass) {
          css += ' ' + this.series.getCssClass(d);
        }
        return css;
      })
      .attr('x', (d, i): any => {
        return this.linearAxisRenderer.getPosition(this.series.getMinValue(d, i));
      })
      .attr('y', (d, i): any => {
        return this.categoryAxisRenderer.getPosition( this.series.getCategory(d, i));
      })
      // we will add a curved at the edges
      .attr('rx', 4)
      .attr('ry', 4)
      // the height will be equal to bandwidth
      .attr('height', this.categoryAxisRenderer.getBandWidth())
      // width needs to be calculated
      .attr('width', (d, i): any => {
        if (this.series.getMaxValue(d, i)) {
          return this.linearAxisRenderer.getPosition(this.series.getMaxValue(d, i))
            - this.linearAxisRenderer.getPosition(this.series.getMinValue(d, i));
        }
      })
      .on('click', this.rectangleClicked.bind(this));
    // if we have a click handler we have to attach them
  }

  /**
   * Called when a rectangle is clicked
   */
  private rectangleClicked(d: any, i: number): void {
    if (this.series.click) {
      this.series.click(d);
    }
  }

  /**
   * Function that will tell if the element can be dragged or not
   * param data
   * returns {boolean}
   */
  public canBeDragged(data: any): boolean {
    // chk if there is a function in the series
    if (this.series.interactive && this.series.interactive.canBeDragged) {
      // the verification power is with the user
      return this.series.interactive.canBeDragged(data);
    }
    // if the chart is interactive & since no function is provided we will make it draggable
    return true;
  }

  /**
   * This function will round the time as per the configuration
   * param {{min: number; max: number}} time
   */
  private roundTimeAsPerConfiguration(data: any, time: { min: number; max: number }) : { min: number; max: number } {
    if (this.series.roundTime) {
      return this.series.roundTime(data, time);
    }
    return time;
  }

  /**
   * this function is called when the data Node position is changed
   */
  public dataNodePositionUpdate(data: any, coOrdinate: { x: number; y: number }): void {
    // we may have to round the coordinates
    const roundedTime: {min: number, max: number} = this.roundTimeAsPerConfiguration(data, this.getMinAndMaxBasedOnCoOrdinates(coOrdinate));
    // lets change the x & y as well
    coOrdinate.x = this.linearAxisRenderer.getPosition(roundedTime.min);
    // we may have to stick to a position
    this.dataRectangle.attr('x', coOrdinate.x);
    this.depCircle.attr('cx', coOrdinate.x + this.getDataNodeDimensions().width);
    // emmit the eveent thta there is a update
    this.chartRenderer.dataNodePositionUpdated(this.series,
                  data, roundedTime);
    // we may have custom elements, we need to reposition them as well
    if (this.customElements.length > 0 ) {
      this.customElements.forEach((e, index) => {
        if (e.config.type === 'path') {
          e.svgElement.attr('d', (d, i) => {
            const y: number = this.categoryAxisRenderer.getPosition(this.series.getCategory(d, i)) + e.config.getYOffSet();
            return e.config.path.getPath(d, coOrdinate.x + e.config.getXOffSet(), y);
          });
        } else if (e.config.type === 'text') {
          e.svgElement.attr('x', coOrdinate.x + e.config.getXOffSet());
        }
      });
    }
  }
  /**
   * This is a validate function that will tell if the new position where the data node is placed
   * is a valid position or not.
   * If this function returns false, the data node will be reset to the position from where the element
   * was dragged
   */
  public validateNewPosition(data: any, coOrdinate: { x: number; y: number }): boolean {
    // If the series has validateTimeChange we will validate the new time
    if (this.series.interactive && this.series.interactive.validateTimeChange) {
      return this.series.interactive.validateTimeChange(this.dataObject, this.getMinAndMaxBasedOnCoOrdinates(coOrdinate));
    }
    // if the chart is interactive & since no function is provided we will make the validation we return true
    return true;
  }

  /**
   * Based on the co-oridinates this fucntion will return the min & max time of the task
   * param {{x: number; y: number}} coOrdinate
   * returns {{min: number; max: number}}
   */
  private getMinAndMaxBasedOnCoOrdinates(coOrdinate: { x: number; y: number }): {min: number, max: number} {
    const currentXRect: number = this.getDataNodeCoOrdinates().x;
    const newStartPoint: number = currentXRect + coOrdinate.x;
    const newEndPoint: number = currentXRect + coOrdinate.x + this.getDataNodeDimensions().width;
    return {
      min: this.linearAxisRenderer.getInvertPosition(newStartPoint).getTime(),
      max: this.linearAxisRenderer.getInvertPosition(newEndPoint).getTime()
    };
  }
  /**
   * this function will return the start co-ordinate of the data node
   * returns {{x: number; y: number}}
   */
  public getDataNodeCoOrdinates(): {x: number, y: number} {
    if (!this.dataRectangle) {
      return null;
    }
    return {
      x : parseFloat(this.dataRectangle.attr('x')),
      y : parseFloat(this.dataRectangle.attr('y'))
    };
  }
  /**
   * this function will return the dimension of the data rect
   * returns {{width: number; height: number}}
   */
  public getDataNodeDimensions(): {width: number, height: number} {
    if (!this.dataRectangle) {
      return null;
    }
    return {
      width: parseFloat(this.dataRectangle.attr('width')),
      height: parseFloat(this.dataRectangle.attr('height'))
    };
  }

  /**
   * Return the Id of the data
   */
  getId(): string {
    return '' + this.dataObject.id;
  }
  /**
   * this function will return the array of the parent on which
   * this task is dependent on
   */
  getParentDependency(): string[] {
    let dep: string[] = [];
    // chk if the series has a callback function to get the array of parent
    if (this.series.dependency.getParentDependency) {
      dep = this.series.dependency.getParentDependency(this.dataObject);
    } else if (this.dataObject.dependency && this.dataObject.dependency.length > 0) {
      dep = this.dataObject.dependency.map((o) => '' + o.id);
    }
    return dep;
  }

  /**
   * The node object from which the dep can be attached
   * returns {any}
   */
  getDependencyNode(): any {
    return this.depCircle;
  }
  /**
   * REturn the data that is associated to this node
   * returns {any}
   */
  getDataObject(): any {
    return this.dataObject;
  }

  /**
   * Get the current position
   * returns {{x: number; y: number}}
   */
  getCurrentPosition(): { x: number; y: number } {
    return {
      x: parseFloat(this.dataRectangle.attr('x')),
      y: parseFloat(this.dataRectangle.attr('y'))
    };
  }
}
