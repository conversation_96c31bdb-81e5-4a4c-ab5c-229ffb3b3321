.rules-display {
    clear: both;
    overflow: hidden;
    padding: 10px 10px;
    &:hover {
        background-color: #FDE6D2;
    }
    .rule-info-container {
        letter-spacing: normal;
        text-align: left;
        color: #6D7692;
        width: 80%;
        float: left;
        .loader-content {
            display: table-cell;
            vertical-align: middle;
            color: #FFFFFF;
            position: relative;
            font-size: 10px;
            padding-top: 25%;
            padding-left: 7px;
            width: 80px;
        }
        img {
            width: 83px;
            height: 55px;
        }
        .loaderdiv {
            width: 83px; 
            height: 55px;
            background: black;
            opacity: 0.6;
            position: absolute;
        }
        .rule-name {
            font-size: 14px;
            line-height: 18px;
            letter-spacing: normal;
            font-family: Montserrat;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            padding: 20px 5px 20px 15px;
            font-weight: 600;
        }
        .default-rule-name {
            padding: 5px 5px 5px 15px;
        }
        .rule-msg {
            font-size: 12px;
            line-height: 15px;
            letter-spacing: 0.4px;
            font-weight: 500;
        }
        .hyperlink-url-container {
            overflow: hidden;
            .url-btn-info {
                font-size: 12px;
                line-height: 15px;
                letter-spacing: 0.4px;
                font-weight: 500;
            }
            .url-link {
                color: #0078D8;
            }
            .hcl-icon-sorting {
                font-size: 13px;
                &:before {
                    transform: rotate(225deg);
                }
            }
            .mt-minus-2 {
                margin-top: -2px;
            }
        }
        .imgalignment{
            padding-left: 13px;
        }
    }
    
    .actions-container {
        padding: 15px 0px;
        color: #6d7692;
        float: left;
        width: 10%;
        display: inline-flex;
        .hcl-icon-kabab {
            font-size: 21px;
            &:before {
                cursor: pointer;
            }
        }
        .edit-delete-icon {
            height: 30px;
            width: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 8px;

        }
    }
    .mtop-3 {
        margin-top: 3px;
    }
    .mtop-8 {
        margin-top: 8px;
    }
    .mr-10 {
        margin-right: 10px;
    }
    .w-90 {
        width: 90%;
    }
    .w-10 {
        width: 10%;
    }
    .w-30 {
        width: 30%;
    }
    .mw-30 {
        max-width: 30%;
    }
    .w-70 {
        width: 70% !important;
    }   
    .ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

}
.defaultrule {
    clear: both;
    overflow: hidden;
    padding: 10px 10px;
    border-bottom: #E0E0E0;
    background-color: #d2dfe9;
    border-top: 1px solid #6d7692;
    &:hover {
        background-color: #ECECEC ;
    }
}