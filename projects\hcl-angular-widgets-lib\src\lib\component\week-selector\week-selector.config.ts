export class weekConfig {
    minDays?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7; // to select min days from weekdays. let say we want user to select at least two day. 
                      // it will serve as a validation.
    weekdays: [days?, days?, days?, days?,days?, days?, days?];
    // weekdays: FixedSizeArray<7, days>;

  }
  export interface days {
        dayId: string | number; // unique identifier Monday or 1
        localeDay: string; // to show Day string on UI screen.
        disable?: boolean; // to disable particular say from selection. let say we don't
                           // want selection on weekends then we can disable them by this flag.
        selected?: boolean; // to select particular day.
  }

//   type FixedSizeArray<N extends number, T> = N extends 0 ? never[] 
//   : {    0: T;    length: N;} & ReadonlyArray<T>;

