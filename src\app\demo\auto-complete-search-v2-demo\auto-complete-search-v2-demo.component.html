<div class="alternate-theme">
    <div class="item">
        <h5>Auto Complete Example with Panel</h5>
    </div>
    <div class="item">
        <hcl-auto-complete-search-v2 (blur)="onBlurEvnt($event)" [config]="acPanelConfig" (optClick)="onClick($event,value)"
            (iconClick)="onIconClick($event)" (optionIconClick)="onOptionClick($event)">
        </hcl-auto-complete-search-v2>
    </div>
    <div class="item">
        <h5>Auto Complete Example with Loading spinner in options and Panel</h5>
    </div>
    <div class="item">
        <hcl-auto-complete-search-v2 [config]="inlineProgressSpinner" (optClick)="onClick($event)" (iconClick)="onIconClick($event)"
        (optionIconClick)="onOptionClick($event,val)">
        </hcl-auto-complete-search-v2>
    </div>

    <div class="item">
        <h5>Auto Complete Example with Zero Suggestions</h5>
    </div>
    <div class="item">
        <hcl-auto-complete-search-v2 [config]="zeroSuggestions" (optClick)="onClick($event)" (iconClick)="onIconClick($event)"
        (optionIconClick)="onOptionClick($event,val)">
        </hcl-auto-complete-search-v2>
    </div>
</div>