import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DataGridV2SVGChartContentDemoComponent } from './data-grid-v2-svg-chart-content-demo.component';

describe('DataGridV2SVGChartContentDemoComponent', () => {
  let component: DataGridV2SVGChartContentDemoComponent;
  let fixture: ComponentFixture<DataGridV2SVGChartContentDemoComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DataGridV2SVGChartContentDemoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DataGridV2SVGChartContentDemoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});