input[type="radio"] {
  /* remove standard background appearance */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  /* create custom radiobutton appearance */
  /* display: inline-block; */
  width: 13px;
  height: 13px;
  padding: 2px;
  /* background-color only for content */
  background-clip: content-box;
  border: 1px solid rgba(0, 0, 0, 0.38);
  background-color: #fff;
  border-radius: 50%;
}

/* appearance for checked radiobutton */
input[type="radio"]:checked {
  border: 1px solid #f5821e;
  background-color: #f5821e;
}

/* optional styles, I'm using this for centering radiobuttons */
.flex {
  display: flex;
  align-items: center;
}

:host .droppable {
  display: inline-block !important;
}

:host .droppable .radio-list.horizontal .radio-list-item {
  display: inline-block !important;
}
:host .required::after {
  content: '*';
  color: red;
  padding: 2px;
}

.error-container {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 24px;
}