import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { UnsubscribeChannelsComponent } from './unsubscribe-channels';

describe('UnsubscribeChannelsComponent', () => {
  let component: UnsubscribeChannelsComponent;
  let fixture: ComponentFixture<UnsubscribeChannelsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
    declarations: [UnsubscribeChannelsComponent],
    teardown: { destroyAfterEach: false }
})
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UnsubscribeChannelsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
