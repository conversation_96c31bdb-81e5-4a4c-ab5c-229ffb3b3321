<div class="mapping-wrapper" *ngIf="loadContent">
    <ng-container *ngFor="let mapping of contentMappingConfigData; let i = index;">
        <div class="content-mapping">
            <div class="attribute-tile ellipsis" [ngClass]="{'required': mapping[0].required}">
                <span hclTooltip="{{ mapping[0].displayTitle }}">{{ mapping[0].displayTitle }}</span>
            </div>
            <div class="mapped-to-container">
                <div class="cms-attr-container">
                    <div *ngIf="mapping[1].dropdownConfig.previousMappingError" class="mapping-error">
                        <span hclTooltip="{{config.translations.previousMappingError}}"
                            data-position="left-bottom-start" class="hcl-icon-warning"></span>
                    </div>
                    <hcl-drop-down [config]="mapping[1].dropdownConfig"
                        [ngClass]="{'mt-2': mapping[1].dropdownConfig.options.length}"
                        (select)="mappedAttributeSelection($event, mapping, i)" class=" w-100 mx-1">
                        <ng-template hclTemplate hclTemplateName="mappedAttribute">
                            <div>
                                <hcl-input (keydown)="$event.stopPropagation()"
                                    (iconClick)="clearMappedAttributeSearch(mapping[1])"
                                    (valueEntered)="filterMappedAttribute($event, mapping[1])"
                                    [config]="mapping[1].searchInput"></hcl-input>
                                <div class="empty-search-result" *ngIf="mapping[1].dropdownConfig.options.length === 0">
                                    {{config.translations.noResultFound}}
                                </div>
                            </div>
                        </ng-template>
                    </hcl-drop-down>
                </div>
            </div>
        </div>
    </ng-container>
</div>