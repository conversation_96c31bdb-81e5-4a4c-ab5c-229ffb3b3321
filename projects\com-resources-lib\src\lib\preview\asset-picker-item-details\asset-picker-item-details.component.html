<div class="ap-item-details-container" [ngClass]="{'no-preview-available': apOverlayInputs.noPreview}">
    <ng-container [ngSwitch]="apItemDetailsState">
        <ng-container *ngSwitchCase="'loaded'">
            <div class="close-pop-over" (click)="closeApItemDetails()">
                <svg class="icon" width="20px" height="20px">
                    <use xlink:href="./assets/asset-picker.svg#close"></use>
                </svg>
            </div>
            <div class="ap-item-scroll-container">
                <div class="ap-item-details">
                    <ng-container *ngIf="apItemDetails.presentationDetails; else other_presentationDetails">
                        <div class="thumbnail"
                            *ngIf="imageMimeTypes?.indexOf(apItemDetails.presentationDetails.multimedia.mimeType) !== -1; else otherType">
                            <ng-container
                                *ngIf="(selectedRepoForPreview && !selectedRepoForPreview.anonymousContent); else other_selectedRepoForPreview">
                                <img [src]="apItemDetails.contentUrl" default="./assets/images/thumbnail-backup_1.png"
                                    title="" titleText="{{translations.thumbnailNotAccessible}}" />
                            </ng-container>
                            <ng-template #other_selectedRepoForPreview>
                                <img *ngIf="!apItemDetails.presentationDetails.multimedia.thumbnailUrl; else thumbnail_url"
                                    src="{{apItemDetails.presentationDetails.multimedia.resourceUrl}}"
                                    default="./assets/images/image-backup_1.png" title=""
                                    titleText="{{translations.imageNotAccessible}}" />
                                <ng-template #thumbnail_url>
                                    <img src="{{apItemDetails.presentationDetails.multimedia.thumbnailUrl}}"
                                        default="./assets/images/thumbnail-backup_1.png" title=""
                                        titleText="{{translations.thumbnailNotAccessible}}" />
                                </ng-template>
                            </ng-template>
                        </div>

                        <ng-template #otherType>
                            <div class="thumbnail">
                                <img *ngIf="apItemDetails.presentationDetails.multimedia.thumbnailUrl; else not_thumbnailUrl"
                                    src="{{apItemDetails.presentationDetails.multimedia.thumbnailUrl}}"
                                    default="./assets/images/thumbnail-backup_1.png" title=""
                                    titleText="{{translations.thumbnailNotAccessible}}" />

                                <ng-template #not_thumbnailUrl>
                                    <svg class="icon">
                                        <use
                                            [attr.xlink:href]="'./assets/asset-picker.svg#' + (documents.get(apItemDetails.presentationDetails.multimedia.mimeType) || 'unknown-type')">
                                        </use>
                                    </svg>
                                </ng-template>

                            </div>
                        </ng-template>

                        <div class="metadata">
                            <p class="fileName">
                                <a *ngIf="!(selectedRepoForPreview && !selectedRepoForPreview.anonymousContent)"
                                    download href="{{apItemDetails.url}}" target="_blank">
                                    <svg class="icon" width="30px" height="30px">
                                        <use xlink:href="./assets/asset-picker.svg#download"></use>
                                    </svg> </a>
                                <span
                                    *ngIf="apItemDetails.presentationDetails.textual.heading || apItemDetails.presentationDetails.textual.name"
                                    class=" ellipsis"
                                    hclTooltip="{{apItemDetails.presentationDetails.textual.heading || apItemDetails.presentationDetails.textual.name}}">
                                    {{apItemDetails.presentationDetails.textual.heading ||
                                    apItemDetails.presentationDetails.textual.name}} </span>
                            </p>
                            <div class="size" *ngIf="apItemDetails.presentationDetails.textual.subheadings.length">
                                <p *ngFor="let text of apItemDetails.presentationDetails.textual.subheadings">
                                    <span hclTooltip="{{text}}">{{text}}</span>
                                </p>
                            </div>
                            <div class="mime-type" *ngIf="apItemDetails.presentationDetails.textual.tags[0]">
                                <span hclTooltip="{{apItemDetails.presentationDetails.textual.tags[0].toUpperCase()}}">
                                    {{apItemDetails.presentationDetails.textual.tags[0].toUpperCase()}}
                                </span>
                            </div>
                        </div>
                        <div class="description" *ngIf="apItemDetails?.presentationDetails.textual.summary">
                            {{apItemDetails?.presentationDetails.textual.summary}}
                        </div>
                    </ng-container>
                    <ng-template #other_presentationDetails>
                        <div class="thumbnail">
                            <img [src]="apItemDetails.contentUrl" default="./assets/images/thumbnail-backup_1.png"
                                title="" titleText="" />
                        </div>
                    </ng-template>
                </div>
            </div>
        </ng-container>
        <div class="error-container" *ngSwitchCase="'no-preview'">
            <span class="ap-no-preview"
                [ngClass]="{'left-arrow':apOverlayInputs.loadAt === 'right', 'right-arrow': apOverlayInputs.loadAt === 'left'}">
                {{translations.couldNotRetrive}}
            </span>
        </div>
        <div class="error-container" *ngSwitchCase="'no-content'">
            <span class="ap-no-preview"
                [ngClass]="{'left-arrow':apOverlayInputs.loadAt === 'right', 'right-arrow': apOverlayInputs.loadAt === 'left'}">
                {{translations.infoNotAvailable}}
            </span>
        </div>
        <div class="error-container" *ngSwitchCase="'no-repo'">
            <span class="ap-no-preview"
                [ngClass]="{'left-arrow':apOverlayInputs.loadAt === 'right', 'right-arrow': apOverlayInputs.loadAt === 'left'}">
                {{translations.noSupportOperation}}
            </span>
        </div>
    </ng-container>
</div>