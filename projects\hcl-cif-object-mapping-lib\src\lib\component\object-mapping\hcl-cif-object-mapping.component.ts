import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ButtonConf, DropDownConfig, InputConfig, SideBarComponent } from 'hcl-angular-widgets-lib';
import { forkJoin, SubscriptionLike } from 'rxjs';
import { HclCifObjectMappingLibService } from '../../hcl-cif-object-mapping-lib.service';
import { AttributesMappingData, CifRepository, CifCategory, ObjectMapping, ObjectMappingConf } from '../../object-mapping-configs';
import { CifFolderBaseEntitiesConf } from './cif-folder-base-entities/cif-folder-base-entities-config';

@Component({
  selector: 'hcl-cif-object-mapping',
  templateUrl: './hcl-cif-object-mapping.component.html',
  styleUrls: ['./hcl-cif-object-mapping.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HclCifObjectMappingComponent implements OnInit, OnDestroy {

  @Input() config: ObjectMappingConf;
  @Output() mappedObject: EventEmitter<AttributesMappingData> = new EventEmitter<AttributesMappingData>();
  @Output() cancleMapping = new EventEmitter();

  @ViewChild(SideBarComponent) sideBarComponentRef: SideBarComponent;

  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  // private loginAttempts = 0;
  // private MAX_LOGIN_ATTEMPTS = 2;
  objectMappingForm: UntypedFormGroup;
  repoConfig: DropDownConfig;
  selectedRepoId: string;
  selectedRepoLabel: string;
  serviceCategoryDropdownConfig: DropDownConfig;
  applicationCategoryDropdownConfig: DropDownConfig;
  repositories: CifRepository[] = [];
  // serviceCategories: CifCategory[] = [];
  // applicationCategories: CifCategory[] = [];
  isServiceCategoryFolderEnabled: boolean;
  isAppicationCategoryFolderEnabled: boolean;
  serviceCategoryInputConf: InputConfig;
  serviceCategoryButtonConf: ButtonConf;
  applicationCategoryInputConf: InputConfig;
  applicationCategoryButtonConf: ButtonConf;
  cancelButtonConfig: ButtonConf;
  saveButtonConfig: ButtonConf;
  addCategorySelectionToDom: boolean;
  // readyTorender = false;
  noRepositories = false;
  applicationRepository: CifRepository;
  servicecategory: CifCategory;
  isCreateMode: boolean;
  isEditMode: boolean;
  isViewMode: boolean;
  serviceSchema: ObjectMapping;
  applicationSchema: ObjectMapping;
  readyTorenderMappingSec = false;
  mappedAttributesData: any;
  currentMappingId: string;
  cifRepoLoaded = false;
  appTitle: string;

  applicationObject = {
    applicationId: null,
    objectType: null,
    schema: null,
    objectId: null
  };

  serviceObject = {
    applicationId: null,
    objectType: null,
    objectId: null,
    schema: null
  };

  appSchemaObj: CifCategory = {
    id: null,
    label: ''
  };

  cifFolderBaseEntitiesConf: CifFolderBaseEntitiesConf;

  constructor(
    private hclCifObjectMappingLibService: HclCifObjectMappingLibService
  ) { }


  ngOnInit(): void {
    this.hclCifObjectMappingLibService.baseUrl = this.config.cifBaseUrl;
    this.hclCifObjectMappingLibService.headers = this.config.cifHeaders;

    this.config.assetContext = 'category';
    if (this.config.applicationMode === 'VIEW' || this.config.applicationMode === 'EDIT') {
      if (this.config.applicationMode === 'VIEW') {
        this.isViewMode = true;
        this.appTitle = this.config.translations.viewModeTitle;
      } else {
        this.isEditMode = true;
        this.appTitle = this.config.translations.editModeTitle;
        if (this.config.allMappingsList && this.config.allMappingsList.length) {
          this.hclCifObjectMappingLibService.allMappingsList = this.config.allMappingsList;
        }
      }
    } else {
      this.isCreateMode = true;
      this.appTitle = this.config.translations.createModeTitle;
      // this.applicationObject.applicationId = this.config.userApplication;
      this.applicationObject.objectType = this.config.objectType;
      if (this.config.allMappingsList && this.config.allMappingsList.length) {
        this.hclCifObjectMappingLibService.allMappingsList = this.config.allMappingsList;
      }
    }

    this.constructForm();
    this.setConfiguration();

    if (!this.isCreateMode) {
      this.applicationObject = { ...this.config.previousData.object1 };
      this.serviceObject = { ...this.config.previousData.object2 };

      this.updateForm();
    }

    this.initApplication();
  }

  constructForm() {
    this.objectMappingForm = new UntypedFormGroup({
      repo: new UntypedFormControl(),
      serviceCategory: new UntypedFormControl(),
      applicationCategory: new UntypedFormControl(),
    });
  }

  updateForm() {
    this.readyTorenderMappingSec = false;
    // TODO - update applicationCategory field in case isAppicationCategoryFolderEnabled and appSchemaEmbeded is false
    this.hclCifObjectMappingLibService.getSingleCategory(this.applicationObject.applicationId,
      this.config.previousData.context.appSchemaObj.id).subscribe((contextObj: CifCategory) => {
        this.appSchemaObj = contextObj;
        this.applicationCategoryInputConf.formControlName.setValue(this.appSchemaObj.label);
      });
    this.objectMappingForm.patchValue({ 'serviceCategory': this.serviceObject.objectType });
    this.applicationCategoryButtonConf.value = this.config.translations.replace;


    // this.applicationObject = { ...this.config.previousData.object1 };
    // this.serviceObject = { ...this.config.previousData.object2 };

    this.subscriptionList.push(forkJoin([this.hclCifObjectMappingLibService
      .getObjectMappingAttributes(this.serviceObject.applicationId, this.serviceObject.objectType), this.hclCifObjectMappingLibService
        .getObjectMappingAttributes(this.applicationObject.applicationId, this.config.schemaObjectName,
          this.config.previousData.context.appSchemaObj.id)]).subscribe((schemaData: any) => {
            this.serviceObject.schema = schemaData[0];
            this.applicationObject.schema = schemaData[1];

            this.hclCifObjectMappingLibService.applicationSchema = JSON.stringify(this.applicationObject.schema);
            this.hclCifObjectMappingLibService.serviceSchema = JSON.stringify(this.serviceObject.schema);

            const object1Schema = this.applicationObject.schema;
            const object2Schema = this.serviceObject.schema;
            const _mappingData = new Map<number, any>();
            this.config.previousData.attributeMappings.forEach((attrMapping: any, index: number) => {
              if (object2Schema.properties[attrMapping.attribute2Id]) {
                const attributeMapping = {
                  attribute1Id: attrMapping.attribute1Id,
                  attribute1Title: object1Schema.properties[attrMapping.attribute1Id].title,
                  attribute2Id: attrMapping.attribute2Id,
                  attribute2Title: object2Schema.properties[attrMapping.attribute2Id].title,
                };
                _mappingData.set(index, attributeMapping);
              }
            });

            this.hclCifObjectMappingLibService.mappingDataMap = new Map<number, any>(_mappingData);

            this.readyTorenderMappingSec = true;
          }));
  }

  setConfiguration() {
    this.repoConfig = {
      options: [],
      placeholder: this.config.translations.selectRepository,
      name: 'repoType',
      formControl: this.objectMappingForm.controls['repo'],
      disabled: this.isViewMode ? true : false
    };

    this.serviceCategoryDropdownConfig = {
      options: [],
      placeholder: this.config.translations.selectAudienceEventType,
      name: 'repoType',
      formControl: this.objectMappingForm.controls['serviceCategory'],
      disabled: this.isViewMode ? true : false
    };

    this.serviceCategoryInputConf = {
      name: 'serviceCategoryInput',
      placeholder: this.config.translations.selectAudienceEventType,
      formControlName: new UntypedFormControl(),
      autofocus: false,
      type: 'text',
      disabled: true
    };

    this.serviceCategoryButtonConf = {
      name: 'serviceCategoryButton',
      value: this.config.translations.browse,
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      borderRadius: 5,
      styleClass: 'hcl-sm-button mat-stroked-button',
    };

    this.applicationCategoryDropdownConfig = {
      options: [],
      placeholder: this.config.translations.selectAudienceEventType,
      name: 'repoType',
      formControl: this.objectMappingForm.controls['applicationCategory'],
      disabled: this.isViewMode ? true : false
    };

    this.applicationCategoryInputConf = {
      name: 'applicationCategoryInput',
      placeholder: this.config.translations.selectApplicationData,
      formControlName: new UntypedFormControl(),
      autofocus: false,
      type: 'text',
      disabled: true
    };

    this.applicationCategoryButtonConf = {
      name: 'applicationCategoryButton',
      value: this.config.translations.browse,
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      borderRadius: 5,
      styleClass: 'hcl-sm-button mat-stroked-button',
    };

    this.cancelButtonConfig = {
      name: 'cancel',
      value: this.config.translations.cancel,
      color: 'accent',
      buttonType: 'stroked',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5
    };

    this.saveButtonConfig = {
      name: 'previous',
      value: this.config.translations.saveClose,
      color: 'accent',
      buttonType: 'flat',
      type: 'button',
      styleClass: 'medium-btn',
      borderRadius: 5,
      disabled: true
    };
  }

  initApplication() {
    this.subscriptionList.push(this.hclCifObjectMappingLibService.getInstances().subscribe((repositories: CifRepository[]) => {
      // this.loginAttempts = 0;
      this.hclCifObjectMappingLibService.repositories = [...repositories];
      this.setupRepositories(repositories);
    }, (error) => this.hclCifObjectMappingLibService.handleServerError(error.message || this.config.translations.generalError)));
  }

  setupRepositories(repos: CifRepository[]) {
    if (repos && repos.length) {
      this.cifRepoLoaded = true;
      repos.some((repo, index) => {
        if (repo.identifier === this.config.userApplication || repo.identifier.startsWith(`${this.config.userApplication} `)) {
          this.applicationObject.applicationId = repo.identifier;
          this.applicationRepository = repos.splice(index, 1)[0];
          this.hclCifObjectMappingLibService.selectedAppRepository = this.applicationRepository;
          return;
        }
      });

      this.repositories = this.isCreateMode ? repos : repos.filter(repo => repo.identifier === this.serviceObject.applicationId);
      this.setAppSubcategories();
      this.repoConfig.options = this.repositories.map((item: CifRepository) => {
        return {
          label: item.displayName,
          value: item.identifier
        };
      });

      this.hclCifObjectMappingLibService.selectedCmsRepository = this.repositories[0];
      this.selectedRepoLabel = this.repositories[0].displayName;
      this.repositoryChanged(this.repositories[0].identifier, true);
    }
  }

  repositoryChanged(repository: string, startup?: boolean) {
    this.selectedRepoId = repository;
    if (!startup) {
      const selectedRepoDetails = this.repositories.find(repo => repo.identifier === repository);
      this.selectedRepoLabel = selectedRepoDetails.displayName;
      this.hclCifObjectMappingLibService.selectedCmsRepository = selectedRepoDetails;
    }
    this.objectMappingForm.patchValue({ 'repo': repository });
    this.serviceObject.applicationId = repository;
    const hasFolderNavigation = this.isFolderNavigationEnabled(repository);
    this.isServiceCategoryFolderEnabled = hasFolderNavigation ? true : false;
    this.setSubcategories(repository, 'service');
  }

  isFolderNavigationEnabled(selectedRepoId: string) {
    const selectedRepository = this.repositories.find(repo => repo.identifier === selectedRepoId);
    return selectedRepository && selectedRepository.features.listCategoryFolders;
  }

  setAppSubcategories() {
    this.isAppicationCategoryFolderEnabled = (this.applicationRepository && this.applicationRepository.features.listCategoryFolders)
      ? true : false;

    if (!this.isAppicationCategoryFolderEnabled) {
      this.setSubcategories(this.applicationRepository.identifier, 'application');
    }
  }

  setSubcategories(repo: string, context: string) {
    this.subscriptionList.push(this.hclCifObjectMappingLibService.getCategories(repo)
      .subscribe((categoriesData: CifCategory[]) => {
        if (categoriesData && categoriesData.length) {
          let categories;
          let dropDownConfig;
          let categoryToSelect;
          if (context === 'service') {
            // categories = this.serviceCategories;
            dropDownConfig = this.serviceCategoryDropdownConfig;
            if (this.hclCifObjectMappingLibService.allMappingsList) {
              this.addDisabledFlagToServiceSubcategories(categoriesData);
            }
            // let categoryToSelect = !this.isCreateMode ? this.serviceObject.objectType : this.getFirstActiveSubcategory(categoriesData);

            if (this.isCreateMode) {
              const firstActiveCategory = this.getFirstActiveSubcategory(categoriesData);
              if (firstActiveCategory) {
                categoryToSelect = firstActiveCategory.id;
              }
            } else {
              categoryToSelect = this.serviceObject.objectType;
            }
          } else {
            // categories = this.applicationCategories;
            dropDownConfig = this.applicationCategoryDropdownConfig;
            // TODO - rething for appSchemaEmbeded
            categoryToSelect = !this.isCreateMode ? this.applicationObject.objectType : categoriesData[0].id;
          }

          categories = categoriesData;

          dropDownConfig.options = categories.map((item: CifCategory) => {
            return {
              label: item.label,
              value: item.id,
              disabled: item.disabled
            };
          });

          if (!categoryToSelect) {
            dropDownConfig.options.push({ label: this.config.translations.noOptionAvailable, value: null });
          } else {
            for (let i = 0; i < dropDownConfig.options.length; i++) {
              if (dropDownConfig.options[i].label === this.config.translations.noOptionAvailable) {
                dropDownConfig.options.splice(i, 1);
                break;
              }
            }
            this.categoryChanged(categoryToSelect, context);
          }
        }
      }, (error) => this.hclCifObjectMappingLibService.handleServerError(error.message || this.config.translations.generalError)));

  }

  addDisabledFlagToServiceSubcategories(categories: CifCategory[]) {
    categories.forEach(category => {
      if (this.hclCifObjectMappingLibService.allMappingsList?.length) {
        category.disabled = this.hclCifObjectMappingLibService.allMappingsList
          .some(mappingItem => mappingItem.object2.objectType === category.id);
      }
    });
  }

  getFirstActiveSubcategory(categories: CifCategory[]) {
    return categories.find(category => !category.disabled);
  }

  categoryChanged(categoryId: string, context: string) {
    if (!this.isViewMode) {
      this.readyTorenderMappingSec = false;
      if (context === 'service') {
        this.objectMappingForm.patchValue({ 'serviceCategory': categoryId });
        this.serviceObject.objectType = categoryId;

        this.subscriptionList.push(this.hclCifObjectMappingLibService
          .getObjectMappingAttributes(this.selectedRepoId, categoryId).subscribe(schemaData => {
            this.hclCifObjectMappingLibService.serviceSchema = JSON.stringify(schemaData);
            this.serviceObject.schema = JSON.stringify(schemaData);
            if (this.hclCifObjectMappingLibService.serviceSchema && this.hclCifObjectMappingLibService.applicationSchema) {
              this.readyTorenderMappingSec = true;
            }
          }, (error) => this.hclCifObjectMappingLibService.handleServerError(error.message || this.config.translations.generalError)));

      } else {
        // TODO - rething for appSchemaEmbeded
        this.objectMappingForm.patchValue({ 'applicationCategory': categoryId });
        this.applicationObject.objectType = categoryId;
        this.subscriptionList.push(this.hclCifObjectMappingLibService
          .getObjectMappingAttributes(this.applicationRepository.identifier, this.config.schemaObjectName, categoryId)
          .subscribe(schemaData => {
            this.hclCifObjectMappingLibService.applicationSchema = JSON.stringify(schemaData);
            this.applicationObject.schema = JSON.stringify(schemaData);
            if (this.hclCifObjectMappingLibService.serviceSchema && this.hclCifObjectMappingLibService.applicationSchema) {
              this.readyTorenderMappingSec = true;
            }
          }, (error) => this.hclCifObjectMappingLibService.handleServerError(error.message || this.config.translations.generalError)));
      }
    } else {
      this.objectMappingForm.patchValue({ 'serviceCategory': this.serviceObject.objectType });
    }
  }

  openCategorySelection(context: string) {
    this.readyTorenderMappingSec = false;
    if (context === 'service') {
      this.hclCifObjectMappingLibService.serviceCategorySelectionActive = true;
    } else {
      this.hclCifObjectMappingLibService.applicationCategorySelectionActive = true;
    }

    if (this.hclCifObjectMappingLibService.serviceCategorySelectionActive) {
      this.cifFolderBaseEntitiesConf = {
        translations: this.config.translations,
        applicationService: this.hclCifObjectMappingLibService,
        cifRepository: this.hclCifObjectMappingLibService.selectedCmsRepository.identifier,
        previousSelectedCategoryId: this.serviceObject.objectType || (this.config.previousData && this.config.previousData.context
          && this.config.previousData.context.appSchemaObj && this.config.previousData.context.appSchemaObj.id) || null,
        rootFolder: { id: 1, displayName: this.config.translations.allCategories }
      };
      this.addCategorySelectionToDom = true;
      setTimeout(() => {
        this.sideBarComponentRef.openSideBar();
      }, 600);

    } else {
      this.cifFolderBaseEntitiesConf = {
        translations: this.config.translations,
        applicationService: this.hclCifObjectMappingLibService,
        cifRepository: this.hclCifObjectMappingLibService.selectedAppRepository.identifier,
        previousSelectedCategoryId: this.appSchemaObj.id || (this.config.previousData && this.config.previousData.context
          && this.config.previousData.context.appSchemaObj && this.config.previousData.context.appSchemaObj.id) || null,
        rootFolder: { id: 1, displayName: this.config.translations.allCategories }
      };
      this.addCategorySelectionToDom = true;
      setTimeout(() => {
        this.sideBarComponentRef.openSideBar();
      }, 600);
    }
  }

  cancelMapping() {
    this.cancleMapping.emit();
  }

  updateCifFbs(categoryData: any) {
    if (this.hclCifObjectMappingLibService.serviceCategorySelectionActive) {
      if (this.serviceObject.objectType !== categoryData.data.id) {
        this.hclCifObjectMappingLibService.mappingDataMap = new Map<number, any>();
      }
      this.servicecategory = categoryData;
      this.objectMappingForm.patchValue({ 'serviceCategory': categoryData.data.id });
      this.serviceObject.objectType = categoryData.data.id;
      this.serviceCategoryInputConf.formControlName.setValue(categoryData.data.label);
      this.serviceCategoryButtonConf.value = this.config.translations.replace;
      this.subscriptionList.push(this.hclCifObjectMappingLibService
        .getObjectMappingAttributes(this.selectedRepoId, this.objectMappingForm.value.serviceCategory)
        .subscribe(schema => {
          this.hclCifObjectMappingLibService.serviceSchema = JSON.stringify(schema);
          this.serviceObject.schema = JSON.stringify(schema);
          if (this.hclCifObjectMappingLibService.applicationSchema) {
            this.readyTorenderMappingSec = true;
          }
        }, (error) => this.hclCifObjectMappingLibService.handleServerError(error.message || this.config.translations.generalError)));
    } else {
      // TODO - rething for appSchemaEmbeded
      if (this.appSchemaObj.id !== categoryData.data.id) {
        this.hclCifObjectMappingLibService.mappingDataMap = new Map<number, any>();
      }
      this.appSchemaObj.id = categoryData.data.id;
      this.applicationCategoryInputConf.formControlName.setValue(categoryData.data.label);
      this.applicationCategoryButtonConf.value = this.config.translations.replace;
      this.subscriptionList.push(this.hclCifObjectMappingLibService
        .getObjectMappingAttributes(this.applicationRepository.identifier, this.config.schemaObjectName, categoryData.data.id)
        .subscribe(schema => {
          this.hclCifObjectMappingLibService.applicationSchema = JSON.stringify(schema);
          this.applicationObject.schema = JSON.stringify(schema);
          if (this.hclCifObjectMappingLibService.serviceSchema) {
            this.readyTorenderMappingSec = true;
          }
        }, (error) => this.hclCifObjectMappingLibService.handleServerError(error.message || this.config.translations.generalError)));
    }
    this.closeCifFbsSidebar();
  }


  closeCifFbsSidebar() {
    this.addCategorySelectionToDom = false;
    this.sideBarComponentRef.close('close');
    this.hclCifObjectMappingLibService.serviceCategorySelectionActive = false;
    this.hclCifObjectMappingLibService.applicationCategorySelectionActive = false;
  }
  disableForm() {
    this.noRepositories = true;
  }

  updateMappedData(data: any) {
    if (data && data.contentMapping) {
      this.mappedAttributesData = data.contentMapping;
      this.hclCifObjectMappingLibService.mappingDataMap = new Map<number, any>(data.contentMapping);
      this.saveButtonConfig.disabled = false;
    } else {
      this.mappedAttributesData = '';
      this.hclCifObjectMappingLibService.mappingDataMap = new Map<number, any>();
      this.saveButtonConfig.disabled = true;
    }
  }

  saveObjectMapping() {
    const attributeMappings = [];
    let requestData;
    if (this.mappedAttributesData) {
      if (this.hclCifObjectMappingLibService.invalidPreviousMappingMap.size) {
        this.mappedAttributesData = this.mappedAttributesData.filter(mapping => {
          if (!this.hclCifObjectMappingLibService.invalidPreviousMappingMap.get(mapping.attribute1Id)) {
            return mapping;
          }
        });
      }

      this.mappedAttributesData.forEach((value, key) => {
        const mappingValue = { ...value };
        const attributeId = value.attribute1Id;
        const id = +attributeId.substring(attributeId.lastIndexOf('.') + 1, attributeId.length);
        if (mappingValue && mappingValue['attribute1Title']) {
          delete mappingValue.attribute1Title;
        }
        if (mappingValue && mappingValue['attribute2Title']) {
          delete mappingValue.attribute2Title;
        }
        attributeMappings.push(mappingValue);

      });

      const cifCategoryLabel = this.serviceCategoryDropdownConfig.options.find((conf: any) =>
        conf.value === this.serviceObject.objectType).label;

      if (attributeMappings.length) {
        const object1 = typeof this.applicationObject.schema === 'object' ? {
          ...this.applicationObject, schema: this.hclCifObjectMappingLibService.applicationSchema
        } : this.applicationObject;

        if (this.isCreateMode && this.config.allMappingsList && this.config.allMappingsList.length && this.config.allMappingsList[0].object1.objectId) {
          object1.objectId = this.config.allMappingsList[0]?.object1.objectId;
        }

        const object2 = typeof this.serviceObject.schema === 'object' ? {
          ...this.serviceObject, schema: this.hclCifObjectMappingLibService.serviceSchema
        } : this.serviceObject;
        requestData = {
          attributeMappings,
          object1,
          object2,
          autoSynchronizationSupport: 'INBOUND',
          context: { appSchemaObj: this.appSchemaObj, cifCategoryLabel }
        };
      }

      if (!requestData.object1.objectId) {
        requestData.object1.objectId = this.getTemporaryID();
      }

      if (this.isEditMode) {
        this.subscriptionList.push(this.hclCifObjectMappingLibService.updateObjectMapping(this.config.previousData.mappingId,
          requestData).subscribe((data: AttributesMappingData) => {
            this.mappedObject.emit(data);
          }, (error) => this.hclCifObjectMappingLibService.handleServerError(error.message || this.config.translations.generalError)));
      } else {
        this.subscriptionList.push(this.hclCifObjectMappingLibService.createObjectMapping(requestData)
          .subscribe((data: AttributesMappingData) => {
            this.mappedObject.emit(data);
          }, (error) => this.hclCifObjectMappingLibService.handleServerError(error.message || this.config.translations.generalError)));
      }
    }
  }

  getTemporaryID() {
    return 'tempId-' + new Date().getTime();
  }



  // 401 and retry

  /**
 * In case error from server this function will be called
 * param error
 */
  // private handleServerError(callback, error) {
  // if (error.status === 401 || error.status === 403) {
  //   if (this.loginAttempts < this.MAX_LOGIN_ATTEMPTS) {
  //     // we have to a login again
  //     this.reLogin(callback);
  //   } else {
  //     this.notificationService.show({
  //       message: this.config.translations.unableToFetchData,
  //       type: 'error', close: true, autoHide: 6000
  //     });
  //     this.disableForm();
  //   }
  // } else {
  //   this.notificationService.show({
  //     message: error.message || this.config.translations.generalError,
  //     type: 'error', close: true, autoHide: 6000
  //   });
  //   // }
  // }

  /**
* In case there is a un auth error we can do a  relogin to get the new token
*/
  // private reLogin(callbackFunction: any): void {
  //   this.loginAttempts++;
  //   // // check if we have a relogin method
  //   // if (this.config.reLogin) {
  //   //   this.config.reLogin(this.reLoginSuccess.bind(this, callbackFunction));
  //   // } else {
  //   this.disableForm();
  //   this.notificationService.show({
  //     message: this.config.translations.unableToFetchData,
  //     type: 'error', close: true, autoHide: 6000
  //   });
  //   // }
  // }

  /**
   * called when the relogin is successful from the caller
   */
  // public reLoginSuccess(callback): void {
  //   if (callback) {
  //     callback();
  //   }
  // }

  ngOnDestroy(): void {
    this.hclCifObjectMappingLibService.cleanUpData();
    this.subscriptionList.forEach(sub => { sub.unsubscribe(); });
  }

}
