export interface D3DraggableNode {
  /**
   * Function that will tell if the elment can be dragged or not
   * param data
   * returns {boolean}
   */
  canBeDragged(data: any): boolean;
  /**
   * This is a validate function that will tell if the new position where the data node is placed
   * is a valid position or not.
   * If this function returns false, the data node will be reset to the position from where the element
   * was dragged
   */
  validateNewPosition(data: any, coOrdinate: {x: number, y: number}): boolean;
  /**
   * this function is called when the data Node position is changed
   */
  dataNodePositionUpdate(data: any, coOrdinate: {x: number, y: number}): void;
  /**
   * Get the current position
   * returns {{x: number; y: number}}
   */
  getCurrentPosition(): {x: number, y: number};
}
