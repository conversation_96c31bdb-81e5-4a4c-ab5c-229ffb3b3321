<div class="html-snippet" style="display: field; overflow: auto; z-index: 0; position: relative;">
  <p *ngIf="snippetRuleAdded" class="rule-count-container" (mouseover)="ruleCountMouseHovered()">
    <span class="rule-count">{{field?.options?.rules?.length}}</span>
    <hcl-button class="dynamic-rule-button" [config]="ruleNameButton" [matMenuTriggerFor]="menu">
    </hcl-button>

    <hcl-button class="dynamic-rule-button" *ngIf="false" [config]="buttonConf" (onclick)="dynamicContentClick()">
    </hcl-button>

    <mat-menu #menu="matMenu" class="rulesdropdown">
      <button mat-menu-item (click)="onDefaultRuleClick()">
        <span> {{ 'RULE_BUILDER.DEFAULT_RULE' | translate}} </span>
      </button>
      <button mat-menu-item *ngFor="let rule of field.options?.rules" (click)="onRuleClick(rule)">
        <span> {{ rule?.name }} </span>
      </button>
    </mat-menu>
  </p>
  <froala-editor (froalaKeyUp)="keyUpCurrentField($event)" (froalaKeyDown)="keyDownCurrentField($event)"
    (froalaFocus)="focusCurrentField($event)" [pfList]="ngb.personalizedTagsFull.data"
    (showLinkSidebar)="showHyperlinkTemp($event)" (saveLinkInfo)="updateHyperLinkInfo($event)"
    (froalaInitialized)="setContent($event)" (froalaBlur)="contentChanged($event)"
    style="display: flex; color:{{field?.options?.color}}; padding:{{this.createPadding(field?.options?.padding)}}; vertical-align:middle; line-height:{{createLineHeight(field?.options?.lineHeight)}}; font-family:{{field?.options?.font?.family}}, {{field?.options?.font?.fallback}}; font-size:{{field?.options?.font?.size}}px; font-style:{{field?.options?.font?.style}}; font-weight:{{field?.options?.font?.weight }}; z-index: 0; position:relative;"></froala-editor>
  <!-- <ng-container *ngIf="innerHtml">
    <div [innerHTML]="innerHtml"></div> 
  </ng-container> -->

  <ng-container *ngIf="(froalaEditor?.editorInstance?.html?.get().trim().length <= 0 || froalaContents.length <= 0)">
    <div class="init-msg"> {{'messages.html-snippet-area' | translate}}</div>
  </ng-container>
</div>
<mat-error *ngIf="field.errors && field.errors.length > 0">
  <div class="error-container">
    <span class="hcl-icon-error" [matTooltip]="fetchComponentErrorMessages()"
      [matTooltipClass]="'multiline-tooltip'"></span>
  </div>
</mat-error>