import { contentConnectorSourceType } from '../../../interfaces';
import { IContentConnectorFieldOptions, ContentConnectorLayoutField, RenderingClass } from '../interfaces';
import { createPadding, createLineHeight, ignoreHTMLMinParse, createBorder, uniqueId } from '../utils';

export class ContentConnectorField implements ContentConnectorLayoutField, RenderingClass {
  constructor(public options: IContentConnectorFieldOptions) { }

  render() {
    const {  contentSourceType, contentSourceInfo, retrieveContentWhen, retrieveContentForViewAsWebpage } = this.options;

    const anchorTemplate = `
    <mj-text>
      <div style="font-size:initial !important;line-height:normal;color:initial;font-weight:initial;" class="hide-on-${this.options.hideOn}">
        <div class="ip-contentConnector-field droppable contentConnector-droppable hide-on-${this.options.hideOn}"
        target="${contentSourceInfo?.url}">
          <div style="width: 100%">
            <a href="${contentSourceInfo?.url}" data-isCms="${contentSourceType === contentConnectorSourceType.CMS_SYSTEM}" target="${contentSourceInfo?.url}" data-retrieveContentWhen="${retrieveContentWhen}" data-retrieveContentForViewAsWebpage="${retrieveContentForViewAsWebpage}"  ${contentSourceType === contentConnectorSourceType.CMS_SYSTEM ? `data-environment="${contentSourceInfo?.environment}" data-cmsName="${contentSourceInfo?.cmsName}" data-contentId="${contentSourceInfo?.contentId}"` : ''}> </a>
          </div>
        </div>
      </div>
    </mj-text>`;

    if (this.options.hideOn === 'desktop') {
      return `
        <mj-raw>
        <!--[if !mso]><!-- --></mj-raw>
        ${anchorTemplate}
        <mj-raw><!--<![endif]--></mj-raw>
      `;
    } else {
      return anchorTemplate;
    }
  }
}
