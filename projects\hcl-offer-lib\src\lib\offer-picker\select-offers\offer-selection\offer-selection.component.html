<div class="outer-manage-selection-container">
    <div class="manage-selection-container">
        <div (click)="loadOffersListing($event)" class="toggle-selection">
            <h3 [ngClass]="{'reset-transform-rotate': verticalLocale()}">{{'OFFER_PICKER.TITLES.SELECT_OFFERS' |
                translate}}</h3>
        </div>
        <div class="manage-selection-listing">
            <div class="manage-selection-label">
                <div>{{'OFFER_PICKER.TITLES.MANAGE_SELECTION' |
                    translate}}{{ selectedOffersAndOlsNo() }}</div>
            </div>
            <div class="manage-selection-list-container">
                <hcl-tabs [config]="tabHorizontalConfig" orientation="horizontal"
                    (selectedTabChange)="tabChange($event)">
                    <ng-template hclTemplate hclTemplateName="offers" type="tab-content">
                        <div class="manage-selection-list">
                            <hcl-data-grid-v2 [config]="customizedGridConfig" (rowSelected)="rowSelected($event)"
                                (rowUnSelected)="rowUnSelected($event)" (gridReady)="manageSelectionGridReady($event)">
                                <ng-template hclTemplate hclTemplateName="displayName" type="cell-renderer" let-cell>
                                    <div *ngIf="cell && cell.row && !variantMap.has(+cell.row.offerId)" class="ellipsis"
                                        (click)="onCellClicked(cell)">
                                        <span *ngIf="cell.row.state" class="bullet-round mr-2"
                                            [ngClass]="{'green-bullet': cell.row.state ==='PUBLISHED',
                                        'grey-bullet': cell.row.state ==='RETIRED','orange-bullet': cell.row.state ==='DRAFT'}"></span>
                                        <span class="link">{{cell.row && cell.row[cell.col.field]}}</span>
                                    </div>
                                    <div *ngIf="cell && cell.row && variantMap.has(+cell.row.offerId)" class="ellipsis">
                                        <span *ngIf="cell.row.state" class="bullet-round mr-2"
                                            [ngClass]="{'green-bullet': cell.row.state ==='PUBLISHED',
                                        'grey-bullet': cell.row.state ==='RETIRED','orange-bullet': cell.row.state ==='DRAFT'}"></span>
                                        <span *ngIf="cell.row.offerId"
                                            class="top-label">{{variantMap.get(+cell.row.offerId).variantDisplayName}}</span>
                                        <span *ngIf="cell.row.offerId" (click)="onCellClicked(cell)"
                                            class="bottom-label link">{{variantMap.get(+cell.row.offerId).offerDisplayName}}</span>
                                    </div>
                                </ng-template>
                            </hcl-data-grid-v2>
                        </div>
                    </ng-template>
                    <ng-template hclTemplate hclTemplateName="offerLists" type="tab-content">
                        <div *ngIf="tabIndex === 1" class="manage-selection-list">
                            <hcl-data-grid-v2 [config]="olGridConfig" (rowSelected)="olRowSelected($event)"
                                (rowUnSelected)="olRowUnSelected($event)"
                                (gridReady)="olManageSelectionGridReady($event)">
                                <ng-template hclTemplate hclTemplateName="displayName" type="cell-renderer" let-cell>
                                    <div *ngIf="cell && cell.row" class="ellipsis" (click)="onOlCellClicked(cell)">
                                        <span *ngIf="cell.row.state" class="bullet-round mr-2"
                                            [ngClass]="{'green-bullet': cell.row.state ==='PUBLISHED',
                                        'grey-bullet': cell.row.state ==='RETIRED','orange-bullet': cell.row.state ==='DRAFT'}"></span>
                                        <span class="link">{{cell.row && cell.row[cell.col.field]}}</span>
                                    </div>
                                </ng-template>
                            </hcl-data-grid-v2>
                        </div>
                    </ng-template>
                </hcl-tabs>
            </div>

            <div class="offer-actions">
                <hcl-button [config]="cancelActionsConf" (onclick)="loadOffersListing($event)" class="action-button">
                </hcl-button>
                <hcl-button [config]="selectVariantButtonConf" (onclick)="updateOfferSelection($event)"
                    class="action-button"></hcl-button>
            </div>
        </div>
    </div>
</div>