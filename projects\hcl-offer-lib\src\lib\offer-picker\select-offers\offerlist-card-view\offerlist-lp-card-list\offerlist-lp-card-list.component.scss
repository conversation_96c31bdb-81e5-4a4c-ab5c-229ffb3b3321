.offerlist-lp-card-list-container {
  height: 100%;
  .cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper {
    padding-bottom: 10px;
  }
  .ol-lp-result-item {
    display: inline-block;
    background-color: #f5f5f5;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.25);
    cursor: default;
    height: 170px;
    width: 266px;
    position: relative;
    margin: 5px;

    &.selected-item {
      background-color: #fde6d2;
      outline: 2px solid #f5821e;
    }

    &:hover {
      outline: 2px solid #f5821e;
      box-shadow: 6px 6px 10px 0 rgb(0 0 0 / 30%);
      .actions-btn-selector,
      .overlay-container {
        display: flex;
      }
    }
    &.result-item-border {
      outline: 2px solid #f5821e;
    }
    .disable-item-msg {
      display: none;
    }
    .display-block {
      display: flex !important;
    }

    .overlay-container {
      position: absolute;
      height: 35px;
      width: 100%;
      top: 0;
      left: 0;
      background-color: #15161c;
      opacity: 0.8;
      z-index: 1;
      display: none;
    }

    .actions-btn-selector {
      height: 35px;
      position: absolute;
      width: 100%;
      z-index: 1;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      display: none;
      cursor: default;

      .select-offer {
        margin-top: 8px;
        .mat-checkbox {
          .mat-checkbox-frame {
            border: 2px solid #959595 !important;
          }
        }
      }

      .menu-wrapper {
        position: relative;
        .hcl-icon-edit {
          color: #959595;
          cursor: pointer;

          &.disabled-edit {
            pointer-events: none;
            opacity: 0.5;
          }
        }
      }
    }

    &.selected {
      outline: 2px solid #f5821e;
      background-color: #fde6d2;
    }

    .metadata {
      padding: 7px 15px;
      color: #6d7692;
      font-size: 12px;
      letter-spacing: 0.4px;
      line-height: 14px;
      width: 100%;
      height: 104px;
      cursor: pointer;

      .ol-name {
        color: #6d7692;
        font-family: Montserrat;
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 0;
        line-height: 19px;
        margin-bottom: 15px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .offer-count {
        color: #444444;
        font-family: Roboto;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 16px;
        margin-bottom: 18px;
        height: 16px;
      }

      .status-type-approval {
        // display: flex;
        // align-items: center;
        // justify-content: space-between;
        margin-bottom: 5px;

        .status {
          display: inline-block;
          border-radius: 8px;
          padding: 2px 10px;
          color: #f5f5f5;
          font-family: Roboto;
          font-size: 11px;
          letter-spacing: 0.37px;
          line-height: 13px;
          text-align: center;
          max-width: 90px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;

          &.retired {
            background-color: #bcbbbb;
          }

          &.published {
            background-color: #c4d056;
          }

          &.draft {
            background-color: #ffb755;
          }
        }
        .ol-type {
          display: inline-block;
          max-width: 90px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          margin: 0 0 0 5px;
          color: #444444;
          font-family: Roboto;
          font-size: 14px;
          letter-spacing: 0;
          line-height: 16px;
        }
        .approval-section {
          display: flex;
          max-width: 90px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;

          i.hcl-icon-step_linkapproval {
            font-size: 15px;
            color: #0078d8;
            &:hover {
              color: #f5821e;
            }
          }
        }
      }
    }

    .devider {
      margin: 0 15px;
      height: 1px;
      background-color: #dfdfdf;
    }

    .offers-section {
      padding: 12px 15px;
      height: 65px;
      position: relative;

      .offer-image-container {
        display: flex;
        position: absolute;
        top: 12px;
        left: 15px;

        .no-offers {
          height: 40px;
          color: #6d7692;
          font-family: Roboto;
          font-size: 12px;
          letter-spacing: 0.4px;
          line-height: 40px;
        }

        .offer-image {
          width: 54px;
          height: 40px;
          margin-right: 5px;
          overflow: hidden;
          position: relative;

          .img-wrapper {
            cursor: pointer;
            img {
              max-width: 100%;
              display: block;
              margin: 0 auto;
              position: absolute;
              top: 47%;
              left: 50%;
              transform: translate(-50%, -50%);
            }
          }
        }

        .more-btn {
          height: 40px;
          width: 55px;
          display: inline-block;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          color: #0078d8;
          font-family: Roboto;
          font-size: 12px;
          letter-spacing: 0.5px;
          line-height: 40px;
          cursor: pointer;
        }
      }
    }

    &.skeleton-item {
      background-color: #c0c4d1;
    }
    &.disable-item {
      border: none;
      opacity: 0.3;
      cursor: default;

      .disable-item-msg {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        height: 100%;
        width: 100%;
        justify-content: center;
        align-items: center;
      }

      .thumbnail,
      .metadata {
        pointer-events: none;
      }
      &:hover {
        outline: none;
        box-shadow: none;
      }
    }
  }
}

@media only screen and (-webkit-min-device-pixel-ratio: 2),
  only screen and (min--moz-device-pixel-ratio: 2),
  only screen and (-o-min-device-pixel-ratio: 2/1),
  only screen and (min-device-pixel-ratio: 2),
  only screen and (min-resolution: 192dpi),
  only screen and (min-resolution: 2dppx) {
  /* Retina-specific stuff here */
  .result-area {
    .cdk-virtual-scroll-viewport {
      .cdk-virtual-scroll-content-wrapper {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
}
