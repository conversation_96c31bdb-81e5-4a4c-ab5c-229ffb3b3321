import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { contentConnectorFieldComponent } from './contentConnector-field.component';

describe('contentConnectorFieldComponent', () => {
  let component: contentConnectorFieldComponent;
  let fixture: ComponentFixture<contentConnectorFieldComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
    declarations: [contentConnectorFieldComponent],
    teardown: { destroyAfterEach: false }
})
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(contentConnectorFieldComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
