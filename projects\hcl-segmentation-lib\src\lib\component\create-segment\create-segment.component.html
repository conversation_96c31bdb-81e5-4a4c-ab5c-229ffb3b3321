<div class="create-segment">
  <div *ngIf="!readyToRender" class="loader-sec">
    <hcl-progress-spinner [config]="segmentInlineSpinner">
    </hcl-progress-spinner>
  </div>
  <ng-container *ngIf="readyToRender">
    <div class="create-segment__title-n-meta">
      <div class="page-title">{{ config.translations.pageTitle }}</div>
      <div class="audience-meta">
        <div class="audience-level">
          <div
            class="meta-label"
            hclTooltip="{{ config.translations.audienceLevel }}"
          >
            {{ config.translations.audienceLevel }}:
          </div>
          <div class="meta-value" hclTooltip="{{ config.audienceName }}">
            {{ config.audienceName }}
          </div>
        </div>
        <div class="audience-table">
          <div
            class="meta-label"
            hclTooltip="{{ config.translations.sourceTable }}"
          >
            {{ config.translations.sourceTable }}:
          </div>
          <div class="meta-value" hclTooltip="{{ config.audienceTableName }}">
            {{ config.audienceTableName }}
          </div>
        </div>
      </div>
    </div>
    <div class="create-segment__content">
      <div class="rule-builder">
        <div class="d-flex justify-content-between">
          <div class="rb-title">{{ config.translations.ruleBuilder }}</div>
          <div>
            <hcl-button
              [config]="testRunButtonConfig"
              (onclick)="testRunSegment()"
            ></hcl-button>
          </div>
        </div>
        <div class="rule-builder-container">
          <hcl-rule-builder
            (ruleBasedState)="setStepState($event, 'ruleBasedState')"
            [config]="config.queryBuilderConfig"
            [applicationMode]="config.applicationMode"
            [audienceInfo]="audienceInfo"
            [dpConfig]="config.dataProfileConfig"
          >
          </hcl-rule-builder>
        </div>
      </div>
      <div class="segment-meta-section">
        <div class="donut-chart">
          <div class="chart-title">
            {{ config.translations.qualifiedRecords }}
          </div>
          <hcl-seg-donut-chart
            *ngIf="renderDonutChart"
            [config]="config.donutChartConfig"
            [segmentType]="'RuleBased'"
            [segmentStats]="segmentStats"
          ></hcl-seg-donut-chart>
        </div>
        <div class="segment-metadata">
          <hcl-segment-metadata
            (segmentMetadataState)="setStepState($event, 'metadataState')"
            [applicationMode]="config.applicationMode"
            [config]="config.segmentMetadatConfig"
            [folderConfig]="config.folderSelectionConf"
            (segFolderSelected)="segFolderSelected($event)"
          ></hcl-segment-metadata>
        </div>
      </div>
    </div>
    <div class="create-segment__actions">
      <hcl-button
        [config]="saveAndPublishButtonConfig"
        (onclick)="saveAndPublishButtonClicked()"
      >
      </hcl-button>
      <hcl-button
        [config]="saveButtonConfig"
        (onclick)="saveSegment()"
      ></hcl-button>
      <hcl-button
        [config]="cancelButtonConfig"
        (onclick)="cancelButtonClicked()"
      ></hcl-button>
    </div>
  </ng-container>
  <!-- <ng-container *ngIf="!readyToRender"> App loading </ng-container> -->
</div>

<ng-template #cancelContentDialogTemplate>
  <p>{{ config.translations.confirm }}</p>
  <p>{{ config.translations.confirmUnsavedChanges }}</p>
</ng-template>

<ng-template #cancelDialogActionsTemplate>
  <hcl-button
    [config]="yesButtonConfig"
    (onclick)="modalService.closeDialog(); cancel()"
  ></hcl-button>
  <hcl-button
    class="ml-2"
    [config]="noButtonConfig"
    (onclick)="modalService.closeDialog()"
  >
  </hcl-button>
</ng-template>
