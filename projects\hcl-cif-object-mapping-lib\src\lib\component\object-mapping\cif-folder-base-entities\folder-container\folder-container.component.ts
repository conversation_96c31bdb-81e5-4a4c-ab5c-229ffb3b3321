import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FolderConfForAssetPicker, FolderService, FolderV2Component } from 'hcl-angular-widgets-lib';
import { Observable } from 'rxjs';

@Component({
  selector: 'hcl-folder-container',
  templateUrl: './folder-container.component.html',
  styleUrls: ['./folder-container.component.scss']
})
export class FolderContainerComponent implements OnInit {

  @ViewChild('folderComponent', { static: false }) folderComponent: FolderV2Component;
  @Output() folderState: EventEmitter<string> = new EventEmitter<string>();
  @Output() selectedFolderData: EventEmitter<any> = new EventEmitter<any>();
  @Input() config: any;

  folderPanelState = 'DEFAULT';
  isFolderNavigation: boolean;
  foldersConfig: FolderConfForAssetPicker;
  folderPathForAssetContents: any[];
  private folderService: FolderService;
  private rootFolder: any;
  currentFolderForContent: any;

  ngOnInit(): void {
    this.rootFolder = this.config.rootFolder;
    this.foldersConfig = this.getDefaultFolderConfigObject();
  }


  /**
   * set default folder configuration
   */
  getDefaultFolderConfigObject(): FolderConfForAssetPicker {
    const folderObject: FolderConfForAssetPicker = {
      folderPanelState: 'DEFAULT',
      folderPanelHeader: this.config.translations.folders.panelHeader,
      // topActionsTemplate: this.folderTopActionsTemplate,
      getFolders: this.loadSubFolders.bind(this),
      folderErrorCallback: this.retryGetFolders.bind(this),
      folderSuccessCallback: this.setLoginAttemptsForGetFolderListing.bind(this),
      hasChildren: this.folderHasChildren.bind(this),
      defaultSorting: {
        sortByLabel: this.config.translations.folders.sortBy,
        ascendingLabel: this.config.translations.folders.ascendingLabel,
        descendingLabel: this.config.translations.folders.descendingLabel,
        elements: [
          { label: this.config.translations.folders.sortByName, sortBy: 'displayName', selection: 'ASC' },
          { label: this.config.translations.folders.createdBy, sortBy: 'createdTimeStamp' }
        ]
      },
      properties: {
        folderCountTemplate: true
      },
      objectConfig: {
        dataKeys: {
          folderName: 'displayName',
          uniqueId: 'id'
        },
      },
      inlineLoader: true,
      rootFolderAsRootFolderParent: this.config.rootFolderAsRootFolderParent
    };
    return folderObject;
  }


  /**
   * load folders for a particular folder
   * @param folder
   */
  loadSubFolders(folder: any): Observable<any[]> {
    if (folder.id === this.rootFolder.id) {
      return this.config.applicationService.getFolders(this.config.cifRepository, this.rootFolder, false, this.config.assetContext);
    } else {
      return this.config.applicationService.getFolders(this.config.cifRepository, folder, true, this.config.assetContext);
    }
  }

  retryGetFolders(callback, error) {

  }

  setLoginAttemptsForGetFolderListing() {

  }

  /**
   * if folder has children, always treu in case of asset picker because asset picker api does not return us child folder count
   * @param folder
   */
  folderHasChildren(folder: any) {
    return true;
  }
  /**
   * get folder properties object
   */
  getFolderPropertiesObject() {
    return {
      folderName: 'displayName',
      uniqueId: 'id',
      folderDescription: 'description',
      policyId: 'policyId',
      parentFolderId: 'parentFolderId'
    };
  }

  /**
   * Folder component REday call back - thiw will
   *  execute callback method 'componentReadyCallback' passed in folder config
   */
  folderComponentReadyCallback(event: any) {
    this.folderService = event.service;
    // lets set the root folder
    this.folderService.setRootFolder(this.rootFolder);
    this.folderClicked(this.rootFolder);
  }

  updateFolderPanelState(state: string) {
    this.folderPanelState = state;
    setTimeout(() => {
      this.folderState.emit(state);
    }, 1000);
  }

  folderClicked(event: any) {
    if (event.id !== this.rootFolder.id) {
      this.currentFolderForContent = event.id;
    } else {
      this.currentFolderForContent = event.id;
      this.folderPathForAssetContents = [...this.folderComponent.currentFolderLinks];
    }
    this.config.applicationService.selectedFolderNAme = event.displayName;
    this.selectedFolderData.emit({
      folderId: event.id,
      contentFolderPath: [...this.folderComponent.currentFolderLinks]
    });
  }




  // 401 and retry

  /**
 * In case error from server this function will be called
 * param error
 */
  // private handleServerError(callback, error) {
  //   if (error.status === 401 || error.status === 403) {
  //     if (this.loginAttempts < this.MAX_LOGIN_ATTEMPTS) {
  //       // we have to a login again
  //       this.reLogin(callback);
  //     } else {
  //       // this.notificationService.show({
  //       //   message: this.config.translations.unableToFetchData,
  //       //   type: 'error', close: true, autoHide: 6000
  //       // });
  //       // this.cancelMapping();
  //     }
  //   } else {
  //     // this.dataLoading = false;
  //     // if (this.noSupportedContentAvailable) {
  //     //   this.throwNoSupportedContentError();
  //     // }
  //   }
  // }

  /**
* In case there is a un auth error we can do a  relogin to get the new token
*/
  private reLogin(callbackFunction: any): void {
    // this.loginAttempts++;
    // check if we have a relogin method
    // if (this.config.reLogin) {
    //   this.config.reLogin(this.reLoginSuccess.bind(this, callbackFunction));
    // } else {
    //   this.notificationService.show({
    //     message: this.config.translations.unableToFetchData,
    //     type: 'error', close: true, autoHide: 6000
    //   });
    // }
  }

  /**
   * called when the relogin is successful from the caller
   */
  // public reLoginSuccess(callback): void {
  //   if (callback) {
  //     callback();
  //   }
  // }



}
