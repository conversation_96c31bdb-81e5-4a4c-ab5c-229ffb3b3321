import { Component, OnInit, OnD<PERSON>roy, Input } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { Approval } from '../../models/approval';
import { Role } from '../../models/role';
import { ApprovalSharedDataService } from '../../service/approval-shared-data.service';

@Component({
  selector: 'hcl-approval-user-info',
  templateUrl: './approval-user-info.component.html',
  styleUrls: ['./approval-user-info.component.scss']
})
export class ApprovalUserInfoComponent implements OnInit, OnDestroy {

  @Input() approval: Approval;
  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  allRoles: Role[] = [];
  approvalOwners: any[] = [];
  approvalApprovers: any = [];

  constructor(private _sharedDataService: ApprovalSharedDataService) { }

  ngOnInit(): void {
    this.populateRolesData();
    this.populateOwners();
    this.populateApprovers();
  }

  private populateRolesData() {
    this.subscriptionList.push(this._sharedDataService.getMemberRoles().subscribe((res: Role[]) => this.allRoles = res));
  }

  private populateOwners() {
    if (this.approval.approvalOwners && this.approval.approvalUsers) {
      this.approvalOwners = [];
      this.approval.approvalOwners.forEach(owner => {
        const user = this.approval.approvalUsers.filter(u => u.userId === owner.userId && u.memTypeId === 41);
        if (user && user.length > 0) {
          user[0].roleId = user[0].roleId ? user[0].roleId : -1;
          user[0].role = this.allRoles.filter(r => r.id === user[0].roleId)[0].name;
          this.approvalOwners.push(user[0]);
        }
      });
    }
  }

  private populateApprovers() {
    if (this.approval.approvers) {
      this.approvalApprovers = [];
      this.approval.approvers.forEach(approver => {
        if (approver.user) {
          const role: Role[] = this.allRoles.filter(r => r.id === approver.roleId);
          if (role && role.length > 0) {
            approver.role = role[0].name;
          }
          this.approvalApprovers.push(approver);
        }
      });
    }
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => {
      sub.unsubscribe();
    });
  }

}
