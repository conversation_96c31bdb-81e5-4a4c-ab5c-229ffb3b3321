import { Rule } from '../types'

export default {
  id: 'src-not-empty',
  description: 'The src attribute of an img(script,link) must have a value.',
  init(parser, reporter, rulePresent, translations: {[key: string]: string}) {
    parser.addListener('tagstart', (event) => {
      const tagName = event.tagName
      const attrs = event.attrs
      let attr
      const col = event.col + tagName.length + 1

      for (let i = 0, l = attrs.length; i < l; i++) {
        attr = attrs[i]

        if (
          ((/^(img|script|embed|bgsound|iframe)$/.test(tagName) === true &&
            attr.name === 'src') ||
            (tagName === 'link' && attr.name === 'href') ||
            (tagName === 'object' && attr.name === 'data')) &&
          attr.value === ''
        ) {
          reporter.error(
            reporter.buildMessage(translations['taottmhav'], attr.name, tagName),
            event.line,
            col + attr.index,
            this,
            attr.raw
          )
        }
      }
    })
  },
} as Rule
