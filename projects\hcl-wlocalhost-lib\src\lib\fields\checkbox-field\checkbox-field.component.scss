input[type="checkbox"] {
  /* remove standard background appearance */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  /* create custom radiobutton appearance */
  display: inline-block;
  width: 13px;
  height: 13px;
  padding: 2px;
  /* background-color only for content */
  background-clip: content-box;
  border: 1px solid rgba(0, 0, 0, 0.38);
  background-color: #fff;
}

/* appearance for checked radiobutton */
input[type="checkbox"]:checked {
  border: 1px solid #f5821e;
  background-color: #f5821e;
}

/* optional styles, I'm using this for centering radiobuttons */
.flex {
  display: flex;
  align-items: center;
}

:host .droppable {
  display: inline-block !important;
}
:host {
  font-size: 0px;
}

:host .droppable .checkbox-list.horizontal .checkbox-list-item {
  display: inline-block !important;
}
:host .required::after {
  content: '*';
  color: red;
  padding: 2px;
}

.error-container {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 24px;
}
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 42px;
  height: 24px;
}

.toggle-switch input { 
  opacity: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #cccccc;
  -webkit-transition: .4s;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  top: 3px;
  background-color: #FFFFFF;
  -webkit-transition: .4s;
  transition: .4s;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(18px);
  -ms-transform: translateX(18px);
  transform: translateX(18px);
}

.slider.round {
  border-radius: 18px;
}

.slider.round:before {
  border-radius: 70%;
}
.toggleLabel {
  top: -2px;
}
.checkboxLabel {
  position: relative;
  display: inline-block;
  left: 8px;
}