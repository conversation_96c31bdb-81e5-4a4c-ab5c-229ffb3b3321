<div class="offerlist-card-view-container" [ngClass]="{'summary-page': isSummaryPage}">
    <div *ngIf="!isSummaryPage" class="metadata-container">
        <span class="offer-count">{{'ASSETPICKER.TOTAL' | translate}} -
            {{offerlistResponseData?.page?.totalElements || 0}}</span>
        <div class="manage-selections">
            <div class="selected-offers" hclTooltip="{{ selectedOffersAndOls() }}">{{ selectedOffersAndOls() }}</div>
            <div class="manage-selections-button">
                <hcl-button class="mr-10px" [config]="manageSelectionsButtonConf" (click)="manageSelections($event)">
                </hcl-button>
            </div>
        </div>
    </div>

    <div class="scroll-container">
        <ng-container *ngIf="!noRecordsFound && !folderWithoutOfferlists">
            <hcl-offerlist-card-list [chunkSize]="chunkSize" [offerlistResponseData]="offerlistResponseData" [isDataLoading]="isDataLoading"
                (loadNewOfferlists)="loadNewOfferlists($event)" *ngIf="isSummaryPage">
            </hcl-offerlist-card-list>
            
            <hcl-offerlist-lp-card-list  [chunkSize]="chunkSize" [offerlistResponseData]="offerlistResponseData" [isDataLoading]="isDataLoading"
                (loadNewOfferlists)="loadNewOfferlists($event)" (offerListSelectionUpdate)="offerlistSelected()"
                *ngIf="!isSummaryPage">
            </hcl-offerlist-lp-card-list>
        </ng-container>
        <ng-container *ngIf="noRecordsFound || folderWithoutOfferlists">
            <div class="no-records" *ngIf="isSummaryPage">
                <div class="title">{{'MESSAGES.NO_OFFERS_TO_SHOW' | translate}}</div>
            </div>
            <div class="no-records" *ngIf="!isSummaryPage">
                <div class="title">{{'MESSAGES.NO_OFFERLISTS_TO_SHOW' | translate}}</div>
            </div>
        </ng-container>
    </div>
</div>