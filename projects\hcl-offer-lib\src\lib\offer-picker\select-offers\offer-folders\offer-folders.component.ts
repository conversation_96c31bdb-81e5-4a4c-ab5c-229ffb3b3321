import { Component, ComponentFactoryResolver, EventEmitter, ElementRef, Injector, Input, OnDestroy, OnInit, Output, ReflectiveInjector, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, SubscriptionLike } from 'rxjs';
import { FoldersComponent, InputConfig, MessageService } from 'hcl-angular-widgets-lib';
import { SelectOffersService } from '../../select-offers.service';
import { OfferFoldersConfig } from '../../select-offers/select-offers.config'

@Component({
  selector: 'hcl-offer-folders',
  templateUrl: './offer-folders.component.html',
  styleUrls: ['./offer-folders.component.scss']
})
export class OfferFoldersComponent implements OnInit, OnDestroy {
  @Output() folderSelect = new EventEmitter();
  @Input() config: OfferFoldersConfig;
  @ViewChild('folders') folders: FoldersComponent;
  @ViewChild('topActionsTemplate', { static: true }) topActionsTemplate: TemplateRef<ElementRef>;

  currentComponent: any;
  checkbox: true;
  events: any;
  refInjector: ReflectiveInjector;
  displayNameConfig: InputConfig;
  createFoldersForm: any;
  editFoldersForm: any;
  sortOrder: string;
  sortBy: string;
  currentFolder: any;
  currentFolderParent: any;
  breadCrumbData = [];
  folderPanesData = [];
  parentSelection = false;
  subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  folderAction = null;
  contextMenuFlag = false;
  folderIndex = 0;
  preFolderLoadedFlag = false;
  serverSideValidationMap: Map<string, any>;
  folderEvent;
  addSideBarToDom = false;
  rootFolderId: number;
  selectedFolders = [];

  @ViewChild('placeHolder', { static: true, read: ViewContainerRef }) placeHolder;
  @ViewChild('sidePaneContainerPlaceHolder', { static: false, read: ViewContainerRef }) sidePaneContainerPlaceHolder;

  constructor(
    private translate: TranslateService,
    private injector: Injector,
    private selectOffersService: SelectOffersService,
    private messageService: MessageService,
    private cmpFctryRslvr: ComponentFactoryResolver
  ) {
  }

  ngOnInit() {
    if (this.config) {
      this.rootFolderId = this.config.rootFolderId ? this.config.rootFolderId : 4;
    }
    this.events = new BehaviorSubject({ eventType: '', data: {} });
    this.contextMenuFlag = false;
    this.setConfiguration();
    this.reloadFolders();
    this.connectListeners();
    this.browseFolderSession();
  }


  setConfiguration() {
  }

  reloadFolders() {
    if (this.selectOffersService.foldersCacheData &&
      this.selectOffersService.foldersCacheData.length > 0) {
      this.breadCrumbData = [...this.selectOffersService.foldersCacheData[0]];
      this.folderPanesData = [...this.selectOffersService.foldersCacheData[1]];
      this.currentFolder = this.selectOffersService.foldersCacheData[2];
      this.folderIndex = this.selectOffersService.foldersCacheData[3];
      this.selectedFolders = [this.currentFolder];
      this.selectOffersService.offersFolder = this.currentFolder.id ? this.currentFolder.id : 4;
      this.selectOffersService.sendFoldersBreadcrumb([...this.breadCrumbData]);
      this.preFolderLoadedFlag = true;
    }
  }

  chooseComponent(componentName, attributes) {
    switch (componentName) {
      case 'FoldersComponent':
        const comp = this.createDynamicComponent(FoldersComponent, attributes);
        return comp;
    }
  }

  createDynamicComponent(type: any, attributes: object = {}): any {
    const factory = this.cmpFctryRslvr.resolveComponentFactory(type);
    this.refInjector = ReflectiveInjector.resolveAndCreate([{ provide: type, useValue: type }], this.injector);
    const comp = this.placeHolder.createComponent(factory, 0, this.refInjector);
    comp.instance.config = {
      componentRef: comp,
      returnChildrenLabel(): string {
        return 'childFolders';
      },
      folderName(): string {
        return 'displayName';
      },
      updateDate(): string {
        return 'createdTimeStamp';
      },
      properties: {
        checkbox: true,
        defaultFolder: {
          id: this.rootFolderId,
          displayName: this.translate.instant('FOLDERS.TITLES.ALL_OFFERS')
        },
        notlazyLoading: true,
        folderCountTemplate: false,
        folderAction: this.folderAction ? this.folderAction : null,
        myFolderPanes: this.folderPanesData.length > 0 ? this.folderPanesData : [],
        myFoldersBreadCrumb: this.breadCrumbData.length > 0 ? this.breadCrumbData : [],
        selectedFolderObj: this.currentFolder ? this.currentFolder : {
          id: this.rootFolderId,
          displayName: this.translate.instant('FOLDERS.TITLES.ALL_OFFERS')
        },
        contextMenuFlag: this.contextMenuFlag,
        defaultFolderIndex: this.folderIndex,
        // dateFormat: this.dataService.getDatePipeFormatLocale()
      },
      topActionsTemplate: this.topActionsTemplate
    };
    Object.keys(attributes['input']).forEach(key => {
      comp.instance[key] = attributes['input'][key];
    });
    Object.keys(attributes['output']).forEach(key => {
      this.subscriptionList.push(comp.instance[key].subscribe((folderId) => {
        attributes['output'][key](folderId);
      }));
    });
    this.currentComponent = comp;
    return comp;
  }



  getPlacementLocation(position) {
    switch (position) {
      case 'sidePane':
        return this.sidePaneContainerPlaceHolder;
    }
  }

  connectListeners() {
    this.subscriptionList.push(this.events.subscribe((event: any) => {
      switch (event.eventType) {
        case 'CREATE_FOLDERS_COMPONENT':
          this.events.next({
            eventType: 'INSERT_COMPONENT', data: {
              location: 'sidePane',
              component: this.chooseComponent(event.data.componentName, event.data.attributes)
            }
          });
          break;
        case 'INSERT_COMPONENT':
          event.data.component.instance.position = event.data.location;
          this.getPlacementLocation(event.data.location);
          break;
      }
    }));
  }

  sortFoldersByItem(event, sortBy) {
    if (this.sortBy !== sortBy) {
      this.sortOrder = 'NONE';
      this.sortBy = sortBy;
    }
    switch (this.sortOrder) {
      case 'ASC': {
        this.sortOrder = 'DESC';
        break;
      }
      case 'DESC': {
        this.sortOrder = 'NONE';
        break;
      }
      default: {
        this.sortOrder = 'ASC';
      }
    }
    this.messageService.sendFoldersSort(this.sortOrder, sortBy);
  }

  cacheFoldersLocally(data) {
    if (data) {
      this.breadCrumbData = data.myFoldersBreadCrumb ? data.myFoldersBreadCrumb : [];
      this.folderPanesData = data.myFolderPanes ? data.myFolderPanes : [];
      if (this.breadCrumbData &&
        this.breadCrumbData[this.breadCrumbData.length - 1].folder.id !== 4) {
        this.selectOffersService.foldersCacheData = [this.breadCrumbData, this.folderPanesData, data.selectedFolderObj, data.index];
      }
    }
  }

  selectedFolder(data) {
    if (data.rootRefresh) {
      this.currentFolder = null;
      this.selectOffersService.offersGlobalSearchData = '';
      this.selectOffersService.offersFolder = 4;
      this.selectOffersService.sendFolderId('offers', this.selectOffersService.offersFolder);
      this.selectOffersService.sendFolderId('offerlists', this.selectOffersService.offersFolder);
      this.selectOffersService.sendFoldersBreadcrumb([]);
    } else {
      this.folderIndex = data.index;
      this.currentFolder = data.folder;
      this.currentFolderParent = data.parent;
      this.folderSelectionActions(data.folder);
    }
  }

  folderSelectionActions(folder) {
    this.folderSelect.emit(folder);
    this.selectOffersService.sendFoldersBreadcrumb([...this.breadCrumbData]);
    this.selectOffersService.clearFolderId();
    this.selectOffersService.offersFolder = folder.id;
    this.selectOffersService.sendFolderId('offers', this.selectOffersService.offersFolder);
    this.selectOffersService.sendFolderId('offerlists', this.selectOffersService.offersFolder);
    this.selectOffersService.offersFolder = folder.id;
    this.selectOffersService.offersGlobalSearchData = '';
  }

  rightIconClicked(data) {
    this.currentFolder = data.folder;
    this.folderSelect.emit(data.folder);
  }

  breadCrumbClicked(data) {
    this.currentFolder = data.folder;
    this.folderIndex = data.index;
    if (!this.rootFolderId) {
      this.rootFolderId = 4;
    }
    if (data.folder.id === this.rootFolderId) {
      this.selectOffersService.offersGlobalSearchData = '';
      this.selectOffersService.offersFolder = this.rootFolderId;
      this.selectOffersService.sendFolderId('offers', this.selectOffersService.offersFolder);
      this.selectOffersService.sendFolderId('offerlists', this.selectOffersService.offersFolder);
      this.selectOffersService.sendFoldersBreadcrumb([]);
      this.selectOffersService.offersFolder = this.rootFolderId;
    } else {
      this.folderSelectionActions(data.folder);
    }
  }


  foldersPaneLoaded() {
    this.sortOrder = 'NONE';
  }

  // When use wants to change or create a folder
  browseFolderSession() {
    this.events.next({
      eventType: 'CREATE_FOLDERS_COMPONENT', data: {
        componentName: 'FoldersComponent',
        attributes: {
          input: {
            getFolders: (folder) => {
              this.subscriptionList.push(this.selectOffersService.getFolders(folder.id).subscribe((folderData) => {
                this.messageService.sendChildren(folderData.childFolders, folderData);
                this.sortOrder = 'NONE';
                if (folder.id === this.rootFolderId && this.folderPanesData && this.folderPanesData.length === 0) {
                  this.folderPanesData = [{ children: folderData.childFolders, index: 0, id: this.rootFolderId }];
                }
              },
                (error) => {
                  this.messageService.sendChildren([], {});
                }));
            }
          },
          output: {
            selectedFolder: (folder) => {
              this.selectedFolder(folder);
            },
            rightIconClicked: (data) => {
              this.rightIconClicked(data);
            },
            cacheFoldersLocally: (data) => {
              this.cacheFoldersLocally(data);
            },
            breadCrumbClicked: (data) => {
              this.breadCrumbClicked(data);
            },
            foldersPaneLoaded: () => {
              this.foldersPaneLoaded();
            }
          }
        }
      }
    });
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => { sub.unsubscribe(); });
  }
}
