/**
 * The dimensions that we will use in charting
 */

export interface Dimension {
  width: number;
  height: number;
}
/**
 * The margin that is provided for the chart area
 */
export interface Margin {
  top: number;
  right: number;
  bottom: number;
  left: number;
}

/**
 * this is the category axis on which the categories are displayed
 */
export interface CategoryAxis {
  // flag that tells if the axis needs to be fixed
  fixedLinearAxis?: 'top';
  // hidden if true the Axis will not be rendered in the UI, default is false
  hidden?: boolean;
  // flag that tells if the tick labels should be auto aligned
  disableAutoAlignText?: boolean;
  // flag that tells if we want the grid lines
  gridLines ?: {
    // the css for the grid lines
    class?: string
    // the grid lines need to be transformed
    transform ?: {x: number, y: number}
  };
  // the Axis title label
  title?: any;
  // The list of categories
  categories?: string[];
  // the padding
  padding?: number;
  // in case the cat array is not available we can use this call back function that will return a array of strings
  categoriesFunction ?: (config: ChartConfig) => string[];
  // the position of the Axis
  position: 'top' | 'bottom' | 'right' | 'left';
  // margin that we want for the category Axis, default is 50
  margin ?: number;
}

export interface Interactive {
  // flag that tells if the bar can be dragged
  draggable ?: boolean;
  // call back function that will be called when the node is dragged
  // if the function returns falsed the node will not be draggable
  canBeDragged?: (data: any) => boolean;
  // before we change the position of the node validateTimeChange will be called to check if the new time set is valid or not
  validateTimeChange?: (data: any, value: {min: number, max: number}) => boolean;
  // flag that tells if the bar can be resized
  reSizable ?: boolean;
  // flag that tells if dependency could be defined or not
  drawDependency ?: boolean;
}

/**
 * the series for the charting
 */
export interface ChartSeries {
  // function that if eturns true the data node will not be plotted
  noDataToPlot?: (data: any) => boolean;
  // round Time for minutes
  roundTime?: (data: any, time: { min: number; max: number }) => { min: number; max: number };
  // flag that tells if the series is plotter in reverse
  isReverse?: boolean;
  // the position of the Axis
  position: 'top' | 'bottom' | 'right' | 'left';
  // flag that tlls if we want the grid lines
  gridLines?: {
    // the css for the grid lines
    class?: string
  };
  // in case user wants to display dependencies this obect needs to be set
  dependency?: {
    // the function to get the dependent node
    // this function will return the parent dependencies. If this function is absent then
    // the data will need to have dependency[] which is a Object arra and has a id variable
    getParentDependency?: (object: any) => string[];
    // this function will be called to check if this dependency is valid or not
    validateDependencyAdd?: (parent: any, child: any) => boolean;
    // validate dep delete
    validateDependencyDelete?: (parent: any, child: any) => boolean;
  };
  // a css for the data node which can be a line, rectangle
  dataCss?: string;
  // get Css at run time based on the data
  getCssClass?: (data: any) => string;
  // get the parent 'g' node class, this can be marked as acive
  getParentCssClass?: (data: any) => string;
  // user can format the tick string by using theis callback
  tickFormat?: (d) => string;
  // margin buffer default 0
  marginBuffer?: number;
  // Flag that tells if the chart is interactive or not
  interactive?: Interactive;
  // for time series
  timeSeries?: {
    // In case of time series you can specify a empty object
    // displayIn tells if the chart needs to be default displayed,
    displayIn: 'year' | 'month' | 'week' | 'day' | 'hour' | 'minute' | 'second';
    // the time Interval this is automatically set
    _timeInterval?: any;
  };
  // hidden if true the Axis will not be rendered in the UI, default is false
  hidden?: boolean;
  // the name of this series
  name: string;
  // the data that we want in the Series
  data?: any[];
  // get category function
  getCategory?: (d: any, index: number) => string;
  // margin that we want for the category Axis, default is 50
  margin?: number;
  // the minimum value
  min?: number;
  // A callback function that will be called to get a value from a single row in the data object
  getMinValue?: (d: any, i: number) => number;
  // the max value
  max?: number;
  // A callback function that will be called to get a value from a single row in the data object
  getMaxValue?: (d: any, i: number) => number;
  // click handler
  click?: (data: any) => void;
  // in case user wants to render custom elements on the series
  customElements?: {
    type: 'rect' | 'path' | 'text',
    // the class for the element
    getClass?: (d: any) => string
    // get x position
    getXOffSet?: () => number;
    getYOffSet?: () => number;
    width?: number;
    height?: number;
    // the configurations that are required for path
    path?:{
      getPath: (data: any, x: number, y: number)=> string;
    },
    text ?: (d) => string;
  }[]
}
/**
 * The margin that is provided for the chart area
 */
export interface ChartMargin {
  top: number;
  right: number;
  bottom: number;
  left: number;
}
/**
 * The configuration of the chart
 */
export interface ChartConfig {
  // Flag that tells if the chart is interactive or not
  interactive ?: {
    // flag that tells if the bar can be dragged
    draggable ?: boolean;
    // flag that tells if the bar can be resized
    reSizable ?: boolean;
    // flag that tells if dependency could be defined or not
    drawDependency ?: boolean;
  };
  // any animation is that is required while loading the chart
  animation ?: {
    duration?: number;
  };
  // array of parent task on which task is dependent upon
  dependency ?: any[];
  // the margin
  margin: ChartMargin;
  // the Dimension of the charts
  dimension?: Dimension;
  // the charting background
  background?: {
    // in case we need to apply any specific class
    class?: string;
    // background color
    bgColor ?: string;
  };
  // we can add the band Scale
  categoryAxis ?: CategoryAxis;
  // the chart series
  series ?: ChartSeries[];
}

export interface DonutChartConfig {
   width: number;
   height: number;
   iconWidth?: number;
   iconHeight?: number;
   outerRadius: number;
   innerRadius: number;
   barImage?: boolean;
   showTooltip? :boolean;
   data: any;
   centerImage?: any;
   spreadSlice?: boolean;
   chartID: string;
   middleText?: string;
   middleTextColor?: string;
   middleTextFontSize?: string;
   showLegend?: string;
   tooltipHTML?: Function;
   returnValue?: Function;
   progressChart?: boolean;
}

export interface DonutChartData {
  id: number;
  label: string; // string
  value: number; // number
  color: string;
}


