import { Component, OnInit, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { PopoverTrigger, PopoverComponent, ProgressSpinner, HclAssetPickerService, ApplicationConfigurationService } from 'hcl-angular-widgets-lib';
import * as _ from 'lodash';
import { DatePipe, getLocaleCurrencySymbol } from '@angular/common';
import { SharedModuleConstants } from '../../shared.constants';
@Component({
  selector: 'com-offer-preview',
  templateUrl: './offer-preview.component.html',
  providers: [DatePipe],
  styleUrls: ['./offer-preview.component.scss']
})
export class OfferPreviewComponent implements OnInit {
  @Input() offerData = null;
  @Input() currencyLocale;
  @Input() dateFormat;
  @Input() translations;
  @Input() isAssetPickerInstalled: boolean = false;
  @Output() apViewIconClick = new EventEmitter();
  @Output() viewRichTextClick = new EventEmitter();
  @ViewChild(PopoverTrigger) popoverTrigger: PopoverTrigger;
  @ViewChild(PopoverComponent) popover: PopoverComponent;

  ssadbAttributeDependencies = [];
  allAttributesMap = new Map<number, any>();
  hiddenAttributesSet = new Set<number>();
  alwaysHiddenAttributesSet = new Set<number>();
  allSsdbAttributesMap = new Map<number, any>();
  accordionCustomConfig: any;
  dynamicConfigs = {};
  apCurrentTargetElement: any;
  staticAttributes: any;
  parameterizedAttributes: any;
  hiddenAttributes = [];
  offerTemplateID: number;
  offerThumbnailDetails = {
    displayName: '',
    id: 'offerThumbnail',
    value: { url: '' }
  };
  selectedTabNumber = null;
  offerTemplateAttributes: any;
  apItemDetails: any = {};
  apItemDetailsState = '';
  apOverlayInputs = {
    popoverPositionY: 'top',
    popoverPositionX: 'start',
    originPositionX: 'end',
    originPositionY: 'top',
    popoverEnterDelay: 0,
    popoverLeaveDelay: 0,
    popoverOffsetY: 0,
    popoverOffsetX: 30,
    noPreview: false,
    loadAt: 'right',
  };

  cpInlineSpinner: ProgressSpinner = {
    isInline: true,
    isLoading: false,
    color: 'primary',
    mode: 'indeterminate',
    value: 75,
    diameter: 20,
    strokeWidth: 3
  };
  selectedRichtextAttribute: any;
  rtSidebarPreviewActive = false;
  selectedRepoForPreview = null;
  contentAssociationMapping = [];
  customActionResponseMapping = new Map<number, any>();
  customActionRequestMapping = new Map<number, any>();
  autoSyncRepos = {};
  currencySymbol = '';

  constructor(private hclAssetPickerService: HclAssetPickerService,
    private applicationConfigurationService: ApplicationConfigurationService) { }


  ngOnInit() {
    this.offerThumbnailDetails.displayName = this.translations.thumbnailUrl;
    this.setConfiguration();
    this.mapAttributes();
    this.hideUnhideAttributesOnLoad();
    this.currencySymbol = this.currencyLocale ? getLocaleCurrencySymbol(this.currencyLocale.replace('_', '-')) : '';
    this.offerThumbnailDetails.value = this.offerData?.thumbnailProperties;
  }

  hideUnhideAttributesOnLoad() {
    if (this.offerData.offerTemplate.rules) {
      this.offerData.offerTemplate.rules.some(rule => {
        if (rule.hasOwnProperty('expression')) {
          rule.action.identifiers.forEach(id => {
            this.alwaysHiddenAttributesSet.add(+id);
          });
          return;
        }
      });

      this.offerData.offerTemplate.rules.forEach(rule => {
        if (rule.hasOwnProperty('nestedCondition')) {
          if (!this.checkRuleConditions(rule.nestedCondition.conditions)) {
            this.hideAttributes(rule.action.identifiers);
          }
        }
      });
    }
  }

  checkRuleConditions(conditionsData) {
    let alwaysHiddenAttributeInCondition = false;

    alwaysHiddenAttributeInCondition = conditionsData.some(condition => {
      if (this.alwaysHiddenAttributesSet.has(+condition.param)) {
        return true;
      }
    });

    if (alwaysHiddenAttributeInCondition) {
      return false;
    }

    let anyConditionIsTrue = false;

    anyConditionIsTrue = conditionsData.some(condition => {
      if (condition.op === 'in') {
        let flag = false;
        flag = condition.value.some((item) => {
          if (item === this.returnValueAccordingToType(this.allAttributesMap.get(+condition.param))) {
            return true;
          }
        });
        if (flag) {
          return true;
        }
      } else {
        let flag = false;
        flag = condition.value.some((item) => {
          if (item === this.returnValueAccordingToType(this.allAttributesMap.get(+condition.param))) {
            return true;
          }
        });
        if (!flag) {
          return true;
        }
      }
    });
    return anyConditionIsTrue;
  }

  returnValueAccordingToType(attribute) {
    if (attribute && attribute.hasOwnProperty('value')) {
      if (attribute.type.id === 100 || attribute.type.id === 5) {
        return attribute.value;
      } else if (attribute.type.id === 10 && attribute.value && attribute.value.idColumnValue) {
        return attribute.value.idColumnValue;
      }
    }
    return null;
  }

  hideAttributes(attributesData) {
    attributesData.forEach(id => {
      this.hiddenAttributesSet.add(+id);
      const attribute = this.allAttributesMap.get(+id);
      if (attribute && attribute.value) {
        attribute.value = null;
      }
    });
  }

  setConfiguration() {
    this.accordionCustomConfig = {
      displayMode: 'default',
      hideToggle: false,
      multi: true,
      step: 0,
      panelActions: false,
      items: [
        {
          label: '',
          headerTemplateName: 'headerTemplate_3',
          descr: '',
          descrTemplateName: 'descriptionTemplate_3',
          content: '',
          contentTemplateName: 'contentTemplate_3',
          disabled: false
        }]
    };
  }

  getRichTextOptions() {
    const options = SharedModuleConstants.richTextOptions;
    options.language = this.getLanguangeForTextEditor();
    options.placeholderText = this.translations.richTextEditor;

    return options;
  }

  getLanguangeForTextEditor() {
    const userDetails = this.applicationConfigurationService.getCurrentUserDetails(),
      id = userDetails.userLocale || 'en_US';

    switch (id) {
      case 'de_DE':
        return 'de';
      case 'en_GB':
        return 'en_gb';
      case 'en_US':
        return 'en_us';
      case 'es_ES':
        return 'es';
      case 'fr_FR':
        return 'fr';
      case 'it_IT':
        return 'it';
      case 'ja_JP':
        return 'ja';
      case 'ko_KR':
        return 'ko';
      case 'pt_BR':
        return 'pt_br';
      case 'ru_RU':
        return 'ru';
      case 'zh_CN':
        return 'zh_cn';
      case 'zh_TW':
        return 'zh_tw';
    }
  }

  mapAttributes() {
    if (this.offerData.offerTemplate && this.offerData.offerTemplate.dependentAttributes &&
      this.offerData.offerTemplate.dependentAttributes.length > 0) {
      this.accordionCustomConfig.items.push(
        {
          label: '',
          headerTemplateName: 'headerTemplate_4',
          descr: '',
          descrTemplateName: '',
          content: '',
          contentTemplateName: 'contentTemplate_4',
          disabled: false
        }
      );
    }

    if (this.offerData?.hiddenAttributes?.length) {
      if (!this.accordionItemAvailable(5)) {
        this.accordionCustomConfig.items.unshift(
          {
            label: '',
            headerTemplateName: 'headerTemplate_5',
            descr: '',
            descrTemplateName: '',
            content: '',
            contentTemplateName: 'contentTemplate_5',
            disabled: false
          }
        );
      }
    }

    if (this.offerData?.parameterizedAttributes?.length) {
      if (!this.accordionItemAvailable(2)) {
        this.accordionCustomConfig.items.unshift(
          {
            label: '',
            headerTemplateName: 'headerTemplate_2',
            descr: '',
            descrTemplateName: '',
            content: '',
            contentTemplateName: 'contentTemplate_2',
            disabled: false
          }
        );
      }
    }

    if (this.offerData?.staticAttributes?.length) {
      if (!this.accordionItemAvailable(1)) {
        this.accordionCustomConfig.items.unshift(
          {
            label: '',
            headerTemplateName: 'headerTemplate_1',
            descr: '',
            descrTemplateName: '',
            content: '',
            contentTemplateName: 'contentTemplate_1',
            disabled: false
          }
        );
      }
    }
  }

  accordionItemAvailable(itemNumber: number) {
    return this.accordionCustomConfig.items.some(accordionItem => +accordionItem.headerTemplateName.split('_')[1] === +itemNumber);
  }

  setOverlayPositions(el, noPreview) {
    const elPositions = el.getBoundingClientRect();
    const winWidth = window.innerWidth;

    if (elPositions.left < (winWidth / 2)) {
      this.apOverlayInputs.popoverOffsetX = 40;
      this.apOverlayInputs.popoverOffsetY = 0;
      this.apOverlayInputs.popoverPositionX = 'start';
      this.apOverlayInputs.originPositionX = 'end';
      this.apOverlayInputs.loadAt = 'right';
    } else {
      this.apOverlayInputs.popoverOffsetX = -10;
      this.apOverlayInputs.popoverOffsetY = 0;
      this.apOverlayInputs.popoverPositionX = 'end';
      this.apOverlayInputs.originPositionX = 'start';
      this.apOverlayInputs.loadAt = 'left';
    }

    if (noPreview) {
      this.apOverlayInputs.popoverPositionY = 'bottom';
      this.apOverlayInputs.originPositionY = 'top';
      this.apOverlayInputs.popoverOffsetX = elPositions.left < (winWidth / 2) ? 5 : 30;
      this.apOverlayInputs.popoverOffsetY = 0;
    }
  }

  closeApItemDetails() {
    this.popoverTrigger.closePopover();

    const backdrop: HTMLElement = document.querySelector('.cdk-overlay-backdrop') as HTMLElement;
    backdrop && backdrop.click();
  }

  triggerApErrorMessage(state: string) {
    this.setOverlayPositions(this.apCurrentTargetElement, true);
    this.apItemDetailsState = state;
    this.apOverlayInputs.noPreview = true;
    setTimeout(() => {
      this.apCurrentTargetElement.previousElementSibling.click();
      setTimeout(() => {
        this.closeApItemDetails();
      }, 4000);
    }, 100);
  }

  viewUrlResource(event, attribute, isOfferThumbnail?: boolean) {
    this.apCurrentTargetElement = event.currentTarget;
    this.dynamicConfigs[attribute.id + 'loader'] = { ...this.cpInlineSpinner };
    if (attribute.value.applicationId && attribute.value.objectId && attribute.value.url) {
      this.dynamicConfigs[attribute.id + 'loader'].isLoading = true;

      this.apViewIconClick.emit(attribute);
    } else if (isOfferThumbnail) {
      this.apItemDetails.contentUrl = this.offerThumbnailDetails.value.url;
      this.apItemDetails.isImageLoaded = true;
      this.apItemDetailsState = 'loaded';
      setTimeout(() => {
        this.dynamicConfigs[attribute.id + 'loader'].isLoading = false;
        this.apCurrentTargetElement.previousElementSibling.click();
      }, 100);
    } else {
      this.triggerApErrorMessage('no-content');
    }
  }

  openRichTextView(attribute) {
    this.viewRichTextClick.emit(attribute);
  }

  setSelectedRepoForPreview(data) {
    this.selectedRepoForPreview = data;
  }

  transformApItemDetails(attribute, data) {
    this.apItemDetails = data;
    this.apItemDetails.url = this.apItemDetails.presentationDetails.multimedia.resourceUrl;
    this.apItemDetails.applicationId = attribute.value.applicationId;
    this.setOverlayPositions(this.apCurrentTargetElement, false);
    if (this.selectedRepoForPreview && !this.selectedRepoForPreview.anonymousContent) {
      this.apItemDetails.presentationDetails.multimedia.resourceUrl = this.hclAssetPickerService.baseUrl + '/'
        + this.apItemDetails.applicationId + '/download?resourceId=' + this.apItemDetails.presentationDetails.multimedia.id +
        '&resource=' + (this.apItemDetails.presentationDetails.multimedia.thumbnailUrl || this.apItemDetails.url);

      this.hclAssetPickerService.downloadContentFromResourceId(this.apItemDetails.presentationDetails.multimedia.resourceUrl).subscribe((resp) => {
        const reader = new FileReader();
        reader.readAsDataURL(resp);
        reader.onload = () => {
          this.apItemDetails.contentUrl = reader.result;
          this.apItemDetails.isImageLoaded = true;

          this.apItemDetailsState = 'loaded';
          setTimeout(() => {
            this.dynamicConfigs[attribute.id + 'loader'].isLoading = false;
            this.apCurrentTargetElement.previousElementSibling.click();
          }, 100);
        };
      });
    } else {
      this.apItemDetailsState = 'loaded';
      setTimeout(() => {
        this.dynamicConfigs[attribute.id + 'loader'].isLoading = false;
        this.apCurrentTargetElement.previousElementSibling.click();
      }, 100);
    }
  }

  apItemDetailsErrorHandler(id, value) {
    this.dynamicConfigs[id + 'loader'].isLoading = false;
    this.triggerApErrorMessage(value);
  }

  apItemDetailsClosed() {
    this.apItemDetails = {};
    this.apItemDetailsState = '';
    this.apOverlayInputs.popoverOffsetY = 0;
    this.apOverlayInputs.popoverOffsetX = 30;
    this.apOverlayInputs.popoverPositionY = 'top';
    this.apOverlayInputs.popoverPositionX = 'start';
    this.apOverlayInputs.originPositionY = 'top';
    this.apOverlayInputs.originPositionX = 'end';
    this.apOverlayInputs.loadAt = '';
    this.apOverlayInputs.noPreview = false;
    this.selectedRepoForPreview = null;
  }
}
