import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import * as d3 from 'd3';
import { RadarChartConfig } from './radar-chart';
@Component({
  selector: 'radar-chart',
  templateUrl: './radar-chart.component.html',
  styleUrls: ['./radar-chart.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class RadarChartComponent implements OnInit {
  @Input() chartData: { name: string, value: number, valueLabel: string}[] = [
    { name: "Clarity", value: 100, valueLabel: 'A'},
    { name: "Concision", value: 100, valueLabel: 'A' },
    { name: "Engagement", value: 80, valueLabel: 'B' },
    { name: "Spam", value: 100, valueLabel: 'A'},
    { name: "Spelling", value: 100, valueLabel: 'A' }
    ]

  constructor() { }

  @Input() radarChartConfig : RadarChartConfig = {
    numofSide : 5,
    numofLevels : 5,
    size : 400,
    drawYAxis: false,
    drawTicks: true,
    drawDataTextLabels: true,
    startGridLinesFromCenter: false,
    chartBgColor: '#FFF',
    gridLineColor: '#808080',
    levelsStrokeColor: '#808080',
    dataFillColor: 'rgba(143,205,176,0.8)',
    dataStrokeColor: 'rgb(84,150,122)',
    dataPointCircleColor: 'rgb(96 128 149)',
    tickTextColor: '#000',
    labelsTextColor: '#000',
    radiusForDataPointsCircle: 3
  }
 
  offset: number = Math.PI;

  polyangle: number;
  r: number;
  r_0: number;
  center: {x: number, y: number};
  lastPolygonPoints = null
  scale: any;
  parentGroupElem: any
 
  ngOnInit(): void {
    this.setChartDimensions();
    this.createSvgAndParentGroup();
    this.drawChart(this.chartData);
  }

  drawChart(chartdata) {
    this.drawScale();
    this.generateAndDrawLevels();
    this.generateAndDrawLines();
    this.drawAxisAndTicks();
    this.drawData(chartdata);
    if (this.radarChartConfig.drawDataTextLabels)
      this.drawLabels(chartdata);
  }

  redrawChart(chartdata) {
    this.recreateSvgAndParentGroup();
    this.drawChart(chartdata)
  }

  recreateSvgAndParentGroup() {
     d3.select("#svg_"+ this.radarChartConfig.chartContainerId).remove()
     this.createSvgAndParentGroup() 
  }

  /**
   * populate angle, center of the radar chart with radius and length
   */
  setChartDimensions() {
    this.polyangle = (Math.PI * 2) / this.radarChartConfig.numofSide;
    this.r = 0.7 * this.radarChartConfig.size,
    this.r_0 = this.r / 2,
    this.center =
    {
      x: this.radarChartConfig.size / 2,
      y: this.radarChartConfig.size / 2
    };
  }

  /**
   * create svg and add a "g" elem stored as 
   * parentGroupElem in a this context variable
   */
  createSvgAndParentGroup() {
    // todo introduce bg color
    const selector = "#" + this.radarChartConfig.chartContainerId;
    d3.select(selector ? selector + ' .chart' : '.chart')
    .append('svg')
    .attr('id', 'svg_' + this.radarChartConfig.chartContainerId)
    .style("background-color", this.radarChartConfig.chartBgColor)
    .attr("width", this.radarChartConfig.size)
    .attr("height", this.radarChartConfig.size);

    // append parent group element
    this.parentGroupElem = d3.select('#svg_' + this.radarChartConfig.chartContainerId).append("g");  
  }

  /**
   * draw scale with domain and range
   */
  drawScale() {
    // find the maximum data value in chart data to set the domain
    const maxDataValue = Math.max(...this.chartData.map(x => x.value));
    this.scale = d3.scaleLinear()
       .domain([0, maxDataValue])
       .range([0, this.r_0])
       .nice();
  }

  /**
   * create ticks array
   * @returns 
   */
  genTicks = (): number[] => {
    const ticks = [];
    const step = 100 / this.radarChartConfig.numofSide;
    for (let i = 0; i <= this.radarChartConfig.numofSide; i++) {
      const num = step * i;       
      if (Number.isInteger(step)) {
        ticks.push(num);
      }
      else {
        ticks.push(num.toFixed(2));
      }
    }
    return ticks;
  };

  /**
   * generate a point wit x and y coordinates
   * @param param0 
   * @returns {x: number, y: number}
   */
  generatePoint = ({length, angle}): {x: number, y: number} => {  
    const point =
    {
      x: this.center.x + (length * Math.sin(this.offset - angle)),
      y: this.center.y + (length * Math.cos(this.offset - angle))
    };

    return point;

  };

  /**
   * draws a path between two points
   * @param points 
   * @param parent 
   */
  drawPath = (points, parent, style: object = {}) => {
    const lineGenerator = d3.line()
      .x((d: any) => d.x)
      .y((d: any) => d.y);
    
    const newPath = parent.append("path")
      .attr("d", lineGenerator(points));

    if (style) {
      for (let key in style) {
        newPath.attr(`${key}`,`${style[key]}`)
      }
    }
  };

  /**
   * method to draw shape of a quadrilateral based on number of
   * sides in config. 
   * Draw multiple quadrilaterals based on number
   * of levels in config.
   */
  generateAndDrawLevels = () => {
    // loop for how many quadrilateral need to be drawn
    for (let level = 1; level <= this.radarChartConfig.numofLevels; level++) {
      const hyp = (level / this.radarChartConfig.numofLevels) * this.r_0;

      const points = [];
      // loop for each quadrilateral having how many sides to identify
      // corners of the shape, in this case 5 corners as its a pentagon
      for (let vertex = 0; vertex < this.radarChartConfig.numofSide; vertex++) {
        const theta = vertex * this.polyangle;
        points.push(this.generatePoint({ length: hyp , angle: theta }));
      }

      // store points for the inner most level
      if (level === 1) {
        this.lastPolygonPoints = points;
      }
      const group = this.parentGroupElem.append("g") //.attr("class", "levels");
      // draw line connecting the corners of the pentagon
      this.drawPath([...points, points[0]], group, {fill: 'none',stroke: this.radarChartConfig.levelsStrokeColor});
    }
  };

  /**
   * method to draw lines from center or from innermost coreners to outermost corners
   */
  generateAndDrawLines = () => {
    const group = this.parentGroupElem.append("g") //.attr("class", "grid-lines");
    for (let vertex = 0; vertex <= this.radarChartConfig.numofSide; vertex++) {
      const theta = vertex * this.polyangle;
      const point = this.generatePoint({ length: this.r_0 , angle: theta });
      if (this.lastPolygonPoints[vertex]) {
          this.drawPath( [this.lastPolygonPoints[vertex], point], group, {stroke:this.radarChartConfig.gridLineColor} ); 
      }
      // drawPath( [center, point], group );
    }
  };

  /**
   * draw circle at the data points of the chart data
   * @param points 
   */
  drawCircles = (points) => {
    this.parentGroupElem.append("g")
      .style("fill", this.radarChartConfig.dataPointCircleColor)
      .selectAll("circle")
      .data(points)
      .enter()
      .append("circle")
      .attr("cx", (d: any) => d.x)
      .attr("cy", (d: any) => d.y)
      .attr("r", this.radarChartConfig.radiusForDataPointsCircle)
  };

  /**
   * draw axis text and data point text
   * @param text 
   * @param point 
   * @param isAxis 
   * @param group 
   */
  drawText = (text, point, isAxis, group) => {
    if (isAxis) {
      const xSpacing = text.toString().includes(".") ? 30 : 22;
      const newTick = group.append("g").classed("tickNewClass", true);
      newTick.append("rect").classed("node", true).attr("x", point.x - 8).attr("y",  point.y - 1).attr("width", 15).attr("height", 15).attr("fill", "#FFF").attr("stroke", "rgba(30, 120, 99, 0.466)");
      newTick.append( "text" )
        .attr("x", point.x)
        .attr("y", point.y + 10)
        .html(() => {
          if(text == 100)
          return 'A'
          else if(text == 80)
          return 'B'
          else if(text == 60)
          return 'C'
          else if(text == 40)
          return 'D'
          else if(text == 20)
          return 'F'
        })
        .style("text-anchor", "middle")
        .style("fill",this.radarChartConfig.tickTextColor)
        .style("font-size", "10px")
        .style("font-family", "sans-serif")
        .style("font-weight","600");
    }
    else {
      group.append("text")
        .attr("x", point.x)
        .attr("y", point.y)
        .html(text)
        .style("text-anchor", "middle")
        .attr("fill", this.radarChartConfig.labelsTextColor)
        .style("font-size", "10px")
        .style("font-family", "sans-serif")
        .style("font-weight","600");
    }
  };

  /**
   * connect the data points to show the spider graph and 
   * draw circles on the data points
   * @param dataset 
   */
  drawData = (dataset) => {
    const points: any = [];
    dataset.forEach((d, i) => {
      const len = this.scale(d.value);
      const theta = i * (2 * Math.PI / this.radarChartConfig.numofSide);

      points.push(
        {
          ...this.generatePoint({ length: len , angle: theta }),
          value: d.value
        });
    });

    const group = this.parentGroupElem.append("g") //.attr("class", "shape");

    this.drawPath([...points, points[0]], group, {stroke: this.radarChartConfig.dataStrokeColor, fill: this.radarChartConfig.dataFillColor});
    this.drawCircles(points);
  };

  /**
   * draw axis and show ticks
   */
  drawAxisAndTicks = () => {
    const groupL = this.parentGroupElem.append("g").attr("class", "tick-lines");
    if (this.radarChartConfig.drawYAxis) {
      const point = this.generatePoint({ length: this.r_0 , angle: 0 });
      this.drawPath([this.lastPolygonPoints, point], groupL);
    }
    if (this.radarChartConfig.drawTicks) {
      const groupT = this.parentGroupElem.append("g").attr("class", "ticks");
      const ticks = this.genTicks();
      ticks.forEach((d, i) => {
          const r = (i / this.radarChartConfig.numofSide) * this.r_0;
          const p = this.generatePoint({ length: r , angle: 0 });
          const points =
            [
              p,
              {
                ...p,
                x: p.x - 10
              }

            ];
          if (this.radarChartConfig.drawYAxis) {
            this.drawPath(points, groupL);
          }
          if (!this.radarChartConfig.startGridLinesFromCenter && i !== 0)
            this.drawText(d, p, true, groupT);
      });
    }

  };

  /**
   * write labels for the data points
   * @param dataset 
   */
  drawLabels = (dataset) => {
    const groupL = this.parentGroupElem.append("g")  //.attr("class", "labels");
    for (let vertex = 0; vertex < this.radarChartConfig.numofSide; vertex++) {
      const angle = vertex * this.polyangle;
      const label = dataset[vertex].name;
      const point = this.generatePoint( {length: (0.8 * (this.radarChartConfig.size / 2) + 5), angle});
      this.drawText(label, point, false, groupL);
    }
  };
 

}
