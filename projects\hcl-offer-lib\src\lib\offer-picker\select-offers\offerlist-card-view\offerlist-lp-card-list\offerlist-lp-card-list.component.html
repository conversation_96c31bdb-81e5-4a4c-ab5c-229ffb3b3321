<div class="offerlist-lp-card-list-container">
    <cdk-virtual-scroll-viewport #scrollViewport class="h-100" itemSize="170">
     <div class="w-100" *cdkVirtualFor="let row of offerlistResponseData?.content">
        <div *ngFor="let offerlist of row"  [ngClass]="{'selected-item': isSelectedItem(offerlist)}"  class="ol-lp-result-item" >
            <span class="overlay-container"></span>
            <div class="actions-btn-selector" (click)="$event.stopPropagation();">
                <div class="select-offer">
                    <hcl-checkbox [config]="setAssetCheckboxConf(offerlist)"
                        (selectionChanged)="offerlistCheckboxClicked($event, offerlist)">
                    </hcl-checkbox>
                </div>
            </div>

            <div class="metadata" (click)="offerlistClicked(offerlist)">
                <p class="ol-name ellipsis" hclTooltip="{{offerlist.displayName}}">
                    {{offerlist.displayName}}</p>

                <div class="offer-count ellipsis">
                    <ng-container *ngIf="offerlist.count > 1">
                        <span hclTooltip="{{translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')}}">
                            {{offerlist.count}} {{translate.instant('LIST_OFFERS_AND_OFFER_LISTS.TITLES.OFFERS')}}
                        </span>
                    </ng-container>

                    <ng-container *ngIf="offerlist.count < 2">
                        <span hclTooltip="{{translate.instant('LABELS.OFFER')}}">
                            {{offerlist.count}} {{translate.instant('LABELS.OFFER')}} </span>
                    </ng-container>
                </div>

                <div class="status-type-approval">
                    <div class="d-flex align-items-center">
                        <div class="status" [ngClass]="getStatusClass(offerlist)">
                            <span hclTooltip="{{getStatusCopy(offerlist)}}">
                                {{getStatusCopy(offerlist)}}
                            </span>
                        </div>
                        <p class="ol-type" hclTooltip="{{returnOfferListType(offerlist.type)}}">
                            {{returnOfferListType(offerlist.type)}}</p>
                    </div>

                </div>
            </div>
            <div class="devider"></div>
            <div class="offers-section">
                <ng-container *ngIf="!offerlist.offers">
                    <div class="offer-image-container">
                        <div class="offer-image">
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ 'border-radius': '0', height: '40px', 'margin-bottom':'0', display:'block' } }">
                            </hcl-skeleton-loader>
                        </div>
                        <div class="offer-image">
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ 'border-radius': '0', height: '40px', 'margin-bottom':'0', display:'block' } }">
                            </hcl-skeleton-loader>
                        </div>
                        <div class="offer-image">
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ 'border-radius': '0', height: '40px', 'margin-bottom':'0', display:'block' } }">
                            </hcl-skeleton-loader>
                        </div>
                        <div class="offer-image">
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ 'border-radius': '0', height: '15px', 'margin-top':'12px', display:'block' } }">
                            </hcl-skeleton-loader>
                        </div>
                    </div>
                </ng-container>
                <ng-container *ngIf="offerlist.offers">
                    <div class="offer-image-container">
                        <div class="no-offers" *ngIf="!offerlist.offers.length">
                            {{'MESSAGES.NO_OFFERS_TO_SHOW' | translate}}
                        </div>
                        <ng-container *ngIf="offerlist.offers.length">
                            <ng-container *ngFor="let offer of offerlist.offers">
                                <div class="offer-image">
                                    <div class="img-wrapper" (click)="openOffersDetailPane(offerlist)">
                                        <ng-container *ngIf="offer.thumbnailProperties?.url && !offer.isImageLoaded">
                                            <hcl-skeleton-loader
                                                [config]="{ contentLoading: true , animation:'progress', theme:{ 'border-radius': '0', height: '40px', 'margin-bottom':'0', display:'block' } }">
                                            </hcl-skeleton-loader>
                                        </ng-container>
                                        <img *ngIf="offer.thumbnailProperties?.url" #offerThumbnail
                                            [src]="offer.thumbnailProperties.url"
                                            (imageLoaded)="updateOfferThumbnailState($event, offer, offerThumbnail)"
                                            default="" title="" titleText="" />

                                        <ng-container *ngIf="offer.brokenThumbnail">
                                            <svg width="54" height="40" xmlns="http://www.w3.org/2000/svg">
                                                <g fill="none" fill-rule="evenodd">
                                                    <path fill="#D8D8D8" d="M0 0h54v40H0z" />
                                                    <path
                                                        d="m30.83 23.83-2.247-2.247-1.12-1.12-3.38-3.38-.587-.587-.327-.327a.415.415 0 0 0-.588.588l.669.672v5.488c0 .458.375.833.833.833h5.488l.668.668a.418.418 0 0 0 .59 0 .415.415 0 0 0 .001-.588Zm-6.747-.913v-4.655l2.85 2.85-.35.438-.833-1.133-1.25 1.666h3.404l.834.834h-4.655Zm1.18-5.834-.834-.833h5.488c.458 0 .833.375.833.833v5.488l-.833-.834v-4.654h-4.655Z"
                                                        fill="#959595" />
                                                </g>
                                            </svg>
                                        </ng-container>

                                        <ng-container *ngIf="!offer.thumbnailProperties?.url">
                                            <svg width="54" height="40" xmlns="http://www.w3.org/2000/svg">
                                                <g fill="none" fill-rule="evenodd">
                                                    <path fill="#6C7794" d="M0-2h60.789v42H0z" />
                                                    <path
                                                        d="M26.323 8.514a1.086 1.086 0 0 1 1.776 0l.894 1.306c.306.447.916.592 1.402.334l1.421-.756c.669-.356 1.496.063 1.573.796l.162 1.558c.056.532.526.934 1.08.923l1.624-.032c.763-.015 1.294.727 1.009 1.41l-.607 1.453a1.012 1.012 0 0 0 .512 1.302l1.452.7c.683.328.796 1.223.214 1.7L37.6 20.222a1.001 1.001 0 0 0-.174 1.383l.949 1.27c.446.597.115 1.44-.63 1.602l-1.583.344c-.542.117-.899.616-.82 1.146l.228 1.55c.108.73-.592 1.327-1.33 1.136l-1.567-.406c-.536-.138-1.092.143-1.278.648l-.545 1.475c-.256.694-1.163.91-1.724.41l-1.193-1.062a1.093 1.093 0 0 0-1.443 0l-1.193 1.062c-.56.5-1.468.284-1.724-.41l-.545-1.475c-.186-.505-.742-.786-1.278-.648l-1.568.406c-.737.191-1.437-.407-1.329-1.136l.229-1.55c.078-.53-.28-1.03-.82-1.146l-1.584-.344c-.745-.162-1.076-1.005-.63-1.602l.95-1.27a1.001 1.001 0 0 0-.175-1.383l-1.236-1.014a1.008 1.008 0 0 1 .214-1.7l1.452-.7c.497-.239.72-.806.512-1.302l-.607-1.453c-.285-.683.246-1.425 1.01-1.41l1.622.032c.555.011 1.025-.39 1.08-.923l.163-1.558c.077-.733.904-1.152 1.573-.796l1.421.756a1.087 1.087 0 0 0 1.402-.334l.895-1.306Z"
                                                        fill="#FFF" fill-rule="nonzero" />
                                                    <path
                                                        d="m22.844 19.627 4.05-4.05a.894.894 0 0 1 .635-.261h3.15c.495 0 .9.405.9.9v3.15a.898.898 0 0 1-.266.639l-4.05 4.05a.894.894 0 0 1-1.269-.005l-3.15-3.15a.88.88 0 0 1-.265-.634c0-.248.103-.477.265-.64Zm7.235-2.311a.5.5 0 1 0 .001-.999.5.5 0 0 0-.001.999Z"
                                                        fill="#6C7794" fill-rule="nonzero" />
                                                </g>
                                            </svg>
                                        </ng-container>
                                    </div>
                                </div>
                            </ng-container>
                            <div class="more-btn" (click)="openOffersDetailPane(offerlist)" *ngIf="offerlist.count > 3">
                                <span hclTooltip="{{translate.instant('BUTTONS.PLUS_MORE',
                                {offerCount: (offerlist.count - 3)})}}">{{translate.instant('BUTTONS.PLUS_MORE',
                                    {offerCount: (offerlist.count - 3)})}}</span>
                            </div>

                        </ng-container>
                    </div>
                </ng-container>

            </div>
        </div>
    </div>
        <ng-container *ngIf="isDataLoading">
            <ng-container *ngFor="let row of loaderItems">
                <div>
                    <div class="ol-lp-result-item skeleton-item" *ngFor="let item of loaderItems">
                        <hcl-skeleton-loader
                            [config]="{ contentLoading: true, animation:'progress', theme:{ 'border-radius': '0', height: '95px', 'margin-bottom':'15px', display:'block' } }">
                        </hcl-skeleton-loader>
                        <div class="metadata">
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ width: '94px', height: '13px', 'margin-bottom':'11px', display:'block' } }">
                            </hcl-skeleton-loader>
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ width: '105px', 'margin-bottom':'9px', height: '7px', display:'block'  } }">
                            </hcl-skeleton-loader>
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ width: '81px',  height: '7px', display:'block' } }">
                            </hcl-skeleton-loader>
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ width: '54px', 'margin-bottom':'10px', height: '7px', display:'block' } }">
                            </hcl-skeleton-loader>
                            <hcl-skeleton-loader
                                [config]="{ contentLoading: true, animation:'progress', theme:{ 'border-radius': '6px', width: '31px', height: '13px', display:'block' } }">
                            </hcl-skeleton-loader>
                        </div>
                    </div>
                </div>
            </ng-container>         
        </ng-container>
    </cdk-virtual-scroll-viewport>

    <hcl-side-bar *ngIf="addSideBarToDom">
        <hcl-offerlist-lp-offers-details [offerlistInfo]="offerlistInfo" (closeSidebar)="closeSidebar()">
        </hcl-offerlist-lp-offers-details>
    </hcl-side-bar>
</div>