<div class="hcl-dataGridDemo-container">
    <hcl-button class="mx-2" [config]="getScrollIndexConf" (onclick)="getScrollIndex($event)">
    </hcl-button>
    <hcl-button class="mx-2" [config]="selectAllConf" (onclick)="selectAll($event)">
    </hcl-button>
    <hcl-button class="mx-2" [config]="deSelectAllConf" (onclick)="deSelectAll($event)">
    </hcl-button>
    <hcl-data-grid-v2 #grid [config]=conf (gridReady)="gridReady($event)" >
      <ng-template hclTemplate hclTemplateName="linearChartTemplate" type="cell-renderer" let-cell>
      <hcl-linear-gauge-chart-v2 [config]="linearGuageChartConfig"></hcl-linear-gauge-chart-v2>
      </ng-template>
      <ng-template hclTemplate hclTemplateName="donutChartTemplate" type="cell-renderer" let-cell>
        <div id="d3_donut_chart1" class="chart-component"></div>
        <hcl-donut-chart-v2 [chartConfig]="donutChartConfig"></hcl-donut-chart-v2>
        </ng-template>
    </hcl-data-grid-v2>
  </div>
  <div class="drop-col" (dragover)="onDragOver($event)" (drop)="onDrop($event)">
    <div id="eDropTarget" class="drop-target"> ==&gt; Drop to here </div>
    <div id="eJsonDisplay" class="json-display"></div>
  </div>
  <!-- <hcl-linear-gauge-chart-v2 [config]="linearGuageChartConfig" [data]="linearGuageChartData"></hcl-linear-gauge-chart-v2> -->