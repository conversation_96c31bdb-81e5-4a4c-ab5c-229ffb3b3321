<ul class="flex flex-wrap list-group list-group-horizontal comm-days">
  <ng-container *ngFor="let item of communicationListDays">
    <li
      class="list-group-item"
      [ngClass]="{ selected: item.selected, disabled: item.disable }"
      (click)="selectCD(item)"
    >
      <span>{{ item.localeDay }}</span>
      <i class="d-none hcl-icon-succsess"></i>
    </li>
  </ng-container>
</ul>
<!-- if some want to use vertical bar then they can use by changing line number 1 with bootstrap class
list-group-vertical -->
