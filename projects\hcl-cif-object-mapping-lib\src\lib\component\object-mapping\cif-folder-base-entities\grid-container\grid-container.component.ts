import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { InputConfig } from 'hcl-angular-widgets-lib';
import { DataGridColumnConf, DataGridConf, DataGridPagination, DataGridV2Component, } from 'hcl-data-grid-lib';
import * as _ from 'lodash';

@Component({
  selector: 'hcl-grid-container',
  templateUrl: './grid-container.component.html',
  styleUrls: ['./grid-container.component.scss']
})
export class GridContainerComponent implements OnInit {

  @ViewChild('grid') set content(content: DataGridV2Component) {
    if (content) { // initially setter gets called with undefined
      this.gridInstance = content;
    }
  }
  @Input() config: any;
  @Input() folderPanelState: string;
  @Input() selectedFolderDatails: any;
  @Output() selectedCategory: EventEmitter<any> = new EventEmitter<any>();

  gridConf: DataGridConf;
  gridApi: any;
  gridInstance: DataGridV2Component;
  searchBoxConfig: InputConfig;
  paginatorConfig: DataGridPagination;

  selectedFolderId: number;
  selectedFolderLabel: any;
  selectedRow = null;
  readyToRenderGrid = false;
  isSearchOn = false;
  selectedItemLable: string;

  constructor() { }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes.folderPanelState && changes.folderPanelState.currentValue) {
      this.gridInstance.resizeColumnsToFit();
    }

    if (changes.selectedFolderDatails && changes.selectedFolderDatails.currentValue) {
      if (this.readyToRenderGrid) {
        this.updateGridData(changes.selectedFolderDatails.currentValue);
      } else {
        this.initializeGrid(changes.selectedFolderDatails.currentValue);
      }
    }
  }

  ngOnInit(): void {
    this.setConfiguration();
    if (this.config.previousSelectedItemLable) {
      this.selectedRow = this.config.previousSelectedCategoryId;
      this.selectedItemLable = this.config.previousSelectedItemLable;
    }
  }

  initializeGrid(folderDetails: any) {
    this.selectedFolderId = folderDetails.folderId;
    this.selectedFolderLabel = this.config.applicationService.selectedFolderNAme;
    this.gridConf.data = [];
    if (!this.config.isPaginated) {
      this.config.applicationService.getCategoriesByFolderId(this.config.cifRepository, folderDetails.folderId).subscribe((data: any) => {
        this.gridConf.isClientSideRowModel = true;
        if (data.length) {
          Object.keys(data[0].additionalFields).forEach((key: string) => {
            const columnConf: DataGridColumnConf = {
              field: key,
              header: key,
              colId: key,
              autoResizeToFit: true,
              tooltip: {
                getTooltip: (category: any) => {
                  return category[key];
                }
              },
              dataFormatter: (category: any) => {
                return category.data[key];
              }
            };
            this.gridConf.columns.push(columnConf);
          });
          this.readyToRenderGrid = true;
        }

        data.forEach(category => {
          this.gridConf.data.push({ id: category.id, label: category.label, ...category.additionalFields });
        });

        if (this.gridApi) {
          this.gridApi.setRowData(this.gridConf.data);
        }
      }, (error) => this.config.applicationService.handleServerError(error.message || this.config.translations.generalError));
    } else if (this.config.isPaginated) {
      this.config.applicationService.getContentsForSelectedFolder(this.config.cifRepository, folderDetails.folderId,
        this.config.assetContext, 0, this.paginatorConfig.rowsPerPage).subscribe((data: any) => {
          this.gridConf.columns = [];
          data.fieldsMetadata.forEach((metadata: any) => {
            const columnConf: DataGridColumnConf = {
              field: metadata.name,
              header: metadata.localizedName,
              colId: metadata.name,
              autoResizeToFit: true,
              // rendererTemplateName: metadata.name,
              useDefaultRenderer: true,
              sortable: metadata.sortable,
              // minWidth: 150,
              tooltip: {
                getTooltip: (category: any) => {
                  return category.data.additionalFields[metadata.name];
                }
              },
              dataFormatter: (category: any) => {
                return category.data.additionalFields[metadata.name];
              }
            };
            this.gridConf.columns.push(columnConf);
          });

          this.gridConf.dataUrl = this.config.assetContext === 'content' ?
            `${this.config.cifBaseUrl}/api/AssetPicker/${this.config.cifRepository}/folders/${this.selectedFolderId}/contents?type=content` :
            `${this.config.cifBaseUrl}/api/AssetPicker/${this.config.cifRepository}/categories?folderId=${this.selectedFolderId}`;
          this.gridConf.rowSelectMode = 'single';
          this.gridConf.pagination = this.paginatorConfig;
          this.gridConf.noRowsTemplate = this.config.translations.noResultFound;
          this.gridConf.suppressRowClickSelection = true;
          this.gridConf.queryParams = { sort: 'code,ASC' };
          this.gridConf.isClientSideRowModel = false;
          this.readyToRenderGrid = true;
        }, (error) => this.config.applicationService.handleServerError(error.message || this.config.translations.generalError));
    } else {
      this.gridApi.setRowData([]);
    }
    // this.selectedRow = null;
    // this.selectedCategory.emit(null);
  }

  updateGridData(folderDetails: any) {
    this.selectedFolderId = folderDetails.folderId;
    this.selectedFolderLabel = this.config.applicationService.selectedFolderNAme;
    if (!this.config.isPaginated) {
      this.gridConf.data = [];
      this.config.applicationService.getCategoriesByFolderId(this.config.cifRepository, folderDetails.folderId).subscribe((data: any) => {
        data.forEach(category => {
          this.gridConf.data.push({ id: category.id, label: category.label, ...category.additionalFields });
        });

        if (this.gridApi) {
          this.gridApi.setRowData(this.gridConf.data);
        }
      }, (error) => this.config.applicationService.handleServerError(error.message || this.config.translations.generalError));

    } else if (this.config.isPaginated) {
      this.paginatorConfig.currentPageIndex = 0;

      this.gridConf.dataUrl = this.config.assetContext === 'content' ?
        `${this.config.cifBaseUrl}/api/AssetPicker/${this.config.cifRepository}/folders/${this.selectedFolderId}/contents?type=content` :
        `${this.config.cifBaseUrl}/api/AssetPicker/${this.config.cifRepository}/categories?folderId=${this.selectedFolderId}`;

      setTimeout(() => {
        this.gridInstance.refreshData();
      }, 500);
    }
  }


  onGridReady(data) {
    this.gridApi = data.params.api;

    if (!this.config.isPaginated) {
      if (this.selectedRow) {
        setTimeout(() => {
          this.gridInstance.selectIndex(_.findIndex(this.gridConf.data, (rowItem: any) => {
            return rowItem.id === this.selectedRow || rowItem.id === this.selectedRow?.data?.id;
          }), true);
        }, 300);
      }

      this.gridApi.setRowData(this.gridConf.data);
    }
  }

  /**
* A callback that will be called whenever data for the grid is loaded
*/
  gridDataLoaded(data) {
    if (this.selectedRow) {
      if (this.config.assetContext === 'content') {
        setTimeout(() => {
          this.gridApi.forEachNode((node) => {
            if (node.data.presentationDetails && (node.data.presentationDetails.multimedia.id === this.selectedRow
              || node.data.presentationDetails.multimedia.id === this.selectedRow?.data?.presentationDetails.multimedia.id)) {
              node.setSelected(true);
            }
          });
        }, 300);
      } else {
        setTimeout(() => {
          this.gridApi.forEachNode((node) => {
            if (node.data.id === this.selectedRow || node.data.id === this.selectedRow?.data?.id) {
              node.setSelected(true);
            }
          });
        }, 300);
      }
    }
  }

  setConfiguration() {
    this.paginatorConfig = {
      rowsPerPage: 10,
      pageSizeArray: [10, 20, 50, 100],
      optionLabels: [10, 20, 50, 100].map(option => option + ' ' + this.config.translations.rows),
      currentPageIndex: 0,
      rowsPerPageSuffix: this.config.translations.rows,
      total: this.config.translations.total,
      firstLabelString: this.config.translations.first,
      prevLabelString: this.config.translations.prev,
      nextLabelString: this.config.translations.next,
      lastLabelString: this.config.translations.last
    };

    this.gridConf = {
      scrollHeight: !this.config.isPaginated ? 475 : 390,
      isClientSideRowModel: true,
      columns: [
        {
          field: 'label',
          header: this.config.translations.name,
          colId: 'id',
          sortable: true,
          autoResizeToFit: true,
          minWidth: 150,
          tooltip: {
            getTooltip: (category: any) => {
              return category.label;
            }
          },
          dataFormatter: (category: any) => {
            return category.data.label;
          }
        }
      ],
      data: [],
      rowSelectMode: 'single',
      noRowsTemplate: this.config.translations.noRowsToShow,
      loadingTemplate: this.config.translations.loading,
      suppressRowClickSelection: true,
      suppressDragLeaveHidesColumns: true
    };

    this.searchBoxConfig = {
      formControlName: new UntypedFormControl(''),
      placeholder: this.config.translations.gridSearchPlaceholder,
      icon: 'hcl-icon-search',
      type: 'text',
      name: 'search',
      autofocus: false
    };

    this.gridConf.gridHeader = this.config.applicationService;
  }

  rowSelected() {
    const rowNode = this.gridApi.getSelectedNodes()[0];
    if (rowNode) {
      if ((this.config.assetContext === 'category' && (this.selectedRow === rowNode.data.id
        || this.selectedRow?.data?.id === rowNode.data.id)) || (this.config.assetContext === 'content' &&
          (this.selectedRow === rowNode.data.presentationDetails.multimedia.id ||
            this.selectedRow?.data?.presentationDetails.multimedia.id === rowNode.data.presentationDetails.multimedia.id))) {
      } else {
        this.selectedRow = rowNode;
        this.selectedCategory.emit(rowNode);
        this.selectedItemLable = this.config.assetContext === 'category' ? rowNode.data.label
          : rowNode.data.additionalFields?.entrySourceName || '';
      }
    } else {
      this.rowUnSelected();
    }
  }

  rowUnSelected() {
    if (this.selectedRow) {
      this.selectedRow = null;
      this.selectedItemLable = '';
      this.selectedCategory.emit(null);
    }
  }

  onFilterTextBoxChanged() {
    if (!this.isSearchOn) {
      this.isSearchOn = true;
      this.searchBoxConfig.icon = 'hcl-icon-close-x';
    } else {
      this.isSearchOn = false;
      this.searchBoxConfig.icon = 'hcl-icon-search';
      this.searchBoxConfig.formControlName.setValue('');
    }
    if (this.gridConf.isClientSideRowModel) {
      this.searchGrid();
    } else {
      // TODO - hanlde content server side search here
    }
  }

  searchGrid() {
    const customData = this.filterGridData(this.searchBoxConfig.formControlName.value, this.gridConf.data);
    this.gridApi.setRowData(customData);
    if (this.gridApi.rowModel.rowsToDisplay.length === 0) {
      this.gridApi.showNoRowsOverlay();
    } else {
      this.gridApi.hideOverlay();
    }
  }

  filterGridData(text: string, data: any[]): any[] {
    const collection = data;
    const results = _.filter(collection, (obj) => {
      return obj.label.toLowerCase().indexOf(text.toLowerCase()) !== -1;
    });
    return results;
  }


  onDataLoad(data) { }
}
