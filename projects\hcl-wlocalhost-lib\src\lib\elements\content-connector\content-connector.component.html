<div fxLayout class="content-connector-container">
  <div class="cc-parent">
    <ng-container *ngIf="block.options?.contentSourceType && block.options?.contentSourceInfo?.url">
      <div class="external-content-details">
        <div>{{ 'CONTENT_CONNECTOR.CONTENT.TITLE.EXTERNAL_CONTENT_SOURCE' | translate }}:</div> 
        <div [hclTooltip]="block.options?.contentSourceInfo?.url">
          {{ block.options?.contentSourceInfo?.url }}
        </div> 
      </div>
    </ng-container>
    <ng-container *ngIf="!block.options?.contentSourceInfo">
      <div class="external-content-label">
        {{ 'CONTENT_CONNECTOR.CONTENT.TITLE.EXTERNAL_CONTENT' | translate }}
      </div>
    </ng-container>
  </div>
</div>