// @import '@angular/material/theming';

ip-field {
  & > .cdk-drag-handle {
    position: absolute;
    left: 0px;
    transform: rotate(180deg);
    cursor: move;
    background-color: rgba($color: #3f51b5, $alpha: 0.5);
    color: white;
    border-radius: 50% 0 0 50%;
    opacity: 0;
    height: 30px;
    width: 30px;
    font-size: smaller;
    mat-icon {
      margin-top: -9px;
      margin-left: 6px;
      transform: rotate(180deg);
    }

    &:hover {
      background-color: #3f51b5;
    }
  }

  &:hover,
  &.cdk-drag-preview {
    .cdk-drag-handle {
      opacity: 1;
    }
  }
}

:host {
  display: inline-block;
  width: 100%;
  border: 1px solid #ccc;
  position: relative;
  place-content: center;
  // grid-template-columns: 1fr;

  & > .tools , .hiddenOn{
    direction: ltr;
    position: absolute;
    opacity: 0;
    left: 0;
    bottom: -25px;
    // transition: opacity 0.5s ease-in-out;
    // will-change: opacity;
    color: #ff4081;

    button {
      background-color: white;
      border-radius: 3px;
      height: 25px;
      width: 25px;
      line-height: 0;
    }
  }
  & > .hiddenOn{
    left: unset;
    right: -25px;
    bottom: unset;
    display: block!important;
    button {
      background-color: #ff4081;
      color: white;
    }
  }
  &.active {
    & > .hiddenOn{
      opacity: 1!important;
      button {
        background-color: #ff4081;
        color: white;
      }
    }
  }
  &:hover,
  &.cdk-drag-preview,
  &.cdk-drag-placeholder,
  &.active {
    box-shadow: 0 0px 1px 2px #ff4081;
    z-index: 1;
  }

  &:hover {
    & > .tools {
      // transform: translate(100%, -50%);
      opacity: 1;

      button {
        background-color: #ff4081;
        color: white;
      }
    }
  }

  &.active {
    & > .tools > .edit {
      display: none;
    }
  }

  // ::ng-deep .cdk-drag-placeholder {
  //   background-color: #3f51b5;
  //   opacity: 0.6;
  //   cursor: move;
  //   height: 10px;
  //   box-shadow: none;
  //   * {
  //     display: none;
  //   }
  // }

  // &.cols_2 {
  //   grid-template-columns: repeat(2, 6fr);
  // }

  // &.cols_3 {
  //   grid-template-columns: repeat(3, 4fr);
  // }

  // &.cols_4 {
  //   grid-template-columns: repeat(4, 3fr);
  // }

  // &.cols_12 {
  //   grid-template-columns: 7fr 5fr;
  // }

  // &.cols_21 {
  //   grid-template-columns: 5fr 7fr;
  // }

  .cdk-drop-list-dragging,
  .cdk-drop-list-receiving {
    // box-sizing: border-box;
    height: 100%;
  }

  // & > .cdk-drop-list-dragging {
  //   box-shadow: 0 0px 1px 2px #3f51b5;
  // }

  & > .column {
    box-sizing: border-box;
    // TODO
    // overflow-y: auto;
    // overflow-x: hidden; 

    &.empty {
      // min-height: 70px;
      // height: 70px;

      .empty-field {
        background-color: rgba(101, 99, 99, 0.21);
        height: 100%;
        min-height: 62px;
        display: flex;
        justify-content: center;
        place-items: center;
        text-align: center;
        flex-direction: column;
        color: rgba(101, 99, 99, 0.3);
        font-size: small;
      }

      &.cdk-drop-list-dragging {
        .empty-field {
          display: none;
        }
      }

      & > .cdk-drag-placeholder {
        min-height: 60px !important;
      }
    }

    // margin: 4px;
    // display: flex;
    // flex-direction: column;
    // background-clip: content-box;
    // will-change: box-shadow;
    // transition: box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
    // &.cdk-drop-list-dragging > *:not(.cdk-drag-placeholder) {
    //   transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    // }
  }
}
