<div class="main-padding rules-tab">
  <div class="no-rule-msg" *ngIf="!getRulesInfo || getRulesInfo?.length === 0">
    <div *ngIf="getCurrentElementType() === 'text' || getCurrentElementType() === 'text-field'">
      <ng-container *ngIf="ngb.isStaticLP else noRuleText">
        <div class="no-rule-msg">{{'RULE_BUILDER.RULES_NA_FOR_STATIC_LP' | translate }}</div>
      </ng-container>
      <ng-template #noRuleText>
        <div class="no-rule-msg">{{'messages.no-rule-text' | translate }}</div>
        <img class="toolbar-btn-image" src="{{dynamicToolbarBtn}}" />
      </ng-template>
    </div>
    <div *ngIf="getCurrentElementType() != 'text' && getCurrentElementType() != 'text-field'">
      <div class="no-rule-msg">{{ getInfoMsg() }}</div>
    </div>
  </div>

  <ng-container *ngIf="getCurrentElementType() !== 'text' && getCurrentElementType() !== 'text-field'">
    <ip-rule-display *ngIf="getRulesInfo?.length > 0" [ruleInfo]="getDefaultRule()"
      [selectedElement]="getCurrentElement()"></ip-rule-display>
    <div class="applied-rules" *ngIf="getRulesInfo?.length > 0">{{ 'ADD_CONTENT.LABEL.DYNAMIC_CONTENT' | translate }}
      ({{getRulesInfo?.length}}) </div>

    <ng-container *ngFor="let rule of getRulesInfo">
      <ip-rule-display [ruleInfo]="rule" [selectedElement]="getCurrentElement(rule)"
        (removeRule)="deleteRule($event)"></ip-rule-display>
    </ng-container>
    <div class="add-rule-button" [hidden]="hideAddRuleButton()">
      <button type="button" (click)="addRule()" color="primary" mat-stroked-button>
        {{ 'settings.add_rule' | translate }}
      </button>
      <div class="bottom-border"></div>
    </div>
  </ng-container>
  <ng-container *ngIf="getCurrentElementType() === 'text' || getCurrentElementType() === 'text-field'">
    <div *ngFor="let link of ngb.hyperlink">
      <ip-rule-display *ngIf="link?.rules?.length > 0" [ruleInfo]="getDefaultLinkRule(link.id)"
        [selectedElement]="getCurrentElement()"></ip-rule-display>
      <div class="applied-rules" *ngIf="getRulesInfo?.length > 0">{{ 'ADD_CONTENT.LABEL.DYNAMIC_CONTENT' | translate }}
        ({{link?.rules?.length}})</div>
      <ng-container *ngFor="let rule of link?.rules">
        <ip-rule-display [ruleInfo]="updateRuleWithLinkText(rule, link)" [selectedElement]="getCurrentElement()"
          (removeRule)="deleteRule($event)"></ip-rule-display>
      </ng-container>
      <div class="add-rule-button" [ngClass]="{ 'invisible': ngb.isStaticLP }">
        <button type="button" (click)="addRuleToHyperlink(link)" color="primary" mat-stroked-button>
          {{ 'settings.add_rule' | translate }}
        </button>
      </div>
      <div class="bottom-border"></div>
    </div>
  </ng-container>
</div>