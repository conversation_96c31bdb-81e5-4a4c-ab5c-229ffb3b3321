.offer-list-info-container {
  display: flex;
  height: 85vh;
  justify-content: space-around;
  min-height: 500px;
  min-width: 1200px;
  padding: 1rem;
  width: 100%;

  .chips-container {
    height: 25%;
    overflow-y: auto;
  }

  .queryView {
    height: 70%;
    overflow: auto;

    .query {
      margin-top: -17px;

      span {
        + span {
          color: #f5821e;
          font-family: Roboto;
          font-size: 12px;
          font-weight: bold;
          line-height: 24px;
        }
      }

      br {
        + span {
          color: #444444;
          font-family: Roboto;
          font-size: 14px;
          line-height: 24px;
        }
      }
    }
  }

  .field-title {
    color: #b8b7b7;
    font-family: <PERSON><PERSON>;
    font-size: 14px;
    line-height: 16px;
  }

  .field-title-folder {
    color: #b8b7b7;
    font-family: Roboto;
    font-size: 14px;
    line-height: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .sub-title1 {
      width: 150px;
    }
    .sub-title2 {
      width: 200px;
      color: #959595;
      font-family: <PERSON><PERSON>;
      font-size: 12px;
      letter-spacing: 0.32px;
      line-height: 14px;
    }
  }

  .deleted-folders {
    color: #b8b7b7;
    font-size: 14px;
    margin: 15px 0;
    display: flex;
    align-items: center;

    .hcl-icon-warning {
      color: #f24747;
      font-size: 18px;
      margin-right: 10px;
    }
  }

  .list-view {
    height: 100%;
    -webkit-transition: width 0.2s linear;
    transition: width 0.2s linear;

    .heading {
      align-items: center;
      background-color: #f5f5f5;
      box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
      display: flex;
      height: 54px;
      padding-left: 1rem;
      margin-bottom: 7px;
      .custom-icon {
        height: 30px;
        line-height: 6px;
      }

      .button-container {
        border-right: 1px solid #959595;
        width: 40px;
        text-align: center;

        i {
          color: #0078d8;

          &:hover {
            color: #f5821e;
          }
        }
      }

      .title {
        color: #6d7692;
        font-family: Montserrat;
        font-size: 20px;
        font-weight: bold;
        line-height: 24px;
      }

      .view-controls {
        display: flex;
        align-items: center;
        margin-left: 10px;

        [class^="hcl-icon-"] {
          font-size: 24px;
          color: #959595;
          cursor: pointer;
          &.active-view {
            color: #f5821e;
            cursor: default;
          }
        }
      }
    }

    .vertical-label > span {
      writing-mode: vertical-lr;
      transform: rotate(180deg);
      margin-top: 15px;
      color: #6d7692;
      font-family: Montserrat, sans-serif;
      font-size: 20px;
      margin-left: 7px;
      font-weight: 600;

      &.reset-transform-rotate {
        transform: rotate(0deg);
      }
    }

    .content {
      height: calc(100% - 75px);
      background-color: #f5f5f5;
      box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
    }
  }

  .static-list {
    flex: 1 1;
    margin-right: 1rem;
    width: 68%;
  }

  .criteria {
    width: 32%;
  }

  .basic-options {
    display: flex;
    padding: 1rem;
    align-items: center;

    .field {
      display: flex;
      width: 33%;
      font-family: Roboto;
      font-size: 14px;
      letter-spacing: 0;
      line-height: 16px;
      padding-right: 40px;

      .field-title {
        color: #b8b7b7;
        margin-right: 10px;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: none;
        padding: 0;
        width: 25%;
      }

      .field-value {
        color: #15161c;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        padding: 0;
        width: 70%;
      }
    }
  }

  .grid-container {
    height: 100%;
    margin-top: 1rem;
  }

  .hcl-grid-container {
    height: 83%;
  }

  .offer-state {
    width: 110px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    .state-label {
      margin: 0 0 0 5px;
      font-size: 14px;
      width: 95px;
    }
  }
  .hcl-icon-down-without-border {
    font-size: 8px;
    display: inline-block;
    width: 15px;
  }
 
}

.ol-action-container {
  display: flex;
  justify-content: flex-end;
  padding: 10px 15px 0 0;
}

.menu-header {
  height: 20px;
  font-size: 12px;
  background-color: #f5f5f5;
  padding: 3px 6px !important;
  line-height: normal;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  pointer-events: none;
  text-overflow: ellipsis;
  max-width: 140px !important;
  width: 100%;
}

.context-menu-item,
.menu-item {
  padding: 0 16px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px !important;
  width: 100%;
}

.ol-tabs-container .mat-tab-body-content {
  overflow: hidden;
  .mat-tab-body-wrapper,
  .mat-tab-group {
    height: 90vh;
  }
}
.offer-menu-list button.mat-menu-item {
  padding: 0 !important;
  max-height: 50px;
  height: auto;
  .menuDiv.offer-menu-row {
    padding: 0;
  }
}