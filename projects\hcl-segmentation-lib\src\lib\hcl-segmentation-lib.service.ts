import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import {
  AudienceApplicationConfig,
  AudienceTableDetails,
  BaseTableMetadata,
  DistributionData,
  SegmentCentralApplicationConfig,
  SegmentObj,
  SegmentRunStats,
  SegmentSubType,
  StatsData,
  UserConfig,
} from "./models/segment";

@Injectable({
  providedIn: "root",
})
export class HclSegmentationLibService {
  private _segmentBaseUrl: string;
  private _audienceBaseUrl: string;
  private _segmentHeaders: HttpHeaders;
  private _audienceHeaders: HttpHeaders;

  constructor(private http: HttpClient) { }

  get segmentBaseUrl() {
    return this._segmentBaseUrl;
  }

  set segmentBaseUrl(url: string) {
    this._segmentBaseUrl = url;
  }

  get segmentHeaders(): HttpHeaders {
    return this._segmentHeaders;
  }

  set segmentHeaders(value: HttpHeaders) {
    this._segmentHeaders = value;
  }

  get audienceHeaders(): HttpHeaders {
    return this._audienceHeaders;
  }

  set audienceHeaders(value: HttpHeaders) {
    this._audienceHeaders = value;
  }

  get audienceBaseUrl() {
    return this._audienceBaseUrl;
  }

  set audienceBaseUrl(url: string) {
    this._audienceBaseUrl = url;
  }

  public getAudienceToken(): Observable<AudienceApplicationConfig> {
    return this.http.get<AudienceApplicationConfig>(
      this.segmentBaseUrl +
      "/api/segmentcentral/v1/audience-central-app-config",
      { headers: this._segmentHeaders, withCredentials: false }
    );
  }

  public getUserConfig(): Observable<UserConfig> {
    return this.http.get<UserConfig>(
      this.segmentBaseUrl + "/api/segmentcentral/v1/user-config",
      { headers: this._segmentHeaders, withCredentials: false }
    );
  }

  public getSegmentCentralApplicationConfig(): Observable<SegmentCentralApplicationConfig> {
    return this.http.get<SegmentCentralApplicationConfig>(
      this.segmentBaseUrl + "/api/segmentcentral/v1/segmentation-app-config",
      { headers: this._segmentHeaders, withCredentials: false }
    );
  }

  public getAudienceTableMetadata(
    jndiName: string,
    tableName: string
  ): Observable<BaseTableMetadata> {
    return this.http.get<BaseTableMetadata>(
      this.audienceBaseUrl +
      `/api/audiencecentral/v1/db_objects/${jndiName}/tables/${tableName}/metadata`,
      { headers: this.audienceHeaders, withCredentials: false }
    );
  }

  public getAudienceTableDetails(id: number): Observable<AudienceTableDetails> {
    return this.http.get<AudienceTableDetails>(
      `${this.audienceBaseUrl}/api/audiencecentral/v1/audience-levels/tables/${id}`,
      { headers: this.audienceHeaders, withCredentials: false }
    );
  }

  public getAudienceBaseTableList(audiece_level: string) {
    return this.http.get(
      this.audienceBaseUrl +
      "/api/audiencecentral/v1/audience-levels/" +
      audiece_level +
      "/tables/profiles/basic-details",
      { headers: this.audienceHeaders, withCredentials: false }
    );
  }

  public getAudienceMappedColumns(mappedColumns: any[], metadata: any[]) {
    const resultList = [];
    mappedColumns.forEach((mappedColumn) => {
      const metadataColumn = metadata.find(
        (column) => column.name === mappedColumn.name
      );
      metadataColumn &&
        resultList.push({
          ...metadataColumn,
          displayName: mappedColumn.displayName,
        });
    });
    return resultList;
  }

  public getTestRunStatistics(
    segmentSubType: SegmentSubType
  ): Observable<SegmentRunStats> {
    return this.http.post<SegmentRunStats>(
      this.segmentBaseUrl + "/api/segmentcentral/v1/segments/test",
      segmentSubType,
      {
        headers: this._segmentHeaders,
        withCredentials: false,
      }
    );
  }

  /**
   * API interaction with server to create new segment
   * @body segmentData
   * @returns Observable<SegmentObj>
   */
  public createSegment(segmentData: SegmentObj): Observable<SegmentObj> {
    return this.http.post<SegmentObj>(
      this.segmentBaseUrl + "/api/segmentcentral/v1/segments",
      segmentData,
      { headers: this.segmentHeaders }
    );
  }

  public saveAndPublishCreateSegment(
    segmentData: SegmentObj
  ): Observable<SegmentObj> {
    return this.http.post<SegmentObj>(
      this.segmentBaseUrl +
      "/api/segmentcentral/v1/segments/actions/save-and-publish",
      segmentData,
      { headers: this.segmentHeaders }
    );
  }

  getfolderPermissions(id: any) {
    return this.http.get<{ name: string; permitted: boolean }[]>(
      this.segmentBaseUrl + "/api/segmentcentral/v1/folder/list-permissions",
      { headers: this.segmentHeaders, params: { folderId: id } }
    );
  }

  public getFolderInfo(id) {
    const headersCopy = this.segmentHeaders.append("ignoreLoader", "true");
    return this.http.get<any>(
      this.segmentBaseUrl + "/api/segmentcentral/v1/folders/" + id,
      { headers: headersCopy }
    );
  }

  // Query builder transformation functions

  public generateQueryJson(
    parentQueryData: any,
    tableName: string,
    groupDisplayOrder?: number
  ) {
    let finalQueryJson: any = { logicalOp: null };
    const logicalOperator = parentQueryData.AND ? "AND" : "OR";
    const conditionsData = parentQueryData && parentQueryData[logicalOperator];
    if (conditionsData && conditionsData.length) {
      finalQueryJson.logicalOp =
        conditionsData.length > 1 ? logicalOperator : null;

      for (let i = 0; i < conditionsData.length; i++) {
        if (
          conditionsData[i] &&
          ((conditionsData[i].AND && conditionsData[i].AND.length) ||
            (conditionsData[i].OR && conditionsData[i].OR.length))
        ) {
          const nestedData = this.generateQueryJson(
            conditionsData[i],
            tableName,
            i
          );

          finalQueryJson.nestedConditions = finalQueryJson.nestedConditions
            ? finalQueryJson.nestedConditions
            : [];
          finalQueryJson.nestedConditions.push(nestedData);
        } else if (conditionsData[i] && conditionsData[i].param) {
          finalQueryJson.conditions = finalQueryJson.conditions
            ? finalQueryJson.conditions
            : [];
          finalQueryJson.conditions.push(
            this.getSingleRulQueryJson(conditionsData[i], tableName, i)
          );
        }
      }
    } else if (parentQueryData && parentQueryData.param) {
      finalQueryJson.conditions = finalQueryJson.conditions
        ? finalQueryJson.conditions
        : [];
      finalQueryJson.conditions.push(
        this.getSingleRulQueryJson(parentQueryData, tableName)
      );
    }

    finalQueryJson.displayOrder = groupDisplayOrder || 0;
    return finalQueryJson;
  }

  private getSingleRulQueryJson(
    singleQueryData: any,
    tableName: string,
    ruleDisplayOrder?: number
  ) {
    let ruleValue;
    if (singleQueryData.datatype === "Date") {
      if (
        singleQueryData.opt === "in" ||
        singleQueryData.opt === "notin" ||
        singleQueryData.opt === "between"
      ) {
        ruleValue = [];
        singleQueryData.value.forEach((item) => {
          ruleValue.push(this.formatDateObjToString(new Date(item)));
        });
      } else {
        ruleValue = this.formatDateObjToString(new Date(singleQueryData.value));
      }
    } else {
      if (
        singleQueryData.opt === "in" ||
        singleQueryData.opt === "notin" ||
        singleQueryData.opt === "between"
      ) {
        ruleValue = [];
        singleQueryData.value.forEach((item) => {
          ruleValue.push(singleQueryData.datatype === "String" ? item : +item);
        });
      } else {
        ruleValue =
          singleQueryData.datatype === "String"
            ? singleQueryData.value
            : +singleQueryData.value;
      }
    }
    return {
      param: `${tableName}.${singleQueryData.param}`,
      op: singleQueryData.opt,
      value: ruleValue,
      displayOrder: ruleDisplayOrder || 0,
    };
  }

  public formatDateObjToString(dateObj: Date) {
    // yyyy-mm-dd hh24:mi:ss:SSSSSS
    let resultString;
    resultString = `${dateObj.getFullYear()}`;
    resultString += `-${this.addZeroToSingleDigits(dateObj.getMonth() + 1)}`;
    resultString += `-${this.addZeroToSingleDigits(dateObj.getDate())}`;
    resultString += ` ${this.addZeroToSingleDigits(dateObj.getHours())}`;
    resultString += `:${this.addZeroToSingleDigits(dateObj.getMinutes())}`;
    resultString += `:${this.addZeroToSingleDigits(dateObj.getSeconds())}`;
    return resultString;
  }

  public addZeroToSingleDigits(digit: number) {
    if (digit < 10) {
      return "0" + digit;
    } else {
      return digit;
    }
  }

  /**
   * API interaction with server to fetch tags suggestions
   * @param searchKey
   * @returns string[]
   */
  public getUsageCategoryTags(searchKey: string): Observable<string[]> {
    const headersCopy = this.segmentHeaders.append("ignoreLoader", "true");
    return this.http.get<string[]>(
      this.segmentBaseUrl + `/api/segmentcentral/v1/search-tags?searchKey=${searchKey}`,
      {
        headers: headersCopy
      }
    );
  }


  public getStatsForSelectedColumns(tableId: number, fieldId: number): Observable<StatsData> {
    return this.http.get<StatsData>(
      `${this.audienceBaseUrl}/api/audiencecentral/v1/audience-levels/tables/${tableId}/fields/${fieldId}/stats`,
      { headers: this.audienceHeaders, withCredentials: false }
    );
  }

  public getDistributionForSelectedColumns(tableId: number, fieldId: number, numberOfBins: number): Observable<DistributionData> {
    this.audienceHeaders = this.audienceHeaders.append("skipError", "true");
    return this.http.get<DistributionData>(
      `${this.audienceBaseUrl}/api/audiencecentral/v1/audience-levels/tables/${tableId}/fields/${fieldId}/distribution?binCount=${numberOfBins}`,
      { headers: this.audienceHeaders, withCredentials: false }
    );
  }
}
