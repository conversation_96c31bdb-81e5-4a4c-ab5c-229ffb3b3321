@use '../../assets/scss/variables' as *;

.donut-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .text-section {
    position: absolute;
    top: 50%;
    left: 20px;
    z-index: 1;
    max-width: 200px;

    &.with-count {
      top: 40%;
    }
  }

  .chart-message {
    color: #6d7692;
    font-family: $font-secondary;
    font-size: 12px;
    letter-spacing: 0.4px;
    line-height: 14px;

    .count {
      color: #6d7692;
      font-family: $font-primary;
      font-size: 24px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 30px;
      display: block;
    }
  }

  .chart-section {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 20%;

    .donut-chart-text {
      display: flex;
      flex-direction: column;
      height: 60px;
      position: absolute;
      bottom: 0px;
      left: calc(50% - 80px);
      width: 150px;
      color: #444444;
      font-family: $font-secondary;
      font-size: 14px;
      font-weight: normal;
      letter-spacing: 0;
      line-height: 20px;
      text-align: center;
      overflow: hidden;

      &.percent {
        bottom: 30px;
        color: #6d7692;
        font-family: $font-primary;
        font-size: 20px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 24px;
      }

      .display-number-of-records {
        height: 40px;
        width: 159px;
        color: #6d7692;
        font-family: $font-primary;
        font-size: 22px;
        line-height: 40px;
        font-weight: 500;
      }

      .multiline-ellipsis {
        -webkit-box-orient: vertical;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
      }
    }
  }
}
