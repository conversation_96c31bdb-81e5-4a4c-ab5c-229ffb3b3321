import { Component, OnInit, ViewChild } from '@angular/core';
import { SliderConfig } from 'projects/hcl-angular-widgets-lib/src/lib/component/slider/slider.config';
import { HalfGaugeComponent } from 'projects/hcl-angular-charts-lib/src/lib/component/half-gauge/half-gauge.component';
import { HalfGaugeChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/half-gauge/half-gauge';

@Component({
  selector: 'app-half-gauge-demo',
  templateUrl: './half-gauge-demo.component.html',
  styleUrls: ['./half-gauge-demo.component.scss']
})
export class HalfGaugeDemoComponent implements OnInit {
  @ViewChild(HalfGaugeComponent) halfGauge: HalfGaugeComponent;
  constructor() { }
  sliderConfig: SliderConfig = {
    disabled: false,
    invert: false,
    max: 280,
    min: 0,
    step: 1,
    thumbLabel: true,
    value: 30,
    vertical: false,
    tickInterval: 0
  };

  halfGaugeChartConfigLarge: HalfGaugeChartConfig = {
    chartID:"chart2",
    minReading:0,
    maxReading:100,
    reading: this.sliderConfig.value,
    readingColor:"#FFFFFF",
    readingFont:"ariel",
    hideReading:false,
    innerCircleColor: "#0066b3",
    needleColor: "#0066b3",
    width: 280,
    startColor: '#00FFFF',
    endColor: '	#00ff00',
  }

  halfGaugeChartConfig: HalfGaugeChartConfig = {
    chartID:"chart1",
    minReading:this.sliderConfig.min,
    maxReading:this.sliderConfig.max,
    reading: this.sliderConfig.value,
    readingColor:"#FFFFFF",
    readingFont:"ariel",
    hideReading:false,
    innerCircleColor: "#0066b3",
    needleColor: "#0066b3",
    width: 580,
    startColor: '#00FFFF',
    endColor: '	#00ff00',
  }

  ngOnInit(): void {
  }

  onSlide(event: any) {
    if (event && event.value) {
      //console.log(event.value);
      this.halfGaugeChartConfig=  {
        chartID:"chart2",
        minReading:this.sliderConfig.min,
        maxReading:this.sliderConfig.max,
        reading: event.value,
        readingColor:"#FFFFFF",
        readingFont:"ariel",
        hideReading:false,
        innerCircleColor: "#0066b3",
        needleColor: "#0066b3",
        width: 580,
        startColor: '#00FFFF',
        endColor: '	#00ff00',
      }
    }
  }

}
