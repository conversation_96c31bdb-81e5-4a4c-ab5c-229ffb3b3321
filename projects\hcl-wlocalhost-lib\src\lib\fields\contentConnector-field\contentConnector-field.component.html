<div fxLayout class="content-connector-container">
  <div class="cc-parent">
    <ng-container *ngIf="field.options?.contentSourceType && field.options?.contentSourceInfo?.url">
      <div class="external-content-details">
        <div>{{ 'CONTENT_CONNECTOR.CONTENT.TITLE.EXTERNAL_CONTENT_SOURCE' | translate }}:</div> 
        <div [hclTooltip]="field.options?.contentSourceInfo?.url">
          {{ field.options?.contentSourceInfo?.url }}
        </div> 
      </div>
    </ng-container>
    <ng-container *ngIf="!field.options?.contentSourceInfo">
      <div style="width:100%;height: 50px;text-align: center;display: block;padding-top: 25px;">
        {{ 'CONTENT_CONNECTOR.CONTENT.TITLE.EXTERNAL_CONTENT' | translate }}
      </div>
    </ng-container>
  </div>
</div>