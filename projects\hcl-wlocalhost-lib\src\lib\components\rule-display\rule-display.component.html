<div [ngClass]="{ 'defaultrule': ruleInfo.isDefault }" class="w-100 rules-display">
    <div class="rule-info-container mtop-3" *ngIf="selectedElement?.type === 'image' || selectedElement?.type === 'image-field'" [ngClass]="{ 'w-100': ruleInfo.isDefault }">
        <div *ngIf="isLoading" class="float-left imgalignment">
            <DIV class="loaderdiv">
            </DIV>
            <SPAN class="loader-content">{{'template.loading' |
                translate}}</SPAN>
          </div>
        <div class="float-left imgalignment">
            <img src="{{ruleInfo.imageSrc}}" (load)="hideLoader()"/>
        </div>
        <div hclTooltip="{{ruleInfo.name}}" class="rule-name" [ngClass]="{ 'default-rule-name': ruleInfo.isDefault }">
            {{ruleInfo.name}}
            <div hclTooltip="{{ruleInfo.ruleMsg}}" class="rule-msg ellipsis" *ngIf="ruleInfo.isDefault">{{ruleInfo.ruleMsg}}</div>
        </div>
    </div>
    <div class="rule-info-container mtop-3" *ngIf="selectedElement?.type === 'html' || selectedElement?.type === 'html-field'" [ngClass]="{ 'w-100': ruleInfo.isDefault }">
           <div *ngIf="isLoading && !htmlSnippetPreviewSource" class="float-left imgalignment">
            <DIV class="loaderdiv">
            </DIV>
            <SPAN class="loader-content">{{'template.loading' |
                translate}}</SPAN>
          </div>
        <div class="float-left imgalignment">
            <img *ngIf="htmlSnippetPreviewSource" class="snippet-preview" src="{{htmlSnippetPreviewSource}}">
        </div>
        <div class="rule-name" [ngClass]="{ 'default-rule-name': ruleInfo.isDefault }">
            <span hclTooltip="{{ruleInfo.name}}">{{ruleInfo.name}}</span>
            <div hclTooltip="{{ruleInfo.ruleMsg}}" class="rule-msg ellipsis" *ngIf="ruleInfo.isDefault">{{ruleInfo.ruleMsg}}</div>
        </div>
    </div>
    <div class="rule-info-container mtop-8" *ngIf="selectedElement?.type === 'button' || selectedElement?.type === 'button-field'" [ngClass]="{ 'w-100': ruleInfo.isDefault }">
        <div hclTooltip="{{ruleInfo.name}}" class="rule-name pt-0 pb-0" [ngClass]="{ 'default-rule-name': ruleInfo.isDefault }">
            {{ruleInfo.name}}
            <div class="hyperlink-url-container">
                <div class="url-btn-info w-90 float-left">
                    <div class="overflow-hidden w-100">
                        <div hclTooltip="{{ruleInfo?.buttonText}}" class="w-30 float-left" *ngIf="ruleInfo.buttonText">
                            <div class="ellipsis">{{ruleInfo?.buttonText}}</div>
                        </div>
                        <div class="w-100 ellipsis float-left" [ngClass]="{'w-70': ruleInfo.buttonText}" *ngIf="ruleInfo.url">
                            {{ ruleInfo.buttonText ? '|' : '' }} {{ ruleInfo.redirection === 'url' ? 'URL: ' : 'LP: ' }} 
                            <span hclTooltip="{{ruleInfo.redirection === 'url' ? ruleInfo.url : ruleInfo.landingPage}}" class="url-link">{{ ruleInfo.redirection === 'url' ? ruleInfo.url : ruleInfo.landingPage }}</span>
                        </div>
                    </div>
                </div>
                <div class="w-10 float-left mt-minus-2" *ngIf="ruleInfo.url && ruleInfo.redirection === 'url'">
                    <i class="hcl-icon-sorting" (click)="openLinkInNewTab();"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="rule-info-container mtop-8" *ngIf="selectedElement?.type === 'text' || selectedElement?.type === 'text-field'" [ngClass]="{ 'w-100': ruleInfo.isDefault }">
        <div class="rule-name pt-0 pb-0" [ngClass]="{ 'default-rule-name': ruleInfo.isDefault }">
            <span hclTooltip="{{ruleInfo.name}}">{{ruleInfo.name}}</span>
            <div class="hyperlink-url-container">
                <div class="url-btn-info w-90 float-left">
                    <div class="overflow-hidden w-100">
                        <div hclTooltip="{{(ruleInfo?.text ? ruleInfo?.text : ruleInfo?.linkText)}}" class="mw-30 float-left" *ngIf="(ruleInfo?.text ? ruleInfo?.text : ruleInfo?.linkText)">
                            <div class="ellipsis">{{(ruleInfo?.text ? ruleInfo?.text : ruleInfo?.linkText)}}</div>
                        </div>
                        <div class="w-100 ellipsis float-left" [ngClass]="{'w-70': (ruleInfo?.text ? ruleInfo?.text : ruleInfo?.linkText)}" *ngIf="ruleInfo.url">
                            {{ (ruleInfo?.text ? ruleInfo?.text : ruleInfo?.linkText) ? '|' : '' }} {{ ruleInfo.redirection === 'url' ? 'URL: ' : 'LP: ' }} 
                            <span hclTooltip="{{ ruleInfo.redirection === 'url' ? ruleInfo.url : ruleInfo.landingPage}}" class="url-link">{{ ruleInfo.redirection === 'url' ? ruleInfo.url : ruleInfo.landingPage }}</span>
                        </div>
                    </div>
                </div>
                <div class="w-10 float-left mt-minus-2" *ngIf="ruleInfo.url && ruleInfo.redirection === 'url'">
                    <i class="hcl-icon-sorting" (click)="openLinkInNewTab();"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="actions-container pt-2 pb-2" *ngIf="!ruleInfo.isDefault">
        <div [ngClass]="{ 'invisible': ngb.isStaticLP }" hclTooltip="{{ 'ADD_CONTENT.LABEL.EDIT' | translate }}" class="edit-delete-icon" >
            <i (click)="actionClicked('edit');" class="hcl-icon-edit mr-10"></i>
        </div>
        <div hclTooltip="{{ 'ADD_CONTENT.LABEL.DELETE' | translate }}" class="edit-delete-icon" >
        <i (click)="actionClicked('delete');" class="hcl-icon-delete"></i>
        </div>
    </div>
</div>