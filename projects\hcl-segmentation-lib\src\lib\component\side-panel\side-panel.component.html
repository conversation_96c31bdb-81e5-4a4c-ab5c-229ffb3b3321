<hcl-side-bar [disableClose]="true" [hidden]="!showSideBar">
    <aside class="h-100 w-100 overflow-hidden" *ngIf="showSideBar">
      <!-- Main container -->
      <div class="h-100 side-panel-container">
        <ng-container [ngSwitch]="currentPanelDetails.panel">
          <ng-container *ngSwitchCase="1">
            <ng-container *ngTemplateOutlet="dataProfile"></ng-container>
          </ng-container>
          
        </ng-container>
      </div>
    </aside>
  </hcl-side-bar>
  
  <!-- Composite Segments list DOM -->
  <ng-template #dataProfile>
    <hcl-data-profile
      [dPData]="currentPanelDetails.inputData"
      (sidebarClosed)="closeSidebar()">
    </hcl-data-profile>
  </ng-template>
  
 