import { ILabelFieldOptions, TextLayoutField, RenderingClass, LabelLayoutField } from '../interfaces';
import { createPadding, createLineHeight, ignoreHTMLMinParse, createBorder, createWidthHeight, uniqueId } from '../utils';
import { IpEmailBuilderService } from '../../../ip-email-builder.service';

export class Label<PERSON>ield implements LabelLayoutField, RenderingClass {
  constructor(public innerText: string, public options: ILabelFieldOptions, private _ngb: IpEmailBuilderService) {}

  render() {
    const { color, font, lineHeight, padding, align } = this.options;
    const textTemplate = `
      <mj-text
        css-class="ip-text-field hide-on-${this.options.hideOn}"
        color="${color}"
        font-family="${font.family}, ${font.fallback}"
        font-size="${font.size}px"
        font-style="${font.style}"
        font-weight="${font.weight}"
        text-decoration="none"
        align="${align}"
        line-height="${createLineHeight(lineHeight)}"
        padding="${createPadding(padding)}">
          ${ignoreHTMLMinParse(this.innerText || '')}
      </mj-text>
    `;
    if (this.options.hideOn === 'desktop') {
      return `
        <mj-raw>
        <!--[if !mso]><!-- --></mj-raw>
        ${textTemplate}
        <mj-raw><!--<![endif]--></mj-raw>
      `;
    } else {
      return textTemplate;
    }
  }
}
