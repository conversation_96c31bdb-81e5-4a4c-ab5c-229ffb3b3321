:host {
  position: absolute;
  z-index: 1000;

  @media only screen and (-webkit-min-device-pixel-ratio: 2),
    only screen and (min--moz-device-pixel-ratio: 2),
    only screen and (-o-min-device-pixel-ratio: 2/1),
    only screen and (min-device-pixel-ratio: 2),
    only screen and (min-resolution: 192dpi),
    only screen and (min-resolution: 2dppx) {
    /* Retina-specific stuff here */
    &::after {
      content: "";
      display: block;
    }
  }
}

:host.ag-tooltip-hiding {
  opacity: 0;
}

.custom-tooltip {
  background: #15161c;
  opacity: 0.8;
  color: #f5f5f5;
  font-size: 12px;
  max-width: 300px;
  min-height: 25px;
  min-width: 60px;
  padding: 5px;
  word-break: break-word;
  justify-content: center;
  display: block;
  overflow: hidden;
  max-height: 600px;
}
