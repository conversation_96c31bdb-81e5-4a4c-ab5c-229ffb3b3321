<DIV style="width: calc(100% - 40px)">
  <DIV class="message-container"
       [ngClass]="{'current-user': config?.isCurrentUser}">
    <DIV class="message-user">
      <i class="element-icon  hcl-icon-user" [title]="config.userName"></i>
    </DIV>
    <DIV class="message-body"
         (mouseover)="toggleActions(true)"
         (mouseout)="toggleActions(false)">
      <DIV class="message-toolbar" #messageToolbar>
        <i class="element-icon  hcl-icon-thumbs-up-alt" title="Like"></i>
        <i class="element-icon  hcl-icon-heart" title="Heart"></i>
        <i class="element-icon  hcl-icon-reply" title="Reply"></i>
        <i class="element-icon  hcl-icon-flag" title="Flag"></i>
      </DIV>
      <DIV class="message-user-details">
        {{config.userName}}
        <span>
          at 06:30pm
        </span>
        <span class="messaged-edited" *ngIf="config?.isEdited">
          Edited
        </span>
      </DIV>
      <DIV class="message">
        {{config.message}}
      </DIV>
    </DIV>
    <DIV class="message-status" *ngIf="config?.isCurrentUser">
      <i class="element-icon"
         [ngClass]="{'hcl-icon-clock': config.status === 3,
                    'hcl-icon-ok': config.status === 1 || config.status === 2,
                    'delivered-and-read': config.status === 1}"
          [title]="getStatusOfMessage()"></i>
    </DIV>
  </DIV>
</DIV>
