import { AfterViewInit, Component, ContentChild, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import * as d3 from 'd3';
import { HclChartTooltipDirective } from '../../directive/hcl-chart-tooltip.directive';
import { LineChartConfig } from './line-chart';
import moment from 'moment-timezone/moment-timezone.js';

@Component({
  selector: 'hcl-line-chart-v2',
  templateUrl: './line-chart.component.html',
  styleUrls: ['./line-chart.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class LineChartComponent implements OnInit, AfterViewInit {
  @Input() config: LineChartConfig;
  @ContentChild(HclChartTooltipDirective)
  toolTip: HclChartTooltipDirective;

  @Output() pointClick = new EventEmitter();

  svg: any;
  svgInner: any;
  yScale: any;
  xScale: any;
  hovered: any;
  pos: any = {};

  constructor() { }

  ngOnInit(): void {
    this.config.height = this.config.height || 700;
    this.config.width = this.config.width || 700;
    this.config.margin = this.config.margin || 50;
  }

  ngAfterViewInit() {
    this.initializeChart();
    this.drawChart();
  }

  public initializeChart(): void {
    this.svg = d3.select('.lineChartContainer')
      .append('svg')
      .attr('height', this.config.height)
      .attr('width', this.config.width); // svg to hold entire chart

    this.svgInner = this.svg.append('g')
      .style('transform', 'translate(' + this.config.margin + 'px, ' + this.config.margin + 'px)');

    this.setAxisScales(); // set x-axis and y-axis scales
  }

  /***
   * Get Min and max values for Axis
   * @param axis: axis object
   * @param isXAxis: if its xAxis, set to true
   */
  getMinMaxValues(axis, isXAxis) {
    let values = [];

    for (const series of this.config.series) {
      const name = isXAxis ? series.xName : series.yName,
        max = axis.max === undefined ? Math.max.apply(Math, series.data.map((obj) => obj[name])) : axis.max,
        min = axis.min === undefined ? Math.min.apply(Math, series.data.map((obj) => obj[name])) : axis.min;

      switch (axis.type) {
        case 'NUMBER':
          if (values.length === 0) {
            values.push(max);
            values.push(min);
          } else {
            values[0] = max > values[0] ? max : values[0];
            values[1] = min < values[1] ? min : values[1];
          }

          break;

        case 'DATETIME':
          const tempValues = d3.extent(series.data, (d: any) => moment(d[name]));

          tempValues[0] = axis.min === undefined ? tempValues[0] : axis.min;
          tempValues[1] = axis.max === undefined ? tempValues[1] : axis.max;

          if (values.length === 0) {
            values = tempValues;
          } else {
            values[0] = values[0] > tempValues[0] ? tempValues[0] : values[0];
            values[1] = values[1] < tempValues[1] ? tempValues[1] : values[1];
          }

          break;
      }
    }

    if (axis.type === 'NUMBER') {
      values[0] = axis.max > values[0] ? axis.max : values[0];
    }

    return values;
  }

  setCrosshair() {
    if (this.config.crosshair) {
      const transparentRect = this.svgInner.append('rect')
        .attr('x', this.config.margin)
        .attr('y', 0)
        .attr('width', this.config.width - this.config.margin * 3)
        .attr('height', this.config.height - this.config.margin * 2)
        .attr('fill', 'white')
        .attr('opacity', 0);

      const verticalLine = this.svgInner.append('line')
        .attr('opacity', 0)
        .attr('y1', 0)
        .attr('y2', this.config.height - this.config.margin * 2)
        .style('stroke-dasharray', '4 4')
        .attr('stroke', this.config.crosshair.vertical.color)
        .attr('stroke-width', this.config.crosshair.vertical.strokeWidth)
        .attr('pointer-events', 'none');

      const horizontalLine = this.svgInner.append('line')
        .attr('opacity', 0)
        .attr('x1', this.config.margin)
        .attr('x2', this.config.width - this.config.margin * 2)
        .style('stroke-dasharray', '4 4')
        .attr('stroke', this.config.crosshair.horizontal.color)
        .attr('stroke-width', this.config.crosshair.horizontal.strokeWidth)
        .attr('pointer-events', 'none');

      const me = this;

      transparentRect.on('mousemove', () => {
        const mouse = d3.mouse(d3.event.currentTarget),
          mousex = mouse[0],
          mousey = mouse[1];

        let x0 = me.xScale.invert(mousex);
        let i = d3.bisector(function (d) { return d[this.config]; }).left;

        verticalLine.attr('x1', mousex).attr('x2', mousex).attr('opacity', 1);
        horizontalLine.attr('y1', mousey).attr('y2', mousey).attr('opacity', 1)
      }).on('mouseout', () => {
        verticalLine.attr('opacity', 0);
        horizontalLine.attr('opacity', 0);
      });
    }
  }

  /**
   * Sets X-axis and Y-axis scales
   */
  private setAxisScales() {
    let xValues = this.getMinMaxValues(this.config.xAxis, true),
      yValues = this.getMinMaxValues(this.config.yAxis, false);

    if (this.config.xAxis.type === 'DATETIME') {
      this.xScale = d3.scaleTime().domain(xValues).range([0, this.config.width - 2 * this.config.margin]);
    } else if (this.config.xAxis.type === 'NUMBER') {
      this.xScale = d3.scaleLinear()
        .domain([xValues[0] + 1, xValues[1]])
        .range([0, this.config.width - 2 * this.config.margin]);
    }

    if (this.config.yAxis.type === 'DATETIME') {
      this.yScale = d3.scaleTime().domain(yValues).range([0, this.config.height - 2 * this.config.margin]);
    } else if (this.config.yAxis.type === 'NUMBER') {
      this.yScale = d3.scaleLinear()
        .domain([yValues[0] + 1, yValues[1]])
        .range([0, this.config.height - 2 * this.config.margin]);
    }
  }

  /**
   * Draws the chart
   */
  public drawChart(): void {
    this.svg.attr('width', this.config.width);

    this.xScale.range([this.config.margin, this.config.width - 2 * this.config.margin]);

    const xAxis = d3.axisBottom(this.xScale);

    if (this.config.xAxis.labelFormat) {
      xAxis.tickFormat(this.config.xAxis.labelFormat);
    }

    if (this.config.xAxis.ticks) {
      xAxis.ticks(this.config.xAxis.ticks);
    }

    if (this.config.xAxis.isInteger) {
      const axisTicks = this.xScale.ticks(this.config.xAxis.ticks).filter(tick => Number.isInteger(tick));
      xAxis.tickValues(axisTicks)
        .tickFormat(d3.format('d'));
    }

    if (this.config.xAxis.gridLines) {
      const xGrid = (g) => g
        .attr('class', 'grid-lines')
        .selectAll('line')
        .data(this.xScale.ticks())
        .join('line')
        .attr('x1', d => this.xScale(d))
        .attr('x2', d => this.xScale(d))
        .attr('y1', this.config.margin)
        .attr('y2', this.config.height - this.config.margin)

      this.svgInner.append('g')
        .style('transform', 'translate(0, ' + (-(this.config.margin)) + 'px)')
        .call(xGrid);
    }


    const yAxis = d3.axisLeft(this.yScale).tickPadding(8);

    if (this.config.yAxis.labelFormat) {
      yAxis.tickFormat(this.config.yAxis.labelFormat);
    }

    if (this.config.yAxis.ticks) {
      yAxis.ticks(this.config.yAxis.ticks);
    }

    if (this.config.yAxis.isInteger) {
      const axisTicks = this.yScale.ticks(this.config.yAxis.ticks).filter(tick => Number.isInteger(tick));
      yAxis.tickValues(axisTicks)
        .tickFormat(d3.format('d'));
    }

    if (this.config.yAxis.gridLines) {
      const yGrid = (g) => g
        .attr('class', 'grid-lines')
        .selectAll('line')
        .data(this.yScale.ticks())
        .join('line')
        .attr('x1', this.config.margin)
        .attr('x2', this.config.width - this.config.margin)
        .attr('y1', d => this.yScale(d))
        .attr('y2', d => this.yScale(d));

      this.svgInner.append('g').call(yGrid);

    }

    this.svgInner.append('g')
      .attr('id', 'xAxis')
      .style('transform', 'translate(0, ' + (this.config.height - 2 * this.config.margin) + 'px)').call(xAxis);

    this.svgInner.append('g')
      .attr('id', 'yAxis')
      .style('transform', 'translate(' + this.config.margin + 'px,  0)').call(yAxis);

    const line = d3.line()
      .x(d => d[0])
      .y(d => d[1])
      .curve(d3[this.config.type]);

    for (let series of this.config.series) {
      const points: [number, number][] = series.data.map(d => [
        this.xScale(this.config.xAxis.type === 'DATETIME' ? moment(d[series.xName]) : d[series.xName]),
        this.yScale(this.config.yAxis.type === 'DATETIME' ? moment(d[series.yName]) : d[series.yName]),
      ]);

      const pointList = this.svgInner.selectAll('.point')
        .data(points)
        .enter()
        .append('circle')
        .attr('cx', function (d) {
          return d[0];
        })
        .attr('cy', function (d) {
          return d[1];
        })
        .attr('r', 5)
        .style('stroke', series.color)
        .style('fill', series.color)
        .style('cursor', 'pointer')
        .on('click', (d, i) => {
          this.pointClick.emit({
            ...series,
            index: i
          });
        });

      if (!this.config.crosshair) {
        pointList.on('mouseover', (d, i) => {
          this.hovered = {
            ...series,
            index: i
          };
          this.pos['top.px'] = d3.event.pageY - 28;
          if (Math.floor(series.data.length / 2) < i) {
            this.pos['right.px'] = window.innerWidth - d3.event.pageX + 15;
          } else {
            this.pos['left.px'] = d3.event.pageX + 15;
          }
        })
          .on('mouseout', () => {
            this.hovered = undefined;
            this.pos = {};
          })
      }

      const path = this.svgInner.append('path');

      path.attr('d', line(points))
        .style('fill', 'none')
        .style('stroke', series.color)
        .style('stroke-width', '2px');
    }

    //this.setCrosshair();
  }

  public removeChart() {
    d3.select('.lineChartContainer svg').remove();
  }
}
