import {
  Component,
  Input
} from '@angular/core';
import { ContentConnectorBlock } from '../../classes/Elements';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'ip-content-connector',
  templateUrl: './content-connector.component.html',
  styleUrls: ['./content-connector.component.css']
})
export class ContentConnectorComponent{
  @Input() block: ContentConnectorBlock;

  constructor(
    public ngb: IpEmailBuilderService,
    public translate: TranslateService,
  ) {}

  transformContentSourceType(value: string): string {
    return this.translate.instant('CONTENT_CONNECTOR.CONTENT.LABELS.' + value
      .split('-')
      .join('_')
      .toUpperCase()
    );
  }
  
}

