import { ChangeDetectorRef, Component, ElementRef, HostListener, Input, OnInit, Renderer2, ViewChild, ViewEncapsulation } from '@angular/core';
import { HtmlField } from '../../classes/Fields';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import { TranslateService } from '@ngx-translate/core';
import {
  createFont,
  createPadding,
  createBorder,
  createLineHeight
} from '../../utils';
import { v4 as uuidv4 } from 'uuid';
import { ButtonConf } from 'hcl-angular-widgets-lib';
import { FroalaEditorComponent } from '../../components/shared/froala-editor/froala-editor.component';

@Component({
  selector: 'ip-html-field',
  templateUrl: './html-field.component.html',
  styleUrls: ['./html-field.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HtmlFieldComponent implements OnInit {
  @Input() field = new HtmlField(this.translate.instant('labels.Snippet'));
  @ViewChild(FroalaEditorComponent) froalaEditor: FroalaEditorComponent;

  @HostListener('document:mouseover', ['$event'])
  public documentMouseover(event: Event): void {
    const isDynamicRuleSection = (event.target as HTMLElement).closest('.rule-count-container');
    const isCdkOverlay = (event.target as HTMLElement).closest('.cdk-overlay-container');
    if (!isDynamicRuleSection && !isCdkOverlay) {
      this.element.nativeElement.querySelector('.dynamic-rule-button') && this.renderer.setStyle(this.element.nativeElement.querySelector('.dynamic-rule-button'), 'visibility', 'hidden');
    }
  }

  snippetRuleAdded: boolean;
  froalaContents: string;
  selectedRule: any;
  defaultRuleSinppetInnerHTML: string = '';

  buttonConf: ButtonConf = {
    color: 'primary',
    buttonType: 'stroked',
    isIconButton: true,
    icon: 'hcl-icon-dynamic-content',
    value: this.translate.instant('RULE_BUILDER.ADD_RULE_LABEL'),
    borderRadius: 1,
    name: 'Dynamic',
    type: 'button'
  };

  ruleNameButton: ButtonConf = {
    color: 'primary',
    buttonType: 'stroked',
    isIconButton: true,
    // icon: 'hcl-icon-dynamic-content',
    value: this.translate.instant('RULE_BUILDER.DEFAULT_RULE'),
    borderRadius: 1,
    name: 'Dynamic',
    type: 'button',
    iconRight: 'hcl-icon-down-dir'
  }

  innerHtml: string = '';
  traversedItemCount: number = -1;
  currentTraversedElement: HTMLElement;
  lastTypedChar: string;
  currentRangeForSelectedLink: any;

  personalizationList: { key: string; value: string }[] = [];
  constructor(
    public translate: TranslateService,
    private chRef: ChangeDetectorRef,
    public ngb: IpEmailBuilderService,
    private element: ElementRef,
    private renderer: Renderer2
  ) {
    this.personalizationList = this.ngb.personalizedTagsFull
  }

  ruleCountMouseHovered() {
    this.renderer.setStyle(this.element.nativeElement.querySelector('.dynamic-rule-button'), 'visibility', 'visible');
  }

  contentChanged(event) {
    if (!this.selectedRule) {
      this.field.options['innerHtml'] = this.addHTMLSnippetContainerWithStyles(event.html);
      this.defaultRuleSinppetInnerHTML = this.addHTMLSnippetContainerWithStyles(this.field.options && this.field.options.innerHtml);
      this['innerHtml'] = this.addHTMLSnippetContainerWithStyles(this.defaultRuleSinppetInnerHTML);
    } else {
      this.selectedRule.updated = true;
      this.selectedRule.digitalAssetContent = this.addHTMLSnippetContainerWithStyles(event.html);
    }
    this.ngb.currentEditingField$.next(this.field);
  }


  ngDoCheck() {
    if (this.field.options?.rules && this.selectedRule) {
      const filteredRule = this.field.options?.rules.filter((rule) => {
        if (rule.id === this.selectedRule.id) {
          return true;
        } else {
          return false;
        }
      });
      if (filteredRule.length <= 0) {
        this.onDefaultRuleClick();
      }
    } else {
      this.onDefaultRuleClick();
    }
    if (!this.field.options?.rules || this.field.options?.rules?.length < 1) {
      this.snippetRuleAdded = false;
    } else {
      this.snippetRuleAdded = true;
    }
    if (this.selectedRule) {
      let ruleindex = this.field.options?.rules?.findIndex((rule) => rule.id === this.selectedRule.id);

      if (this.froalaEditor?.editorInstance) {
        if ((ruleindex > -1 && this.selectedRule.digitalAssetContent != this.addHTMLSnippetContainerWithStyles(this.innerHtml)) || ruleindex === -1) {
          this['innerHtml'] = this.addHTMLSnippetContainerWithStyles(this.selectedRule.digitalAssetContent);
          this.froalaEditor?.editorInstance.html.set(this.removeHTMLSnippetContainerWithStyles(this.selectedRule.digitalAssetContent));
        }
      }
    } else {
      if (this.froalaEditor?.editorInstance) {
        this.ruleNameButton.value = this.translate.instant('RULE_BUILDER.DEFAULT_RULE');
        if (this.defaultRuleSinppetInnerHTML !== this.addHTMLSnippetContainerWithStyles(this.innerHtml) || this.addHTMLSnippetContainerWithStyles(this.innerHtml) !== this.addHTMLSnippetContainerWithStyles(this.field.options.innerHtml)) {
          this.field.options['innerHtml'] = this.addHTMLSnippetContainerWithStyles(this.field.options.innerHtml);
          this.field.options?.rules?.forEach((rule) => {
            rule['updated'] = true;
            rule['digitalAssetContent'] = this.addHTMLSnippetContainerWithStyles(rule['digitalAssetContent']);
          })
          this.defaultRuleSinppetInnerHTML = this.addHTMLSnippetContainerWithStyles(this.field.options && this.field.options.innerHtml);
          this['innerHtml'] = this.addHTMLSnippetContainerWithStyles(this.defaultRuleSinppetInnerHTML);
          this.froalaEditor?.editorInstance.html.set(this.removeHTMLSnippetContainerWithStyles(this.innerHtml));
          this.froalaContents = this.froalaEditor?.editorInstance.html.get();
        }
      }
    }
  }

  ngOnInit(): void {
    this['innerHtml'] = this.addHTMLSnippetContainerWithStyles(this.field.options.innerHtml);
    this.froalaEditor?.editorInstance?.html.set(this.removeHTMLSnippetContainerWithStyles(this.innerHtml));
    this.froalaContents = this.froalaEditor?.editorInstance.html.get();
    this.defaultRuleSinppetInnerHTML = this.addHTMLSnippetContainerWithStyles(this.field.options && this.field.options.innerHtml);
    if (!this.field.options.id) {
      this.field.options.id = 'temp-' + uuidv4()
    }
    this.ngb.currentEditingRule$.next(null);
  }

  createPadding = ({
    top = 10,
    right = 25,
    bottom = 10,
    left = 25
  }): string => {
    return `${top}px ${right}px ${bottom}px ${left}px`;
  };

  createLineHeight = ({
    value = 22,
    unit = 'px'
  }): string | number => {
    return unit !== 'none' ? `${value}${unit}` : 'normal';
  };

  addHTMLSnippetContainerWithStyles(plainHtml: string) {
    plainHtml = this.removeHTMLSnippetContainerWithStyles(plainHtml);
    const {
      color,
      font,
      lineHeight,
      padding
    } = this.field.options;
    let originalSnippetCode: any = new DOMParser().parseFromString(plainHtml, 'text/html');
    const allHTMLSnippetContainers: any = originalSnippetCode.querySelectorAll('div#hcl-snippet-container');
    if (allHTMLSnippetContainers.length <= 0) {
      originalSnippetCode = new DOMParser().parseFromString('<div id="temp-hcl-snippet-container">' + plainHtml + '</div>', 'text/html');
      const containerElement = document.createElement('div');
      containerElement.setAttribute('id', 'hcl-snippet-container')
      containerElement.setAttribute('style', `color:${color}; padding:${this.createPadding(padding)}; vertical-align:middle; line-height:${this.createLineHeight(lineHeight)}; font-family:${font.family}, ${font.fallback}; font-size:${font.size}px; font-style:${font.style}; font-weight:${font.weight};`);
      containerElement['innerHTML'] = originalSnippetCode.documentElement.querySelector('#temp-hcl-snippet-container').innerHTML;
      originalSnippetCode.body['innerHTML'] = '';
      originalSnippetCode.body.appendChild(containerElement)
    }
    plainHtml = originalSnippetCode.documentElement.getElementsByTagName('body')[0].innerHTML;
    return plainHtml;
  }

  addStylesToBody(plainHtml: string) {
    const {
      color,
      font,
      lineHeight,
      padding
    } = this.field.options;
    const originalSnippetCode: any = new DOMParser().parseFromString(plainHtml, 'text/html');
    originalSnippetCode.documentElement.getElementsByTagName('body')[0].setAttribute('style', `color:${color}; padding:${this.createPadding(padding)}; vertical-align:middle; line-height:${this.createLineHeight(lineHeight)}; font-family:${font.family}, ${font.fallback}; font-size:${font.size}px; font-style:${font.style}; font-weight:${font.weight};`);
    plainHtml = originalSnippetCode.documentElement.getElementsByTagName('body')[0].outerHTML;
    return plainHtml;
  }

  setContent(event) {
    this.froalaEditor?.editorInstance.html.set(this.removeHTMLSnippetContainerWithStyles(this.innerHtml));
    this.froalaContents = this.froalaEditor?.editorInstance.html.get();
  }

  removeHTMLSnippetContainerWithStyles(plainHtml: string) {
    const originalSnippetCode: any = new DOMParser().parseFromString(plainHtml, 'text/html');
    const allHTMLSnippetContainers: any = originalSnippetCode.querySelectorAll('div#hcl-snippet-container');
    if (allHTMLSnippetContainers.length > 0) {
      plainHtml = allHTMLSnippetContainers[0].innerHTML;
    }
    return plainHtml;
  }

  onRuleClick(rule) {
    // if (this.froalaEditor?.editorInstance) {
    //   this.froalaEditor?.editorInstance.edit.off()
    // }
    // this['innerHtml'] = this.addHTMLSnippetContainerWithStyles(rule.digitalAssetContent);
    this.ruleNameButton.value = rule.name;
    this.selectedRule = rule;
    this.ngb.currentEditingRule$.next(this.selectedRule);
  }

  dynamicContentClick() {
    this.ngb.addEditRuleBuilder.next();
  }

  onDefaultRuleClick() {
    // if (this.froalaEditor?.editorInstance) {
    //   this.froalaEditor?.editorInstance.edit.on()
    // }
    // this['innerHtml'] = this.addHTMLSnippetContainerWithStyles(this.defaultRuleSinppetInnerHTML);
    this.ruleNameButton.value = this.translate.instant('RULE_BUILDER.DEFAULT_RULE');
    this.selectedRule = null;
    this.ngb.currentEditingRule$.next(null);
  }

  getParentStyles() {
    const {
      backgroundColor,
      border,
      color,
      font,
      lineHeight,
      innerPadding,
      fullWidth
    } = this.field.options;
    const { align, padding } = this.field.options;

    return {
      color,
      ...createLineHeight(lineHeight),
      width: fullWidth ? '100%' : 'auto',
      backgroundColor,
      ...createFont(font),
      ...createPadding(innerPadding),
      ...createBorder(border),
      justifyContent:
        (align === 'center' && 'center') ||
        (align === 'right' && 'flex-end') ||
        'flex-start',
      ...createPadding(padding)
    };
  }

  getTextStyles() {
    const { color, font, lineHeight, padding } = this.field.options;

    return {
      color,
      ...createLineHeight(lineHeight),
      ...createFont(font),
      ...createPadding(padding)
    };
  }

  fetchComponentErrorMessages() {
    const errorMessages = new Set();
    for (let iErrorCounter = 0; iErrorCounter < this.field.errors.length; iErrorCounter++) {
      errorMessages.add(this.translate.instant('messages.' + this.field.errors[iErrorCounter].key + '-collated'))
    }
    return `${Array.from(errorMessages).join(`\n\r`)}`;
  }

  showHyperlinkTemp(linkURL) {
    this.ngb.onDNDHyperLinkClick.next({ ...linkURL });
  }

  updateHyperLinkInfo(event) {
    this.ngb.onHyperLinkUpdate.next(event)
  }

  focusCurrentField(event) {
    this.ngb.currentEditingField$.next(this.field);
    this.ngb.currentEditingBlockFocusSnippet$.next(event);
  }

  keyDownCurrentField(event) {
    this.ngb.currentEditingSnippetContent$.next(event);
  }

  keyUpCurrentField(event) {
    this.froalaContents = event
    this.ngb.currentEditingSnippetContent$.next(event);
  }

}
