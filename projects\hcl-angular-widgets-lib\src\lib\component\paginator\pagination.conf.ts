export interface DataGridPagination {
  totalRecords?: number;
  totalSelectedRecords? : number;
  total?: string; // for 'Total' string translations
  selected?: string; // for 'Selected' string translations
  currentPageIndex?: number;
  rowsPerPage?: number;
  pageSizeArray?: number[];
  rowsPerPageSuffix?: string; // Display no of rows drop-down suffix
  lazy?: boolean;
  pagination?: boolean;
  onLazyLoad?: (totalRecordTillNow: number, numberOfRows: number) => void;
  firstLabelString?: string;
  lastLabelString?: string;
  nextLabelString?: string;
  prevLabelString?: string;
  optionLabels?: string[];
  isCustomHandler?: boolean;
  previousPageIndex?: number;
  previousRowsPerPage?: number;
}
