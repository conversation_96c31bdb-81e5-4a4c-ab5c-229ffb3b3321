<div class="hcl-dataGridDemo-container">
    <hcl-button [config]="addRowBtn" (onclick)="createNewRow($event)"></hcl-button>
    <hcl-button [config]="deleteRowBtn" (onclick)="deleteRow()"></hcl-button>
  <hcl-button [config]="updateRowBtn" (onclick)="updateRow($event)"></hcl-button>
<div class="hcl-demo-columnSelection-container">
<hcl-data-grid-column-selection-enterprise [data-grid]="ch" #addRemoveColumn (applyBtnClick)="applyBtnClick($event)"
  (resetBtnClick)="resetBtnClick($event)">
</hcl-data-grid-column-selection-enterprise>
</div>
<hcl-button class="mx-2" [config]="hideRowConf" (onclick)="hideRows($event)">
</hcl-button>
<hcl-button class="mx-2" [config]="selectAllConf" (onclick)="selectAll($event)">
</hcl-button>
<hcl-button class="mx-2" [config]="deSelectAllConf" (onclick)="deSelectAll($event)">
</hcl-button>
<hcl-data-grid-enterprise [config]=conf (rowSelected)="rowSelected($event)" (columnSorting)="columnSorted($event)"
(rowUnSelected)="rowUnSelected($event)" (gridReady)="gridReady($event)" (gridDrop)="gridDrop($event)"
(rowClicked)="onRowClicked($event)">
<!--&lt;!&ndash;The edit cell needs to be drawn in a specific manner, so lets add a template for that&ndash;&gt;-->
<!--<ng-template hclTemplate hclTemplateName="headerColumn" type="cell-renderer" let-x>-->
<!--<button (click)="funny()" >-->
<!--{{x.row[x.col.field]}}-->
<!--</button>-->
<!--</ng-template>-->
<ng-template hclTemplate hclTemplateName="addrule" type="full-width" let-cell>
  <div style="padding-top: 3%; margin-left: 10px; font-size: 15px; font-weight: bold;">
    <div style=" width: 60%; display: inline-block; left: 55px; position: relative;">
      Drag and drop here to create new rules
    </div>
    <div style=" width: 10%; display: inline-block;">
      <a href="javascript:void(0)"> Add Rule </a>
    </div>
    <div style=" width: 10%; display: inline-block;">
      <a href="javascript:void(0)"> Hide section</a>
    </div>
  </div>
</ng-template>
<!-- <ng-template hclTemplate hclTemplateName="entrySourceCellName" type="cell-renderer" let-cell>
  <!- <a class="cell-tooltip" hclTooltip="{{cell.row && cell.row[cell.col.field]}}"
    href="javascript:void(0);"> -->
  <!-- <span>{{cell.row && cell.row[cell.col.field]}}</span> -->
    <!-- <div class="row" *ngFor="let opt of cell.row[cell.col.field]">
      <span>{{opt.link}}</span>
  </div> -->
<!-- </a> --
</ng-template> -->
<ng-template hclTemplate hclTemplateName="entrySourceCell" type="cell-renderer" let-cell>
  <span>{{cell.row && cell.row[cell.col.field]}}</span>
</ng-template>
<ng-template hclTemplate hclTemplateName="inUseCell" type="cell-renderer" let-cell>
  <span>{{cell.row && cell.row[cell.col.field] ? 'In Use' : 'Idle'}}</span>
</ng-template>
<ng-template hclTemplate hclTemplateName="createdDateCell" type="cell-renderer" let-cell>
  <span>{{parseDate(cell.row && cell.row[cell.col.field])}}</span>
</ng-template>
<ng-template hclTemplate hclTemplateName="lastModifiedByCell" type="cell-renderer" let-cell>
  <span>{{cell.row && cell.row[cell.col.field]}}</span>
</ng-template>
<ng-template hclTemplate hclTemplateName="entrySourceHeader" type="header-renderer" let-cell>
  <a href="javascript:void(0);">Entry</a>
</ng-template>
<ng-template hclTemplate hclTemplateName="customPopover" type="popover-renderer" let-cell>
  <a href="javascript:void(0);">Custom Popover</a>
</ng-template>
</hcl-data-grid-enterprise>
</div>
<div class="drop-col" (dragover)="onDragOver($event)" (drop)="onDrop($event)">
<div id="eDropTarget" class="drop-target"> ==&gt; Drop to here </div>
<div id="eJsonDisplay" class="json-display"></div>
</div>

