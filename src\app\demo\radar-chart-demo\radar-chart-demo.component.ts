import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { RadarChartConfig } from 'projects/hcl-angular-charts-lib/src/public-api';

@Component({
  selector: 'app-radar-chart-demo',
  templateUrl: './radar-chart-demo.component.html',
  styleUrls: ['./radar-chart-demo.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class RadarChartDemoComponent implements OnInit {

  constructor() { }

  MainChartConfig: RadarChartConfig = {
    numofSide: 5,
    numofLevels: 5,
    size: 400,
    drawYAxis: false,
    drawTicks: true,
    drawDataTextLabels: true,
    startGridLinesFromCenter: false,
    chartBgColor: '#FFF',
    gridLineColor: 'rgba(30, 120, 99, 0.466)',
    levelsStrokeColor: 'rgba(30, 120, 99, 0.466)',
    dataFillColor: 'rgba(30, 120, 99, 0.466)',
    dataStrokeColor: 'rgba(30, 120, 99, 0.466)',
    labelsTextColor: '#000',
    dataPointCircleColor: 'rgb(96 128 149)',
    tickTextColor: 'rgb(80 148 120)',
    radiusForDataPointsCircle: 3,
    chartContainerId: 'main-chart'
  }

  SmallChartConfig: RadarChartConfig = {
    numofSide: 5,
    numofLevels: 5,
    size: 35,
    drawYAxis: false,
    drawTicks: false,
    drawDataTextLabels: false,
    startGridLinesFromCenter: false,
    chartBgColor: '#FFF',
    gridLineColor: 'rgba(30, 120, 99, 0.466)',
    levelsStrokeColor: 'rgba(30, 120, 99, 0.466)',
    dataFillColor: 'rgba(30, 120, 99, 0.466)',
    dataStrokeColor: 'rgba(30, 120, 99, 0.466)',
    dataPointCircleColor: 'rgb(96 128 149)',
    radiusForDataPointsCircle: 1
  }
  
  ngOnInit(): void {

  }
}
