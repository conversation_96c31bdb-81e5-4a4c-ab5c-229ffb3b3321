.cif-object-mapping {
  min-width: 680px;
  height: 100%;
  position: relative;
  overflow: hidden;
  padding: 30px 30px 20px;
  background-color: #ececec;
  // box-shadow: -15px 0 10px -2px rgba(0, 0, 0, 0.3);

  &__header {
    height: 7%;
    overflow: hidden;

    h2 {
      color: #6d7692;
      font-family: Montserrat;
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 24px;
      margin: 0;
    }
  }

  &__content {
    height: 86%;
    overflow: hidden;

    .metadata {
      margin-bottom: 25px;

      .cms-repos,
      .category-dropdown {
        width: 375px;
      }

      .category-folder-sec {
        display: flex;
        align-items: baseline;

        .category-input {
          width: 375px;
        }
      }
    }

    .mapping-section {
      height: calc(100% - 222px);
      .titles {
        h4 {
          color: #15161c;
          font-family: Roboto;
          font-size: 14px;
          letter-spacing: 0;
          line-height: 16px;
          margin-bottom: 15px;
        }

        p {
          color: #6d7692;
          font-family: Roboto;
          font-size: 12px;
          letter-spacing: 0.4px;
          line-height: 14px;
        }
      }

      .mapping-area {
        background-color: #f5f5f5;
        box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.25);
        height: calc(100% - 70px);
        overflow: hidden;
        margin: 0 3px;
        padding: 0 15px;

        .headers {
          display: flex;
          border-bottom: 2px solid #e0e0e0;
          padding-right: 16px;

          .attr-header {
            width: 50%;
            margin: 15px 0;
            border-right: 1px solid #e0e0e0;
            padding: 0 10px;

            span {
              display: block;
              color: #6d7692;
              font-family: Montserrat;
              font-size: 16px;
              font-weight: 600;
              letter-spacing: 0;
              line-height: 19px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }

        .attributes-mapping {
          height: calc(100% - 51px);
          overflow-x: hidden;
          overflow-y: auto;
        }
      }
    }
  }

  &__actions {
    height: 7%;
    overflow: hidden;
    display: flex;
    flex-flow: row-reverse;
    -ms-flex-flow: row-reverse;
    align-items: end;

    hcl-button {
      margin-left: 20px;
    }
  }

  .no-repos-state {
    height: 93%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.cif-object-mapping-category-selection-wrapper {
  width: 90vw;
  height: 100%;
  position: relative;
  background-color: #ececec;
  box-shadow: -15px 0 10px -2px rgb(0 0 0 / 30%);
}
