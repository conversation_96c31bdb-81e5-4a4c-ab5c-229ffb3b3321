.addRemoveColumnOuter {
  .hcl-icon-angle-down {
    float: right;
    padding: 3px 0px 0px 0px;
  }
}
.add-remove-col-outer {
  display: flex;
	flex-direction: column;
	max-height: 420px;
  min-width:350px;

  .resetBtn {
    button{
      line-height: 25px;
    }
  }
  .applyBtn {
    button {
      line-height: 29px;
    }
  }
  .check-box-outer {
     overflow: auto;
     max-height: 350px !important;
     min-height: 290px !important;
  }

  .checkbox-section {
    display: block;
    height: auto;
    position: relative;
    padding: 0;
  }

  .checkbox-data {
    display: inline-flex;
    width: 100%;
    margin: 0;
    background: #fff;
    padding: 11px 15px;
    font-size: 14px;

    &.checkbox-data-disabled {
      color: #b8b7b7 !important;
      background: transparent !important;
      opacity: 0.7;
    }

    &.checkbox-data-selected {
      background-color: #FDE6D2;
      font-weight: 500;
    }

    &:hover:not(.checkbox-data-disabled):not(.checkbox-data-selected) {
      background: #ececec;

      & .mat-checkbox-frame {
        border-color: #f5821e !important;
      }
    }
  }

  .mat-checkbox-layout {
    margin-bottom: 0;
  }

  .checkbox-label {
    margin-left: 10px;
    vertical-align: middle;
    margin-bottom: 0;
  }

  .mat-checkbox-checked {
    &:not(.mat-checkbox-disabled) {
      + .checkbox-label {
        color: #444444;
      }
    }
  }
  .template-action {
    background: #fff;
    width: 100%;
    height: 64px;

    .button-container {
      justify-content: flex-end;
      margin: 15px 0px 4px;
    }
  }
}
