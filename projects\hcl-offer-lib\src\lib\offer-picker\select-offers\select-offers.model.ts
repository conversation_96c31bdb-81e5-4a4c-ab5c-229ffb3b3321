export interface OfferTemplate {
    url: string;
    id: number;
    createdBy: number;
    createdTimeStamp: string;
    creatorFlag: number;
    creatorObjectId: number;
    description: string;
    displayIcon: string;
    displayOrder: number;
    isForInteract: boolean;
    displayName: string;
    rules: AttributeRule[];
    dependentAttributes: any[];
    offerCodeFormats: string[];
    offerCodeGenerator: string;
    offerTemplAttributes: any[];
    policyId: number;
    isRetired: true;
    suggestedUsage: string;
    treatmentCodeFrmt: string;
    treatmentCodeGenerator: string;
    updatedBy: number;
    updatedTimeStamp: string;
    staticAttributes: CustomAttribute[];
    hiddenAttributes: CustomAttribute[];
    parameterisedAttributes: CustomAttribute[];
    offersCount: number;
    objectMappingIds?: number[];
}

export interface AttributeRule {
    nestedCondition: NestedRuleConditions;
    action: RuleAction;
}

export interface NestedRuleConditions {
    logicalOp: 'or';
    conditions: RuleCondition[];
}

export interface RuleCondition {
    param: string | number;
    op: 'in' | 'ni';
    value: number[];
}

export interface RuleAction {
    name: string;
    identifierType: string;
    identifiers: string[];
}

export interface CustomAttribute {
    id: number;
    internalName: string;
    displayName: string;
    description: string;
    dependencyExists: boolean;
    type: {
        id: number,
        internalName: string;
        displayName: string,
        isSystemDefined: boolean,
        definition: {},
        dataType?: string,
        isEnumerated: boolean,
        attributes: Array<any>,
        properties: {
            sortOrder?: 'Alphabetical Ascending' | 'Alphabetacal Descending' | 'Chronological' | 'Reverse Chronological',
            options?: Array<String>,
            allowAddAtForms?: boolean,
            maxLength?: number,
            decimalPlaces?: number,
            defaultOption?: number,
            table?: string,
            idColumn?: string,
            displayColumn?: string,
            sortByColumn?: string,
            idColumnType?: string,
            sortByColumnType?: string,
            defaultValueId?: number,
            values?: Array<any>,
            sort?: 'ASC' | 'DESC',
            defaultValue?: any
        }
    };
    isRetired: boolean;
    isSystemDefined: boolean;
    createdTimeStamp: number;
    createdBy: string;
    updatedTimeStamp: number;
    updatedBy: string;
    creatorSystemId: number;
    isMandatory: string;
    value?: any;
}

export interface RichtextObjectData {
    key?: string;
    charCounterCount?: boolean;
    placeholderText?: string;
    charCounterMax?: number;
    attribution?: boolean;
    language?: string;
    heightMin?: number;
    heightMax?: number;
    fileUpload?: boolean;
    imagePaste?: boolean;
    htmlRemoveTags?: string[];
    toolbarButtons?: {};
    colorsBackground?: string[];
    colorsStep?: number;
    colorsText?: string[];
    tableColors?: string[];
    tableColorsStep?: number;
    linkInsertButtons?: string[];
    linkEditButtons?: string[];
    linkAlwaysBlank?: boolean;
    quickInsertButtons?: string[];
    wordAllowedStyleProps?: string[];
    pasteDeniedTags?: string[];
    editorClass?: string;
    toolbarSticky?: boolean;
}


export interface PlatformConfig {
    isCentralizedOfferAppEnabled: boolean;
    isInteractAppInstalled: boolean;
    isAssetPickerAppInstalled: boolean;
    isDeliverAppInstalled: boolean;
    isPlanAppInstalled: boolean;
    reportingEngine: 'UnicaInsights' | 'Cognos' | 'None';
}


export interface OfferApplicationConfig {
    allowVariableLengthCodes: boolean;
    currencyLocale: string;
    defaultLocale: string;
    displayOfferCodes: boolean;
    offerCodeDelimiter: string;
    supportedLocales: string;
    offerApplicationId: number;
    textSizeValidationType: 'char' | 'byte' | 'unicode-byte';
    offerCodeGeneratorName: string;
    offerServerURL: string;
    stringMaxlength: { [key: string]: number };
}

export interface AssetpickerApplicationConfig {
    isInstalled: boolean;
    token: string;
    url: string;
    validity: number;
}

export interface UserConfig {
    displayName: string;
    locale: string;
    folderSecurityPoliciesMap: object;
    offerSecurityPoliciesMap: object;
    offerListSecurityPoliciesMap: object;
    offerTemplateSecurityPoliciesMap: object;
    securityPoliciesMap: object;
    defaultOfferListingView: 'Card view' | 'Grid view';
}

export interface OfferGetResponse {
    content?: Offer[];
    page?: any;
}

export interface Offer {
    id: number;
    displayName: string;
    description: string;
    createdTimeStamp: string;
    createdBy: number;
    updatedTimeStamp: string;
    updatedBy: number;
    offerCodes: string[]; // which 1 to display?
    templateId: number;
    isRetired: boolean;
    folderId: number;
    effectiveDateFlag: boolean; // check
    expirationDateFlag: boolean; // check
    policyId: number;
    isDeletePermitted: boolean;
    creatorSystemId: number;
    creatorObjectId: number;
    staticAttributes: OfferAttributes[];
    hiddenAttributes: OfferAttributes[];
    parameterizedAttributes: OfferAttributes[];
    productConditions: any[];
    suppressionConditions: any[];
    state: string;
    isImageLoaded: boolean;
    brokenThumbnail: boolean;
    thumbnailProperties: {
        applicationId: string,
        objectId: string,
        url: string,
        objectType: string
    };
}

export interface OfferAttributes {
    id: number;
    value: any;
    type: AttributeTypeDTO;
}

export interface AttributeTypeDTO { // maybe duplicate
    id: number;
    internalName: string;
    displayName: string;
    dataType: string;
    isSystemDefined: boolean;
    isEnumerated: boolean;
    properties: { key: string, value: any }[];
    attributes: CustomAttribute;
}

export interface OfferListGetResponse {
    content?: OfferList[];
    page?: any;
}

export interface OfferList {
    id: number;
    displayName: string;
    description: string;
    type: OfferListType;
    includeSubFolders: boolean;
    isRetired: boolean;
    createdTimeStamp?: string;
    createdBy: string;
    state: string;
    updatedTimeStamp?: string;
    updatedBy: string;
    policyId: number;
    creatorSystemId: number;
    creatorObjectId: number;
    attributeMap?: any;
    folderId: number;
}

export interface OfferListType {
    id: 'SMART' | 'STATIC';
    properties?: OfferListProperties;
}

export interface OfferListProperties {
    offerIds?: number[]; // Only for static
    // Below 3 for Smart
    offerQuery?: string;
    size?: number;
    folderIds?: number[];
    includeSubFolders?: boolean;
    folderDTOs: Folder[];
    deletedFolderIds: number[];
}

export interface Folder {
    id: number;
    displayName: string;
    description: string;
    policyId: number;
    parentFolderId: number;
    createdTimeStamp: string;
    createdBy: number;
    updatedTimeStamp: string;
    updatedBy: number;
    creatorSystemId: number;
    creatorObjectId: number;
}