import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NotificationService } from 'hcl-angular-widgets-lib';
import { GridHeader } from 'hcl-data-grid-lib';
import { Observable, Subject } from 'rxjs';
import { AttributesMappingData, ObjectMapping } from './object-mapping-configs';

@Injectable({
  providedIn: 'root'
})
export class HclCifObjectMappingLibService implements GridHeader {

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService) { }

  private _baseUrl;
  private _headers;
  private _repositories;
  private _selectedCmsRepository: any;
  private _selectedAppRepository: any;
  private _selectedFolderData: Subject<any> = new Subject<any>();
  private _serviceCategorySelectionActive: boolean;
  private _applicationCategorySelectionActive: boolean;
  private _serviceSchema: any;
  private _applicationSchema: any;
  private _mappingDataMap = new Map<number, any>();
  private _invalidPreviousMappingMap: Map<string, boolean> = new Map([]);
  private _selectedFolderName: any;
  private _allMappingsList = [];

  applicationConstants = {
    capitalString(): string { return 'STRING'; },
    capitalNumber(): string { return 'NUMBER'; },
    capitalInteger(): string { return 'INTEGER'; },
    capitalBoolean(): string { return 'BOOLEAN'; },
    capitalDateTime(): string { return 'DATETIME'; },
    capitalDescription(): string { return 'DESCRIPTION'; },
    capitalDisplayName(): string { return 'DISPLAYNAME'; },
    capitalUrl(): string { return 'URL'; },
    capitalHtml(): string { return 'HTML'; },
    capitalResponse(): string { return 'RESPONSE'; },
    capitalRequest(): string { return 'REQUEST'; },
    lowerCaseAuto(): string { return 'auto'; },
    capitalEmail(): string { return 'EMAIL'; },
  };


  get baseUrl() {
    return this._baseUrl;
  }

  set baseUrl(value: string) {
    this._baseUrl = value + '/api/AssetPicker';
  }

  get headers(): HttpHeaders {
    return this._headers;
  }

  set headers(value: HttpHeaders) {
    this._headers = value;
  }

  get repositories() {
    return this._repositories;
  }

  set repositories(value) {
    this._repositories = value;
  }

  get serviceCategorySelectionActive() {
    return this._serviceCategorySelectionActive;
  }

  set serviceCategorySelectionActive(value: boolean) {
    this._serviceCategorySelectionActive = value;
  }

  get applicationCategorySelectionActive() {
    return this._applicationCategorySelectionActive;
  }

  set applicationCategorySelectionActive(value: boolean) {
    this._applicationCategorySelectionActive = value;
  }

  get serviceSchema() {
    return this._serviceSchema;
  }

  set serviceSchema(value: any) {
    this._serviceSchema = value;
  }

  get applicationSchema() {
    return this._applicationSchema;
  }

  set applicationSchema(value: any) {
    this._applicationSchema = value;
  }

  get selectedFolderNAme() {
    return this._selectedFolderName;
  }

  set selectedFolderNAme(value: any) {
    this._selectedFolderName = value;
  }

  set mappingDataMap(value: Map<number, any>) {
    this._mappingDataMap = value;
  }

  get mappingDataMap(): Map<number, any> {
    return this._mappingDataMap;
  }

  get invalidPreviousMappingMap(): Map<string, boolean> {
    return this._invalidPreviousMappingMap;
  }

  set invalidPreviousMappingMap(value: Map<string, boolean>) {
    this._invalidPreviousMappingMap = value;
  }

  set allMappingsList(value: any) {
    this._allMappingsList = value;
  }

  get allMappingsList(): any {
    return this._allMappingsList;
  }

  set selectedAppRepository(value: any) {
    this._selectedAppRepository = value;
  }

  get selectedAppRepository(): any {
    return this._selectedAppRepository;
  }

  set selectedCmsRepository(value: any) {
    this._selectedCmsRepository = value;
  }

  get selectedCmsRepository(): any {
    return this._selectedCmsRepository;
  }

  public getInstances(allRepos?: boolean) {
    if (allRepos) {
      return this.http.get(this.baseUrl + '/instances?all=' + allRepos, { headers: this.headers, withCredentials: false });
    } else {
      return this.http.get(this.baseUrl + '/instances', { headers: this.headers, withCredentials: false });
    }
  }

  public getCategories(repo: string) {
    return this.http.get(this.baseUrl + '/' + repo + '/categories', { headers: this.headers, withCredentials: false });
  }

  public getSingleCategory(repo: string, id: string) {
    return this.http.get(this.baseUrl + '/' + repo + '/categories/' + id, { headers: this.headers, withCredentials: false });
  }

  public getCategoriesByFolderId(repo: string, folderId: string) {
    return this.http.get(this.baseUrl + '/' + repo + '/categories?folderId=' + folderId, { headers: this.headers, withCredentials: false });
  }

  setselectedFolderData(value: any) {
    this._selectedFolderData.next(value);
  }

  getselectedFolderData(): any {
    return this._selectedFolderData.asObservable();
  }

  //  schema
  public getObjectMappingAttributes(applicationName: string, objectType: string, schemaObjectId?: string):
    Observable<ObjectMapping> {
    if (objectType) {
      if (schemaObjectId) {
        return this.http.post<ObjectMapping>(this.baseUrl + '/object-mapping/application/'
          + applicationName + '/object/' + objectType + '/' + schemaObjectId + '/schema', null,
          { headers: this.headers, withCredentials: false });
      } else {
        return this.http.post<ObjectMapping>(this.baseUrl + '/object-mapping/application/'
          + applicationName + '/object/' + objectType + '/schema', null,
          { headers: this.headers, withCredentials: false });

      }
    }
  }


  public getFolders(systemId: any, folder: any, ignoreLoader?: boolean): Observable<any> {
    if (folder) {
      let headerCopy = this.headers;
      if (ignoreLoader) {
        headerCopy = headerCopy.append('ignoreLoader', 'true');
      }
      return this.http.get(this.baseUrl + '/' + systemId + '/folders?categories=true&parentFolderId=' + folder.id,
        { headers: headerCopy, withCredentials: false });
    } else {
      return this.http.get(this.baseUrl + '/' + systemId + '/folders?categories=true',
        { headers: this.headers, withCredentials: false });
    }
  }

  public createObjectMapping(data): Observable<AttributesMappingData> {
    return this.http.post<AttributesMappingData>(this.baseUrl + '/object-mapping',
      { ...data }, { headers: this.headers, withCredentials: false });
  }

  public updateObjectMapping(mappingId, data): Observable<AttributesMappingData> {
    return this.http.put<AttributesMappingData>(this.baseUrl + '/object-mapping/' + mappingId,
      { ...data }, { headers: this.headers, withCredentials: false });
  }

  handleServerError(errorMsg: string) {
    this.notificationService.show({
      message: errorMsg,
      type: 'error', close: true, autoHide: 6000
    });
  }

  /* this method supports the grid config */
  getHeaders(): HttpHeaders {
    return this.headers;
  }


  cleanUpData() {
    this.baseUrl = null;
    this.headers = null;
    this.repositories = null;
    this.applicationSchema = null;
    this.serviceSchema = null;
    this.mappingDataMap = null;
    this.allMappingsList = [];
    this.serviceCategorySelectionActive = false;
    this.applicationCategorySelectionActive = false;
    this.selectedCmsRepository = null;
    this.selectedAppRepository = null;
    this.setselectedFolderData(null);
  }
}
