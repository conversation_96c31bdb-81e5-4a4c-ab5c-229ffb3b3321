.cif-export-object {
  height: 100%;
  width: 630px;
  padding: 30px;
  padding-right: 0;
  overflow: auto;
  background-color: #ececec;
  box-shadow: -15px 0 10px -2px rgba(0, 0, 0, 0.3);

  p.section-title {
    color: #6d7692;
    font-family: Montserrat;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 19px;
    margin-bottom: 10px;
  }

  .required .mat-form-field-appearance-legacy .mat-form-field-label::before,
  .required .mat-checkbox-label::before,
  .required .mat-radio-label-content::before {
    content: "* ";
    color: #cc0000;
    font-size: 14px;
  }

  &__content-wrapper {
    height: 100%;
    padding-right: 30px;
    overflow: auto;
  }

  &__config-missing {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 90%;
    padding-right: 30px;
    overflow: auto;

    .error-msg {
      color: #6d7692;
      font-family: Roboto;
      font-size: 14px;
      letter-spacing: 0.4px;
      line-height: 20px;
      text-align: center;
      margin: 0 auto;
      padding: 0 50px;
    }
  }

  &__meta {
    height: 90px;
    margin-bottom: 10px;

    h2 {
      color: #6d7692;
      font-family: Montserrat;
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 24px;
      margin-bottom: 10px;
    }
  }

  &__source-selection {
    height: 75px;
  }

  &__target-selection {
    height: 230px;

    .category-folder-sec {
      display: flex;
      align-items: baseline;

      .category-input {
        flex: 1;
      }
    }
  }

  &__field-mappings {
    margin-bottom: 60px;
    .titles {
      p.mapping-info {
        color: #6d7692;
        font-family: Roboto;
        font-size: 12px;
        letter-spacing: 0.4px;
        line-height: 14px;
        margin-top: 25px;
      }
    }

    .mapping-area {
      background-color: #f5f5f5;
      box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.25);
      max-height: 500px;
      overflow: auto;
      margin: 0 3px;
      padding: 0 15px;

      .headers {
        display: flex;
        border-bottom: 2px solid #e0e0e0;
        padding-right: 16px;
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: #f5f5f5;

        .attr-header {
          width: 50%;
          margin: 15px 0;
          border-right: 1px solid #e0e0e0;
          padding: 0 10px;

          span {
            display: block;
            color: #6d7692;
            font-family: Montserrat;
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0;
            line-height: 19px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }

      .attributes-mapping {
        // height: calc(100% - 51px);
        // overflow-x: hidden;
        // overflow-y: auto;
      }
    }
  }

  &__actions {
    background-color: #ececec;
    height: 50px;
    overflow: hidden;
    display: flex;
    flex-flow: row-reverse;
    -ms-flex-flow: row-reverse;
    align-items: end;
    justify-content: end;
    width: calc(100% - 60px);
    position: absolute;
    bottom: 25px;

    &.expanded {
      hcl-button {
        &:first-child {
          margin-right: 15px;
        }
      }
    }

    hcl-button {
      margin-left: 20px;
    }
  }
}

.cif-object-mapping-category-selection-wrapper {
  width: 90vw;
  height: 100%;
  position: relative;
  background-color: #ececec;
  box-shadow: -15px 0 10px -2px rgb(0 0 0 / 30%);
}
