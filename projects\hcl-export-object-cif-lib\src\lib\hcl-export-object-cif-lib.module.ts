import { CommonModule } from '@angular/common';
import {  NgModule } from '@angular/core';
import { HclAngularWidgetsLibModule } from 'hcl-angular-widgets-lib';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { HclExportObjectCifLibComponent } from './component/export-mapping/hcl-export-object-cif-lib.component';
import { FieldSubscriptionsComponent } from './component/field-subscriptions/field-subscriptions.component';
import { CifFolderBaseEntitiesComponent } from './component/cif-folder-base-entities/cif-folder-base-entities.component';
import { FolderContainerComponent } from './component/cif-folder-base-entities/folder-container/folder-container.component';
import { GridContainerComponent } from './component/cif-folder-base-entities/grid-container/grid-container.component';
import { HclDataGridModule } from 'hcl-data-grid-lib';

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  declarations: [
    HclExportObjectCifLibComponent,
    FieldSubscriptionsComponent,
    CifFolderBaseEntitiesComponent,
    FolderContainerComponent,
    GridContainerComponent
  ],
  imports: [
    CommonModule,
    HclAngularWidgetsLibModule,
    HclDataGridModule,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      },
      isolate: false
    }),
  ],
  exports: [
    HclExportObjectCifLibComponent
  ],
  providers: []
})
export class HclExportObjectCifLibModule { }
