import { Component, Input } from '@angular/core';
import * as d3 from 'd3';
import { HalfGaugeChartConfig } from './half-gauge';
@Component({
  selector: 'hcl-half-gauge',
  templateUrl: './half-gauge.component.html',
  styleUrls: ['./half-gauge.component.scss']
})
export class HalfGaugeComponent {
  @Input() halfGaugeChartConfig:HalfGaugeChartConfig;
  chartLoaded:boolean = false;
  arcColorFn: any;
  svg:any;
  arcConstruct:any;
  arcConstructMeter:any;
  pie = d3.pie().startAngle( (-1*Math.PI) / 2 )
    .endAngle( Math.PI / 2 );
  ngOnInit(): void {
  }
    ngOnChanges(changes) {
      if (!this.chartLoaded) {
        this.chartLoaded = true;          
        this.pieChartConstruct();
        this.gradientGenerator();
        this.buildGauge();
      }
      this.rotateNeedle();
    }
    /* D3 create wraper for half gauge and initialize piecharts within */
    pieChartConstruct(){
      
      const chartID = this.halfGaugeChartConfig.chartID ? this.halfGaugeChartConfig.chartID : 'halfGauge';
      this.svg = d3.select('#' + chartID)
      .append( "svg" )
      .attr( "width", this.halfGaugeChartConfig.width )
      .attr( "height", (this.halfGaugeChartConfig.width/2)+10 );

      this.arcConstructMeter = d3.arc()
        .innerRadius( 0 )
        .outerRadius( 15*this.halfGaugeChartConfig.width/100 );
    }
    /*Function to generate gradient colors for piechart*/
    gradientGenerator(){
      this.arcColorFn=d3.interpolateHsl(d3.rgb(this.halfGaugeChartConfig.startColor), d3.rgb(this.halfGaugeChartConfig.endColor));
    }
    // fetch the json data. In here we build the guage
    buildGauge(){
    //function to convert percentage to degree of needle
    // initialize pie chart
    const tickData = d3.range(720).map(function () {
      return 1 / 720;
    });
    let deg2rad = deg => (deg * Math.PI) / 180;
    this.arcConstruct = d3.arc()
    .innerRadius( 35*this.halfGaugeChartConfig.width/100 )
    .outerRadius( 50*this.halfGaugeChartConfig.width/100 )
    .startAngle(function (d:any, i:any) {
      var ratio = d * i;
      return deg2rad(ratio * -90);
    })
    .endAngle(function (d:any, i:any) {
      var ratio = d * (i);
      return deg2rad(90 + ratio * -180);
    });
    // draw the arcs. one for each color
    this.svg.selectAll( '.arc' )
      .data(tickData)
      .enter()
      .append( 'path' )
      .attr('class', 'arc')
      .attr( "d", this.arcConstruct )
      .attr( "transform", "translate("+this.halfGaugeChartConfig.width/2+","+this.halfGaugeChartConfig.width/2+")" )
      .style( "fill", ( d:any, i:any ) => {
        return this.arcColorFn(d * i);
      });
            
      // set up the needle
      this.svg?.selectAll( ".needle" )
      .data( [0] )
      .enter()
      .append( 'line' )
      .attr( "x1", 2 )
      .attr( "x2", (-35*this.halfGaugeChartConfig.width/100))
      .attr( "y1", 0 )
      .attr( "y2", 0 )
      .attr("stroke",this.halfGaugeChartConfig.needleColor)  
      .attr("stroke-width",this.halfGaugeChartConfig.width*0.0056)  
      .attr("marker-end","url(#arrow)")
      .classed("needle", true)
      .attr( "transform", "translate("+this.halfGaugeChartConfig.width/2+","+this.halfGaugeChartConfig.width/2+")" );
      //create arrow marker for meter needle
    this.svg?.append("marker")
      .attr("id", "arrow")
      .attr("markerWidth", 10)
      .attr("markerHeight", 9)
      .attr("refX", 7)
      .attr("refY", 3)
      .attr("fill",this.halfGaugeChartConfig.needleColor)
      .attr("orient", "auto")
      .attr("markerUnits", "strokeWidth")
      .append("path")
      .attr("d", "M0,0 L0,6 L9,3 z");   
      //Draw inner meter
      this.svg?.selectAll( '.arc1' )
      .data( this.pie([320]))
      .enter()
      .append( 'path' )
      .attr('class', 'arc')
      .attr( "d", this.arcConstructMeter )
      .attr( "transform", "translate("+this.halfGaugeChartConfig.width/2+","+(-5+(this.halfGaugeChartConfig.width/2))+")" )
      .style( "stroke", this.halfGaugeChartConfig.innerCircleColor)
      .style( "fill", this.halfGaugeChartConfig.innerCircleColor);
      this.svg?.append('rect')
      .attr('x', 0)
      .attr('y', 0)
      .attr('width', 15*this.halfGaugeChartConfig.width/50)
      .attr('height', 5)
      .attr( "transform", "translate("+((this.halfGaugeChartConfig.width/2)-(15*this.halfGaugeChartConfig.width/100))+","+(-5+(this.halfGaugeChartConfig.width/2))+")" )
      .attr('stroke', this.halfGaugeChartConfig.innerCircleColor)
      .attr('fill', this.halfGaugeChartConfig.innerCircleColor);
          this.svg?.append("text");
      }
  rotateNeedle(){
    let rotationDegree = percentage => percentage*1.8;
    //Set needle rotation as per percentage provided
    this.svg?.selectAll( ".needle" ).data( [0] )
      .transition()
      .ease( d3.easeElasticOut )
      .duration( 2000 )
      .attr( "transform", ()=> {
        return "translate("+this.halfGaugeChartConfig.width/2+","+((this.halfGaugeChartConfig.width/2)-2)+") rotate(" + rotationDegree(this.halfGaugeChartConfig.reading/this.halfGaugeChartConfig.maxReading*100) + ")"
    });
    if(!this.halfGaugeChartConfig.hideReading){
      //Display percentage in small piechart of needle
      let percentage=String(this.halfGaugeChartConfig.reading).padStart(2, '0');
      this.svg?.selectAll('text') 
      .text(percentage)
      .attr("x", -this.halfGaugeChartConfig.width*(percentage.length<3?5.35714283:8.4)/100)
      .attr("y", -this.halfGaugeChartConfig.width*1.78571428571/100)
      .attr( "transform", "translate("+this.halfGaugeChartConfig.width/2+","+this.halfGaugeChartConfig.width/2+")" )
      .attr("fill",this.halfGaugeChartConfig.readingColor)
      .style("font", this.halfGaugeChartConfig.readingFont)
      .style("font-size", this.halfGaugeChartConfig.width*10.5/100+"px");
    }
  }
}
 