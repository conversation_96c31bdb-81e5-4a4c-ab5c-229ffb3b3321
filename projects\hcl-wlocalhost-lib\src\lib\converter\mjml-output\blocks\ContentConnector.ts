import { contentConnectorSourceType } from '../../../interfaces';
import { IContentConnectorBlockOptions, ContentConnectorBlock, RenderingClass } from '../interfaces';
import { createPadding, createLineHeight, ignoreHTMLMinParse, createBorder, uniqueId } from '../utils';

export class ContentConnector implements ContentConnectorBlock, RenderingClass {
  constructor(public options: IContentConnectorBlockOptions) { }
  
  render() {
    const {  contentSourceType, contentSourceInfo, retrieveContentWhen, retrieveContentForViewAsWebpage } = this.options;
    const anchorTemplate = `
    <mj-text>
      <div style="font-size:initial !important;line-height:normal;color:initial;font-weight:initial;" class="hide-on-${this.options.hideOn}">
        <div class="ip-content-connector-block droppable contentConnector-droppable">
          <div style="width: 100%">
            <a href="${contentSourceInfo?.url}" data-isCms="${contentSourceType === contentConnectorSourceType.CMS_SYSTEM}" target="${contentSourceInfo?.url}" data-retrieveContentWhen="${retrieveContentWhen}" data-retrieveContentForViewAsWebpage="${retrieveContentForViewAsWebpage}" ${contentSourceType === contentConnectorSourceType.CMS_SYSTEM ? `data-environment="${contentSourceInfo?.environment}" data-cmsName="${contentSourceInfo?.cmsName}" data-contentId="${contentSourceInfo?.contentId}"` : ''}>${contentSourceInfo?.url}</a>
          </div>
        </div>
      </div>
    </mj-text>`;

    if (this.options.hideOn === 'desktop') {
      return `
        <mj-raw>
        <!--[if !mso]><!-- --></mj-raw>
        ${anchorTemplate}
        <mj-raw><!--<![endif]--></mj-raw>
      `;
    } else {
      return anchorTemplate;
    }
  }
}
