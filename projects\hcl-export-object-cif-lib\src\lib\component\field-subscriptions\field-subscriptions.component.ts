import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { HclExportObjectCifLibService } from '../../hcl-export-object-cif-lib.service';

@Component({
  selector: 'hcl-export-cif-field-subscriptions',
  templateUrl: './field-subscriptions.component.html',
  styleUrls: ['./field-subscriptions.component.scss']
})
export class FieldSubscriptionsComponent implements OnInit {

  @Input() config: any;
  @Input() isEditMode: boolean;
  @Output() mappedData: EventEmitter<any> = new EventEmitter<any>();

  loadContent = false;
  targetSchema: any;
  sourceSchema: any;
  sourceSchemaAttributes = []; // attributes from application schema api
  targetAttributeDropdownOptions = []; // service attributes combined with html to show as dropdown option
  serviceAttributesMap = new Map<string, string>(); // Service attribute id-title mapping
  crossAttributesIdMappingMap = new Map<string, string>(); // service attr id-application attr id mapping
  contentMappingMap = new Map<number, any>(); // indexed map of mapped attributes with both service and application id & titles
  contentMappingConfigData = [];
  fieldMappingFormGroup: UntypedFormGroup;

  constructor(private hclExportObjectCifLibService: HclExportObjectCifLibService) { }

  ngOnInit(): void {
    // as this is outbound mapping we have to reverse the mapping context
    this.targetSchema = JSON.parse(this.hclExportObjectCifLibService.sourceSchema);
    this.sourceSchema = JSON.parse(this.hclExportObjectCifLibService.targetSchema);
    this.fieldMappingFormGroup = new UntypedFormGroup({});
    this.loadAttributes();
  }

  loadAttributes() {
    this.loadContent = false;
    this.sourceSchemaAttributes = [];
    this.targetAttributeDropdownOptions = [];
    if (this.sourceSchema && this.sourceSchema.properties) {
      for (const property in this.sourceSchema.properties) {
        if (this.sourceSchema.properties[property]) {
          this.sourceSchemaAttributes.push(this.sourceSchema.properties[property]);
        }
      }
    }


    this.hclExportObjectCifLibService.mappingDataMap.forEach((value, key) => {
      this.crossAttributesIdMappingMap.set(value.attribute1Id, value.attribute2Id);
    });


    if (this.targetSchema && this.targetSchema.properties) {
      for (const property in this.targetSchema.properties) {
        if (this.targetSchema.properties[property]) {
          const item = this.targetSchema.properties[property];
          this.targetAttributeDropdownOptions.push({
            label: `${item.title} (${this.capitalizeFirstLetter(item.format || item.type + '')})`, value: item.$id,
            toolTipHTML: `<span>${item.title} (${this.capitalizeFirstLetter(item.format || item.type + '')})</span>`,
            item: item
          });
          this.serviceAttributesMap.set(item.$id, item.title);
        }
      }
    }

    if (this.sourceSchemaAttributes.length) {
      this.sourceSchemaAttributes.forEach((item, index) => {
        this.loadContentItems(item, index);
      });
      this.emitMappedData();
    }

    if (this.config.applicationMode === 'VIEW') {
      this.contentMappingConfigData.map((configData: any) => {
        configData[1].dropdownConfig.disabled = true;
      });
    }

    this.loadContent = true;
  }

  loadContentItems(attribute, index) {
    this.fieldMappingFormGroup.addControl(attribute.$id, new UntypedFormControl(null));

    if (attribute.required) {
      this.fieldMappingFormGroup.controls[attribute.$id].setValidators([Validators.required]);
    }

    const options = this.returnOptionsAccordingToType(attribute);

    if (options && options.length) {
      options.unshift({
        label: `--${this.config.translations.select}--`,
        value: null,
      });
    }

    const config = [
      {
        displayTitle: `${attribute.title} (${this.capitalizeFirstLetter(
          (attribute.format && attribute.format !== this.hclExportObjectCifLibService.applicationConstants.lowerCaseAuto && attribute.format)
          || attribute.type + '')})`, ...attribute
      }, {
        dropdownConfig: {
          options: [...options],
          baseOptions: [...options],
          isToolTip: true,
          placeholder: options && options.length ? this.config.translations.selectAttribute :
            this.config.translations.noCompatibleAttrAvailableForMapping,
          name: attribute.$id,
          formControl: this.fieldMappingFormGroup.controls[attribute.$id],
          dynamicAction: { templateName: 'mappedAttribute', template: null },
          disabled: options && options.length ? false : true,
          errorList: [{
            errorCondition: 'required',
            errorMsg: this.config.translations.requiredField
          }],
          previousMappingError: this.isEditMode ? this.checkAndUpdatePreviousMappingMap(this.crossAttributesIdMappingMap.get(attribute.$id), options) : false
        },
        searchInput: {
          name: 'mappedAttributeSearch',
          placeholder: this.config.translations.search,
          formControlName: new UntypedFormControl(''),
          icon: 'hcl-icon-search',
          type: 'text',
          autofocus: true
        }
      }, {
        // templateEdit: this.hclExportObjectCifLibService.previousObjectMappingData ? true : false,
        edited: false
      }];


    if (this.crossAttributesIdMappingMap.size && this.crossAttributesIdMappingMap.has(attribute.$id)) {
      this.fieldMappingFormGroup.controls[attribute.$id].setValue(this.crossAttributesIdMappingMap.get(attribute.$id));
      const attributeMapping = {
        attribute1Id: attribute.$id,
        attribute1Title: attribute.title,
        attribute2Id: this.crossAttributesIdMappingMap.get(attribute.$id),
        attribute2Title: this.serviceAttributesMap.get(this.crossAttributesIdMappingMap.get(attribute.$id))
      };
      this.contentMappingMap.set(index, attributeMapping);
    } else {
      this.fieldMappingFormGroup.controls[attribute.$id].setValue('');
    }
    this.setMapping(config);
  }

  returnOptionsAccordingToType(attribute) {
    let options = [];
    switch (attribute.type.toUpperCase()) {
      case this.hclExportObjectCifLibService.applicationConstants.capitalString():
        if (attribute.hasOwnProperty('format') &&
          attribute.format.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalUrl()) {
          options = this.targetAttributeDropdownOptions
            .filter(item => item.item && item.item.type && item.item.format &&
              (item.item.format.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalUrl)
              && item.item.type.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalString);
        } else if (attribute.hasOwnProperty('format') &&
          attribute.format.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalHtml()) {
          options = [...this.targetAttributeDropdownOptions];
        } else if (attribute.hasOwnProperty('format') &&
          attribute.format.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalEmail()) {
          options = this.targetAttributeDropdownOptions
            .filter(item => (item.item && item.item.type &&
              (item.item.type.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalString())) ||
              (item.item && item.item.type && item.item.format &&
                (item.item.format.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalEmail())));
        } else {
          const htmlFilteredAttributes = this.targetAttributeDropdownOptions.filter(attr => {
            return !attr.item.format ||
              attr.item.format.toUpperCase() !== this.hclExportObjectCifLibService.applicationConstants.capitalHtml();
          });
          options = [...htmlFilteredAttributes];
        }
        return options;
      case this.hclExportObjectCifLibService.applicationConstants.capitalNumber():
        options = this.targetAttributeDropdownOptions
          .filter(item => item.item && item.item.type &&
            (item.item.type.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalInteger() ||
              item.item.type.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalNumber()));
        return options;
      case this.hclExportObjectCifLibService.applicationConstants.capitalInteger():
        if (attribute.hasOwnProperty('format') &&
          attribute.format.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalDateTime()) {
          options = this.targetAttributeDropdownOptions
            .filter(item => item.item && item.item.type && item.item.format &&
              (item.item.format.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalDateTime())
              && item.item.type.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalInteger());
        } else {
          options = this.targetAttributeDropdownOptions
            .filter(item => item.item && item.item.type &&
              item.item.type.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalInteger());
        }
        return options;
      case this.hclExportObjectCifLibService.applicationConstants.capitalBoolean():
        options = this.targetAttributeDropdownOptions
          .filter(item => item.item && item.item.type &&
            item.item.type.toUpperCase() === this.hclExportObjectCifLibService.applicationConstants.capitalBoolean());
        return options;
    }
  }

  checkAndUpdatePreviousMappingMap(targetAttribute: string, options) {
    let isMappedAttribute = false;
    this.crossAttributesIdMappingMap.forEach((key) => {
      if (key === targetAttribute) {
        isMappedAttribute = true;
      }
    });
    let previousMappingTargetAttrAvailable = true;
    if (isMappedAttribute) {
      previousMappingTargetAttrAvailable = options.some((option: any) => option.item && option.item.$id === targetAttribute);
    }

    if (isMappedAttribute && !previousMappingTargetAttrAvailable) {
      this.hclExportObjectCifLibService.invalidPreviousMappingMap.set(targetAttribute, true);
      // this.serviceAttributesMap.delete(targetAttribute);
      return true;
    }
    return false;
  }

  mappedAttributeSelection(attributeValue, config, index) {
    config[1].dropdownConfig.previousMappingError = false;
    this.hclExportObjectCifLibService.invalidPreviousMappingMap.delete(config[0]['$id']);
    if (attributeValue) {
      const attributeMapping = {
        attribute1Id: config[0]['$id'],
        attribute1Title: config[0]['title'],
        attribute2Id: attributeValue,
        attribute2Title: this.serviceAttributesMap.has(attributeValue) ? this.serviceAttributesMap.get(attributeValue) : ''
      };
      this.contentMappingMap.set(index, attributeMapping);
    } else {
      if (this.contentMappingMap.has(index)) {
        this.contentMappingMap.delete(index);
      }
    }

    config[1].searchInput.icon = 'hcl-icon-search';
    this.emitMappedData();
  }


  emitMappedData() {
    if (this.contentMappingMap.size) {
      this.mappedData.emit({ contentMapping: this.contentMappingMap, formState: this.fieldMappingFormGroup.valid });
    } else {
      this.mappedData.emit();
    }
  }

  clearMappedAttributeSearch(mappingConfig) {
    mappingConfig.dropdownConfig.options = [...mappingConfig.dropdownConfig.baseOptions];
    mappingConfig.searchInput.formControlName.setValue('');
  }

  filterMappedAttribute(searchString, mappingConfig) {
    if (typeof searchString === 'string') {
      mappingConfig.dropdownConfig.options = [...mappingConfig.dropdownConfig.baseOptions];
      if (searchString.length > 0) {
        mappingConfig.dropdownConfig.options = mappingConfig.dropdownConfig.options
          .filter(element => (element.label + '').toUpperCase().includes((searchString + '').toUpperCase()));
      }
      this.setIcon(searchString, mappingConfig.searchInput);
    }
  }

  setIcon(searchString, inputConfig) {
    if (searchString) {
      inputConfig.icon = 'hcl-icon-close-x';
    } else {
      inputConfig.icon = 'hcl-icon-search';
    }
  }

  setMapping(attributeConfig: any) {
    this.contentMappingConfigData.push(attributeConfig);
  }


  capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }
}
