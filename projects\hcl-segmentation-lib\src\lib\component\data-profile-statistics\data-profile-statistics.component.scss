@use '../../assets/scss/variables' as *;

.statistics-container {
    h3 {
        font-family: $font-primary;
        font-size: 16px;
        font-weight: 600;
        line-height: 19.5px;
        color: #6D7692;
        padding-bottom: 20px;
    }

    .loader-sec{
        width: 100%;
        height: 60dvh;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .row-item {
        font-family: $font-secondary;
        font-size: 14px;
        font-weight: 400;
        line-height: 40px;
        display: flex;
        align-items: center;
        min-height: 30px;
        overflow: hidden;
        max-width: 350px;

        .row-label {
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 0 10px 0 0;
            color: #6D7692;
        }

        .row-value {
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #444444;
        }

        .w-40 {
            width: 40%;
        }

        .w-60 {
            width: 60%;
        }
    }
}