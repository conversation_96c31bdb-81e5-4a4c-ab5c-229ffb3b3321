import { Component, OnInit, ViewChild } from '@angular/core';
import { ApacheDonutChartConfig } from 'projects/hcl-angular-charts-lib/src/public-api';
import { ApacheChartComponent } from 'projects/lib/hcl-angular-charts-lib';

@Component({
  selector: 'app-apache-donut-chart-demo',
  templateUrl: './apache-donut-chart-demo.component.html',
  styleUrls: ['./apache-donut-chart-demo.component.scss']
})
export class ApacheDonutChartDemoComponent implements OnInit {

  @ViewChild('apacheDonutChart1') apacheDonutChart1:ApacheChartComponent;

  apacheDonutChartConfig: ApacheDonutChartConfig = {
    title: {
      id : 'donutChart1'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: '5%',
      left: 'center'
    },
    series: [
      {
        name: 'Sales Target Achieved',
        type: 'pie',
        radius: ['40%', '60%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 1048, name: '2020' },
          { value: 735, name: '2021' },
          { value: 580, name: '2022' },
          { value: 484, name: '2023' },
          { value: 300, name: '2024' }
        ]
      }
    ]
  };

  constructor() { }

  ngOnInit(): void {
  }

}
