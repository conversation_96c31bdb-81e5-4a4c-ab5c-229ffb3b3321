<div class="w-100 h-100 responder-history-info-container">
    <div class="info-title mb-3">{{ 'APPROVALPICKER.TITLES.RESPONSE_STATUS' | translate }}</div>
    <div class="grid-container mt-2 mb-3 pr-3">
        <hcl-data-grid-v2 [config]="responseStatusConf" (gridReady)="onGridReady($event)">
            <ng-template hclTemplate hclTemplateName="nameCell" type="cell-renderer" let-cell>
                <span *ngIf="cell.row">{{printApprovers(cell.row)}}</span>
            </ng-template>
            <ng-template hclTemplate hclTemplateName="statusCell" type="cell-renderer" let-cell>
                <span>{{printStatus(cell.row.statusCode)}}</span>
                <span
                    *ngIf="!(cell.row.statusCode === 'NOT_NOTIFIED' || cell.row.statusCode === 'WAITING') && cell.row.respDate">{{printResponseDate(cell.row.respDate)}}</span>
            </ng-template>
            <ng-template hclTemplate hclTemplateName="requiredCell" type="cell-renderer" let-cell>
                <hcl-checkbox [config]="setRequiredVal(cell.row)"></hcl-checkbox>
            </ng-template>
            <ng-template hclTemplate hclTemplateName="instructionCell" type="cell-renderer" let-cell>
                <span class="cell-tooltip disable-browser-tooltip" hclTooltip="{{cell.row.instructions}}"
                    data-position="bottom">{{cell.row.instructions}}</span>
            </ng-template>
        </hcl-data-grid-v2>
    </div>

    <div class="info-title mb-3">
        <span>{{ 'APPROVALPICKER.TITLES.RESP_HISTORY' | translate }}</span>
        <span class="pl-4 total-records">{{ 'APPROVALPICKER.TITLES.COMMENTS_PARAM' | translate : {num : historyList.length} }}</span>
    </div>
    <ul class="response-history-list">
        <ng-container *ngIf="historyList.length > 0">
            <li class="history-record position-relative" *ngFor="let record of historyList; let i = index">
                <span>#{{historyList.length - i}}. </span>
                <span class="ellipsis hoverable-text" *ngIf="record.user"
                    hclTooltip="{{record.user.nameWithTimeZone}}">{{record.user.nameWithTimeZone}}</span>
                <span class="ellipsis hoverable-text" *ngIf="record.team"
                    hclTooltip="{{record.team.name}}">{{record.team.name}}</span>
                <span *ngIf="record.lastModDate">{{printResponseDate(record.lastModDate)}} </span>
                <span [ngClass]="getRecordStatusClass(record.curState)">{{printStatus(record.curState)}} </span>
                <span *ngIf="record.denyReasonId">{{'APPROVALPICKER.TITLES.DENY_REASON' | translate}} :
                    {{record.detailedHistory}} </span>
                <span class="ellipsis hoverable-text disable-browser-tooltip"
                    hclTooltip="{{record.comments}}">{{record.comments}} </span>
            </li>
        </ng-container>
        <li *ngIf="historyList.length === 0" class="h-100 history-record no-record">
            <span>{{'APPROVALPICKER.TITLES.NO_RESP_COMMENT' | translate}}</span>
        </li>
    </ul>
</div>