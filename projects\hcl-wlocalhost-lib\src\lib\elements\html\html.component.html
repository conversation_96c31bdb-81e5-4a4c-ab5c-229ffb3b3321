<div class="html-snippet" style="display: block; overflow: auto; z-index: 0; position: relative;">
  <p *ngIf="snippetRuleAdded" class="rule-count-container" (mouseover)="ruleCountMouseHovered()">
    <span class="rule-count">{{block?.options?.rules?.length}}</span>
    <hcl-button class="dynamic-rule-button" [config]="ruleNameButton" [matMenuTriggerFor]="menu">
    </hcl-button>

    <hcl-button class="dynamic-rule-button" *ngIf="false" [config]="buttonConf" (onclick)="dynamicContentClick();">
    </hcl-button>

    <mat-menu #menu="matMenu" class="rulesdropdown">
      <button mat-menu-item (click)="onDefaultRuleClick()">
        <span> {{ 'RULE_BUILDER.DEFAULT_RULE' | translate}} </span>
      </button>
      <button mat-menu-item *ngFor="let rule of block.options?.rules" (click)="onRuleClick(rule)">
        <span> {{ rule?.name }} </span>
      </button>
    </mat-menu>

  </p>

  <froala-editor (froalaKeyUp)="keyUpCurrentBlock($event)" (froalaKeyDown)="keyDownCurrentBlock($event)"
    (froalaFocus)="focusCurrentBlock($event)" [pfList]="ngb.personalizedTagsFull.data" (showAISidebar)="showAITemp($event)" (showLinkSidebar)="showHyperlinkTemp($event)" (saveLinkInfo)="updateHyperLinkInfo($event)" (froalaInitialized)="setContent($event)" (froalaBlur)="contentChanged($event)" style="display: flex; color:{{block?.options?.color}}; padding:{{this.createPadding(block?.options?.padding)}}; vertical-align:middle; line-height:{{createLineHeight(block?.options?.lineHeight)}}; font-family:{{block?.options?.font?.family}}, {{block?.options?.font?.fallback}}; font-size:{{block?.options?.font?.size}}px; font-style:{{block?.options?.font?.style}}; font-weight:{{block?.options?.font?.weight}}; z-index: 0; position:relative;" (froalaContentChanged)="contentChanged($event)"></froala-editor>

  <!-- <ng-container *ngIf="innerHtml">
    <div [innerHTML]="innerHtml"></div> 
  </ng-container>-->

  <ng-container *ngIf="(froalaEditor?.editorInstance?.html?.get().trim().length <= 0 || froalaContents.length <= 0)">
    <div class="init-msg"> {{'messages.html-snippet-area' | translate}}</div>
  </ng-container>
</div>
<mat-error *ngIf="block.errors && block.errors.length > 0">
  <div class="error-container">
    <span class="hcl-icon-error" [matTooltip]="fetchComponentErrorMessages()"
      [matTooltipClass]="'multiline-tooltip'"></span>
  </div>
</mat-error>
