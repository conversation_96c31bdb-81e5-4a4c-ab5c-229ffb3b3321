import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root'
})
export class CifFolderBaseCategoriesService {

  constructor(
    private http: HttpClient,
    private notificationService: NotificationService) { }

  private _baseUrl;
  private _headers;
  private _selectedFolderData: Subject<any> = new Subject<any>();
  private _selectedFolderName: any;


  get baseUrl() {
    return this._baseUrl;
  }

  set baseUrl(value: string) {
    this._baseUrl = value + '/api/AssetPicker';
  }

  get headers(): HttpHeaders {
    return this._headers;
  }

  set headers(value: HttpHeaders) {
    this._headers = value;
  }

  get selectedFolderNAme() {
    return this._selectedFolderName;
  }

  set selectedFolderNAme(value: any) {
    this._selectedFolderName = value;
  }


  public getCategoriesByFolderId(repo: string, folderId: string) {
    return this.http.get(this.baseUrl + '/' + repo + '/categories?folderId=' + folderId, { headers: this.headers, withCredentials: false });
  }

  setselectedFolderData(value: any) {
    this._selectedFolderData.next(value);
  }

  getselectedFolderData(): any {
    return this._selectedFolderData.asObservable();
  }


  public getFolders(systemId: any, folder: any, ignoreLoader?: boolean): Observable<any> {
    // if (folder) {
    let headerCopy = this.headers;
    if (ignoreLoader) {
      headerCopy = headerCopy.append('ignoreLoader', 'true');
    }
    return this.http.get(this.baseUrl + '/' + systemId + '/folders?categories=true&parentFolderId=' + (folder ? folder.id : 1),
      { headers: headerCopy, withCredentials: false });
    // } else {
    //   return this.http.get(this.baseUrl + '/' + systemId + '/folders?categories=true',
    //     { headers: this.headers, withCredentials: false });
    // }
  }


  handleServerError(errorMsg: string) {
    this.notificationService.show({
      message: errorMsg,
      type: 'error', close: true, autoHide: 6000
    });
  }

  /* this method supports the grid config */
  getHeaders(): HttpHeaders {
    return this.headers;
  }


  cleanUpData() {
    this.baseUrl = null;
    this.headers = null;
    this.setselectedFolderData(null);
  }
}
