import { NgModule } from "@angular/core";
import { DataGridV2Component } from "./data-grid-v2.component";
import { AgGridModule } from "ag-grid-angular";
import { CommonModule } from "@angular/common";
import { HclAngularWidgetsLibModule } from "hcl-angular-widgets-lib";
import { GroupRowInnerRendererComponent } from "./ag-grid/group-row-inner-renderer/group-row-inner-renderer.component";
import { CustomTooltipComponent } from "./ag-grid/custom-tooltip/custom-tooltip.component";
import { CustomCellComponent } from "./ag-grid/custom-cell/custom-cell.component";
import { CustomCellEditorComponent } from "./ag-grid/custom-cell-editor/custom-cell-editor.component";
import { CustomHeaderComponent } from "./ag-grid/custom-header/custom-header.component";
import { CustomPopoverComponent } from "./ag-grid/custom-popover/custom-popover.component";
import { FullWidthCellRenderer } from "./ag-grid/full-width-cell-renderer/full-width-cell-renderer.component";
import { ColumnSelectionComponent } from "./column-selection/column-selection.component";
import { LicenseManager } from "@ag-grid-enterprise/core";

export function initAgGridLicense() {
  LicenseManager.setLicenseKey(
    "CompanyName=HCL TECHNOLOGIES LIMITED,LicensedGroup=HCL-SOFTWARE,LicenseType=MultipleApplications,LicensedConcurrentDeveloperCount=12,LicensedProductionInstancesCount=200,AssetReference=AG-024688,ExpiryDate=14_April_2023_[v2]_MTY4MTQyNjgwMDAwMA==8d743fa36624cb82df28053d2c3448cd"
  );
}

@NgModule({
  declarations: [
    DataGridV2Component,
    CustomTooltipComponent,
    GroupRowInnerRendererComponent,
    CustomCellComponent,
    CustomCellEditorComponent,
    CustomHeaderComponent,
    CustomPopoverComponent,
    FullWidthCellRenderer,
    ColumnSelectionComponent
  ],
  imports: [
    CommonModule,
    HclAngularWidgetsLibModule,
    AgGridModule.withComponents([
      GroupRowInnerRendererComponent,
      CustomTooltipComponent,
    ]),
  ],
  exports: [DataGridV2Component, ColumnSelectionComponent],
})
export class HclDataGridModule {}
