import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StackedSingleBarChartComponent } from './stacked-single-bar-chart.component';

describe('StackedSingleBarChartComponent', () => {
  let component: StackedSingleBarChartComponent;
  let fixture: ComponentFixture<StackedSingleBarChartComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ StackedSingleBarChartComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StackedSingleBarChartComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
