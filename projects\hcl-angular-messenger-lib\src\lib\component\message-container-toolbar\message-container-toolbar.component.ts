import {Component, EventEmitter, OnInit, Output, ViewChild, ViewEncapsulation} from '@angular/core';
import {ButtonConf, InputConfig, MenuComponent, MenuConfig} from 'hcl-angular-widgets-lib';
import {UntypedFormControl} from '@angular/forms';

@Component({
  selector: 'hcl-message-container-toolbar',
  templateUrl: './message-container-toolbar.component.html',
  styleUrls: ['./message-container-toolbar.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class MessageContainerToolbarComponent implements OnInit {
  /***
   * Constants in this Toolbar
   */
  public static readonly FILTER_ALL_MESSAGES: number = 1;
  public static readonly FILTER_TAGGED_YOU: number = 2;
  public static readonly FILTER_FLAGGED_YOU: number = 3;
  /**
   * The instance of the Filter menu
   */
  @ViewChild('filterMenu') filterMenu: MenuComponent;
  // Disable the filter based on search is cancelled either by clicking on cross icon or by removing all the text in search input
  @Output() cancelSearch: EventEmitter<boolean> = new EventEmitter<boolean>();
  // Emit the text in search text box to parent component
  @Output() searchedText: EventEmitter<{filter: {id: number}, text: string}> = new EventEmitter<{filter: {id: number}, text: string}>();
  // The current selected Filter
  private selectedFilter: {id: number};
  /**
   * we have a filter button in the top
   * this is the configuration fot the button
   */
  filterButtonConfig: ButtonConf = {
    buttonType: 'mat',
    color: 'accent',
    borderRadius: 5,
    name: 'filterBtn',
    type: 'button',
    isIconButton: true,
    value: 'All messages',
    iconRight: 'hcl-icon-angle-down'
  };
  /**
   * The drop down menu for the Filter
   */
  filterMenuConfig: MenuConfig = {
    items: [{
      label: 'All messages',
      title: 'All messages',
      data: {
        id: MessageContainerToolbarComponent.FILTER_ALL_MESSAGES
      }
    },
    {
      label: 'Tagged you',
      title: 'Tagged you',
      data: {
        id: MessageContainerToolbarComponent.FILTER_TAGGED_YOU
      }
    },
    {
      label: 'Flagged',
      title: 'Flagged',
      data: {
        id: MessageContainerToolbarComponent.FILTER_FLAGGED_YOU
      }
    }]
  };
  /**
   * The configuration that is required for the search box
   */
  searchBoxConfig: InputConfig = {
    icon: 'hcl-icon-search',
    type: 'text',
    name: 'search',
    maximumLength: 256,
    placeholder: 'Search message',
    formControlName: new UntypedFormControl('')
  };
  /**
   * The default constructor
   */
  constructor() { }
  /**
   * Do initialization if required
   */
  ngOnInit(): void {
  }
  /**
   * This function will open the Filter drop-down menu
   * param event
   */
  openFilterMenu(event) {
    this.filterMenu.openMenu(event);
    this.filterButtonConfig.iconRight = 'hcl-icon-angle-up';
  }
  /**
   * When the filter menu is closed this function is called
   * This function will just update the icon of the filter button
   */
  filterMenuClosed() {
    this.filterButtonConfig.iconRight = 'hcl-icon-angle-down';
  }
  /**
   * This function will set the selected option of the filter
   * param data
   */
  setSelectedFilter(data) {
    // if the new value is different than the current value then only do server call
    if (this.selectedFilter.id !== data.item.data.id) {
      this.filterButtonConfig.value = this.filterMenuConfig.items.find(x => x.data.id ===  data.item.data.id).label;
      this.selectedFilter = data.item.data;
      this.searchedText.emit({
        filter: this.selectedFilter,
        text: this.searchBoxConfig.formControlName.value
      });
    }
  }
  /**
   * Toggle icon based on input value present or not
   * param text
   */
  toggleIconOnChange() {
    if (!this.searchBoxConfig.formControlName.value || this.searchBoxConfig.formControlName.value === '') {
      this.searchBoxConfig.icon = 'hcl-icon-search';
      // emit false value to disable the filter
      this.cancelSearch.emit(false);
    }
  }
  /**
   * Method for icon click. For search icon click, if value is present emit search event toggle icon to close. Search event omce triggered
   * should be responsible for disabling the filter control
   * For close icon click
   */
  searchOrCloseIconClick() {
    if (this.searchBoxConfig.icon === 'hcl-icon-close-x') {
      this.searchBoxConfig.formControlName.setValue('');
      this.searchBoxConfig.icon = 'hcl-icon-search';
      // emit false value to disable the filter
      this.cancelSearch.emit(false);
    } else if (this.searchBoxConfig.icon === 'hcl-icon-search' && this.searchBoxConfig.formControlName.value
      && this.searchBoxConfig.formControlName.value !== '') {
      this.searchedText.emit({
        filter: this.selectedFilter,
        text: this.searchBoxConfig.formControlName.value
      });
      this.searchBoxConfig.icon = 'hcl-icon-close-x';
    }
  }
}
