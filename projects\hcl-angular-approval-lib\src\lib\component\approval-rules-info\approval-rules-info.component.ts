import { Component, OnInit, On<PERSON><PERSON>roy, Input, OnChanges, ViewEncapsulation } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { Approval } from '../../models/approval';
import { RadioConfig, CheckboxConfig } from 'hcl-angular-widgets-lib';
import { UntypedFormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'hcl-approval-rules-info',
  templateUrl: './approval-rules-info.component.html',
  styleUrls: ['./approval-rules-info.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ApprovalRulesInfoComponent implements OnInit, OnDestroy, OnChanges {

  @Input() approval: Approval;
  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
  rulesconfig: RadioConfig = {
    value: '',
    name: 'rules',
    elements: [
      {
        label: this._translate.instant('APPROVALPICKER.TITLES.IF_ALL'),
        checked: false, value: '101', color: 'primary'
      },
      {
        label: this._translate.instant('APPROVALPICKER.TITLES.IF_CURRENT'),
        checked: false, value: '102', color: 'primary'
      },
      {
        label: this._translate.instant('APPROVALPICKER.TITLES.IF_SELECT'),
        checked: false, value: '103', color: 'primary'
      }
    ],
    horizontal: false,
    formControl: new UntypedFormControl()
  };
  dispositionConfig: CheckboxConfig = {
    type: 'single',
    formControl: new UntypedFormControl(),
    singleCheckboxData: {
      name: 'disposition',
      value: 'disposition',
      disabled: false,
      checked: false,
      label: this._translate.instant('APPROVALPICKER.TITLES.ENABLE_REQUIRE'),
      color: 'primary'
    }
  };
  enableCommentsConfig: CheckboxConfig = {
    type: 'single',
    formControl: new UntypedFormControl(),
    singleCheckboxData: {
      name: 'enableComments',
      value: 'enableComments',
      disabled: false,
      checked: false,
      label: this._translate.instant('APPROVALPICKER.TITLES.ENABLE_COMMENT'),
      color: 'primary'
    }
  };
  autoCompleteConfig: CheckboxConfig = {
    type: 'single',
    formControl: new UntypedFormControl(),
    singleCheckboxData: {
      name: 'autoComplete',
      value: 'autoComplete',
      disabled: false,
      checked: false,
      label: this._translate.instant('APPROVALPICKER.TITLES.ENABLE_AUTO'),
      color: 'primary'
    }
  };
  approveWithChangesConfig: CheckboxConfig = {
    type: 'single',
    formControl: new UntypedFormControl(),
    singleCheckboxData: {
      name: 'approveWithChanges',
      value: 'approveWithChanges',
      disabled: false,
      checked: false,
      label: this._translate.instant('APPROVALPICKER.TITLES.ALLOW_WITH_CHANGE'),
      color: 'primary'
    }
  };

  constructor(private _translate: TranslateService) { }

  ngOnChanges() {
    if (this.approval) {
      this.setRulesInfo();
      this.setRulesParameters();
    }
  }

  ngOnInit(): void {
    this.toggleUIControls();
  }

  private setRulesInfo() {
    this.rulesconfig.formControl.setValue(this.approval.reapprovalRule ? this.approval.reapprovalRule.toString() : null);
  }

  private setRulesParameters() {
    this.dispositionConfig.formControl.setValue(this.setCheckboxValue(this.approval.dispositionAllDocs));
    this.enableCommentsConfig.formControl.setValue(this.setCheckboxValue(this.approval.enableCommtsAttchmnt));
    this.autoCompleteConfig.formControl.setValue(this.setCheckboxValue(this.approval.autoComplete));
    this.approveWithChangesConfig.formControl.setValue(this.setCheckboxValue(this.approval.approveWithChanges));
  }

  private setCheckboxValue(data: string): boolean {
    if (data) {
      return data === 'Y' ? true : false;
    }
    return false;
  }

  private toggleUIControls() {
    this.rulesconfig.disabled = true;
    this.dispositionConfig.formControl.disable();
    this.enableCommentsConfig.formControl.disable();
    this.autoCompleteConfig.formControl.disable();
    this.approveWithChangesConfig.formControl.disable();
  }

  ngOnDestroy() {
    this.subscriptionList.forEach(sub => {
      sub.unsubscribe();
    });
  }

}
