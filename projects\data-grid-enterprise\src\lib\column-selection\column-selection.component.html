<!-- <div class="add-remove-column">
    <a (click)="_openMenu()">
    Add/Remove Column
  </a>
    <hcl-menu [config]="menuConfig" #menu>
    </hcl-menu>
</div> -->

<div class="float-right addRemoveColumnOuter">
  <hcl-button [config]="addRemoveColBtn" (onclick)="openAddColModal($event)"></hcl-button>
  <div class="popupMenuOuter">
    <hcl-popup-menu config="" popupMenuTemplate="menutemplate" #addRemoveCol>
      <ng-template hclTemplate hclTemplateName="menutemplate" type="menutemplate">
        <div (click)="$event.stopPropagation();" class="add-remove-col-outer">
          <hcl-input *ngIf="config?.isSearchField" (valueEntered)="searchColumn($event)" [config]="searchBoxConfig" class="col-12 mt-2"></hcl-input>
          <div class="check-box-outer">
            <hcl-checkbox [config]="userColumnConfig" (selectionChanged)="userPrefferedCols($event)" #chckbx>
            </hcl-checkbox>
          </div>
          <div class="template-action">
            <div class="d-flex px-2 button-container">
              <hcl-button class="resetBtn" [config]="resetToDefBtnConfig" (onclick)="resetToDefault($event)">
              </hcl-button>
              <hcl-button class="applyBtn ml-3" [config]="applyBtnConfig" (onclick)="colConfigApplyFn($event)">
              </hcl-button>
            </div>
          </div>
        </div>
      </ng-template>
    </hcl-popup-menu>
  </div>
</div>