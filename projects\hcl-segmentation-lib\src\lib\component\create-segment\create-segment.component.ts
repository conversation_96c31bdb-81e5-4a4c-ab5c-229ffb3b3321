import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
} from "@angular/core";
import {
  ButtonConf,
  ModalConfig,
  ModalService,
  NotificationService,
  ProgressSpinner,
} from "hcl-angular-widgets-lib";
import { HclSegmentationLibDataService } from "../../hcl-segmentation-lib-data.service";
import { HclSegmentationLibService } from "../../hcl-segmentation-lib.service";
import {
  AudienceApplicationConfig,
  BaseTableColumn,
  SegPermission,
  SegmentCentralApplicationConfig,
  SegmentObj,
  SegmentRunStats,
  UserConfig,
} from "../../models/segment";
import { SegmentationConfig } from "../../segmentation-config";
import { HttpHeaders } from "@angular/common/http";
import { forkJoin } from "rxjs";

@Component({
  selector: "hcl-create-segment",
  templateUrl: "./create-segment.component.html",
  styleUrls: ["./create-segment.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class CreateSegmentComponent implements OnInit, AfterViewInit {
  @ViewChild("cancelContentDialogTemplate")
  cancelContentDialogTemplate: TemplateRef<ElementRef>;
  @ViewChild("cancelDialogActionsTemplate")
  cancelDialogActionsTemplate: TemplateRef<ElementRef>;

  @Input() config: SegmentationConfig;
  @Output() segCreationCancled = new EventEmitter();
  @Output() segmentCreated = new EventEmitter();
  @Output() folderSelected = new EventEmitter();

  cancelButtonConfig: ButtonConf;
  saveButtonConfig: ButtonConf;
  saveAndPublishButtonConfig: ButtonConf;
  testRunButtonConfig: ButtonConf;
  noButtonConfig: ButtonConf;
  yesButtonConfig: ButtonConf;
  segmentStats: SegmentRunStats;
  cancelDialogConfig: ModalConfig;
  segmentDonutDefaultValues: SegmentRunStats = {
    segmentState: null,
    segmentCount: -1,
    totalCount: 0,
    asOnTimeStamp: null,
  };
  metadataState: boolean;
  ruleBasedState: boolean;
  audienceInfo: { [key: string]: any } = {};
  renderDonutChart = true;

  private loginAttempts = 0;
  private audLoginAttempts = 0;
  private MAX_LOGIN_ATTEMPTS = 2;
  readyToRender = false;
  securityPoliciesLoaded = false;
  qbColumnFieldsLoaded = false;
  errorResponse: any;

  segmentInlineSpinner: ProgressSpinner = {
    isInline: true,
    isLoading: true,
    color: 'primary',
    mode: 'indeterminate',
    value: 75,
    diameter: 50,
    strokeWidth: 3
  };

  constructor(
    private modalService: ModalService,
    private hclSegmentationLibService: HclSegmentationLibService,
    private dataService: HclSegmentationLibDataService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    const segHeaders = new HttpHeaders()
      .set("m_user_name", this.config.applicationUser)
      .set("m_tokenId", this.config.segmentToken)
      .set("api_auth_mode", "manager")
      .set("401", "ignore")
      .set("403", "ignore")
      .set("skipError", "true");


    this.hclSegmentationLibService.segmentHeaders = segHeaders;
    this.config.folderSelectionConf.applicationHeaders = segHeaders;

    this.audienceInfo.audienceName = this.config.audienceName;
    this.audienceInfo.audienceTableId = this.config.audienceTableId;
    this.audienceInfo.audienceTablePhysicalName =
      this.config.audienceTablePhysicalName;
    this.hclSegmentationLibService.segmentBaseUrl = this.config.segmentBaseUrl;
    this.config.folderSelectionConf.reLogin = this.config.reLogin;
    this.config.segmentMetadatConfig.reLogin = this.config.reLogin;

    this.updateAudienceToken();
    this.checkSegmentCreationPermission();
  }

  ngAfterViewInit(): void {
    this.cancelDialogConfig = {
      disableClose: false,
      hasBackdrop: true,
      title: this.config.translations.unsavedChanges,
      height: "auto",
      width: "400px",
      contentTemplate: this.cancelContentDialogTemplate,
      actionsTemplate: this.cancelDialogActionsTemplate,
    };
  }

  setConfiguration() {
    this.segmentStats = this.config.donutChartConfig.segmentStats || this.segmentDonutDefaultValues;
    this.saveButtonConfig = {
      name: "save",
      value: this.config.translations.save,
      color: "accent",
      buttonType: "stroked",
      type: "button",
      styleClass: "medium-button",
      borderRadius: 5,
      disabled: true,
    };
    this.saveAndPublishButtonConfig = {
      name: "saveAndPublish",
      value: this.config.translations.savePublish,
      color: "accent",
      buttonType: "flat",
      type: "button",
      styleClass: "medium-button",
      borderRadius: 5,
      disabled: true,
    };
    this.testRunButtonConfig = {
      name: "testRun",
      value: this.config.translations.testRun,
      color: "accent",
      buttonType: "stroked",
      type: "button",
      // styleClass: "medium-button",
      borderRadius: 5,
      disabled: true,
    };

    this.cancelButtonConfig = {
      name: "cancel",
      value: this.config.translations.cancel,
      color: "accent",
      buttonType: "stroked",
      type: "button",
      styleClass: "medium-button",
      borderRadius: 5,
    };

    this.yesButtonConfig = {
      name: "yes",
      value: this.config.translations.yes,
      color: "accent",
      buttonType: "stroked",
      type: "button",
      styleClass: "medium-button",
      borderRadius: 5,
    };

    this.noButtonConfig = {
      name: "no",
      value: this.config.translations.no,
      color: "accent",
      buttonType: "flat",
      type: "button",
      styleClass: "medium-button",
      borderRadius: 5,
    };
  }

  checkSegmentCreationPermission() {
    this.hclSegmentationLibService
      .getfolderPermissions(this.config.segRootFolderId)
      .subscribe((segPermissionList: SegPermission[]) => {
        this.loginAttempts = 0;
        if (
          segPermissionList.length &&
          segPermissionList.find(
            (segPermission: SegPermission) => segPermission.name === "SEG_NEW"
          ).permitted
        ) {
          this.setConfiguration();
          if (!this.dataService.userConfig) {
            this.getAppAndUserDetails();
          } else {
            this.config.segmentMetadatConfig.securityPolicies =
              this.dataService.userConfig.securityPoliciesMap;

            this.config.segmentMetadatConfig.securityPoliciesForSegment =
              this.dataService.userConfig.segmentSecurityPoliciesMap;

            this.config.segmentMetadatConfig.securityPoliciesForFolder =
              this.dataService.userConfig.folderSecurityPoliciesMap;

            this.config.segmentMetadatConfig.textSizeValidationType =
              this.dataService.textSizeValidationType;

            this.securityPoliciesLoaded = true;
            this.readyToRender = this.qbColumnFieldsLoaded ? true : false;
          }
        } else {
          this.notificationService.show({
            message: this.config.translations.permissionError,
            type: "error",
            close: true,
            autoHide: 6000,
          });

          this.segCreationCancled.emit();
        }
      }, this.handleServerError.bind(this, this.checkSegmentCreationPermission.bind(this), "seg"));
  }

  updateAudienceToken(callback?) {
    this.hclSegmentationLibService
      .getAudienceToken()
      .subscribe((audConfig: AudienceApplicationConfig) => {
        this.loginAttempts = 0;
        const audHeaders = new HttpHeaders()
          .set("m_user_name", this.config.applicationUser)
          .set("m_tokenId", audConfig.token)
          .set("api_auth_mode", "manager")
          .set("401", "ignore")
          .set("403", "ignore");

        this.hclSegmentationLibService.audienceHeaders = audHeaders;
        this.hclSegmentationLibService.audienceBaseUrl = audConfig.serverURL;
        if (!this.config.queryBuilderConfig.columnFields.length) {
          this.getAudienceMetaAndTableDetails();
        } else {
          this.qbColumnFieldsLoaded = true;
          this.readyToRender = this.securityPoliciesLoaded ? true : false;
        }

        if (callback) {
          callback();
        }
      }, this.handleServerError.bind(this, this.updateAudienceToken.bind(this), "seg"));
  }

  getAppAndUserDetails() {
    forkJoin([
      this.hclSegmentationLibService.getUserConfig(),
      this.hclSegmentationLibService.getSegmentCentralApplicationConfig(),
    ]).subscribe(
      ([userConfig, segAppConfig]: [
        UserConfig,
        SegmentCentralApplicationConfig
      ]) => {
        this.loginAttempts = 0;
        this.dataService.userConfig = userConfig;

        this.dataService.textSizeValidationType =
          segAppConfig.textSizeValidationType;

        this.config.segmentMetadatConfig.securityPolicies =
          userConfig.securityPoliciesMap;

        this.config.segmentMetadatConfig.securityPoliciesForSegment =
          userConfig.segmentSecurityPoliciesMap;

        this.config.segmentMetadatConfig.securityPoliciesForFolder =
          userConfig.folderSecurityPoliciesMap;

        this.config.segmentMetadatConfig.textSizeValidationType =
          segAppConfig.textSizeValidationType;
        this.securityPoliciesLoaded = true;
        this.readyToRender = this.qbColumnFieldsLoaded ? true : false;
      },
      this.handleServerError.bind(
        this,
        this.getAppAndUserDetails.bind(this),
        "seg"
      )
    );
  }

  getAudienceMetaAndTableDetails() {
    forkJoin([
      this.hclSegmentationLibService.getAudienceTableMetadata(
        this.config.audienceDataSource,
        this.config.audienceTablePhysicalName
      ),
      this.hclSegmentationLibService.getAudienceTableDetails(
        this.config.audienceTableId
      ),
    ]).subscribe(([metadata, audienceTable]) => {
      this.audLoginAttempts = 0;
      const audienceMappedColumns =
        this.hclSegmentationLibService.getAudienceMappedColumns(
          audienceTable.fields,
          metadata.columns
        );

      this.config.queryBuilderConfig.columnFields = audienceMappedColumns.map(
        (column: BaseTableColumn) => {
          return {
            fieldLabel: column.displayName,
            fieldValue: column.name,
            fieldDataType:
              column.internalType === "Text" ? "String" : column.internalType,
            fieldLength: column.columnSize,
          };
        }
      );
      this.qbColumnFieldsLoaded = true;
      this.readyToRender = this.securityPoliciesLoaded ? true : false;
    }, this.handleServerError.bind(this, this.getAudienceMetaAndTableDetails.bind(this), "aud"));
  }

  setStepState(state: boolean, step: string) {
    this[step] = state;
    this.toggleActionButtonsState();
  }

  toggleActionButtonsState() {
    let validState = this.metadataState && this.ruleBasedState;
    this.saveAndPublishButtonConfig.disabled = !validState;
    this.saveButtonConfig.disabled = !validState;
    this.testRunButtonConfig.disabled = !this.ruleBasedState;
  }

  testRunSegment() {
    // const segmentData: SegmentObj = this.dataService.getSegmentData();

    const subType: any = {
      type: "RuleBased"
    };

    const { audienceTableId, nestedCondition } = this.dataService.rulebasedSegmentForm.value;
    subType.audienceTableId = audienceTableId;
    subType.nestedCondition = nestedCondition;

    this.hclSegmentationLibService
      .getTestRunStatistics(subType)
      .subscribe((segmentStats: SegmentRunStats) => {
        this.loginAttempts = 0;
        this.renderDonutChart = false;
        this.segmentStats = segmentStats;
        setTimeout(() => {
          this.renderDonutChart = true;
        }, 0);
      }, this.handleServerError.bind(this, this.testRunSegment.bind(this), "seg"));
  }

  cancelButtonClicked() {
    const segSubTypeUpdated =
      this.dataService.rulebasedSegmentForm &&
      this.dataService.rulebasedSegmentForm.dirty;

    const segmentUpdated =
      (this.dataService.segmentMetadataForm &&
        this.dataService.segmentMetadataForm.dirty) ||
      segSubTypeUpdated;

    if (segmentUpdated) {
      this.modalService.openDialog(this.cancelDialogConfig);
    } else {
      this.cancel();
    }
  }

  cancel() {
    if (this.errorResponse) {
      this.segCreationCancled.emit({ errorDetails: this.errorResponse });
    } else {
      this.segCreationCancled.emit();
    }
  }

  saveSegment() {
    const newSegmentData: SegmentObj = this.dataService.getSegmentData();
    this.hclSegmentationLibService
      .createSegment(newSegmentData)
      .subscribe((response) => {
        this.loginAttempts = 0;
        this.segmentCreated.emit({
          action: "save",
          segDetails: { ...response, status: "SUCCESS" },
          bulkSaveOrSavePublish: false
        });
        this.errorResponse = null;
      }, this.handleServerError.bind(this, this.saveSegment.bind(this), "seg"));
  }

  saveAndPublishButtonClicked() {
    const newSegmentData: SegmentObj = this.dataService.getSegmentData();
    this.hclSegmentationLibService
      .saveAndPublishCreateSegment(newSegmentData)
      .subscribe((response) => {
        this.loginAttempts = 0;
        this.segmentCreated.emit({
          action: "savePublish",
          segDetails: { ...response, status: "SUCCESS" },
          bulkSaveOrSavePublish: false
        });
        this.errorResponse = null;
      }, this.handleServerError.bind(this, this.saveAndPublishButtonClicked.bind(this), "seg"));
  }

  segFolderSelected(event) {
    this.folderSelected.emit(event);
  }

  /**
   * In case error from server this function will be called
   * param error
   */
  private handleServerError(callback, context, err) {
    if (err.status === 401) {
      if (context === "seg" && this.loginAttempts < this.MAX_LOGIN_ATTEMPTS) {
        // we have to a login again
        this.reLogin(callback);
      } else if (
        context === "aud" &&
        this.audLoginAttempts < this.MAX_LOGIN_ATTEMPTS
      ) {
        this.audLoginAttempts++;
        this.updateAudienceToken(callback);
      } else {
        this.notificationService.show({
          message: err.message
            ? err.message
            : this.config.translations.unableToFetchData,
          type: "error",
          close: true,
          autoHide: 6000,
        });
      }
    } else if (err.status === 403) {
      this.notificationService.show({
        message: err.error["403"][0]
          ? err.error["403"][0]
          : this.config.translations.unableToFetchData,
        type: "error",
        close: true,
        autoHide: 6000,
      });
    } else if (err.status === 400) {
      // bad request error
      const error: any = err.error,
        errArray: { filed: string; messages: string[] }[] = [];

      for (const x in error) {
        if (error.hasOwnProperty(x)) {
          const field: string =
            x.indexOf('[') > -1
              ? x.substring(0, x.lastIndexOf('['))
              : x,
            errorMessages: string[] = [];

          error[x].forEach((s: any) => {
            errorMessages.push(s);
          });
          errArray.push({
            filed: field,
            messages: errorMessages,
          });
        }
      }

      errArray.forEach((element) => {
        this.notificationService.show({
          message: `${element.messages.join(", ")}`,
          type: "error",
          close: true,
          autoHide: 6000,
        });
      });
    } else {

      if (
        err.error &&
        err.error[err.status] &&
        Array.isArray(err.error[err.status])
      ) {
        if (err.error[err.status] && err.error[err.status][0]) {
          if (err.error[422]) {
            this.errorResponse = err.error;
            this.notificationService.show({
              message: err.error[err.status].toString(),
              type: 'warning',
              close: true,
              autoHide: 6000,
            });
          } else {
            // errorShown = true;
            err.error[err.status].forEach((element: any) => {
              this.notificationService.show({
                message: element,
                type: 'error',
                close: true,
                autoHide: 6000,
              });
            });
          }

        }
      } else if (err.message) {
        this.notificationService.show({
          message: err.message,
          type: "error",
          close: true,
          autoHide: 6000,
        });
      }
    }
  }

  /**
   * In case there is a un auth error we can do a  relogin to get the new token
   */
  private reLogin(callbackFunction: any): void {
    this.loginAttempts++;
    // check if we have a relogin method
    if (this.config.reLogin) {
      this.config.reLogin(this.reLoginSuccess.bind(this, callbackFunction));
    } else {
      this.notificationService.show({
        message: this.config.translations.unableToFetchData,
        type: "error",
        close: true,
        autoHide: 6000,
      });
    }
  }

  /**
   * called when the relogin is successful from the caller
   */
  public reLoginSuccess(callback): void {
    if (callback) {
      callback();
    }
  }
}
