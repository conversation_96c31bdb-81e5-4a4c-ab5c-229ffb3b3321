import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { RowNode } from 'ag-grid-community';

@Component({
  selector: 'hcl-group-row-inner-renderer',
  templateUrl: './group-row-inner-renderer.component.html',
  styleUrls: ['./group-row-inner-renderer.component.scss']
})
export class GroupRowInnerRendererComponent implements ICellRendererAngularComp {

  groupHeaderRow: RowNode;
  rowDescription: any;
  noGroupKeyDesc: any;

  constructor() { }

  refresh(params: any): boolean {
    return false;
  }

  agInit(params: any): void {
    this.groupHeaderRow = params.node;
    const colName = params.node.rowGroupColumn.getUserProvidedColDef().headerName;
    const childCount = this.groupHeaderRow && this.groupHeaderRow.allChildrenCount;
    if (params && params.rowDescription && params.rowDescription.label) {
      this.rowDescription = params.rowDescription.label.replace('##', childCount) + ' ' + colName.toLowerCase();
    } else {
      this.rowDescription = '';
    }
    if (params && params.rowDescription && params.rowDescription.noGroupBy) {
      this.noGroupKeyDesc = params.rowDescription.noGroupBy.replace('##', childCount) + ' ' + colName.toLowerCase();
    } else {
      this.noGroupKeyDesc = '';
    }
  }
}
