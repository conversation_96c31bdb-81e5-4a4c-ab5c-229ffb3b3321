import { D3<PERSON><PERSON><PERSON><PERSON><PERSON> } from './D3ChartRenderer';
import {D3Drag<PERSON>enderer} from './D3DragRenderer';
import {ChartSeries, Dimension} from '../../config/chart-config';
import {D3GanttDataNodeRenderer} from './gantt/D3GanttDataNodeRenderer';
import {D3GanttDependencyRenderer} from './gantt/D3GanttDependencyRenderer';
import {AutoDimensionHelper} from './helper/AutoDimensionHelper';
import {ElementRef} from '@angular/core';

export class D3GanttChartRenderer extends D3ChartRenderer {
  // task group in which all task bars and other elements should be appended
  private taskGroup: any;
  // the data node renderer as per the series name
  private seriesDataRenderer: Map<string, {series: ChartSeries,
                                            nodeRenderer: D3GanttDataNodeRenderer[],
                                            dependencyRenderer: D3GanttDependencyRenderer}>
                                              = new Map<string, {series: ChartSeries, nodeRenderer: D3GanttDataNodeRenderer[],
                                                                    dependencyRenderer: D3GanttDependencyRenderer}>();
  // stores rect elements of task group
  private task: any;
  // The circle at the end of the task bar from which the dependency will start
  private dependencyCircle: any;
  // the node renderer
  private nodeRenderer: D3GanttDataNodeRenderer[];
  // the dep renderer
  private depRenderer: D3GanttDependencyRenderer;
  /**
   * In case the data is draggable
   */
  protected dragRenderer: D3DragRenderer;

  /**
   * this function will actually render the chart
   * param {element} the element inside which the chart needs to be rendered
   */
  public render(element: ElementRef): void {
    super.render(element);
    // since thi is a gantt chart we need to have the linear axis always on top
    this.linearAxisRenderer.makeAxisFixed(element.nativeElement);
  }

  /**
   * Refresh the data at index
   * param {number} index
   */
  public refreshDataForIndex(index: number): void {
    const ht: number = this.svg.node().parentNode.offsetHeight;
    this.svg.transition()
      .attr('height', ht);
    // since the cat Asi may have changed we may have to update the dims
    this.categoryAxisRenderer.updateScale();
    this.nodeRenderer = [];
    this.depRenderer.removeAllDependency();
    this.chart.selectAll('.taskbarGroup').remove();
    this.plotChart();
    // we need to redraw the rectangle at the specified index
    const nodeRenderer: D3GanttDataNodeRenderer = this.nodeRenderer[index];
    if (nodeRenderer) {
      nodeRenderer.reRenderNodes();
    }
    this.depRenderer.renderDependency();
  }

  /**
   *
   * param {number} index
   */
  public hideDataAtIndex(index: number[]): void {
    index.forEach(i => {
      const nodeRenderer: D3GanttDataNodeRenderer = this.nodeRenderer[i];
      if (nodeRenderer) {
        nodeRenderer.hideNode();
      }
    });
  }

  /**
   * this function will populate the data in the chart
   */
  protected plotChart(): void {
    // lets iterate and draw through all the series
    for (let i: number = 0; i < this.chartConfig.series.length; i++) {
      // for all the series we need to draw a global inside which we will draw the rectangle
      this.taskGroup = this.chart.append('g')
        .attr('class', 'taskbarGroup')
        .selectAll('g')
        .data(this.chartConfig.series[i].data)
        .enter()
        .append('g')
        .attr('transform', 'translate(0,0)')
        .attr('class', (d) => {
          return 'taskbar' +
            + (this.chartConfig.series[i].getParentCssClass ? this.chartConfig.series[i].getParentCssClass(d) : '');
        });
      // time to draw the rectangle
      this.nodeRenderer = this.renderRawDataRectangle(this.chartConfig.series[i]);
      // we have drawn the data points its time to draw the dependencies
      if (this.chartConfig.series[i].dependency) {
        // we have to render the dependencies for this series
        this.depRenderer = new D3GanttDependencyRenderer(this.chartConfig.series[i],
                                                      this.linearAxisRenderer,
                                                      this.categoryAxisRenderer,
                                                      this.nodeRenderer,
                                                      this,
                                                      this.chart);
        this.depRenderer.renderDependency();
      }
      // add all the details to the seriesDataRenderer map
      this.seriesDataRenderer.set(this.chartConfig.series[i].name, {
        series: this.chartConfig.series[i],
        nodeRenderer: this.nodeRenderer,
        dependencyRenderer: this.depRenderer
      });
    }
  }
  /**
   * this function will return bars of gantt chart with required width and
   * height and position
   */
  protected renderRawDataRectangle(series: ChartSeries): D3GanttDataNodeRenderer[] {
    const nodeRenderer: D3GanttDataNodeRenderer[] = [];
    // in all the group we need to adda Rectangle
    this.taskGroup.each((dataObject: any, index: number, d3element: any[]) => {

        const r: D3GanttDataNodeRenderer = new D3GanttDataNodeRenderer(
          this.linearAxisRenderer,
          this.categoryAxisRenderer,
          series,
          dataObject,
          this,
          d3element[index]
        );
        if (!series.noDataToPlot || !series.noDataToPlot(dataObject)) {
          // render the nodes
          r.render();
        } else {
          r.setDoNotPlot(true);
        }
        nodeRenderer.push(r);
    });
    return nodeRenderer;
  }

  /**
   * When a node postion is updated this function can be invoked
   */
  public dataNodePositionUpdated(series: ChartSeries, data: any, value: {min: number, max: number}): void {
    super.dataNodePositionUpdated(series, data, value);
    // now since the data node position has been updated we also need to update the dependencies as well
    const seriesRenderer: any = this.seriesDataRenderer.get(series.name);
    // now we have to re-render the dependencies
    seriesRenderer.dependencyRenderer.renderDependency();
  }
  /**
   * This function will change the scale
   * param scale
   */
  public changeAxisScale(series: ChartSeries, displayIn: any): void {
    // we need to change the char dimensions as well
    series.timeSeries.displayIn = displayIn;
    series.marginBuffer = 0;
    // change the chart dimensions 1st
    const dim: Dimension = AutoDimensionHelper.getDimensions(this.chartConfig);
    this.chartConfig.dimension.width = dim.width;
    this.svg.transition()
      .duration(800)
      .attr('width', this.chartConfig.dimension.width)
      .attr('height', this.chartConfig.dimension.height);
    // tell the linear Axis to change the scale
    this.linearAxisRenderer.updateScale(displayIn);
    // now we need to rePlot the chart
    this.nodeRenderer.forEach((nodeRenderer: D3GanttDataNodeRenderer) => {
      if (nodeRenderer) {
        // we have to re render the data
        nodeRenderer.reRenderNodes();
      }
    });
    // render the dep
    if (this.depRenderer) {
      this.depRenderer.removeAllDependency();
      setTimeout(() => this.depRenderer.renderDependency(), 700);
    }
  }

  /**
   * This function will re-render the chart in case there are changes in cat axis
   */
  public reRender(): void {
    const ht: number = this.svg.node().parentNode.offsetHeight;
    this.svg.transition()
      .attr('height', ht);
    // since the cat Asi may have changed we may have to update the dims
    this.categoryAxisRenderer.updateScale();
    // now we need to rePlot the chart
    this.nodeRenderer.forEach((nodeRenderer: D3GanttDataNodeRenderer) => {
      if (nodeRenderer) {
        // we have to re render the data
        nodeRenderer.reRenderNodes();
      }
    });
    // render the dep
    if (this.depRenderer) {
      this.depRenderer.removeAllDependency();
      setTimeout(() => this.depRenderer.renderDependency(), 700);
    }
  }
}
