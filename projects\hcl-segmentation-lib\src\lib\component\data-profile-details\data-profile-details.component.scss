@use '../../assets/scss/variables' as *;

@mixin custom-scrollbar {
    &::-webkit-scrollbar {
        width: 5px;
        height: 5px;
    }

    &::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
        background-color: #808080;
    }
}

.details-container {
    width: 100%;
    height: 100%;

    .details-container-title-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h3 {
            font-family: $font-primary;
            font-size: 16px;
            font-weight: 600;
            line-height: 19.5px;
            color: #6D7692;
        }
    }

    .deatails-action-container {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        margin-bottom: 15px;

        .bincount-input-container {
            display: flex;
            width: 50%;
            align-items: baseline;

            .input-sec {
                width: 75%;
            }

            .button-sec {
                width: 25%;
            }
        }

        .action-btn-container {
            display: flex;
            border: 2px solid $primary-color;
            width: 100px;
            height: 30px;
            border-radius: 5px;

            .icon-container {
                width: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .unselect {
                background-color: $card-background;
                color: $primary-color;
            }

            .select {
                background-color: $primary-color;
                color: $card-background;
            }

            .border-left-top-bottom {
                border-top-left-radius: 3px;
                border-bottom-left-radius: 3px;
            }

            .border-right-top-bottom {
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
            }

            .hcl-icon-bar-chart {
                font-size: 22px;
            }

            .hcl-icon-list-view {
                font-size: 20px;
            }
        }
    }

    .loader-sec {
        width: 100%;
        height: 60dvh;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .bar-chart-container {
        width: 100%;
        height: calc(100% - 120px);
        overflow: hidden;
        position: relative;

        .bar-chart-sec {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
    }

    .list-view-container {
        width: 100%;
        height: calc(100% - 115px);
        overflow: hidden;
        position: relative;

        .list-view-sec {
            width: 100%;
            height: 100%;
            overflow: hidden;

            table {
                display: block;
                overflow: hidden;
                width: 100%;
                height: 100%;
                border-collapse: collapse;
                border-spacing: 0;

                thead {
                    display: block;

                    th {
                        font-family: $font-primary;
                        font-size: 16px;
                        font-weight: 600;
                        line-height: 19.5px;
                        text-align: left;
                        color: #6D7692;
                        border-right: 1px solid $tango-border-color;
                        padding: 10px;
                        display: inline-block;
                        width: calc(50% - 1px);

                        .width-minuns-5 {
                            width: calc(50% - 5px);
                        }
                    }
                }

                tbody {
                    height: calc(100% - 45px);
                    overflow: auto;
                    display: block;

                    td {
                        font-family: $font-secondary;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 16.41px;
                        text-align: left;
                        color: #444444;
                        padding: 10px;
                        display: inline-block;
                        width: 50%;
                    }

                    .scrollable-tbody {
                        display: block;
                        max-height: 400px;
                        /* Adjust the height as needed */
                        overflow-y: auto;
                    }

                    .scrollable-tbody tr {
                        display: table;
                        width: 100%;
                        table-layout: fixed;
                    }

                    // .scrollable-tbody td,
                    // .scrollable-tbody th {
                    //     width: 33.33%;
                    //     /* Adjust the width as needed */
                    // }

                    @include custom-scrollbar;
                }

                tr {
                    display: block;
                    width: 100%;
                    border-bottom: 1px solid $tango-border-color;
                }
            }
        }
    }


}