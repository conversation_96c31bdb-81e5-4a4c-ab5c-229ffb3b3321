import {
    Component,
    OnInit,
    ViewEncapsulation,
    OnDestroy,
    Output,
    EventEmitter,
    Input,
    ViewChild,
    AfterViewInit,
  } from "@angular/core";
  import { TranslateService } from "@ngx-translate/core";
  import FroalaEditor from "froala-editor";
  import { ButtonConf, CheckboxConfig, MenuComponent, ModalService, PopupMenuConfig, Utility } from "hcl-angular-widgets-lib";
  
  import Tribute from "tributejs";
  
  import { UntypedFormControl } from '@angular/forms';
  import { Observable, SubscriptionLike } from "rxjs";
  // import Alwan from "hcl-angular-widgets-lib/assets/color-picker";
  import alwan from "alwan";
  import { IpEmailBuilderService } from "../../../ip-email-builder.service";
  
  @Component({
    selector: "froala-editor",
    templateUrl: "./froala-editor.component.html",
    styleUrls: ["./froala-editor.component.scss"],
    encapsulation: ViewEncapsulation.None,
  })
  export class FroalaEditorComponent implements OnInit, OnDestroy, AfterViewInit {
    randomId: string = "UJHAEFZMUJOYGYQE";
    alwanColorPickerInstance = null;
    editorContent: string = "";
    html = null;
    tribute: any = null;
    editorInstance: any;
    @Input() htmlString: string = "";
    @Input() userLocale: string = "en_US";
    @Input() pfList: { key: string; value: string }[] = [];
    @Input() closeHyperLinkInfoEvent: Observable<void>;
    @Output() showLinkSidebar: EventEmitter<any> = new EventEmitter<any>();
    @Output() showAISidebar: EventEmitter<any> = new EventEmitter<any>();
    @Output() froalaBlur: EventEmitter<any> = new EventEmitter<any>();
    @Output() froalaFocus: EventEmitter<any> = new EventEmitter<any>();
    @Output() froalaContentChanged: EventEmitter<any> = new EventEmitter<any>();
    @Output() froalaKeyDown: EventEmitter<any> = new EventEmitter<any>();
    @Output() froalaKeyUp: EventEmitter<any> = new EventEmitter<any>();
    @Output() froalaInitialized: EventEmitter<any> = new EventEmitter<any>();
    @Output() emitToggleViewMode: EventEmitter<{ mode: number }> = new EventEmitter<{ mode: number }>();
    private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();
    currentSnap: any;
    froalaId: string = 'froalaId-'+new Date().getTime().toString();
    toggleClicked: boolean = false;
    translationObject: any = null;
    editLinkNode: any;
    editImageNode: any;
    newRangeSelected: string = '';
    static materialDesignLabel = "material_design";
    editorOptions: any = {
      key: "BWC6D-16D3E3F3H3D1A6A4wc2DBKSPJ1WKTUCQOd1OURPE1KDc1C-7J2A4D4B4C6D2A1F4G1C1==",
      charCounterCount: false,
      placeholderText: this.translate.instant('settings.insert_text_here'),
      charCounterMax: -1,
      linkAutoPrefix: '',
      language: this.getLanguangeForTextEditor(),
      fileUpload: false,
      imagePaste: false,
      htmlUntouched: true,
      
      htmlAllowedTags : ['a', 'abbr', 'address', 'area', 'article', 'aside', 'audio', 'b', 'base', 'bdi', 'bdo', 'blockquote', 'br', 'button', 'canvas', 'caption', 'cite', 'code', 'col', 'colgroup', 'datalist', 'dd', 'del', 'details', 'dfn', 'dialog', 'div', 'dl', 'dt', 'em', 'embed', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header', 'hgroup', 'hr', 'i', 'iframe', 'img', 'input', 'ins', 'kbd', 'keygen', 'label', 'legend', 'li', 'link', 'main', 'map', 'mark', 'menu', 'menuitem', 'meter', 'nav', 'noscript', 'object', 'ol', 'optgroup', 'option', 'output', 'p', 'param', 'pre', 'progress', 'queue', 'rp', 'rt', 'ruby', 's', 'samp', 'script', 'style', 'section', 'select', 'small', 'source', 'span', 'strike', 'strong', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'textarea', 'tfoot', 'th', 'thead', 'time', 'title', 'tr', 'track', 'u', 'ul', 'var', 'video', 'wbr', 'uaepf', 'svg', 'clipPath', 'g', 'defs', 'rect', 'path', 'animate', 'animateMotion', 'animateTransform', 'circle', 'desc', 'ellipse', 'feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feDropShadow', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence', 'filter', 'foreignObject', 'image', 'line', 'linearGradient', 'marker', 'mask', 'metadata', 'pattern', 'polygon', 'polyline', 'radialGradient', 'set', 'stop', 'switch', 'symbol', 'text', 'textPath', 'tspan', 'use', 'view'],

      htmlAllowedAttrs : ['accept', 'accept-charset', 'accesskey', 'action', 'align', 'allowfullscreen', 'allowtransparency', 'alt', 'aria-.*', 'async', 'autocomplete', 'autofocus', 'autoplay', 'autosave', 'background', 'bgcolor', 'border', 'charset', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'color', 'cols', 'colspan', 'content', 'contenteditable', 'contextmenu', 'controls', 'coords', 'data', 'data-.*', 'datetime', 'default', 'defer', 'dir', 'dirname', 'disabled', 'download', 'draggable', 'dropzone', 'enctype', 'for', 'form', 'formaction', 'frameborder', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'http-equiv', 'icon', 'id', 'ismap', 'itemprop', 'keytype', 'kind', 'label', 'lang', 'language', 'list', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'mozallowfullscreen', 'multiple', 'muted', 'name', 'novalidate', 'open', 'optimum', 'pattern', 'ping', 'placeholder', 'playsinline', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'reversed', 'rows', 'rowspan', 'sandbox', 'scope', 'scoped', 'scrolling', 'seamless', 'selected', 'shape', 'size', 'sizes', 'span', 'src', 'srcdoc', 'srclang', 'srcset', 'start', 'step', 'summary', 'spellcheck', 'style', 'tabindex', 'target', 'title', 'type', 'translate', 'usemap', 'value', 'valign', 'webkitallowfullscreen', 'width', 'wrap', 'custom-data-href', 'data-href', 'data-asset', 'pfname', 'viewBox', 'fill', 'xmlns', 'clip-path', 'transform', 'd', 'accent-height', 'accumulate', 'additive', 'alignment-baseline', 'allowReorder', 'alphabetic', 'amplitude', 'arabic-form', 'ascent', 'attributeName', 'attributeType', 'autoReverse', 'azimuth', 'baseFrequency', 'baseline-shift', 'baseProfile', 'begin', 'bias', 'by', 'calcMode', 'cap-height', 'clip', 'clipPathUnits', 'clip-rule', 'color-interpolation', 'color-interpolation-filters', 'color-rendering', 'contentScriptType', 'contentStyleType', 'cursor', 'cx', 'cy', 'decelerate', 'descent', 'diffuseConstant', 'direction', 'display', 'divisor', 'dominant-baseline', 'dur', 'dx', 'dy', 'edgeMode', 'elevation', 'enable-background', 'end', 'exponent', 'externalResourcesRequired', 'fill-opacity', 'fill-rule', 'filter', 'filterRes', 'filterUnits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'format', 'from', 'fr', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyph-orientation-horizontal', 'glyph-orientation-vertical', 'glyphRef', 'gradientTransform', 'gradientUnits', 'hanging', 'horiz-adv-x', 'horiz-origin-x', 'ideographic', 'image-rendering', 'in', 'in2', 'intercept', 'k', 'k1', 'k2', 'k3', 'k4', 'kernelMatrix', 'kernelUnitLength', 'kerning', 'keyPoints', 'keySplines', 'keyTimes', 'lengthAdjust', 'letter-spacing', 'lighting-color', 'limitingConeAngle', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerHeight', 'markerUnits', 'markerWidth', 'mask', 'maskContentUnits', 'maskUnits', 'mathematical', 'max', 'media', 'method', 'min', 'mode', 'name', 'numOctaves', 'offset', 'opacity', 'operator', 'order', 'orient', 'orientation', 'origin', 'overflow', 'overline-position', 'overline-thickness', 'panose-1', 'paint-order', 'path', 'pathLength', 'patternContentUnits', 'patternTransform', 'patternUnits', 'ping', 'pointer-events', 'points', 'pointsAtX', 'pointsAtY', 'pointsAtZ', 'preserveAlpha', 'preserveAspectRatio', 'primitiveUnits', 'r', 'radius', 'referrerPolicy', 'refX', 'refY', 'rendering-intent', 'repeatCount', 'repeatDur', 'requiredExtensions', 'requiredFeatures', 'restart', 'result', 'rotate', 'rx', 'ry', 'scale', 'seed', 'shape-rendering', 'slope', 'spacing', 'specularConstant', 'specularExponent', 'speed', 'spreadMethod', 'startOffset', 'stdDeviation', 'stemh', 'stemv', 'stitchTiles', 'stop-color', 'stop-opacity', 'strikethrough-position', 'strikethrough-thickness', 'string', 'stroke', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke-width', 'surfaceScale', 'systemLanguage', 'tableValues', 'targetX', 'targetY', 'text-anchor', 'text-decoration', 'text-rendering', 'textLength', 'to', 'type', 'u1', 'u2', 'underline-position', 'underline-thickness', 'unicode', 'unicode-bidi', 'unicode-range', 'units-per-em', 'v-alphabetic', 'v-hanging', 'v-ideographic', 'v-mathematical', 'values', 'vector-effect', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'viewTarget', 'visibility', 'widths', 'word-spacing', 'writing-mode', 'x', 'x-height', 'x1', 'x2', 'xChannelSelector', 'xlink:actuate', 'xlink:arcrole', 'xlink:href', 'xlink:role', 'xlink:show', 'xlink:title', 'xlink:type', 'xml:base', 'xml:lang', 'xml:space', 'xmlns', 'xmlns:xlink', 'y', 'y1', 'y2', 'yChannelSelector', 'zoomAndPan'],

      linkEditButtons: [
        "linkBold",
        "linkItalic",
        "linkUnderline",
        "linkFontFamilyDropdown",
        "linkFontSizeDropdown",
        "linkFontColor",
        "linkClearFormat",
        "editCustomLink" + this.froalaId,
        "customLinkDelete" + this.froalaId,
        "editMaxAI" + this.froalaId
      ],
      imageEditButtons: [],
      shortcutsEnabled: ['undo', 'redo'],
      toolbarInline: true,
      htmlRemoveTags: ["script", "title", "base"],
      toolbarButtons: {
        moreText: {
          buttons: [
            "bold",
            "italic",
            "underline",
            "strikeThrough",
            "textAlignDropdown",
            "fontFamilyDropdown",
            "fontSizeDropdown",
            "fontColor" + this.froalaId,
            "addCustomLink" + this.froalaId,
            "clearFormatting" + this.froalaId,
            "addMaxAI" + this.froalaId
          ],
          buttonsVisible: 11,
        },
      },
      fontFamily: FroalaEditorComponent.fontFamilyOptions(),
      fontSize: FroalaEditorComponent.fontSizeOptions(),
      codeMirrorOptions: {
        indentWithTabs: true,
        lineNumbers: true,
        lineWrapping: true,
        mode: "text/html",
        tabMode: "indent",
        tabSize: 2,
      },
      spellcheck: false,
      imageResize: false,
      zIndex: 1,
      pluginsEnabled: [
        "image",
        "link",
        "align",
        "fontSize",
        "fontFamily",
        "colors",
        "colorPlugin",
        "linkColorPlugin"
      ],
      events: {
        keydown: this.bindKeyDownEvent.bind(this),
        keyup: this.bindKeyUpEvent.bind(this),
        initialized: this.initializeEvent.bind(this),
        'commands.before': this.commandsBeforeEvent.bind(this),
        'commands.after': this.commandsAfterEvent.bind(this),
        'toolbar.show': (selection) => {
          if ((this.editorInstance.selection.element().nodeName === "UAEPF") || (this.editorInstance.selection.element().querySelector("uaepf")?.length > 0) || (selection?.toElement?.nodeName === "UAEPF")) {
            this.editorInstance.toolbar.disable()
          }
        },
        'blur': () => {
          this.froalaBlur.emit({'html': this.transformFroalaHtmlString()})
        },
        'click': () => {
          this.froalaFocus.emit(this.editorInstance.html.get())
        },
        'contentChanged': () => {
          this.froalaContentChanged.emit({'html': this.transformFroalaHtmlString()})
        }
      },
    };

    @ViewChild('maxAIPopupMenu') maxAIPopupMenu: MenuComponent;

    rewriteOptions: string[] = [];
    maxAIRewritePopupMenuOptions: PopupMenuConfig = {
      alwaysOpen: false,
      class: 'maxAI-menu',
      triggerClass: 'custom-trigger',
      xPosition: 'before',
      yPosition: 'above',
    };
  
    commandsAfterEvent(cmd) {
      if (cmd == "undo") {
        this.editorInstance.$win[0].window.detachTribute();
        this.removeTributeContainers();
        this.editorInstance.$win[0].window.attachTribute();
        this.editorInstance.selection.restore();
      }
    }
  
    commandsBeforeEvent(cmd) {
      if (cmd == "addEditLinkOnImage" + this.froalaId || cmd === "linkFontColor" + this.froalaId) {
        let img = this.editorInstance.image.get();
        this.editorInstance.events.disableBlur();
      } else if (cmd == "editCustomLink" + this.froalaId || cmd == "addCustomLink" + this.froalaId) {
        this.editorInstance.events.disableBlur();
        this.editorInstance.selection.save();
        this.editorInstance.undo.reset();
      } else if (cmd == "undo") {
        this.editorInstance.events.disableBlur();
        this.editorInstance.selection.save();
        window.setTimeout(() => {
          this.editorInstance.selection.clear();
          this.editorInstance.events.focus(true);
          this.editorInstance.toolbar.hide();
        }, 0)
      } else if (cmd == "redo") {
        window.setTimeout(() => {
          this.editorInstance.selection.clear();
          this.editorInstance.events.focus(true);
          this.editorInstance.toolbar.hide();
        }, 0)
      } else if (cmd == "fontColor" || cmd == "linkFontColor" + this.froalaId) {
        this.editorInstance.undo.saveStep();
      } else if (cmd == "confirmColor") {
        this.editorInstance.selection.restore();
      } else if (cmd == "addMaxAI" + this.froalaId || cmd == "editMaxAI" + this.froalaId) {
        this.editorInstance.events.disableBlur();
        this.editorInstance.selection.save();
      }
    }
  
    constructor(private translate: TranslateService,
      private modalService: ModalService,
      public ngb: IpEmailBuilderService) {
      this.translate.stream('labels').subscribe((value) => {
        this.translationObject = value;
      })
      this.rewriteOptions = this.ngb.contentRewriteOptions;
    }
  
    ngAfterViewInit() {
      window.setTimeout(() => {
        const tribute = new Tribute({
          trigger: "#",
          values: this.pfList,
          selectTemplate: function (item) {
            return (
              '<span contenteditable="false" class="fr-deletable fr-tribute uaepf-tag-class"><uaepf><--' +
              item.original.key +
              "--></uaepf></span>"
            );
          },
        })
        tribute.attach(document.querySelector("#" + this.froalaId + " .froala-editor .fr-box .fr-wrapper .fr-element"));
      }, 10)
    }
  
    /**
     * default icon tenplate in froala editor
     * */
    static getDefaultIconTemplate(): string {
      return '<i class="hcl-icon-[NAME]"></i>';
    }
  
    /**
     *  icon config for all the custom icons
     * @param name - this is icon name
    */
    static getIconConfig(name: string): { NAME: string; template: string } {
      return {
        NAME: name,
        template: FroalaEditorComponent.materialDesignLabel,
      };
    }
  
    /**
     * returns config object for all the custom buttons
     * @param title 
     * @param icon 
     * @param callbackFn 
     * @param undo
     * @param refresh 
     * @returns 
     */
    static getCommandConfigObj(
      title: string,
      icon: string,
      callbackFn: Function,
      undo: boolean = false,
      refresh?: Function,
      froalaId?: string
    ) {
      return {
        title: title,
        icon: icon,
        focus: true,
        undo: undo,
        refreshAfterCallback: false,
        callback: callbackFn.bind(this),
        ...(refresh && { refresh: refresh.bind(this) }),
        froalaId: froalaId
      };
    }
  
    static viewAsWebpageClassName: string = 'view-online-block';
    static unsubscribeClassName: string = 'unsubscribe-block';
  
    static fontFamilyOptions(): any {
      return {
        "Times New Roman, Times, serif": "Times New Roman",
        "Arial, Helvetica, sans-serif": "Arial",
        "Arial Black, Gadget, sans-serif": "Arial Black",
        "Comic Sans MS, cursive, sans-serif": "Comic Sans M",
        "Impact, Charcoal, sans-serif": "Impact",
        "Tahoma, Geneva, sans-serif": "Tahoma",
        "Trebuchet MS, Helvetica, sans-serif": "Trebuchet MS",
        "Verdana, Geneva, sans-serif": "Verdana",
        "Courier New, Courier, monospace": "Courier New",
        "Lucida Console, Monaco, monospace": "Lucida Console",
        "Lucida Sans Unicode, Lucida Grande, sans-serif": "Lucida Sans Unicode",
        "Palatino Linotype, Book Antiqua, Palatino, serif": "Palatino Linotype",
      }
    }
  
    static fontSizeOptions(): any {
      return ['6', '7', '8', '9', '12', '14', '16', '18', '21', '24', '36', '48', '60', '72', '84', '96'];
    }
  
    static fontSizeDropdownOptions() {
      const fontSizesList = FroalaEditorComponent.fontSizeOptions();
      const sizeObject = {};
      fontSizesList.forEach(x => {
        sizeObject[x] = x;
      });
      return sizeObject;
    }
  
    ngOnInit(): void {
      this.ngb.closeHyperLinkInfo$.subscribe(() => {
        this.editorInstance.events.enableBlur();
        this.editImageNode = null;
        this.editLinkNode = null;
        this.editorInstance.selection.clear()
        this.newRangeSelected = null;
      });
      this.subscriptionList.push(this.ngb.updateDNDFroalaContentInfo$.subscribe((event: any) => {
        switch (event.function) {
          case 'INSERT_NEW_CUSTOM_LINK':
            this.insertNewCustomLink(event.url, event.redirection, event.attrs, event.froalaId, event.aliasNameInfo);
          break;
          case 'INSERT_NEW_AI_TEXT':
            this.insertNewAIText(event.text, event.froalaId);
          break;
          case 'RESTORE_SELECTION':
            this.restoreSelectionForCurrentFroalaId(event.froalaId, event.text);
          break;
        }
      }));
      FroalaEditor.DefineIconTemplate(
        FroalaEditorComponent.materialDesignLabel,
        FroalaEditorComponent.getDefaultIconTemplate()
      );
      this.configureImageEdittingOptions();
      this.configureAddLinkOption();
      // this.configureAddAIOption();
      this.configureTextFormattingOptions();
      this.configureEditLinkOption();
    }
  
    /**
     * method to return language code by using user locale from commeditor constants
     * @returns language code expected by froala
     */
    getLanguangeForTextEditor() {
      const id = this.userLocale;
      switch (id) {
        case 'de_DE':
          return 'de';
        case 'en_GB':
          return 'en_gb';
        case 'en_US':
          return 'en_us';
        case 'es_ES':
          return 'es';
        case 'fr_FR':
          return 'fr';
        case 'it_IT':
          return 'it';
        case 'ja_JP':
          return 'ja';
        case 'ko_KR':
          return 'ko';
        case 'pt_BR':
          return 'pt_br';
        case 'ru_RU':
          return 'ru';
        case 'zh_CN':
          return 'zh_cn';
        case 'zh_TW':
          return 'zh_tw';
      }
    }

    menuOptionMaxAIClicked(option) {
      this.showAISidebar.emit({ type: "text", context: "inline", action: option, text: this.newRangeSelected, showCustomViewAsWebpage: true, froalaId: this.editorInstance.froalaId });
      this.editorInstance.events.enableBlur();
      window.setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 1)
      // const text = this.quillEditorComponent.quillEditor.getText(this.currentRangeForSelectedTextAI.index, this.currentRangeForSelectedTextAI.length);
      // console.log('text', text);
  
      // this.ngb.onAIClick.next({
      //   callbackFn: this.saveTextAI,
      //   currentRef: this,
      //   text: text
      // });
      this.maxAIPopupMenu.closeMenu(false);
    }
  
    // aiHandler(event) {
    //   this.maxAIPopupMenu.openMenu(event);
    //   this.currentRangeForSelectedTextAI = this.quillEditorComponent.quillEditor.getSelection();
    //   const text = this.quillEditorComponent.quillEditor.getText(this.currentRangeForSelectedTextAI.index, this.currentRangeForSelectedTextAI.length);
    //   console.log('text', text);
    // }
  
    // saveTextAI(currentRef, text) {
    //   // currentRef.quillEditorComponent.quillEditor.format('link', url);
    //   if (currentRef.currentRangeForSelectedTextAI) {
    //     currentRef.quillEditorComponent.quillEditor.setSelection(currentRef.currentRangeForSelectedTextAI.index, currentRef.currentRangeForSelectedTextAI.length, Quill.sources.SILENT);
    //   }
    //   currentRef.quillEditorComponent.quillEditor.deleteText(currentRef.currentRangeForSelectedTextAI.index, currentRef.currentRangeForSelectedTextAI.length);
    //   currentRef.quillEditorComponent.quillEditor.insertText(currentRef.currentRangeForSelectedTextAI.index, text);
    //   currentRef.currentRangeForSelectedTextAI.length = text.length;
    //   currentRef.quillEditorComponent.quillEditor.theme.tooltip.hide();
    // }
  
    /**
     * binds - image change button, image link button and remove button
     */
    configureImageEdittingOptions() {
      FroalaEditor.DefineIcon(
        "changeImage" + this.froalaId,
        FroalaEditorComponent.getIconConfig("loop")
      );
      FroalaEditor.RegisterCommand(
        "changeImage" + this.froalaId,
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.REPLACE_IMAGE,
          "changeImage",
          () => {
            this.editImageNode = this.editorInstance.image.get();
            window.setTimeout(() => {
              window.dispatchEvent(new Event('resize'));
            }, 1)
          }
        )
      );
  
      FroalaEditor.DefineIcon(
        "addEditLinkOnImage" + this.froalaId,
        FroalaEditorComponent.getIconConfig("link-tool custom-link")
      );
      FroalaEditor.RegisterCommand(
        "addEditLinkOnImage" + this.froalaId,
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.BUTTONS.ADD_EDIT_LINK,
          "addEditLinkOnImage",
          () => {
            this.currentSnap = null;
            this.editLinkNode = this.editorInstance.link.get();
            this.editImageNode = this.editorInstance.image.get();
            if (this.editLinkNode && this.editImageNode) {
              this.openLinkSidebar(false);
            } else {
              this.showLinkSidebar.emit({ type: "link", text: "", showCustomViewAsWebpage: false });
            }
            this.editorInstance.events.enableBlur();
            window.setTimeout(() => {
              window.dispatchEvent(new Event('resize'));
            }, 1)
          },
          true
        )
      );
  
      FroalaEditor.DefineIcon(
        "imageDelete" + this.froalaId,
        FroalaEditorComponent.getIconConfig("delete")
      );
  
      FroalaEditor.RegisterCommand(
        "imageDelete" + this.froalaId,
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.BUTTONS.DELETE,
          "imageDelete",
          () => {
            this.editImageNode = this.editorInstance.image.get();
            this.modalService.openConfirmDialog(this.translationObject.MESSAGES.IMAGE_DELETE_CUSTOM_MSG,
              this.translationObject.BUTTONS.DELETE,
              this.translationObject.BUTTONS.CONFIRM_COLOR,
              this.translationObject.BUTTONS.CANCEL_COLOR,
              this.deleteImage.bind(this),
              () => this.modalService.closeDialog()
            );
            window.dispatchEvent(new Event('resize'));
          }
        )
      );
    }
  
    /**
     * bind a button to crate a link on any text
     */
    configureAddLinkOption() {
      FroalaEditor.DefineIcon(
        "customLink" + this.froalaId,
        FroalaEditorComponent.getIconConfig("link-tool")
      );
      FroalaEditor.RegisterCommand(
        "addCustomLink" + this.froalaId,
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.BUTTONS.CREATE_LINK,
          "customLink" + this.froalaId,
          () => {
            this.editLinkNode = null;
            this.newRangeSelected = this.editorInstance.selection.text();
            this.editorInstance.selection.save();
            this.currentSnap = null;
            this.showLinkSidebar.emit({ type: "link", text: "", showCustomViewAsWebpage: true, froalaId: this.editorInstance.froalaId, aliasName: '', id: Utility.generateId() });
            this.editorInstance.events.enableBlur();
            window.setTimeout(() => {
              window.dispatchEvent(new Event('resize'));
            }, 1)
          },
          false
        )
      );
    }

    /**
     * bind a button to generate AI on any text / link
     */
      configureAddAIOption() {
      FroalaEditor.DefineIcon(
        "customAI" + this.froalaId,
        FroalaEditorComponent.getIconConfig("link-tool")
      );
      FroalaEditor.RegisterCommand(
        "addMaxAI" + this.froalaId,
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.BUTTONS.CREATE_LINK,
          "customAI" + this.froalaId,
          (event) => {
            this.editLinkNode = null;
            this.newRangeSelected = this.editorInstance.selection.text();
            this.editorInstance.selection.save();
            this.currentSnap = this.editorInstance.snapshot.get();
            this.showAISidebar.emit({ type: "text", context: "inline", action: undefined, text: this.newRangeSelected, showCustomViewAsWebpage: true, froalaId: this.editorInstance.froalaId });
            this.editorInstance.events.enableBlur();
            window.setTimeout(() => {
              window.dispatchEvent(new Event('resize'));
            }, 1)
            // setTimeout(() => {
            //   this.maxAIPopupMenu.openMenu(event);
            // }, 0);
            // setTimeout(() => {
            //   const elements = document.getElementsByClassName(this?.maxAIRewritePopupMenuOptions?.class);
            //   if (elements.length > 0) {
            //       const buttonElement = document.querySelector('.fr-toolbar[style*="display: block"] button.fr-command.fr-btn:last-child');
            //       const overlayElement = document.querySelector('.cdk-overlay-connected-position-bounding-box');
            //       if (buttonElement && overlayElement) {
            //           const checkElementVisibility = () => {
            //               const buttonRect = buttonElement.getBoundingClientRect();
            //               const overlayRect = overlayElement.getBoundingClientRect();
            //               if (buttonRect.width !== 0 && buttonRect.height !== 0 && overlayRect.width !== 0 && overlayRect.height !== 0) {
            //                   const leftOffset = buttonRect.left - overlayRect.left;
            //                   let menu = document.querySelector('.maxAI-menu')as HTMLElement;
            //                   for (let i = 0; i < elements.length; i++) {
            //                     const element = elements[i] as HTMLElement;
            //                     element.style.position = 'absolute';
            //                     if (buttonRect.top - element.offsetHeight <= 50) {
            //                         element.style.top = `${buttonRect.bottom - element.offsetHeight + 20}px`;
            //                     } else {
            //                         element.style.top = `${buttonRect.top - element.offsetHeight}px`;
            //                     }
            //                     element.style.left = `${leftOffset}px`;
            //                   }
            //                 menu.style.visibility = 'visible';
            //               } else {
            //                   console.error('Element has zero width or height');
            //               }
            //           };
            //         const observer = new MutationObserver((mutationsList, observer) => {
            //             checkElementVisibility();
            //             observer.disconnect();
            //         });
            //         observer.observe(document.body, { childList: true, subtree: true });
            //         setTimeout(checkElementVisibility, 100);
            //       }
            //   }
            // }, 100);
          },
          false
        )
      );
      FroalaEditor.DefineIcon(
        "editCustomLinkAI" + this.froalaId,
        FroalaEditorComponent.getIconConfig("link-tool")
      );
      FroalaEditor.RegisterCommand(
        "editMaxAI" + this.froalaId,
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.BUTTONS.EDIT_LINK,
          "editCustomLinkAI" + this.froalaId,
          (event) => {
            this.editLinkNode = this.editorInstance.link.get();
            setTimeout(() => {
              this.maxAIPopupMenu.openMenu(event);
            }, 0);
            setTimeout(() => {
              const elements = document.getElementsByClassName(this?.maxAIRewritePopupMenuOptions?.class);
              if (elements.length > 0) {
                  const buttonElement = document.querySelector('.fr-toolbar[style*="display: block"] button.fr-command.fr-btn:last-child');
                  const overlayElement = document.querySelector('.cdk-overlay-connected-position-bounding-box');
                  if (buttonElement && overlayElement) {
                      const checkElementVisibility = () => {
                          const buttonRect = buttonElement.getBoundingClientRect();
                          const overlayRect = overlayElement.getBoundingClientRect();
                          if (buttonRect.width !== 0 && buttonRect.height !== 0 && overlayRect.width !== 0 && overlayRect.height !== 0) {
                              const leftOffset = buttonRect.left - overlayRect.left;
                              let menu = document.querySelector('.maxAI-menu')as HTMLElement;
                              for (let i = 0; i < elements.length; i++) {
                                const element = elements[i] as HTMLElement;
                                element.style.position = 'absolute';
                                if (buttonRect.top - element.offsetHeight <= 50) {
                                    element.style.top = `${buttonRect.bottom - element.offsetHeight + 20}px`;
                                } else {
                                    element.style.top = `${buttonRect.top - element.offsetHeight}px`;
                                }
                                element.style.left = `${leftOffset}px`;
                              }
                            menu.style.visibility = 'visible';
                          } else {
                              console.error('Element has zero width or height');
                          }
                      };
                    const observer = new MutationObserver((mutationsList, observer) => {
                        checkElementVisibility();
                        observer.disconnect();
                    });
                    observer.observe(document.body, { childList: true, subtree: true });
                    setTimeout(checkElementVisibility, 100);
                  }
              }
            }, 100);
          },
          false
        )
      );
    }
  
    /**
     * 1. bind all text formatting options like b, i, u, strike,
     *    color, size, family, clear
     * 2. bind custom color plugin
     * 3. bind custom alig drop down
     */
    configureTextFormattingOptions() {
      FroalaEditor.DefineIcon(
        "bold",
        FroalaEditorComponent.getIconConfig("bold")
      );
      FroalaEditor.RegisterCommand("bold", {
        title: this.translationObject.BUTTONS.BOLD
      });
      FroalaEditor.DefineIcon(
        "italic",
        FroalaEditorComponent.getIconConfig("italics")
      );
      FroalaEditor.RegisterCommand("italic", {
        title: this.translationObject.BUTTONS.ITALIC
      });
      FroalaEditor.DefineIcon(
        "underline",
        FroalaEditorComponent.getIconConfig("underline")
      );
      FroalaEditor.RegisterCommand("underline", {
        title: this.translationObject.BUTTONS.UNDERLINE
      });
      FroalaEditor.DefineIcon(
        "strikeThrough",
        FroalaEditorComponent.getIconConfig("strike-through")
      );
      FroalaEditor.RegisterCommand("strikeThrough", {
        title: this.translationObject.BUTTONS.STRIKE_THROUGH
      });
      this.createCustomFontFamilyDropdown('fontFamilyDropdown', ((val) => {
        this.editorInstance.fontFamily.apply(val);
        this.editorInstance.events.focus(true);
      }).bind(this));
      this.createCustomFontSizeDropdown('fontSizeDropdown', ((val) => {
        this.editorInstance.fontSize.apply(val + 'px');
        this.editorInstance.selection.clear();
        this.editorInstance.events.disableBlur();
        this.editorInstance.popups.hideAll();
        this.editorInstance.toolbar.hide();
      }).bind(this));
      FroalaEditor.DefineIcon(
        "clearFormatting",
        FroalaEditorComponent.getIconConfig("clear-format")
      );
      FroalaEditor.RegisterCommand("clearFormatting", {
        title: this.translationObject.BUTTONS.CLEAR_FORMATTING
      });
      this.createCustomColorPicker();
      this.createCustomAlignDropdown();
    }
  
    /**
     * define a custom pop up which will open on 
     * a custom button "fontColor" also defined in this function only
     */
    createCustomColorPicker() {
      FroalaEditor.POPUP_TEMPLATES["colorPlugin.popup"] = '[_CUSTOM_LAYER_][_BUTTONS_]';
      Object.assign(FroalaEditor.DEFAULTS, {
        popupButtons: ['confirmColor', 'cancelColor'],
      });
      FroalaEditor.PLUGINS.colorPlugin = function (editor) {
        var currentColor: '';
        const initPopup = () => {
          var popup_buttons = '';
          if (editor.opts.popupButtons.length > 0) {
            popup_buttons += '<div class="custom-layer-buttons">';
            popup_buttons += editor.button.buildList(editor.opts.popupButtons);
            popup_buttons += '</div>';
          }
          var template = {
            buttons: popup_buttons,
            custom_layer: '<div class="custom-layer" id="'+editor.froalaId+'"></div>'
          };
          var $popup = editor.popups.create('colorPlugin.popup', template);
          return $popup;
        }
        const showPopup = (popupContainer?: any) => {
          editor.selection.save();
          var $popup = editor.popups.get('colorPlugin.popup');
          if (!$popup)
            $popup = initPopup();
          editor.popups.setContainer('colorPlugin.popup', editor.$tb);
          const colorPopupHeight = 297;
          const fixedHeaderHeight = 130;
          const finalWindowHeight = document.body.clientHeight
          const fontColorButtonPositionTop = editor.$tb.find('.fr-command[data-cmd="fontColor'+editor.froalaId+'"]').offset().top;
          const scrollDeltaTop = document.body.scrollTop;
          let fontColorButtonHeight = 0;
          if (editor.$tb.hasClass('fr-above')) {
            fontColorButtonHeight = 40;
          } else {
            fontColorButtonHeight = -40;
          }
          let colorPopupTop = 0;
          var $btn = editor.$tb.find('.fr-command[data-cmd="fontColor'+editor.froalaId+'"]');
          var $cursorCoordinates = editor.position.getBoundingRect();
          var left = $cursorCoordinates.left;
          var width = $cursorCoordinates.width;
          var top = $cursorCoordinates.top;
          $popup[0].classList.add('color-custom-popup');
          if ((fontColorButtonPositionTop - fixedHeaderHeight) < ((finalWindowHeight * (1 / 3)) + 0)) {
            colorPopupTop = fontColorButtonPositionTop + fontColorButtonHeight;
          } else if ((((fontColorButtonPositionTop - fixedHeaderHeight) > (finalWindowHeight * (1 / 3)) + 0)) && (((fontColorButtonPositionTop - fixedHeaderHeight) < (finalWindowHeight * (2 / 3)) + 0))) {
            colorPopupTop = fontColorButtonPositionTop - (colorPopupHeight / 2);
          } else {
            colorPopupTop = fontColorButtonPositionTop - colorPopupHeight;
          }
          editor.popups.show('colorPlugin.popup', left , top, $btn.outerHeight());
          if (editor.alwanColorPickerInstance) {
            editor.alwanColorPickerInstance.destroy();
          }
          editor.alwanColorPickerInstance = new alwan('body', {
            popover: false,
            target: "#"+editor.froalaId+".custom-layer",
            format: "hex",
            copy: false,
            preview: false,
            opacity: false,
            toggle: false
          });
          editor.alwanColorPickerInstance.open();
          editor.alwanColorPickerInstance.on('change', (color) => {
            currentColor = color.hex;
          });
        }
        const setColor = () => {
          if (currentColor) {
            editor.colors.text(currentColor);
            editor.undo.saveStep();
          }
          hidePopup();
        }
        const hidePopup = () => {
          editor.popups.hide('colorPlugin.popup');
          editor.events.focus(true);
        }
        return {
          showPopup: showPopup,
          hidePopup: hidePopup,
          setColor: setColor
        }
      }
      FroalaEditor.DefineIconTemplate('custom-footer-button', '<div class="custom-footer-button [CLASS]"><span>[NAME]</span></div>');
      FroalaEditor.DefineIcon('confirmColor', { NAME: this.translationObject.BUTTONS.CONFIRM_COLOR, CLASS: 'confirm-button', template: 'custom-footer-button' });
      FroalaEditor.RegisterCommand('confirmColor', {
        title: this.translationObject.BUTTONS.CONFIRM_COLOR,
        icon: 'confirmColor',
        undo: false,
        focus: false,
        callback: function () {
          this.colorPlugin.setColor();
        }
      });
      FroalaEditor.DefineIcon('cancelColor', { NAME: this.translationObject.BUTTONS.CANCEL_COLOR, CLASS: 'cancel-button', template: 'custom-footer-button' });
      FroalaEditor.RegisterCommand('cancelColor', {
        title: this.translationObject.BUTTONS.CANCEL_COLOR,
        icon: 'cancelColor',
        undo: false,
        focus: false,
        callback: function () {
          this.colorPlugin.hidePopup();
        }
      });
      FroalaEditor.DefineIcon(
        "fontColor",
        FroalaEditorComponent.getIconConfig("font-color")
      );
      FroalaEditor.RegisterCommand('fontColor' + this.froalaId, {
        title: this.translationObject.BUTTONS.CHANGE_COLOR,
        icon: 'fontColor',
        undo: false,
        focus: false,
        plugin: 'colorPlugin',
        callback: function (editor) {
          const isLinkNode = this.link.get();
          if (isLinkNode) {
            this.colorPlugin.showPopup(this.popups.get('link.edit'));
          } else {
            this.colorPlugin.showPopup();
          }
        }
      });
    }
  
    /**
     * define a custom pop up which will open on 
     * a custom button "fontColor" also defined in this function only
     */
    createCustomColorPickerForLink() {
      FroalaEditor.POPUP_TEMPLATES["linkColorPlugin.popup"] = '[_LINK_CUSTOM_LAYER_][_LINK_BUTTONS_]';
      Object.assign(FroalaEditor.DEFAULTS, {
        customLinkPopupButtons: ['confirmLinkColor' + this.froalaId, 'cancelLinkColor' + this.froalaId],
      });
      FroalaEditor.PLUGINS.linkColorPlugin = function (editor) {
        var currentColor: '';
        const initPopup = () => {
          var popup_buttons = '';
          if (editor.opts.customLinkPopupButtons.length > 0) {
            popup_buttons += '<div class="link-custom-layer-buttons">';
            popup_buttons += editor.button.buildList(editor.opts.customLinkPopupButtons);
            popup_buttons += '</div>';
          }
          var template = {
            link_buttons: popup_buttons,
            link_custom_layer: '<div class="link-custom-layer"></div>'
          };
          var $popup = editor.popups.create('linkColorPlugin.popup', template);
          return $popup;
        }
        const showPopup = (popupContainer?: any) => {
          if (editor.popups.get('link.edit').hasClass('fr-above')) {
            editor.popups.get('link.edit').openedAbove = true;
          } else {
            editor.popups.get('link.edit').openedAbove = false;
          }
          var $popup = editor.popups.get('linkColorPlugin.popup');
          if (!$popup)
            $popup = initPopup();
  
          editor.popups.setContainer('linkColorPlugin.popup', editor.$box);
          const colorPopupHeight = 297;
          const fixedHeaderHeight = 130;
          const finalWindowHeight = document.body.clientHeight
          const fontColorButtonPositionTop = popupContainer.find('.fr-command[data-cmd="linkFontColor'+editor.froalaId+'"]').offset().top;
          const scrollDeltaTop = document.body.scrollTop;
          let fontColorButtonHeight = 0;
          if (popupContainer.hasClass('fr-above')) {
            fontColorButtonHeight = 40;
          } else {
            fontColorButtonHeight = -40;
          }
          let colorPopupTop = 0;
          var $btn = popupContainer.find('.fr-command[data-cmd="linkFontColor'+editor.froalaId+'"]');
          var $cursorCoordinates = editor.position.getBoundingRect();
          var left = $cursorCoordinates.left;
          var width = $cursorCoordinates.width;
          var top = $cursorCoordinates.top;
          $popup[0].classList.add('link-color-custom-popup');
          if ((fontColorButtonPositionTop - fixedHeaderHeight) < ((finalWindowHeight * (1 / 3)) + 0)) {
            colorPopupTop = fontColorButtonPositionTop + fontColorButtonHeight;
          } else if ((((fontColorButtonPositionTop - fixedHeaderHeight) > (finalWindowHeight * (1 / 3)) + 0)) && (((fontColorButtonPositionTop - fixedHeaderHeight) < (finalWindowHeight * (2 / 3)) + 0))) {
            colorPopupTop = fontColorButtonPositionTop - (colorPopupHeight / 2);
          } else {
            colorPopupTop = fontColorButtonPositionTop - colorPopupHeight;
          }
          editor.popups.show('linkColorPlugin.popup', left + width, colorPopupTop - fixedHeaderHeight + scrollDeltaTop, $btn.outerHeight());
          if (window['alwan'].alwanColorPickerInstance) {
            window['alwan'].alwanColorPickerInstance.destroy();
          }
          window['alwan'].alwanColorPickerInstance = new alwan('body', {
            target: ".link-custom-layer",
            format: "hex",
            copy: false,
            preview: false,
            opacity: false
          });
          window['alwan'].alwanColorPickerInstance.open();
          window['alwan'].alwanColorPickerInstance.on('change', (color) => {
            currentColor = color.hex;
          });
          const colorObj = $popup.find("input[type=color]")[0];
          if (colorObj) {
            colorObj.addEventListener('change', (e) => {
              currentColor = e.target.value;
            });
            colorObj.click();
          }
        }
        const setColor = () => {
          const currentNode = editor.link.get();
          if (currentColor) {
            editor.parentInstance.applyRemoveSizeFamilyColor(currentNode, "color", currentColor);
            editor.undo.saveStep();
          }
          hidePopup();
        }
        const hidePopup = () => {
          editor.popups.hide('linkColorPlugin.popup');
          editor.popups.show('link.edit');
          editor.selection.restore();
          if (editor.popups.get('link.edit').openedAbove == true) {
            editor.popups.get('link.edit').addClass('fr-above');
          } else {
            editor.popups.get('link.edit').removeClass('fr-above');
          }
          editor.popups.show('link.edit');
        }
        return {
          showPopup: showPopup,
          hidePopup: hidePopup,
          setColor: setColor
        }
      }
      FroalaEditor.DefineIconTemplate('custom-footer-button', '<div class="custom-footer-button [CLASS]"><span>[NAME]</span></div>');
      FroalaEditor.DefineIcon('confirmLinkColor' + this.froalaId, { NAME: this.translationObject.BUTTONS.CONFIRM_COLOR, CLASS: 'confirm-button', template: 'custom-footer-button' });
      FroalaEditor.RegisterCommand('confirmLinkColor' + this.froalaId, {
        title: this.translationObject.BUTTONS.CONFIRM_COLOR,
        icon: 'confirmLinkColor' + this.froalaId,
        undo: true,
        focus: false,
        callback: function () {
          this.selection.restore();
          this.linkColorPlugin.setColor();
        }
      });
      FroalaEditor.DefineIcon('cancelLinkColor' + this.froalaId, { NAME: this.translationObject.BUTTONS.CANCEL_COLOR, CLASS: 'cancel-button', template: 'custom-footer-button' });
      FroalaEditor.RegisterCommand('cancelLinkColor' + this.froalaId, {
        title: this.translationObject.BUTTONS.CANCEL_COLOR,
        icon: 'cancelLinkColor' + this.froalaId,
        undo: false,
        focus: false,
        callback: function () {
          this.selection.restore();
          this.linkColorPlugin.hidePopup();
        }
      });
      FroalaEditor.DefineIcon(
        "linkFontColor" + this.froalaId,
        FroalaEditorComponent.getIconConfig("font-color")
      );
      FroalaEditor.RegisterCommand('linkFontColor' + this.froalaId, {
        title: this.translationObject.BUTTONS.CHANGE_COLOR,
        icon: 'linkFontColor' + this.froalaId,
        undo: true,
        focus: false,
        plugin: 'linkColorPlugin',
        callback: function (editor) {
          const isLinkNode = this.link.get();
          if (isLinkNode) {
            this.linkColorPlugin.showPopup(this.popups.get('link.edit'));
          }
        }
      });
    }
  
    createCustomAlignDropdown() {
      FroalaEditor.DefineIcon(
        "textAlignDropdown",
        FroalaEditorComponent.getIconConfig("align-tool")
      );
      FroalaEditor.RegisterCommand('textAlignDropdown', {
        title: this.translationObject.BUTTONS.ALIGN,
        type: 'dropdown',
        focus: false,
        undo: false,
        refreshAfterCallback: false,
        options: {
          'left': '<i class=hcl-icon-align-left></i>',
          'right': '<i class=hcl-icon-align-right></i>',
          'center': '<i class=hcl-icon-align-center></i>',
          'justify': '<i class=hcl-icon-align-justify></i>'
        },
        callback: (cmd, val) => {
          this.editorInstance.align.apply(val);
        },
        refresh: function ($btn) {
        },
        refreshOnShow: function ($btn, $dropdown) {
        }
      });
    }
  
    createCustomFontFamilyDropdown(commandName: string, callbackFn: Function) {
      FroalaEditor.DefineIcon(
        commandName,
        FroalaEditorComponent.getIconConfig("font-family")
      );
      FroalaEditor.RegisterCommand(commandName, {
        title: this.translationObject.BUTTONS.FONT_FAMILY,
        type: 'dropdown',
        focus: false,
        undo: true,
        refreshAfterCallback: false,
        options: FroalaEditorComponent.fontFamilyOptions(),
        callback: (cmd, val) => {
          callbackFn(val);
        },
        refresh: function ($btn) {
        },
        refreshOnShow: function ($btn, $dropdown) {
        }
      });
    }
  
    createCustomFontSizeDropdown(commandName: string, callbackFn: Function) {
      FroalaEditor.DefineIcon(
        commandName,
        FroalaEditorComponent.getIconConfig("font-size")
      );
      FroalaEditor.RegisterCommand(commandName, {
        title: this.translationObject.BUTTONS.FONT_SIZE,
        type: 'dropdown',
        focus: false,
        undo: true,
        refreshAfterCallback: false,
        options: FroalaEditorComponent.fontSizeDropdownOptions,
        callback: (cmd, val) => {
          callbackFn(val);
        },
        refresh: ($btn) => {
        },
        refreshOnShow: function ($btn, $dropdown) {
        }
      });
    }
  
    /**
     * bind edit link button which will show on any existing link
     */
    configureEditLinkOption() {
      FroalaEditor.DefineIcon(
        "linkBold",
        FroalaEditorComponent.getIconConfig("bold")
      );
      FroalaEditor.RegisterCommand(
        "linkBold",
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.BUTTONS.BOLD,
          "linkBold",
          () => {
            this.applyRemoveBoldItalicUnderline(this.editorInstance.link.get(), "font-weight", "bold", "revert");
          },
          true,
          ($btn) => {
          }
        )
      );
  
      FroalaEditor.DefineIcon(
        "linkItalic",
        FroalaEditorComponent.getIconConfig("italics")
      );
      FroalaEditor.RegisterCommand(
        "linkItalic",
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.BUTTONS.ITALIC,
          "linkItalic",
          () => {
            this.applyRemoveBoldItalicUnderline(this.editorInstance.link.get(), "font-style", "italic", "none");
          },
          true,
          ($btn) => {
          }
        )
      );
  
      FroalaEditor.DefineIcon(
        "linkUnderline",
        FroalaEditorComponent.getIconConfig("underline")
      );
      FroalaEditor.RegisterCommand(
        "linkUnderline",
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.BUTTONS.UNDERLINE,
          "linkUnderline",
          () => {
            this.applyRemoveBoldItalicUnderline(this.editorInstance.link.get(), "text-decoration", "underline", "none");
          },
          true,
          ($btn) => {
          }
        )
      );
      const callbackFnForFontFamily = (val) => {
        this.applyRemoveSizeFamilyColor(this.editorInstance.link.get(), "font-family", val)
        this.editorInstance.events.focus(true);
      };
      const callbackFnForFontSize = (val) => {
        this.applyRemoveSizeFamilyColor(this.editorInstance.link.get(), "font-size", val + "px")
        this.editorInstance.selection.clear();
        this.editorInstance.popups.hideAll();
        this.editorInstance.toolbar.hide();
      }
      this.createCustomFontFamilyDropdown('linkFontFamilyDropdown', callbackFnForFontFamily);
      this.createCustomFontSizeDropdown('linkFontSizeDropdown', callbackFnForFontSize);
      this.createCustomColorPickerForLink();
      FroalaEditor.DefineIcon(
        "linkClearFormat",
        FroalaEditorComponent.getIconConfig("clear-format")
      );
      FroalaEditor.RegisterCommand(
        "linkClearFormat",
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.BUTTONS.CLEAR_FORMATTING,
          "linkClearFormat",
          () => {
            const linkNode = this.editorInstance.link.get();
            const isParentSpanSet = linkNode.parentElement.classList.contains('comm-link-wrapper');
            if (isParentSpanSet) {
              linkNode.removeAttribute('style');
              const preStyles = isParentSpanSet && linkNode.parentElement.dataset.preStyles;
              if (preStyles) {
                const preStylesArray = preStyles.split(';');
                preStylesArray.forEach((preStyle) => {
                  const propNvalue = preStyle.split(':');
                  linkNode.style[propNvalue[0].trim()] = propNvalue[1].trim();
                })
              }
              linkNode.parentElement.classList.remove('comm-link-wrapper');
              linkNode.parentElement.removeAttribute('data-pre-styles');
            } else {
              const linkClassList = [...this.editorInstance.link.get().classList];
              linkClassList.forEach((className) => {
                if (className.startsWith("dynamic-style-" + this.randomId + "-" + "font-size" + "-")) {
                  const linkUnderlineClass = className.split("-");
                  this.deleteClassForLink(this.randomId, linkUnderlineClass[(linkUnderlineClass.length - 1)], "font-size", "");
                  this.removeClassFromLink(this.editorInstance.link.get(), className);
                }
                if (className.startsWith("dynamic-style-" + this.randomId + "-" + "font-family" + "-")) {
                  const linkUnderlineClass = className.split("-");
                  this.deleteClassForLink(this.randomId, linkUnderlineClass[(linkUnderlineClass.length - 1)], "font-family", "");
                  this.removeClassFromLink(this.editorInstance.link.get(), className);
                }
                if (className.startsWith("dynamic-style-" + this.randomId + "-" + "font-weight" + "-")) {
                  const linkUnderlineClass = className.split("-");
                  this.deleteClassForLink(this.randomId, linkUnderlineClass[(linkUnderlineClass.length - 1)], "font-weight", "");
                  this.removeClassFromLink(this.editorInstance.link.get(), className);
                }
                if (className.startsWith("dynamic-style-" + this.randomId + "-" + "font-style" + "-")) {
                  const linkUnderlineClass = className.split("-");
                  this.deleteClassForLink(this.randomId, linkUnderlineClass[(linkUnderlineClass.length - 1)], "font-style", "");
                  this.removeClassFromLink(this.editorInstance.link.get(), className);
                }
                if (className.startsWith("dynamic-style-" + this.randomId + "-" + "text-decoration" + "-")) {
                  const linkUnderlineClass = className.split("-");
                  this.deleteClassForLink(this.randomId, linkUnderlineClass[(linkUnderlineClass.length - 1)], "text-decoration", "");
                  this.removeClassFromLink(this.editorInstance.link.get(), className);
                  const generatedClassName = this.generateClassForLink(this.randomId, new Date().getTime().toString(), "text-decoration", "none");
                  this.addClassToLink(this.editorInstance.link.get(), generatedClassName);
                }
                if (className.startsWith("dynamic-style-" + this.randomId + "-" + "color" + "-")) {
                  const linkUnderlineClass = className.split("-");
                  this.deleteClassForLink(this.randomId, linkUnderlineClass[(linkUnderlineClass.length - 1)], "color", "");
                  this.removeClassFromLink(this.editorInstance.link.get(), className);
                }
              });
            }
          },
          true,
          ($btn) => {
          }
        )
      );
      FroalaEditor.DefineIcon(
        "customLinkEdit" + this.froalaId,
        FroalaEditorComponent.getIconConfig("link-tool custom-link")
      );
      FroalaEditor.RegisterCommand(
        "editCustomLink" + this.froalaId,
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.BUTTONS.EDIT_LINK,
          "customLinkEdit" + this.froalaId,
          () => {
            this.currentSnap = this.editorInstance.snapshot.get();
            this.editLinkNode = this.editorInstance.link.get();
            this.editorInstance.selection.save();
            this.openLinkSidebar(this.editLinkNode.children[0].localName.toLowerCase() !== 'button');
            window.dispatchEvent(new Event('resize'));
          },
          false,
          ($btn) => {
            this.editLinkNode = this.editorInstance.link.get();
            if (this.editLinkNode) {
              const dataViewAsWebpage = this.editLinkNode.getAttribute('data-custom-view-as-webpage');
              const dataHref = this.editLinkNode.getAttribute('data-href');
              const href = this.editLinkNode.getAttribute('href');
              if (!dataViewAsWebpage && ((dataHref && dataHref.indexOf('SELF_LANDING_PAGE') > -1) || (href && href.indexOf('SELF_LANDING_PAGE') > -1))) {
                $btn[0].style.display = 'none';
              } else {
                $btn[0].style.display = 'inline-block';
              }
            }
          }, this.froalaId
        )
      );
      FroalaEditor.DefineIcon(
        "customLinkDelete" + this.froalaId,
        FroalaEditorComponent.getIconConfig("delete")
      );
      FroalaEditor.RegisterCommand(
        "customLinkDelete" + this.froalaId,
        FroalaEditorComponent.getCommandConfigObj(
          this.translationObject.BUTTONS.DELETE,
          "customLinkDelete" + this.froalaId,
          () => {
            this.modalService.openConfirmDialog(this.translate.instant('COMMUNICATION.EMAIL_EDITOR.HTML_IMPORT_EDITOR.MESSAGES.LINK_DELETE_CUSTOM_MSG'),
              this.translationObject.BUTTONS.DELETE,
              this.translationObject.BUTTONS.CONFIRM_COLOR,
              this.translationObject.BUTTONS.CANCEL_COLOR,
              this.deleteCustomLink.bind(this),
              () => this.modalService.closeDialog()
            );
            window.dispatchEvent(new Event('resize'));
          },
          true
        )
      );
    }
  
    deleteCustomLink() {
      const linkToBeDeleted = this.editorInstance.link.get();
      linkToBeDeleted['innerHTML'] = '';
      this.editorInstance.link.remove();
      this.modalService.closeDialog();
    }
  
    deleteImage() {
      this.editorInstance.image.remove(this.editImageNode);
      this.modalService.closeDialog();
    }
  
    addRemoveCssStyleFromLink(currentNode: any, stylePropName: string, stylePropValue: string) {
      if (currentNode.style[stylePropName]) {
        currentNode.style.removeProperty(stylePropName);
      } else {
        currentNode.style[stylePropName] = stylePropValue;
      }
    }
  
    applyRemoveSizeFamilyColor(linkNode, styleName, styleValueApply) {
      this.updateLinkNodeWithInlineStyle(linkNode, styleName, styleValueApply)
    }
  
    inlineStylePresentInLinkNode(linkNode) {
      const inlineStyles = [];
      if (linkNode.style.textDecoration) {
        inlineStyles.push(`text-decoration:${linkNode.style.textDecoration}`);
      }
      if (linkNode.style.fontFamily) {
        inlineStyles.push(`font-family:${linkNode.style.fontFamily}`);
      }
      if (linkNode.style.fontSize) {
        inlineStyles.push(`font-size:${linkNode.style.fontSize}`);
      }
      if (linkNode.style.fontStyle) {
        inlineStyles.push(`font-style:${linkNode.style.fontStyle}`);
      }
      if (linkNode.style.fontWeight) {
        inlineStyles.push(`font-weight:${linkNode.style.fontWeight}`);
      }
      if (linkNode.style.color) {
        inlineStyles.push(`color:${linkNode.style.color}`);
      }
      return inlineStyles.length ? inlineStyles.join('; ') : null;
    }
  
    applyRemoveBoldItalicUnderline(linkNode, styleName, styleValueApply, styleValueRemove) {
      this.updateLinkNodeWithInlineStyle(linkNode, styleName, styleValueApply)
    }
  
    getFontFamilyValueFromClassKey(classKey: string) {
      switch (classKey) {
        case 'TimesNewRomanTimesserif':
          return "Times New Roman, Times, serif";
        case 'ArialHelveticasans':
          return "Arial, Helvetica, sans-serif";
        case 'ArialBlackGadgetsans':
          return "Arial Black, Gadget, sans-serif";
        case 'ComicSansMScursivesans':
          return "Comic Sans MS, cursive, sans-serif";
        case 'ImpactCharcoalsans':
          return "Impact, Charcoal, sans-serif";
        case 'TahomaGenevasans':
          return "Tahoma, Geneva, sans-serif";
        case 'TrebuchetMSHelveticasans':
          return "Trebuchet MS, Helvetica, sans-serif";
        case 'VerdanaGenevasans':
          return "Verdana, Geneva, sans-serif";
        case 'CourierNewCouriermonospace':
          return "Courier New, Courier, monospace";
        case 'LucidaConsoleMonacomonospace':
          return "Lucida Console, Monaco, monospace";
        case 'LucidaSansUnicodeLucidaGrandesans':
          return "Lucida Sans Unicode, Lucida Grande, sans-serif";
        case 'PalatinoLinotypeBookAntiquaPalatinoserif':
          return "Palatino Linotype, Book Antiqua, Palatino, serif";
        default:
          return "Times New Roman, Times, serif";
      }
    }
  
    updateLinkNodeWithInlineStyle(linkNode, styleName, styleValueApply) {
      let existingStyles: any = this.inlineStylePresentInLinkNode(linkNode);
      existingStyles = existingStyles && existingStyles.split(';');
      linkNode.classList.forEach((className: any) => {
        if (className.startsWith("dynamic-style-")) {
          if (!className.includes('none') && !className.includes('revert')) {
            existingStyles.some((style, ind) => {
              const propNvalue = style.split(':');
              if (className.includes(propNvalue[0].trim())) {
                const styleValue = propNvalue[0].trim() === 'font-family' ? this.getFontFamilyValueFromClassKey(className.split('-')[className.split('-').length - (className.includes('serif-') ? 3 : 2)]) : propNvalue[0].trim() === 'color' ? "#" + className.split('-')[className.split('-').length - 2] : className.split('-')[className.split('-').length - 2];
                existingStyles[ind] = `${propNvalue[0].trim()}: ${styleValue}`;
                linkNode.style[propNvalue[0].trim()] = styleValue;
                return true;
              }
            })
          }
        }
      });
      for (let i = linkNode.classList.length - 1; i >= 0; i--) {
        if (linkNode.classList[i].startsWith("dynamic-style-")) {
          linkNode.classList.remove(linkNode.classList[i]);
        }
      }
      existingStyles = existingStyles && existingStyles.join('; ') || '';
      const isParentSpanSet = linkNode.parentElement.classList.contains('comm-link-wrapper');
      if (!isParentSpanSet) {
        const customDataHrefAvailable = linkNode.parentElement.attributes.getNamedItem('custom-data-href');
        if (customDataHrefAvailable) {
          linkNode.style[styleName] = styleValueApply;
          linkNode.parentElement.classList.add('comm-link-wrapper');
          if (existingStyles && !linkNode.parentElement.getAttribute('data-pre-styles')) {
            linkNode.parentElement.setAttribute('data-pre-styles', existingStyles);
          }
          const elementText = linkNode.innerHTML;
          linkNode['innerHTML'] = '';
          this.editorInstance.html.insert(`${linkNode.outerHTML}`);
          linkNode['innerHTML'] = elementText;
        } else {
          let elementText = linkNode.innerHTML;
          let tagsAvailable = false;
          if (elementText.indexOf('</s>') > -1) {
            elementText = elementText.replace('<s>', '').replace('</s>', '');
            tagsAvailable = true;
          }
          if (elementText.indexOf('</strong>') > -1) {
            elementText = elementText.replace('<strong>', '').replace('</strong>', '');
            existingStyles = existingStyles ? existingStyles + '; font-weight: 600' : 'font-weight: 600';
            linkNode.style.fontWeight = 600;
            tagsAvailable = true;
          }
          if (elementText.indexOf('</em>') > -1) {
            elementText = elementText.replace('<em>', '').replace('</em>', '');
            existingStyles = existingStyles ? existingStyles + '; font-style: italic' : 'font-style: italic';
            linkNode.style.fontStyle = 'italic';
            tagsAvailable = true;
          }
          if (elementText.indexOf('</u>') > -1) {
            elementText = elementText.replace('<u>', '').replace('</u>', '');
            existingStyles = existingStyles ? existingStyles + '; text-decoration: underline' : 'text-decoration: underline';
            linkNode.style.textDecoration = 'underline';
            tagsAvailable = true;
          }
          if (elementText.indexOf('</span>') > -1) {
            elementText = elementText.replace('<span ', '').replace('</span>', '');
            let stylesAndText = elementText.split('>');
            elementText = stylesAndText[1];
            const spanStyles = stylesAndText[0].replace(/\'/g, '').replace(/\"/g, '').replace(/"/g, '').replace(/'/g, '').split('=')[1];
            existingStyles = existingStyles ? existingStyles + `; ${spanStyles}` : `${spanStyles}`;
            spanStyles.split('; ').forEach(style => {
              if (style.indexOf('font-family') > -1) {
                linkNode.style.fontFamily = style.split(':')[1].replace('&quot;', '"');
              } else if (style.indexOf('font-size') > -1) {
                linkNode.style.fontSize = style.split(':')[1];
              } else {
                linkNode.style.color = style.split(':')[1];
              }
            })
            tagsAvailable = true;
          }
          existingStyles = existingStyles && existingStyles.replace(/"/g, "'");
          linkNode.style[styleName] = styleValueApply;
          if (!tagsAvailable) {
            const elOuterHTML = linkNode.outerHTML;
            if (linkNode.parentElement.localName !== 'span') {
              linkNode.outerHTML = `<span custom-data-href="#reset" data-pre-styles="${existingStyles ? existingStyles : ''}" class="comm-link-wrapper" >${elOuterHTML}</span>`;
            } else {
              linkNode.parentElement.classList.add('comm-link-wrapper');
              linkNode.parentElement.setAttribute('custom-data-href', "#reset");
              if (existingStyles) {
                linkNode.parentElement.setAttribute('data-pre-styles', existingStyles);
              }
            }
          } else {
            linkNode['innerHTML'] = '';
            const newAnchorTag = linkNode.outerHTML.substring(0, linkNode.outerHTML.indexOf('</a>')) + elementText + '</a>';
            if (linkNode.parentElement.localName !== 'span') {
              linkNode.outerHTML = `<span custom-data-href="#reset" data-pre-styles="${existingStyles ? existingStyles : ''}" class="comm-link-wrapper" >${newAnchorTag}</span>`;
            } else {
              linkNode.parentElement['innerHTML'] = newAnchorTag;
              linkNode.parentElement.classList.add('comm-link-wrapper');
              linkNode.parentElement.setAttribute('custom-data-href', "#reset");
              if (existingStyles) {
                linkNode.parentElement.setAttribute('data-pre-styles', existingStyles);
              }
            }
          }
        }
      } else {
        if (styleName === 'font-weight') {
          linkNode.style[styleName] = linkNode.style[styleName] === 'bold' ? 'normal' : styleValueApply;
        } else if (styleName === 'font-style') {
          linkNode.style[styleName] = linkNode.style[styleName] === 'italic' ? 'normal' : styleValueApply;
        } else if (styleName === 'text-decoration') {
          linkNode.style[styleName] = linkNode.style[styleName] === 'underline' ? 'none' : styleValueApply;
        } else {
          linkNode.style[styleName] = styleValueApply;
        }
      }
    }
  
    generateClassForLink(className: string, randomString: string, stylePropName: string, stylePropValue: string) {
      const dynamicStyleElement = document.createElement("style");
      const htmlSafeStylePropValue = stylePropValue.replace(/,/gi, "").replace(/ /gi, "").replace(/#/gi, "")
      dynamicStyleElement.setAttribute("data-id", "dynamic-style-" + className + "-" + stylePropName + "-" + htmlSafeStylePropValue + "-" + randomString);
      const inlineDynamicStyleElement = document.createTextNode(`
        .dynamic-style-${className}-${stylePropName}-${htmlSafeStylePropValue}-${randomString} {
          ${stylePropName}: ${stylePropValue} !important;
        }
      `);
      dynamicStyleElement.appendChild(inlineDynamicStyleElement);
      this.editorInstance.el.appendChild(dynamicStyleElement);
      return ("dynamic-style-" + className + "-" + stylePropName + "-" + htmlSafeStylePropValue + "-" + randomString);
    }
  
    deleteClassForLink(className: string, randomString: string, stylePropName: string, stylePropValue: string) {
      const htmlSafeStylePropValue = stylePropValue.replace(/,/gi, "").replace(/ /gi, "").replace(/#/gi, "")
      const dynamicStyleTag = this.editorInstance.el.querySelector(`[data-id="dynamic-style-${className}-${stylePropName}-${htmlSafeStylePropValue}-${randomString}"]`);
      if (dynamicStyleTag) {
        dynamicStyleTag.parentElement.removeChild(dynamicStyleTag);
      }
      return ("dynamic-style-" + className + "-" + stylePropName + "-" + htmlSafeStylePropValue + "-" + randomString)
    }
  
    addClassToLink(currentNode: any, className: string) {
      if (!currentNode.classList.contains(className)) {
        currentNode.classList.add(className);
      }
    }
  
    removeClassFromLink(currentNode: any, className: string) {
      if (currentNode.classList.contains(className)) {
        currentNode.classList.remove(className);
      }
    }
  
    removeAllFormattingClassesFromLink(currentNode: any) {
      const classesToRemove = [];
      currentNode.classList.forEach((className) => {
        if (className.startsWith("dynamic-style-")) {
          classesToRemove.push(className);
        }
      });
      classesToRemove.forEach((className) => {
        currentNode.classList.remove(className);
      })
    }
  
    removeAllFormattingStyles(currentNode: any, stylePropNames: string[]) {
      stylePropNames.forEach(propName => {
        currentNode.style.removeProperty(propName);
      });
    }
  
    removeAllFormattingClasses(currentNode: any, classNames: string[]) {
      classNames.forEach(propName => {
        currentNode.classList.remove("")
      });
    }
  
    checkStylesForValue(currentNode: any, stylePropName: string, stylePropValue: string) {
      if (currentNode.style[stylePropName] == stylePropValue) {
        return true;
      } else {
        return false;
      }
    }

    restoreSelectionForCurrentFroalaId(froalaId: string, text) {
      if (froalaId !== this.froalaId) {
        return;
      }
      // this.editorInstance.snapshot.restore(this.currentSnap);
      //   this.editorInstance.selection.restore();
      //   this.editorInstance.html.insert(FroalaEditor.START_MARKER + text + FroalaEditor.END_MARKER);
      //   this.editorInstance.selection.save();
      if (this.editLinkNode) {
        this.editorInstance.popups.show('link.edit');
      } else {
        this.editorInstance.selection.restore();
      }
        
      //   this.currentSnap = this.editorInstance.snapshot.get();
      setTimeout(() => {
        // this.editorInstance.events.focus(true);
        
        setTimeout(() => {
          
        // this.editorInstance.snapshot.restore(this.currentSnap);
        // this.editorInstance.selection.restore();
        }, 1000);
      }, 1000);
      // this.editorInstance.events.focus(true);
    }

    insertNewAIText(text, froalaId: string) {
      if (froalaId !== this.froalaId) {
        return;
      }
      // if (this.newRangeSelected) {
      //   this.editorInstance.selection.restore();
      //   this.editorInstance.events.focus(true);
      //   this.editorInstance.html.insert(text);
      //   this.newRangeSelected = null;
      // }
      // if (this.currentSnap) {
      //   if (redirection !== 'vaw' && this.editLinkNode.getAttribute("data-custom-view-as-webpage")) {
      //     this.currentSnap.html = this.currentSnap.html.replace('data-href=\"<--SELF_LANDING_PAGE-->\"', '');
      //     this.currentSnap.html = this.currentSnap.html.replace('data-custom-view-as-webpage=\"true\"', '');
      //   }
      //   this.editorInstance.snapshot.restore(this.currentSnap);
      //   this.currentSnap = null;
      // } else {
      //   this.editorInstance.selection.restore();
      // }
  
      if (this.newRangeSelected) {

        this.editorInstance.snapshot.restore(this.currentSnap);
        this.editorInstance.selection.restore();
        this.editorInstance.html.insert(FroalaEditor.START_MARKER + text + FroalaEditor.END_MARKER);
        this.editorInstance.selection.restore();
        this.editorInstance.selection.save();
        
        // this.currentSnap = this.editorInstance.snapshot.get();
        // this.editorInstance.events.focus(true);
     
        // this.newRangeSelected = null;
      } else {
      //   if (this.editLinkNode && this.editImageNode) {
      //     this.editLinkNode.removeAttribute('href');
      //     this.editImageNode.trigger('click');
      //     this.editLinkNode.setAttribute('href', url);
      //     if (attrs && attrs["target"]) {
      //       this.editLinkNode.setAttribute("target", attrs["target"]);
      //     } else {
      //       this.editLinkNode.removeAttribute("target");
      //     }
      //     this.editLinkNode.removeAttribute("data-name");
      //     if (redirection === "lp")
      //       this.editLinkNode.setAttribute("data-name", attrs["data-name"]);
      //   } else if (this.editLinkNode && !this.editImageNode) {
      //     this.editorInstance.link.insert(
      //       url,
      //       '',
      //       {
      //         target: attrs && attrs["target"] ? "_blank" : "",
      //         rel: "nofollow",
  
      //         ...attrs,
      //       }
      //     );
      //   } else if (!this.editLinkNode && this.editImageNode) {
      //     this.editImageNode.trigger('click');
      //     this.editorInstance.link.insert(
      //       url,
      //       '',
      //       {
      //         target: attrs && attrs["target"] ? "_blank" : "",
      //         rel: "nofollow",
      //         style: "text-decoration: none",
      //         ...attrs,
      //       }
      //     );
      //   }
      //   if (this.editImageNode) {
      //     this.editorInstance.popups.show('image.edit');
      //     this.editImageNode = null;
      //   }
      //   else {
      //     this.editorInstance.popups.show('link.edit');
      //   }
      if (this.editLinkNode && this.editImageNode) {
        //     this.editLinkNode.removeAttribute('href');
        //     this.editImageNode.trigger('click');
        //     this.editLinkNode.setAttribute('href', url);
        //     if (attrs && attrs["target"]) {
        //       this.editLinkNode.setAttribute("target", attrs["target"]);
        //     } else {
        //       this.editLinkNode.removeAttribute("target");
        //     }
        //     this.editLinkNode.removeAttribute("data-name");
        //     if (redirection === "lp")
        //       this.editLinkNode.setAttribute("data-name", attrs["data-name"]);
          } else if (this.editLinkNode && !this.editImageNode) {
            this.editLinkNode.innerText = text;
            this.editorInstance.popups.show('link.edit');
          } else if (!this.editLinkNode && this.editImageNode) {
            // this.editImageNode[0].src = aiResponse.imageUrl;
            // this.editImageNode.trigger('click');
            // this.editorInstance.link.insert(
            //   url,
            //   '',
            //   {
            //     target: attrs && attrs["target"] ? "_blank" : "",
            //     rel: "nofollow",
            //     style: "text-decoration: none",
            //     ...attrs,
            //   }
            // );
          }
          if (this.editImageNode) {
            // this.editorInstance.popups.show('image.edit');
            // this.editImageNode = null;
          }
          else {
            // this.editorInstance.popups.show('link.edit');
          }
      }
  
    }
  
    insertNewCustomLink(url, redirection, attrs: { "data-name": string }, froalaId: string, aliasNameInfo) {
      if (froalaId !== this.froalaId) {
        return;
      }
      if (this.currentSnap) {
        if (redirection !== 'vaw' && this.editLinkNode.getAttribute("data-custom-view-as-webpage")) {
          this.currentSnap.html = this.currentSnap.html.replace('data-href=\"<--SELF_LANDING_PAGE-->\"', '');
          this.currentSnap.html = this.currentSnap.html.replace('data-custom-view-as-webpage=\"true\"', '');
        }
        this.editorInstance.snapshot.restore(this.currentSnap);
        this.currentSnap = null;
      } else {
        this.editorInstance.selection.restore();
      }
  
      if (this.newRangeSelected) {
        this.editorInstance.events.focus(true);
        this.editorInstance.link.insert(
          url,
          this.editorInstance.selection.text(),
          {
            target: attrs && attrs["target"] ? "_blank" : "",
            rel: "nofollow",
            style: "text-decoration: none",
            ...attrs,
           'data-alias': aliasNameInfo?.name,
           'data-id': aliasNameInfo?.id
          }
        );
        this.newRangeSelected = null;
      } else {
        if (this.editLinkNode && this.editImageNode) {
          this.editLinkNode.removeAttribute('href');
          this.editImageNode.trigger('click');
          this.editLinkNode.setAttribute('href', url);
          if (attrs && attrs["target"]) {
            this.editLinkNode.setAttribute("target", attrs["target"]);
          } else {
            this.editLinkNode.removeAttribute("target");
          }
          this.editLinkNode.removeAttribute("data-name");
          if (redirection === "lp")
            this.editLinkNode.setAttribute("data-name", attrs["data-name"]);
        } else if (this.editLinkNode && !this.editImageNode) {
          this.editorInstance.link.insert(
            url,
            '',
            {
              target: attrs && attrs["target"] ? "_blank" : "",
              rel: "nofollow",
  
              ...attrs,
              'data-alias': aliasNameInfo.name,
              'data-id': aliasNameInfo.id
            }
          );
        } else if (!this.editLinkNode && this.editImageNode) {
          this.editImageNode.trigger('click');
          this.editorInstance.link.insert(
            url,
            '',
            {
              target: attrs && attrs["target"] ? "_blank" : "",
              rel: "nofollow",
              style: "text-decoration: none",
              ...attrs,
              'data-alias': aliasNameInfo?.name,
              'data-id': aliasNameInfo?.id
            }
          );
        }
        if (this.editImageNode) {
          this.editorInstance.popups.show('image.edit');
          this.editImageNode = null;
        }
        else {
          this.editorInstance.popups.show('link.edit');
        }
      }
  
    }
  
    openLinkSidebar(isTextElement: boolean) {
      if (this.editLinkNode) {
        const hrefValue = (this.editLinkNode.getAttribute('href')?.trim().toLowerCase() ? this.editLinkNode.getAttribute('href')?.trim().toLowerCase() : "");
        if (hrefValue.indexOf("lp://") > -1 || hrefValue.indexOf("lp%3a//") > -1) {
          this.showLinkSidebar.emit({
            type: "landing-page",
            text: (this.editLinkNode.getAttribute('href') ? this.editLinkNode.getAttribute('href') : ""),
            name: this.editLinkNode.getAttribute("data-name"),
            showCustomViewAsWebpage: isTextElement,
            newWindow: this.editLinkNode.getAttribute('target') ? true : false,
            froalaId: this.froalaId,
            id: this.editLinkNode.getAttribute("data-id") ? this.editLinkNode.getAttribute("data-id") : Utility.generateId(),
            aliasName: this.editLinkNode.getAttribute("data-alias") ? this.editLinkNode.getAttribute("data-alias") : ""
          });
        } else {
          this.showLinkSidebar.emit({
            type: this.editLinkNode.getAttribute("data-custom-view-as-webpage") ? "vaw" : "link",
            text: (this.editLinkNode.getAttribute('href') ? this.editLinkNode.getAttribute('href') : ""),
            name: "",
            showCustomViewAsWebpage: isTextElement,
            newWindow: this.editLinkNode.getAttribute('target') ? true : false,
            froalaId: this.froalaId,
            id: this.editLinkNode.getAttribute("data-id") ? this.editLinkNode.getAttribute("data-id") : Utility.generateId(),
            aliasName: this.editLinkNode.getAttribute("data-alias") ? this.editLinkNode.getAttribute("data-alias") : ""
          });
        }
      }
    }
  
    initializeEvent(instance) {
      if (!instance) {
        return;
      }
      this.editorInstance = instance.getEditor();
      this.editorInstance['froalaId'] = this.froalaId;
      this.editorInstance.helpers.sanitizeURL = (url) => url;
      this.froalaInitialized.emit({});
      this.currentSnap = this.editorInstance.snapshot.get();
      this.editorInstance.snapshot.restore(this.currentSnap);
    }
  
    bindKeyDownEvent(instance) {
      const currentSelectedElem = this.editorInstance.selection.element();
      if (currentSelectedElem.nodeName.toLowerCase() === 'a' && currentSelectedElem.children.length === 0) {
        if (currentSelectedElem.innerHTML.trim() === '') {
          this.editorInstance.link.remove();
        } else if (currentSelectedElem.innerHTML.length === 1) {
          const textContent = currentSelectedElem.innerHTML.replace(/\u200B/g, '');
          if (textContent.length === 0) {
            this.editorInstance.link.remove();
          }
        }
      }
      this.froalaKeyDown.emit(this.editorInstance.html.get());
      return true;
    }

    bindKeyUpEvent(instance) {
      const currentSelectedElem = this.editorInstance.selection.element();
      if (currentSelectedElem.nodeName.toLowerCase() === 'a' && currentSelectedElem.children.length === 0) {
        if (currentSelectedElem.innerHTML.trim() === '') {
          this.editorInstance.link.remove();
        } else if (currentSelectedElem.innerHTML.length === 1) {
          const textContent = currentSelectedElem.innerHTML.replace(/\u200B/g, '');
          if (textContent.length === 0) {
            this.editorInstance.link.remove();
          }
        }
      }
      this.froalaKeyUp.emit(this.editorInstance.html.get());
      return true;
    }
  
    createPadding = (padding): string => {
      let top = 10,
      right = 25,
      bottom = 10,
      left = 25;
      if (padding) {
        top = padding.top;
        right = padding.right;
        bottom = padding.bottom;
        left = padding.left
      }
      return `${top}px ${right}px ${bottom}px ${left}px`;
    };
  
    createLineHeight = (lineHeight): string | number => {
      let value = 22,
      unit = 'px'
      if (lineHeight) {
        value = lineHeight.value;
        unit = lineHeight.unit;
      }
      return unit !== 'none' ? `${value}${unit}` : 'normal';
    };
  
    printPFValues() {
      let pfValuesString = '';
      const pfValuesArray = [];
      if (this.pfList && this.pfList.length > 0) {
        this.pfList.forEach((item) => {
          pfValuesArray.push({key: "${item.key}", value: "${item.value}"});
        });
      }
      pfValuesString = `[${pfValuesArray.join(',')}]`;
      return pfValuesString;
    }
  
    removeTributeContainers() {
      this.editorInstance.el.querySelectorAll('.tribute-container').forEach((child) => {
        child.parentElement.removeChild(child);
      })
    }
  
    initialize(initControls) {
      initControls.initialize();
    }
  
    removeDataAssetProperty() {
      let curentNode = this.editImageNode[0].parentElement;
      while (!curentNode.classList.contains('froala-editor')) {
        if (curentNode.nodeName.toLowerCase() === 'a') {
          curentNode.removeAttribute('data-asset');
          break;
        } else {
          curentNode = curentNode.parentElement;
        }
      }
    }
  
    setMainContainerWidth() {
      const parser = new DOMParser();
      const bodyHTML: Document = Object.assign(parser.parseFromString(this.htmlString, 'text/html'));
      const bodyInnerHTML: any = bodyHTML.body;
      let currentChild = null;
      let containerWidth = '1000px';
      for (let i = 0; i < bodyInnerHTML.children.length; i++) {
        currentChild = bodyInnerHTML.children[i];
        if (currentChild) {
          if (currentChild.nodeName.toLowerCase() === 'p' && currentChild.innerText.trim() === '') {
            continue;
          } else {
            containerWidth = this.getWidthOfNodeElement(currentChild);
            if (containerWidth !== '') {
              break;
            }
          }
        }
      }
    }
  
    refreshFroalaHTMLContents(html) {
      this.editorInstance.html.set(html || ' ');
    }
  
    insertTemplateInFroalaHTML(customTemplate, isInsertBefore) {
      const currentHTML = this.editorInstance.html.get();
      const parser = new DOMParser();
      const froalaHtml: Document = Object.assign(parser.parseFromString(currentHTML, 'text/html'));
      const bodyFromFroala: any = froalaHtml.body;
      if (isInsertBefore) {
        if (bodyFromFroala.children[0].nodeName.toLowerCase() === 'p' && bodyFromFroala.children[0]?.innerText.trim() === '') {
          bodyFromFroala.insertBefore(customTemplate, bodyFromFroala.children[1]);
        } else {
          bodyFromFroala.insertBefore(customTemplate, bodyFromFroala.firstChild);
        }
      } else {
        bodyFromFroala.appendChild(customTemplate);
      }
      froalaHtml.body['innerHTML'] = bodyFromFroala.innerHTML;
      this.editorInstance.html.set(froalaHtml.documentElement.innerHTML);
    }
  
    removeTemplateFromFroalaHTML(className: string) {
      const currentHTML = this.editorInstance.html.get();
      const parser = new DOMParser();
      const froalaHtml: Document = Object.assign(parser.parseFromString(currentHTML, 'text/html'));
      const element = froalaHtml.getElementsByClassName(className)[0];
      if (element) element.remove();
      this.editorInstance.html.set(froalaHtml.documentElement.innerHTML);
    }
  
    createHeaderFooterBlock(customHtml: string, parentClass: string): HTMLElement {
      const customBlock = document.createElement('table');
      customBlock.setAttribute('align', 'center');
      customBlock.setAttribute('border', '0');
      customBlock.setAttribute('cellpadding', '0');
      customBlock.setAttribute('cellspacing', '0');
      customBlock.setAttribute('class', parentClass);
      customBlock.setAttribute('style', 'width:100%;');
      customBlock['innerHTML'] = '<tbody><tr><td align="center" bgcolor="#ffffff" style="font-family: Arial; font-size: 12px; padding-bottom:10px;" valign="top">' + customHtml + '</td></tr></tbody>';
      return customBlock;
    }
  
    insertViewAsWebpageBlock(event) {
      if (event.checked) {
        const viewAsWebpageElem = this.createHeaderFooterBlock('<a data-href="<--SELF_LANDING_PAGE-->" href="SELF_LANDING_PAGE">' + this.translationObject.VIEW_AS_WEBPAGE + '</a>', FroalaEditorComponent.viewAsWebpageClassName);
        this.insertTemplateInFroalaHTML(viewAsWebpageElem, true);
      } else {
        this.removeTemplateFromFroalaHTML(FroalaEditorComponent.viewAsWebpageClassName)
      }
    }
  
    getWidthOfNodeElement(nodeElement) {
      let containerWidth = '';
      if (nodeElement.innerText.trim() !== '') {
        if (nodeElement.getAttribute('width') && nodeElement.getAttribute('width') !== '100%') {
          containerWidth = nodeElement.getAttribute('width').split('px')[0];
        } else if (nodeElement.style.width && nodeElement.style.width !== '100%') {
          containerWidth = nodeElement.style.width.split('px')[0];
        } else if (nodeElement.style.maxWidth && nodeElement.style.maxWidth !== '100%') {
          containerWidth = nodeElement.style.maxWidth.split('px')[0];
        } else if (containerWidth === '') {
          for (let j = 0; j < nodeElement.children.length; j++) {
            containerWidth = this.getWidthOfNodeElement(nodeElement.children[j]);
            if (containerWidth !== '') {
              break;
            }
          }
        }
      }
      return containerWidth;
    }
  
    transformFroalaHtmlString(): string {
      const parser = new DOMParser();
      const originalHTML: Document = Object.assign(parser.parseFromString(this.htmlString, 'text/html'));
      const froalaHtml: Document = Object.assign(parser.parseFromString('<div id="temp-hcl-snippet-container">'+this.editorInstance.html.get()+'</div>', 'text/html'));
      froalaHtml.body.querySelectorAll('.tribute-container').forEach((child) => {
        child.parentElement.removeChild(child);
      });
      const bodyFromFroala: any = froalaHtml.body;
      if (bodyFromFroala.children?.length > 0 && bodyFromFroala.childNodes[0]?.nodeName.toLowerCase() === '#text' && bodyFromFroala.childNodes[0]?.nodeValue.trim() === '') {
        bodyFromFroala.removeChild(bodyFromFroala.childNodes[0]);
      }
      const tributeStyleTag = froalaHtml.querySelector('[data-id="tributejs-style"]');
      if (tributeStyleTag) {
        tributeStyleTag.parentElement.removeChild(tributeStyleTag);
      }
      originalHTML.body['innerHTML'] = bodyFromFroala.querySelector('#temp-hcl-snippet-container').innerHTML;
      return bodyFromFroala.querySelector('#temp-hcl-snippet-container').innerHTML;
    }
  
    /**
     * Unsubscribe all subscriptions
     */
    ngOnDestroy(): void {
      this.subscriptionList.forEach((sub: SubscriptionLike) => {
        sub.unsubscribe();
      });
    }
  }