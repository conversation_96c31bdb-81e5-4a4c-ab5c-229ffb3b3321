import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {UntypedFormControl} from '@angular/forms';
import {InputConfig} from 'hcl-angular-widgets-lib';

@Component({
  selector: 'hcl-send-message-editor',
  templateUrl: './send-message-editor.component.html',
  styleUrls: ['./send-message-editor.component.scss']
})
export class SendMessageEditorComponent implements OnInit {

  /**
   * When user sends a message this event wuill be emitted
   */
  @Output() private sendMessage: EventEmitter<{message: string}> = new EventEmitter<{message: string}>();
  /**
   * Right now we are using a simple text-box as edittor to send message
   * this is the configuration for that input box
   */
  sendMessageBoxConfig: InputConfig = {
    icon: 'hcl-icon-paper-plane',
    type: 'text',
    name: 'message',
    maximumLength: 1024,
    placeholder: 'Type your messahe here',
    formControlName: new UntypedFormControl('')
  };

  constructor() { }

  ngOnInit(): void {
  }

  /**
   * this means user has clicked on the icon to send the message
   */
  _sendMessage(): void {
    // emitt a event to send the message
    this.sendMessage.emit({
      message : this.sendMessageBoxConfig.formControlName.value
    });
    // once we have sent the message we have to clear the text-box
    this.sendMessageBoxConfig.formControlName.setValue('');
  }
}
