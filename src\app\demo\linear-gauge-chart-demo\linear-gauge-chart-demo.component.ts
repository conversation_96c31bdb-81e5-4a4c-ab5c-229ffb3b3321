import { Component, OnInit } from '@angular/core';
import { LinearGaugeChartConfig } from 'projects/hcl-angular-charts-lib/src/lib/component/linear-gauge-chart/linear-gauge-chart';


@Component({
  selector: 'app-linear-gauge-chart-demo',
  templateUrl: './linear-gauge-chart-demo.component.html',
  styleUrls: ['./linear-gauge-chart-demo.component.scss']
})
export class LinearGaugeChartDemoComponent implements OnInit {

  constructor() { }

  linearGuageChartConfig: LinearGaugeChartConfig = {
    chartContainerId: `linearGuage_chart1`,
    height : 100,
    width: 400,
    ticks: 5,
    percentageColorStops: [{percentage: 0, label: "0%",  color: "#245aa5"}, 
                          {percentage: 20, label: "10%", color: "#2b67bf"},
                          {percentage: 40, label: "20%", color: "#377ddb"},
                          {percentage: 60, label: "30%", color: "#6da7e2"},
                          {percentage: 80, label: "40%", color: "#a87a97"},
                          {percentage: 100, label: "50%", color: "#eb4b4b"}],
    title: "Unresponsive rate"
  }

  linearGuageChartData = [0,50];



  ngOnInit() {
  }

  

}