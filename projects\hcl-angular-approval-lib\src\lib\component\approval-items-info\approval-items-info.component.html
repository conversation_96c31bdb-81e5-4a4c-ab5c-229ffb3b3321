<div class="w-100 h-100 approval-items-info-container">
    <ng-container *ngIf="approval.items && approval.items.length > 0">
        <div class="items-available mb-3">
            {{approval.items.length}} {{ 'APPROVALPICKER.TITLES.ATTACHMENT_AVAILABLE' | translate }}
        </div>
        <ul class="items-to-approve-list">
            <li class="d-flex" *ngFor="let item of approval.items">
                <div class="icon-container" [ngClass]="getItemImage(item)"></div>
                <div class="item-details-container">
                    <div class="file-name">{{item.origFileName}}
                        <span>(v{{item.itemHistoryList?.length+1}})</span>
                    </div>
                    <div class="item-detail">{{getUserAndDate(item)}}</div>
                    <div class="item-action">
                        <a class='list-action-link' href="javascript:void(0);">{{getCountForItem(item)}}</a>
                    </div>
                    <div class="item-notes">{{item.notes}}</div>
                </div>
            </li>
        </ul>
    </ng-container>
    <ng-container *ngIf="approval.items.length === 0">
        <div class="no-data-container">
            <div class="no-attachment-found">{{ 'APPROVALPICKER.TITLES.NO_ATTACHMENT_FOUND' | translate }}</div>
            <div class="no-item-msg">{{ 'APPROVALPICKER.TITLES.NO_ITEMS_TO_APPROVE' | translate }}</div>
        </div>
    </ng-container>
</div>