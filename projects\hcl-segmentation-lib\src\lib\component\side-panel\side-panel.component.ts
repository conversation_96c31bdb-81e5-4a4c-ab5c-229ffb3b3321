import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { SideBarComponent } from 'hcl-angular-widgets-lib';
import { SubscriptionLike } from 'rxjs';
import { SidePanelDetails } from './side-panel-details';
import { UtilsService } from '../../utils.service';

@Component({
  selector: 'hcl-side-panel',
  templateUrl: './side-panel.component.html',
  styleUrls: ['./side-panel.component.scss']
})
export class SidePanelComponent implements OnInit {

  private subscriptionList: Array<SubscriptionLike> =
    new Array<SubscriptionLike>();
  @ViewChild(SideBarComponent) sideBarComponentRef: SideBarComponent;
  currentPanelDetails: SidePanelDetails = {
    panel: -1,
  };
  showSideBar: boolean = false;
  @Output() refreshProfileGrid: EventEmitter<boolean> =
    new EventEmitter<boolean>();
  @Output() handleSegmentCreation: EventEmitter<any> = new EventEmitter<any>();

  constructor(private utils: UtilsService) { }

  ngOnInit(): void {
    this.subscriptionList.push(
      this.utils.sidePanelDetails$.subscribe((detail: SidePanelDetails) => {
        this.currentPanelDetails = detail;
        this.showSideBar = true;
        this.sideBarComponentRef.openSideBar();
      })
    );
  }

  /**
   * This method will close the side panel
   */
  closeSidebar(event?: any) {
   
    this.currentPanelDetails = { ...{ panel: -1 } };
    this.showSideBar = false;
    this.sideBarComponentRef.close('close');
  }

  /**
   * Unsubscribe all subscriptions
   */
  ngOnDestroy(): void {
    this.subscriptionList.forEach((sub: SubscriptionLike) => {
      sub.unsubscribe();
    });
  }
}
