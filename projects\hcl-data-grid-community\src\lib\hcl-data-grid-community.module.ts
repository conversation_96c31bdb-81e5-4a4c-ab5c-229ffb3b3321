import { NgModule } from "@angular/core";
import { DataGridV2Component } from "./data-grid-v2.component";
import { CommonModule } from "@angular/common";
import { AgGridModule } from "ag-grid-angular";
import { CustomTooltipComponent } from "./ag-grid/custom-tooltip/custom-tooltip.component";
import { CustomCellComponent } from "./ag-grid/custom-cell/custom-cell.component";
import { CustomCellEditorComponent } from "./ag-grid/custom-cell-editor/custom-cell-editor.component";
import { CustomHeaderComponent } from "./ag-grid/custom-header/custom-header.component";
import { CustomPopoverComponent } from "./ag-grid/custom-popover/custom-popover.component";
import { FullWidthCellRenderer } from "./ag-grid/full-width-cell-renderer/full-width-cell-renderer.component";
import { HclAngularWidgetsLibModule } from "hcl-angular-widgets-lib";
import { ColumnSelectionComponent } from "./column-selection/column-selection.component";

@NgModule({
  declarations: [
    DataGridV2Component,
    CustomTooltipComponent,
    CustomCellComponent,
    CustomCellEditorComponent,
    CustomHeaderComponent,
    CustomPopoverComponent,
    FullWidthCellRenderer,
    ColumnSelectionComponent
  ],
  imports: [
    CommonModule,
    HclAngularWidgetsLibModule,
    AgGridModule.withComponents([CustomTooltipComponent]),
  ],
  exports: [DataGridV2Component, ColumnSelectionComponent],
})
export class HclDataGridModule { }
