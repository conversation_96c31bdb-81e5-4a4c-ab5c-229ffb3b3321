// import { IpEmailBuilderService } from '../../../ip-email-builder.service';
import { IpEmailBuilderService } from '../../../ip-email-builder.service';
import { IHtmlBlockOptions, RenderingClass, HtmlBlock } from '../interfaces';
import {
  createBorder,
  createLineHeight,
  createPadding,
  ignoreHTMLMinParse,
  uniqueId
} from '../utils';

export class Html implements HtmlBlock, RenderingClass {
  constructor(public options: IHtmlBlockOptions, private _ngb: IpEmailBuilderService) { }

  // render() {
  //     return `<mj-raw>${this.options.innerHtml} </mj-raw>`;
  // }

  render() {
    const {
      color,
      font,
      lineHeight,
      padding,
      rules,
      id
    } = this.options;
    const textId = uniqueId();
    let ruleMapId = "";
    if (rules && rules.length > 0) {
      this._ngb.setRuleMapper(textId, rules);
      ruleMapId = "ruleMap_" + textId;
    }
    // const buttonId = uniqueId();
    const htmlTemplate = `
    <mj-raw>
  <tr>
    <td class="hide-on-${this.options.hideOn}">
      <div class="ip-html-block"> 
        <!--[if mso]>
         <table role="presentation" border="0" cellpadding="0" cellspacing="0">

        <tr>
        <td style="padding: ${createPadding(padding)}; font-weight: ${font.weight};" >
    <![endif]-->
            <div class="droppable html-droppable ${ruleMapId} ${id ? '_ASSET_' + id : ''}">
              ${this.options.innerHtml}
            </div>
             <!--[if mso]>
        </td></tr></table>
    <![endif]-->
      </div>          
    </td>
  </tr>      
      </mj-raw> `;
  
  if (this.options.hideOn === 'desktop') {
    return `
      <mj-raw>
      <!--[if !mso]><!-- --></mj-raw>
      ${htmlTemplate}
      <mj-raw><!--<![endif]--></mj-raw>
    `;
  } else {
    return htmlTemplate;
  }
  }
}

