import { D3LinearAxisRenderer } from './D3LinearAxisRenderer';
import { ChartConfig } from './../../config/chart-config';
import * as d3 from 'd3';
import {D3GanttChartRenderer} from './D3GanttChartRenderer';
import {BaseChartRenderer} from '../BaseChartRenderer';
import {D3DraggableNode} from './interface/D3DraggableNode';
import {D3ReferenceLineRenderer} from './D3ReferenceLineRenderer';
import {D3CategoryAxisRenderer} from './D3CategoryAxisRenderer';
import {D3ChartRenderer} from './D3ChartRenderer';
export class D3DragRenderer {
  /**
   * the element that is being dragged
   */
  private draggedElement: any;
  /**
   * The start x-coordinate where the drag was started
   */
  private dragStartXCoordinate: number;
  /**
   * The start y-coordinate where the drag was started
   */
  private dragStartYCoordinate: number;
  /**
   * the final transformed X & y coordinates
   */
  private transformedCoOrdinated: {x: number, y: number} = {x: 0, y: 0};
  /**
   * The reference line that will tell user the start location of the element that is beign dragged
   * type {null}
   */
  private referenceLine: D3ReferenceLineRenderer = null;
  /**
   * The default constructor
   * param {D3GanttChartRenderer} ganttChartRenderer
   */
  constructor(private draggableNode: D3DraggableNode,
              private chartRenderer: D3ChartRenderer) { }

  /**
   * Event handler for Drag to be called on task which is to be dragged
   */
  public drag() {
    return d3.drag()
      .on('start', (d, i, n) => this.dragStarted(d, i, n))
      .on('drag', (d, i, n) => this.dragged(d, i, n))
      .on('end', (d, i, n) => this.dragEnded(d, i, n));
  }

  /**
   * Event handler for Drag and move task along x axis
   * Executed at the starting of drag event
   */
  protected dragStarted(dataObject, index, allSvgDataNodes) {
    // check if the node is draggable
    if (this.draggableNode.canBeDragged(dataObject)) {
      const currPosition: {x: number, y: number} = this.draggableNode.getCurrentPosition();
      // we have to add a reference line
      this.referenceLine = this.chartRenderer.createReferenceLineRenderer(true, false, currPosition);
      // we need to get the instance of the object that is being dragged, the parent will be the group to which we will add a transform
      // for a smooth translation
      this.draggedElement = d3.select(allSvgDataNodes[index].parentNode);
      // we will mark the element as active
      d3.select(allSvgDataNodes[index]).classed('active', true);
      // the starting X-coordinate
      // TODO : d3.event.sourceEvent.x may not work in Safari, need to verify
      this.dragStartXCoordinate = d3.event.sourceEvent.x;
      // the starting Y-coordinate
      this.dragStartYCoordinate = d3.event.sourceEvent.y;
    }
  }

  /**
   * Event handler for Drag and move task along x axis
   * Moves the task horizontally
   */
  protected dragged(d, i, n) {
    this.transformedCoOrdinated.x = (d3.event.sourceEvent.x - this.dragStartXCoordinate);
    // let now add transform
    this.draggedElement.attr('transform', 'translate(' + this.transformedCoOrdinated.x + ',0)');
    // position the ref line
    this.referenceLine.position(this.transformedCoOrdinated.x, this.dragStartYCoordinate);
    const currPosition: {x: number, y: number} = this.draggableNode.getCurrentPosition();
    this.referenceLine.position(currPosition.x + this.transformedCoOrdinated.x, currPosition.y);
    // IF X IS -VE WE ARE MOVING TOWARDS LEFT, ELSE RIGHT
    //if (currPosition.x + this.transformedCoOrdinated.x < 0) {
      // we may require to scroll the parent container
    // document.getElementById('gantt-chart-container').scrollLeft = currPosition.x;
    console.info(document.getElementById('gantt-chart-container').scrollLeft + "---" + (currPosition.x + this.transformedCoOrdinated.x));
    if(document.getElementById('gantt-chart-container').scrollLeft > (currPosition.x + this.transformedCoOrdinated.x)) {
      document.getElementById('gantt-chart-container').scrollLeft = (currPosition.x + this.transformedCoOrdinated.x)
    } else {
      // debugger
    }
  }

  /**
   * Event handler for Drag and move task along the x axis
   * Executed at the end of drag event
   */
  protected dragEnded(dataObject, index, allSvgDataNodes) {
    if (this.draggableNode.canBeDragged(dataObject)) {
      // we also have to check if we want to snap the value
      d3.select(allSvgDataNodes[index]).classed('active', false);
      // check if the new position is valid or not
      if (this.draggableNode.validateNewPosition(dataObject, {
        x: this.transformedCoOrdinated.x,
        y: this.transformedCoOrdinated.y
      })) {
        // now we have moved the g object, time to update the coordinates of all the elements inside the g tag
        // let the tell the chart renderer to update the coordinates of the data node accordingly
        this.draggableNode.dataNodePositionUpdate(dataObject, {
          x: this.transformedCoOrdinated.x,
          y: this.transformedCoOrdinated.y
        });
        this.draggedElement.attr('transform', 'translate(0,0)');
      } else {
        // we need to reset the element to the original position as te new location seems to be invalid
        // TODO: add a bit of animation so it looks like the bar slided to the original position
        this.draggedElement.attr('transform', 'translate(0,0)');
      }
      // clear the ref line as well
      this.referenceLine.remove();
      // reset the values
      this.draggedElement = null;
      this.transformedCoOrdinated = {x: 0, y: 0};
    }
  }
}
