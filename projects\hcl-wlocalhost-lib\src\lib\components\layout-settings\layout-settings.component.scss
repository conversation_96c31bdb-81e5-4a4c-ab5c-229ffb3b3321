.settings-container {
  .mat-accordion {
    .mat-expansion-panel {
      background: #f5f5f5;
      margin: 0;

      &:first-of-type,
      &:last-of-type {
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
      }

      .mat-expansion-panel-header:not(.mat-expanded) {
        background: #f5f5f5;
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
        border-bottom: 1px solid #e0e0e0;
      }
      .mat-expansion-panel-header {
        padding: 0 20px;
        .mat-content {
          color: #6d7692;
          font-family: "Montserrat";
          font-size: 14px;
          font-weight: 500;
          letter-spacing: 0;
          line-height: 18px;
        }
        .mat-expansion-indicator {
          &:after {
            color: #959595;
          }
        }
      }

      .mat-expansion-panel-content {
        .mat-expansion-panel-body {
          padding: 0 20px 5px;
          border-bottom: 1px solid #e0e0e0;
          .mat-form-field-wrapper {
            .mat-form-field-outline {
              border: 1px solid #bcbbbb;
              border-radius: 6px;
              background-color: #ececec;
            }
          }
          .mat-input-element,
          .mat-select-value {
            color: #444444;
            font-family: Roboto;
            font-size: 14px;
            letter-spacing: 0;
            line-height: 16px;
          }

          .sec-subhead {
            color: #444444;
            font-family: Roboto;
            font-size: 14px;
            font-weight: bold;
            letter-spacing: 0;
            line-height: 16px;
          }
        }
      }

      .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
        background-color: rgba(245, 130, 30, 0.54);
      }

      .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
        background-color: #f5821e;
      }

      .mat-form-field-label {
        color: #6d7692;
      }

      .mat-radio-button.mat-accent .mat-radio-inner-circle,
      .mat-radio-button.mat-accent
        .mat-radio-ripple
        .mat-ripple-element:not(.mat-radio-persistent-ripple),
      .mat-radio-button.mat-accent.mat-radio-checked
        .mat-radio-persistent-ripple,
      .mat-radio-button.mat-accent:active .mat-radio-persistent-ripple {
        background-color: #f5821e;
      }

      .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
        border-color: #f5821e;
      }
    }
  }
}
