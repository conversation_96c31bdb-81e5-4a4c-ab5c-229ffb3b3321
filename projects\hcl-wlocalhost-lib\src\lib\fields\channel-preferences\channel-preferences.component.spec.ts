import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ChannelPreferencesComponent } from './channel-preferences.component';

describe('ChannelPreferencesComponent', () => {
  let component: ChannelPreferencesComponent;
  let fixture: ComponentFixture<ChannelPreferencesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
    declarations: [ChannelPreferencesComponent],
    teardown: { destroyAfterEach: false }
})
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ChannelPreferencesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
