<section *ngIf="readyToRender" class="default-values">
    <div class="variation-header">
        <div class="variation-name" *ngIf="variantName">
            {{ variantName }}
        </div>
    </div>
    <div class="attributes-container">
        <div class="metadata">
            <!-- <div class="d-flex justify-content-between position-relative"> -->
            <div class="form-field">
                <span class="ro-label" hclTooltip="{{'CREATE_OFFER.LABELS.OFFER_NAME' | translate}}"
                    [attr.data-position]="'bottom-top-start'">{{
                    'CREATE_OFFER.LABELS.OFFER_NAME' | translate}}</span>
                <span class="ro-value" hclTooltip="{{offerMetadata.displayName}}"
                    [attr.data-position]="'bottom-top-start'">{{offerMetadata.displayName}}</span>
            </div>
            <div class="form-field">
                <span class="ro-label" hclTooltip="{{'CREATE_OFFER.LABELS.SECURITY_POLICY' | translate}}"
                    [attr.data-position]="'bottom-top-start'">{{'CREATE_OFFER.LABELS.SECURITY_POLICY' |
                    translate}}</span>
                <span class="ro-value" hclTooltip="{{getSecurityPolicy()}}"
                    [attr.data-position]="'bottom-top-start'">{{getSecurityPolicy()}}</span>
            </div>
            <div class="form-field">
                <span class="ro-label" hclTooltip="{{'CREATE_OFFER.LABELS.OFFER_CODE' | translate}}"
                    [attr.data-position]="'bottom-top-start'">{{'CREATE_OFFER.LABELS.OFFER_CODE' | translate}}</span>
                <span class="ro-value" hclTooltip="{{getOfferCodes()}}"
                    [attr.data-position]="'bottom-top-start'">{{getOfferCodes()}}</span>
            </div>
            <!-- </div> -->
            <div class="description">
                <span [ngClass]="{'multi-line' : isDescMultiline(descValueEl)}" class="ro-label"
                    hclTooltip="{{'CREATE_OFFER.LABELS.DESCRIPTION' | translate}}"
                    [attr.data-position]="'bottom-top-start'">{{'CREATE_OFFER.LABELS.DESCRIPTION' | translate}}</span>
                <span #descValueEl [ngClass]="{'desc-ellipsis': isEllipsisApplicable(descValueEl)}" class="ro-value"
                    hclTooltip="{{getDescription()}}"
                    [attr.data-position]="'bottom-top-start'">{{getDescription()}}</span>
            </div>
        </div>
        <hcl-accordion [config]="accordionCustomConfig">
            <ng-template hclTemplate hclTemplateName="headerTemplate_1">
                <span *ngIf="staticAttributes.length" class="title">{{'CREATE_OFFER.TITLES.STATIC_ATTRIBUTES' |
                    translate}}</span>
            </ng-template>

            <ng-template hclTemplate hclTemplateName="contentTemplate_1">
                <div class="default-attributes" *ngIf="staticAttributes.length">
                    <ng-container *ngFor="let attribute of staticAttributes; let i = index">
                        <ng-container *ngIf="!isComposedField(attribute)">
                            <ng-template [ngTemplateOutlet]="nonComposedField"
                                [ngTemplateOutletContext]="{attribute: attribute, type: 'staticAttributes'}">
                            </ng-template>
                        </ng-container>

                        <ng-container *ngIf="isComposedField(attribute)">
                            <ng-container *ngIf="!isEffectiveExpirationDates(attribute.type.id)">
                                <ng-template [ngTemplateOutlet]="nonComposedField"
                                    [ngTemplateOutletContext]="{attribute: attribute , type: 'staticAttributes'}">
                                </ng-template>
                            </ng-container>
                            <ng-container *ngIf="isEffectiveExpirationDates(attribute.type.id) && !selectOffersService.hiddenAttributesSet.has(+attribute.id) &&
                                !alwaysHiddenAttributesSet.has(+attribute.id)">
                                <ng-template [ngTemplateOutlet]="nonComposedField"
                                    [ngTemplateOutletContext]="{attribute: attribute.type.attributes[0] , type: 'staticAttributes'}">
                                </ng-template>
                                <ng-template [ngTemplateOutlet]="nonComposedField"
                                    [ngTemplateOutletContext]="{attribute: attribute.type.attributes[1] , type: 'staticAttributes'}">
                                </ng-template>
                            </ng-container>
                        </ng-container>
                    </ng-container>
                </div>
            </ng-template>

            <ng-template hclTemplate hclTemplateName="headerTemplate_2">
                <span *ngIf="parameterisedAttributes.length"
                    class="title">{{'CREATE_OFFER.TITLES.PARAMETRIZED_ATTRIBUTES' |
                    translate}}</span>
            </ng-template>

            <ng-template hclTemplate hclTemplateName="contentTemplate_2">
                <div class="default-attributes" *ngIf="parameterisedAttributes.length">
                    <ng-container *ngFor="let attribute of parameterisedAttributes; let i = index">
                        <ng-container *ngIf="!isComposedField(attribute)">
                            <ng-template [ngTemplateOutlet]="nonComposedField"
                                [ngTemplateOutletContext]="{attribute: attribute, type: 'parameterisedAttributes'}">
                            </ng-template>
                        </ng-container>

                        <ng-container *ngIf="isComposedField(attribute)">
                            <ng-container *ngIf="!isEffectiveExpirationDates(attribute.type.id)">
                                <ng-template [ngTemplateOutlet]="nonComposedField"
                                    [ngTemplateOutletContext]="{attribute: attribute , type: 'parameterisedAttributes'}">
                                </ng-template>
                            </ng-container>

                            <ng-container *ngIf="isEffectiveExpirationDates(attribute.type.id) && !selectOffersService.hiddenAttributesSet.has(+attribute.id) &&
                                !alwaysHiddenAttributesSet.has(+attribute.id)">
                                <div class="attribute">
                                    <div class="form-field">
                                        <span class="ro-label" hclTooltip="{{attribute.type.attributes[0].displayName}}"
                                            [attr.data-position]="'bottom-top-start'">{{attribute.type.attributes[0].displayName}}</span>
                                        <span class="ro-value" hclTooltip="{{getEffectiveDateValue(attribute)}}"
                                            [attr.data-position]="'bottom-top-start'">{{getEffectiveDateValue(attribute)}}</span>
                                    </div>
                                </div>
                                <div class="attribute">
                                    <div class="form-field">
                                        <span class="ro-label" hclTooltip="{{attribute.type.attributes[1].displayName}}"
                                            [attr.data-position]="'bottom-top-start'">{{attribute.type.attributes[1].displayName}}</span>
                                        <span class="ro-value" hclTooltip="{{getExpirationDateValue(attribute)}}"
                                            [attr.data-position]="'bottom-top-start'">{{getExpirationDateValue(attribute)}}</span>
                                    </div>
                                </div>
                            </ng-container>

                        </ng-container>
                    </ng-container>
                </div>
            </ng-template>

            <ng-template hclTemplate hclTemplateName="headerTemplate_5">
                <span *ngIf="hiddenAttributes.length" class="title">{{'CREATE_OFFER_TEMPLATE.TITLES.INTERNAL_ATTRIBUTES'
                    |
                    translate}}</span>
            </ng-template>

            <ng-template hclTemplate hclTemplateName="contentTemplate_5">
                <div class="default-attributes" *ngIf="hiddenAttributes.length">
                    <ng-container *ngFor="let attribute of hiddenAttributes; let i = index">
                        <ng-container *ngIf="!isComposedField(attribute)">
                            <ng-template [ngTemplateOutlet]="nonComposedField"
                                [ngTemplateOutletContext]="{attribute: attribute, type: 'hiddenAttributes'}">
                            </ng-template>
                        </ng-container>

                        <ng-container *ngIf="isComposedField(attribute)">
                            <ng-container *ngIf="!isEffectiveExpirationDates(attribute.type.id)">
                                <ng-template [ngTemplateOutlet]="nonComposedField"
                                    [ngTemplateOutletContext]="{attribute: attribute , type: 'hiddenAttributes'}">
                                </ng-template>
                            </ng-container>

                            <ng-container *ngIf="isEffectiveExpirationDates(attribute.type.id) && 
                                !selectOffersService.hiddenAttributesSet.has(+attribute.id) &&
                                !alwaysHiddenAttributesSet.has(+attribute.id)">
                                <div class="attribute">
                                    <div class="form-field">
                                        <span class="ro-label" hclTooltip="{{attribute.type.attributes[0].displayName}}"
                                            [attr.data-position]="'bottom-top-start'">{{attribute.type.attributes[0].displayName}}</span>
                                        <span class="ro-value" hclTooltip="{{getEffectiveDateValue(attribute)}}"
                                            [attr.data-position]="'bottom-top-start'">{{getEffectiveDateValue(attribute)}}</span>
                                    </div>
                                </div>
                                <div class="attribute">
                                    <div class="form-field">
                                        <span class="ro-label" hclTooltip="{{attribute.type.attributes[1].displayName}}"
                                            [attr.data-position]="'bottom-top-start'">{{attribute.type.attributes[1].displayName}}</span>
                                        <span class="ro-value" hclTooltip="{{getExpirationDateValue(attribute)}}"
                                            [attr.data-position]="'bottom-top-start'">{{getExpirationDateValue(attribute)}}</span>
                                    </div>
                                </div>
                            </ng-container>

                        </ng-container>
                    </ng-container>
                </div>
            </ng-template>

            <ng-template *ngIf="selectOffersService.offerTemplate.dependentAttributes &&
            selectOffersService.offerTemplate.dependentAttributes.length" hclTemplate
                hclTemplateName="headerTemplate_4">
                <span class="title">{{'CREATE_OFFER_TEMPLATE.TITLES.DEPENDENT_ATTRIBUTES' | translate}}</span>
            </ng-template>
            <ng-template *ngIf="selectOffersService.offerTemplate.dependentAttributes &&
            selectOffersService.offerTemplate.dependentAttributes.length" hclTemplate
                hclTemplateName="contentTemplate_4">
                <div class="dependent-attribute-container">
                    <div class="content-item" *ngFor="let dependnecy of ssadbAttributeDependencies">
                        <span class="mr-2" hclTooltip="{{ dependnecy[0].displayName }}">{{
                            dependnecy[0].displayName}}</span>
                        <span class="depends-on">{{ 'CREATE_OFFER_TEMPLATE.TITLES.DEPENDS_ON'| translate }}</span>
                        <span class="ml-2" hclTooltip="{{ dependnecy[1].displayName }}">{{
                            dependnecy[1].displayName }}</span>
                    </div>
                </div>
            </ng-template>

            <ng-template hclTemplate hclTemplateName="headerTemplate_3">
                <span class="title">{{'CREATE_OFFER.TITLES.RELEVANT_PRODUCTS' | translate}}</span>
            </ng-template>

            <ng-template hclTemplate hclTemplateName="descriptionTemplate_3"> </ng-template>

            <ng-template hclTemplate hclTemplateName="contentTemplate_3">
                <ng-container *ngIf="!relevantproductRuleList.length">
                    <div class="relevant-products-message">{{'MESSAGES.NO_RELEVANT_PRODUCTS_ADDED' | translate}}</div>
                </ng-container>
                <ng-container *ngIf="relevantproductRuleList.length">
                    <div class="relevant-products-list">
                        <hcl-checkbox [config]="relevantProductsCheckboxConfig">
                        </hcl-checkbox>
                    </div>
                </ng-container>
            </ng-template>
        </hcl-accordion>
    </div>
</section>

<ng-template #nonComposedField let-attribute="attribute" let-type="type">
    <div class="attribute" *ngIf="!selectOffersService.hiddenAttributesSet.has(+attribute.id) &&
    !alwaysHiddenAttributesSet.has(+attribute.id)">
        <ng-container
            *ngIf="isNumberField(attribute.type.id) || (isStringField(attribute.type.id) && !isCreativeUrlAttribute(attribute.id)) || isSelectBoxField(attribute.type.id)">
            <div class="form-field">
                <span class="ro-label" hclTooltip="{{attribute.displayName}}"
                    [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
                <span class="ro-value" hclTooltip="{{attribute.value}}" [attr.data-position]="'bottom-top-start'">
                    <span class="pr-1" *ngIf="isCurrencyField(attribute.type.id)">{{getCurrencySymbol()}}</span>
                    {{getAttributeValue(attribute)}}</span>
            </div>
        </ng-container>
        <ng-container *ngIf="isDateField(attribute.type.id)">
            <div class="form-field">
                <span class="ro-label" hclTooltip="{{attribute.displayName}}"
                    [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
                <span class="ro-value" hclTooltip="{{attribute.value | date: getDateFormat()}}"
                    [attr.data-position]="'bottom-top-start'">{{attribute.value | date: getDateFormat()}}</span>
            </div>
        </ng-container>
        <ng-container *ngIf="isSSDBField(attribute.type.id)">
            <div class="form-field">
                <span class="ro-label" hclTooltip="{{attribute.displayName}}"
                    [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
                <span class="ro-value" hclTooltip="{{attribute.value?.displayColumnValue}}"
                    [attr.data-position]="'bottom-top-start'">{{attribute.value?.displayColumnValue}}</span>
            </div>
        </ng-container>
        <ng-container *ngIf="isUrlField(attribute.type.id) || isCreativeUrlAttribute(attribute.id)">
            <div class="form-field">
                <span class="ro-label" hclTooltip="{{attribute.displayName}}"
                    [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
                <div *ngIf="attribute.value?.url" class="ro-value d-flex align-items-center">
                    <span *ngIf="!isValidUrl(attribute.value.url)" class="mr-3"
                        [attr.data-position]="'bottom-top-start'"
                        hclTooltip="{{attribute.value.url}}">{{attribute.value.url}}</span>
                    <a *ngIf="isValidUrl(attribute.value.url)" download href="{{attribute.value.url}}" target="_blank"
                        class="ellipsis mr-5">
                        <span [attr.data-position]="'bottom-top-start'"
                            hclTooltip="{{attribute.value.url}}">{{attribute.value.url}}</span>
                    </a>
                    <span *ngIf="isAssetpickerInstalled()" class="ap-popover-container">
                        <div class="inline-loader">
                            <hcl-progress-spinner [config]="dynamicConfigs[attribute.id + 'loader']">
                            </hcl-progress-spinner>
                        </div>
                        <span (click)="$event.stopPropagation();" class="ap-popover-trigger"
                            [popoverTriggerFor]="apPopover" popoverTriggerOn="click" [popoverCloseOnClick]="false"
                            [originPositionX]="'end'" [originPositionY]="'top'" (closed)="apItemDetailsClosed()"></span>
                        <span class="hcl-icon-view ap-popover-icon" (click)="openApItemDetail($event, attribute)">
                        </span>
                    </span>
                </div>
            </div>
        </ng-container>
        <ng-container *ngIf="isBooleanField(attribute.type.id)">
            <div class="form-field">
                <span class="ro-label" hclTooltip="{{attribute.displayName}}"
                    [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
                <span class="ro-value" hclTooltip="{{getBooleanAttrValue(attribute) }}"
                    [attr.data-position]="'bottom-top-start'">{{getBooleanAttrValue(attribute)}}</span>
            </div>
        </ng-container>
        <ng-container *ngIf="isRichTextField(attribute.type.id)">
            <div #richTextEl class="form-field">
                <span class="ro-label" hclTooltip="{{attribute.displayName}}"
                    [attr.data-position]="'bottom-top-start'">{{attribute.displayName}}</span>
                <span class="ro-value" hclTooltip="{{'CREATE_OFFER.LABELS.RICHTEXT' | translate}}"
                    [attr.data-position]="'bottom-top-start'">{{'CREATE_OFFER.LABELS.RICHTEXT' | translate}}

                    <span class="hcl-icon-view" [popoverTriggerFor]="rtPopover" popoverTriggerOn="hover"
                        [popoverCloseOnClick]="true" [originPositionX]="'end'" [originPositionY]="'top'">
                    </span>
                </span>
            </div>
            <hcl-popover #rtPopover="hclPopover" [popoverPositionX]="apOverlayInputs.popoverPositionX"
                [popoverPositionY]="apOverlayInputs.popoverPositionY"
                [originPositionX]="apOverlayInputs.originPositionX" [originPositionY]="apOverlayInputs.originPositionY"
                [popoverEnterDelay]="apOverlayInputs.popoverEnterDelay"
                [popoverLeaveDelay]="apOverlayInputs.popoverLeaveDelay" [popoverOffsetX]="0"
                [popoverOffsetY]="apOverlayInputs.popoverOffsetY">

                <div class="rt-preview-container" (click)="openRichTextView(attribute)">
                    <div class="view">{{'CREATE_OFFER.LABELS.VIEW' | translate}}
                    </div>
                    <div class="disabled-editor" [froalaEditor]="getRichTextOptions()"
                        [(froalaModel)]="attribute.value">
                    </div>
                </div>
            </hcl-popover>
        </ng-container>
    </div>
</ng-template>

<hcl-popover #apPopover="hclPopover" [popoverPositionX]="apOverlayInputs.popoverPositionX"
    [popoverPositionY]="apOverlayInputs.popoverPositionY" [originPositionX]="apOverlayInputs.originPositionX"
    [originPositionY]="apOverlayInputs.originPositionY" [popoverEnterDelay]="apOverlayInputs.popoverEnterDelay"
    [popoverLeaveDelay]="apOverlayInputs.popoverLeaveDelay" [popoverOffsetX]="apOverlayInputs.popoverOffsetX"
    [popoverOffsetY]="apOverlayInputs.popoverOffsetY">
    <div class="ap-item-details-container" [ngClass]="{'no-preview-available': apOverlayInputs.noPreview}">
        <div *ngIf="apItemDetailsState === 'loaded'" class="close-pop-over" (click)="closeApItemDetails()">
            <span class="hcl-icon-close-x"></span>
        </div>
        <div class="ap-item-scroll-container" *ngIf="apItemDetailsState === 'loaded'">
            <div class="ap-item-details">

                <div class="thumbnail"
                    *ngIf="isImage(apItemDetails.presentationDetails.multimedia.mimeType); else otherType">
                    <ng-container *ngIf="resourceNeedsToBeLoaded()">
                        <img [src]="apItemDetails.contentUrl" default="{{getThumbnailBackupSource()}}" title=""
                            titleText="{{'Thumbnail not accessible'}}" />
                    </ng-container>
                    <ng-container *ngIf="!resourceNeedsToBeLoaded()">
                        <img *ngIf="apItemDetails.presentationDetails.multimedia.thumbnailUrl"
                            src="{{getThumbnailSource()}}" default="{{getThumbnailBackupSource()}}" title=""
                            titleText="{{'thumbnail not accessible'}}" />
                        <img *ngIf="!apItemDetails.presentationDetails.multimedia.thumbnailUrl"
                            src="{{getImageSource()}}" default="{{getImageBackupSource()}}" title=""
                            titleText="{{'Image not accessible'}}" />
                    </ng-container>
                </div>

                <ng-template #otherType>
                    <div class="thumbnail">
                        <img *ngIf="apItemDetails.presentationDetails.multimedia.thumbnailUrl"
                            src="{{getThumbnailSource()}}" default="{{getThumbnailBackupSource()}}" title=""
                            titleText="{{'Thumbnail not accessible'}}" />

                        <i *ngIf="!apItemDetails.presentationDetails.multimedia.thumbnailUrl"
                            class="{{getIcon(apItemDetails.presentationDetails.multimedia.mimeType)}}"></i>
                    </div>
                </ng-template>

                <div class="metadata">
                    <p class="fileName">
                        <a *ngIf="!resourceNeedsToBeLoaded()" download href="{{apItemDetails.url}}" target="_blank">
                            <span class="hcl-icon-download"></span> </a>
                        <!-- <a *ngIf="resourceNeedsToBeLoaded()" download [href]="apItemDetails.contentUrl"
                            target="_blank"><span class="hcl-icon-download"></span> </a> -->
                        <span *ngIf="getContentPreviewTitle()" class=" ellipsis"
                            hclTooltip="{{getContentPreviewTitle()}}">
                            {{getContentPreviewTitle()}} </span>
                    </p>
                    <div class="size" *ngIf="apItemDetails.presentationDetails.textual.subheadings.length">
                        <p *ngFor="let text of apItemDetails.presentationDetails.textual.subheadings">
                            <span hclTooltip="{{text}}">{{text}}</span>
                        </p>
                    </div>
                    <div class="mime-type" *ngIf="apItemDetails.presentationDetails.textual.tags[0]">
                        <span hclTooltip="{{apItemDetails.presentationDetails.textual.tags[0].toUpperCase()}}">
                            {{apItemDetails.presentationDetails.textual.tags[0].toUpperCase()}}
                        </span>
                    </div>
                </div>
                <div class="description" *ngIf="apItemDetails?.presentationDetails.textual.summary">
                    {{apItemDetails?.presentationDetails.textual.summary}}
                </div>
            </div>
        </div>
        <div class="error-container" *ngIf="apItemDetailsState === 'no-preview'">
            <span class="ap-no-preview"
                [ngClass]="{'left-arrow':apOverlayInputs.loadAt === 'right', 'right-arrow': apOverlayInputs.loadAt === 'left'}">
                {{'MESSAGES.COULD_NOT_RETRIVE_CONTENT_INFO' | translate}}
            </span>
        </div>
        <div class="error-container" *ngIf="apItemDetailsState === 'no-content'">
            <span class="ap-no-preview"
                [ngClass]="{'left-arrow':apOverlayInputs.loadAt === 'right', 'right-arrow': apOverlayInputs.loadAt === 'left'}">
                {{'MESSAGES.CONTENT_INFO_NOT_AVAILABLE' | translate}}
            </span>
        </div>
        <div class="error-container" *ngIf="apItemDetailsState === 'no-repo'">
            <span class="ap-no-preview"
                [ngClass]="{'left-arrow':apOverlayInputs.loadAt === 'right', 'right-arrow': apOverlayInputs.loadAt === 'left'}">
                {{'MESSAGES.CONTENT_REPO_NO_SUPPORT_OPERATION' | translate}}
            </span>
        </div>
    </div>
</hcl-popover>

<div id="offer-info-sidebar">
    <hcl-side-bar #richTextSidebar [disableClose]="true">
        <div *ngIf="rtSidebarPreviewActive" class="rt-sidebar-container">
            <div class="title">{{'CREATE_OFFER.TITLES.VIEW_RICH_TEXT' | translate}}</div>
            <div #rtSidebar class="disabled-editor" [froalaEditor]="getRichTextOptions()"
                [(froalaModel)]="selectedRichtextAttribute.value">
            </div>

            <hcl-button [config]="cancleBtnConfig" (onclick)="closeSidebar()"></hcl-button>

        </div>
    </hcl-side-bar>
</div>