import Quill from "quill";
import { Subject } from "rxjs";
let Inline = Quill.import("blots/inline");

export class EdtitorTextNode extends Inline {
  static blotName = "editortextblock";
  static className = "droppable";
  static tagName = "span";
  static onTextNodeClick = new Subject();
  static TextNodeClickClicked$ = EdtitorTextNode.onTextNodeClick.asObservable();
  public domNode: HTMLElement;
  // the data object that we sent to this blot
  public contentData: any;

  constructor(name, value) {
    super(name, value);
    this.contentData = value;
    this.domNode.addEventListener("click", (e) => {
      EdtitorTextNode.onTextNodeClick.next({
        value: this.contentData,
      });
    });
  }

  clone() {
    const obj: EdtitorTextNode = super.clone();
    obj.updateContentData(this.contentData);
    return obj;
  };

  /**
   * This function will actually create the bolt
   * param {{label: string; length: number}} value
   * returns {any}
   */
  static create(value: { label: string, length: number }) {
    let node = super.create(value);
    this.setNodeConfigurations(node, value);
    return node;
  }

  /**
   * This will return the text length
   * returns {number}
   */
  getTextLength(): number {
    if (this.domNode) {
      return this.domNode.innerText.length
    }
    return 0
  }

  /**
   * this will sent the content data that user has sent
   * to this blot
   * returns {any}
   */
  getContentData(): any {
    return this.contentData;
  }
  /**
   * this will update the content data
   * param data
   */
  updateContentData(data: any) {
    this.contentData = data;
  }
  /**
   * We will set some configurations on the node
   * param node
   * param value
   */
  static setNodeConfigurations(node, value: { label: string, length: number, id?: any }) {
    if (value.label) {
      node['innerHTML'] = value.label;
      node.id = value.id;
    }
    node.setAttribute("contenteditable", false);
    if (value.length === 0) {
      value.length = value['messageBody'].length;
    }
  }

  static formats(node) {
    return node.innerHTML;
  }
}
