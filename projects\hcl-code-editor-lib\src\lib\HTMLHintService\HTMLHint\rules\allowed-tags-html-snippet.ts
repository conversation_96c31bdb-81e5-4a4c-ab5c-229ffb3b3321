import { Rule } from '../types';

export default {
  id: 'allowed-tags-html-snippet',
  description: 'html, head, and title tags are not allowed.',
  init(parser, reporter, rulePresent, translations: { [key: string]: string }) {
    const invalidTags = ['html', 'head', 'title', 'script', 'body', 'doctype'];

    parser.addListener('tagstart', (event) => {
      const tagName = event.tagName.toLowerCase();
      const isInValidTag = invalidTags.includes(tagName);

      if (isInValidTag) {
        reporter.error(
          reporter.buildMessage(translations['invalidTag'], tagName),
          event.line,
          event.col,
          this,
          tagName
        );
      }
    });
  },
} as Rule;
