import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ApprovalUserInfoComponent } from './approval-user-info.component';

describe('ApprovalUserInfoComponent', () => {
  let component: ApprovalUserInfoComponent;
  let fixture: ComponentFixture<ApprovalUserInfoComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ApprovalUserInfoComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ApprovalUserInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
