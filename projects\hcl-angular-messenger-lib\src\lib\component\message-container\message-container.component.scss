/*
 * This is the Css for the rectangular contaier
 * inside which the messages are displayed
 */
.message-container {
  height: 100%;
  //padding: 20px 40px;

  .message-top-bar {
    padding: 10px 0px 0px 0px;
    display: flex;
  }

  .message-box {
    box-sizing: border-box;
    min-height: 450px;
    height: calc(100% - 190px);
    // min-height: 450px;
    width: 100%;
    border: 1px solid #bcbbbb;
    background-color: #f5f5f5;
    overflow: auto;
    padding-top: 20px;
  }

  .message-send-box {
    height: 50px;
    margin-top: 10px;
  }
  .action-container {
    height: 40px;
    margin-top: 10px;
    display: flex;
  }
}
