<div class="variations-grid-container" *ngIf="loadTemplate">
    <div class="variations-search">
        <hcl-input (iconClick)="onFilterTextBoxChanged()" (keyup.enter)="onFilterTextBoxChanged()"
            (valueEntered)="valueEntered()" [config]="searchBoxConfig"></hcl-input>
    </div>
    <div
        [ngClass]="{'variations-grid': !selectOffersService.disableVariants, 'variations-grid-2': selectOffersService.disableVariants}">
        <hcl-data-grid-v2 [config]="variationsGridConfig" (cellClicked)="onCellClicked($event)"
            (gridReady)="variationsGridReady($event)">
        </hcl-data-grid-v2>
        <div class="disable-variants-text" *ngIf="selectOffersService.disableVariants && !selectOffersService.directPathAccess">
            {{'OFFER_PICKER.TITLES.SELECTION_AVAILABLE_AT_OFFER_LEVEL' |
            translate}}
        </div>
    </div>
</div>