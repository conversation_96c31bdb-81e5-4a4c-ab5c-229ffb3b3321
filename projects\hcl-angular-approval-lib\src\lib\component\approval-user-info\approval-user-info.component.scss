$title-font-color: #6d7692;
$title-font-family: Montserrat;
$value-font-color: #444444;

.approval-user-info-container {
    padding-top: 35px;
    font-size: 14px;
    .info-title {
        color: $title-font-color;
        font-family: $title-font-family;
    }
    .owner-list, .approver-list {
        overflow: auto;
        min-height: 80px;
        max-height: 160px;
        padding: 0px;
        li {
            list-style: none;
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 10px;
            .owner, .approver {
                min-width: 210px;
                width: auto;
                box-sizing: border-box;
                height: 30px;
                border: 2px solid #d2d6e1;
                border-radius: 5px;
                padding: 3px 25px;
                background-color: #f5f5f5;
                color: $value-font-color;
            }
        }
    }
    .no-data-message {
        min-height: 80px;
        max-height: 160px;
        text-align: center;
        color: $value-font-color;
    }
}
