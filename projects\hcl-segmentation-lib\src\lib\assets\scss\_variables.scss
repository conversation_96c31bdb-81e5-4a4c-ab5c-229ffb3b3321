$theme: tango;

$primary-font-color: none;
$primary-selected-background-color: none;
$link-color: none;
$border-radius-8: none;
$tango-warn-color: none;
$tango-success-color: none;
$steper-background-color: none;
$steper-border-normal-color: none;
$tango-border-color: none;
$defualt-label-color: none;
$tab-hover-background-color: none;
$tab-press-color: none;
$primary-hover-background-color: none;
$grid-text-font-size: none;
$grid-text-font-size-smaller: none;
$grid-primary-icon-color: none;
$grid-primary-icon-hover-color: none;
$primary-hover-darker-background-color: none;
$icon-enabled-color: none;
$icon-selected-color: none;
$notification-background-color: none;
$notification-info-color: none;
$notification-success-color: none;
$notification-warning-color: none;
$notification-error-color: none;
$dark-shadow-color-1_5: none;
$card-background-hover: none;

/****************************************
 *  Day Theme Start
 ***************************************/
$primary-color: #0078d8;
$ascent-color: #f5821e;
$warn-color: #cc0000;
$white: #ffffff;
$black: #000000;
$dark-black: #00000040;

$page-background: #ececec;
$card-background: #f5f5f5;
$disabled: #d2d6e1;
$card-hover: #ececec;
$border-light: #dee2e6;

$icon-active: #7a400e;
$disabled-light: #bdc1cf;
$disabled-dark: #3e4560;
$gray-dark: #959595;
$gray-medium: #bcbbbb;
$gray-light: #e0e0e0;
$orange-lighter: #fde6d2;
$orange-light: #ffd3ad;
$gray-scrollbar: #808080;

$text-normal: #6d7692;
$text-dark: #444444;
$text-light: #b8b7b7;
$text-error: #e12424;

$message-success: #c4d056;
$message-warning: #ffb755;
$message-error: #f24747;

$donut-fill-yellow: #ddbb4d;
$donut-fill-purple: #b66ac1;

$tooltip-bg: rgba(21, 22, 28, 0.8);
$dark-shadow-color-1: rgba(0, 0, 0, 0.1);
$dark-shadow-color-2: rgba(0, 0, 0, 0.2);
$dark-shadow-color-2_5: rgba(0, 0, 0, 0.25);
$dark-shadow-color-3: rgba(0, 0, 0, 0.3);
$dark-shadow-color-4: rgba(0, 0, 0, 0.8);

$day-page-header-gradient-start-color: #f5f5f6;
$day-page-header-gradient-end-color: #bfc2bf;
$day-page-header: #6d7692;

$font-primary: Montserrat;
$font-secondary: Roboto;

$card-background-color: #f5f5f5;
$chip-background-color: #f5f5f5;
$chip-border-color: #d2d6e1;
$chip-hover-color: #ececec;
$form-heading-color: #6d7692;
$icons-default-color: #959595;
$primary-color: #0078d8;
$rounded-chip-default-state-color: #fde6d2;
$secondary-color: #f5821e;
$secondary-text-color: #6d7692;
$chip-border-color-secondary: #cce4f7;

/****************************************
 *  Day Theme End
 ***************************************/

/****************************************
 *  Tango Theme Start
 ***************************************/
@if $theme ==tango {
  $page-background: #ecfeff;

  $primary-color: #038D99;
  $primary-selected-background-color: rgba(3, 141, 153, 0.2);
  $primary-hover-background-color: #E0EEEF;
  $ascent-color: #006075;
  $white: #ffffff;

  $background-color: #ecfeff;
  $card-background: #ffffff;
  $card-background-hover: #f5f5fa;
  $steper-background-color: rgba(236, 254, 255, 0.1);

  $dark-shadow-color-1_5: rgba(0, 0, 0, 0.15);
  $dark-shadow-color-2: rgba(0, 0, 0, 0.20);
  $dark-shadow-color-3: rgba(0, 0, 0, 0.3);

  $font-primary: HCLTechRoobert;
  $font-secondary: HCLTechRoobert;

  $text-normal: #1D1D23;

  $grid-text-font-size: 14px;
  $grid-text-font-size-smaller: 12px;
  $grid-primary-icon-color: #717182;
  $grid-primary-icon-hover-color: #006075;
  $primary-hover-darker-background-color: #E0EEEF;

  $icon-enabled-color: #717182;
  $icon-selected-color: #006075;

  $primary-font-color: #1D1D23;
  $defualt-label-color: #5b5b5b;
  $background-color-secondary: #f5f5f5;

  $link-color: #0f5fdc;

  $border-radius-8: 8px;
  $tango-border-color: #d3d3e5;

  $tango-warn-color: #D85438;
  $tango-success-color: #56D74A;

  $notification-background-color: #cde8eb;
  $notification-info-color: #038d99;
  $notification-success-color: #56d74a;
  $notification-warning-color: #ff9914;
  $notification-error-color: #d85438;

  $message-success: #56D74A;
  $message-error: #D85438;
  $message-warning: #FF9914;

  $steper-background-color: rgba(236, 254, 255, 0.1);
  $steper-border-normal-color: #6d7692;

  $tab-hover-background-color: #bde7eb;
  $tab-press-color: #a6dce0;
}

@mixin custom-scrollbar {
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  }

  &::-webkit-scrollbar-thumb {
    background-color: $gray-scrollbar;
  }
}