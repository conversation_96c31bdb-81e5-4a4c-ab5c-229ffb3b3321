<hcl-data-grid-community [config]=conf (rowSelected)="rowSelected($event)" (columnSorting)="columnSorted($event)"
(rowUnSelected)="rowUnSelected($event)" (gridReady)="gridReady($event)" (rowClicked)="onRowClicked($event)">
    <ng-template hclTemplate hclTemplateName="entrySourceCell" type="cell-renderer" let-cell>
        <span>{{cell.row && cell.row[cell.col.field]}}</span>
      </ng-template>
      <ng-template hclTemplate hclTemplateName="inUseCell" type="cell-renderer" let-cell>
        <span>{{cell.row && cell.row[cell.col.field] ? 'In Use' : 'Idle'}}</span>
      </ng-template>
      <ng-template hclTemplate hclTemplateName="createdDateCell" type="cell-renderer" let-cell>
        <span>{{parseDate(cell.row && cell.row[cell.col.field])}}</span>
      </ng-template>
      <ng-template hclTemplate hclTemplateName="lastModifiedByCell" type="cell-renderer" let-cell>
        <span>{{cell.row && cell.row[cell.col.field]}}</span>
      </ng-template>
      <ng-template hclTemplate hclTemplateName="entrySourceHeader" type="header-renderer" let-cell>
        <a href="javascript:void(0);">Entry</a>
      </ng-template>
      <ng-template hclTemplate hclTemplateName="customPopover" type="popover-renderer" let-cell>
        <a href="javascript:void(0);">Custom Popover</a>
      </ng-template>
</hcl-data-grid-community>