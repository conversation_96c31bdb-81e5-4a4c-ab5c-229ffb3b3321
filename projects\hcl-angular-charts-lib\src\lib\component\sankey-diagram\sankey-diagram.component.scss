.svg-container {
    display: block;
    overflow: auto;
    resize: both;
    border: 2px solid #eee;

    &.has-grey-node {
        .sankey-link {
            stroke: #000000 !important;
        }
    }

    .sankey-link {
        cursor: pointer;
        transition: stroke-opacity 250ms;
        -webkit-transition: stroke-opacity 250ms;

        &:hover {
            stroke-opacity: 0.8 !important;
        }
    }

    .sankey-node {
        transition: opacity 250ms;
        -webkit-transition: opacity 250ms;

        &:hover {
            opacity: 1 !important;
        }
    }

    .no-highlight-node {
        .sankey-node {
            opacity: 0.3 !important;
        }
    }

    .no-highlight-link {
        .sankey-link {
            stroke-opacity: 0.1 !important;
        }
    }
}