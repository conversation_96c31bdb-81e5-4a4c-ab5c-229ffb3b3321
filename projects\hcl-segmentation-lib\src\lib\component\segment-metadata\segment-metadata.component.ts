import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation,
} from "@angular/core";
import {
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import {
  AutoCompleteConf,
  ButtonConf,
  DropDownConfig,
  DropDownElement,
  FolderSelectionConf,
  InputConfig,
  NotificationService,
  SideBarComponent,
  TextareaConfig,
} from "hcl-angular-widgets-lib";
import { SubscriptionLike } from "rxjs";
import { HclSegmentationLibDataService } from "../../hcl-segmentation-lib-data.service";
import { HclSegmentationLibService } from "../../hcl-segmentation-lib.service";
import { Folder, SegmentMetadatConfig } from "../../models/segment";
import { debounceTime, distinctUntilChanged, switchMap } from "rxjs/operators";

@Component({
  selector: "hcl-segment-metadata",
  templateUrl: "./segment-metadata.component.html",
  styleUrls: ["./segment-metadata.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class SegmentMetadataComponent implements OnInit {
  @ViewChild(SideBarComponent) sidebarContainer: SideBarComponent;

  @Output() segmentMetadataState: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() segFolderSelected: EventEmitter<boolean> = new EventEmitter<any>();
  @Input() applicationMode: "create" | "edit" | "createWithQbData";
  @Input() config: SegmentMetadatConfig;
  @Input() folderConfig: FolderSelectionConf;

  segmentNameConfig: InputConfig;
  folderNameConfig: InputConfig;
  segmentDescriptionConfig: TextareaConfig;
  usageCategoryConfig: AutoCompleteConf;
  securityPolicyConfig: DropDownConfig;
  originalSecurityPolicyOptions: DropDownElement[];
  segmentMetadataForm: UntypedFormGroup;
  usageCategoryTags: String[] = [];
  subscriptionList: SubscriptionLike[] = [];
  segmentNameMaxLength: number;
  segmentDescriptionMaxLength: number;
  addTagBtnConfig: ButtonConf;
  browseButtonConfig: ButtonConf;
  securityPolicies = [];
  securityPoliciesForSegment = [];
  showSidebar: boolean = false;
  private loginAttempts = 0;
  private MAX_LOGIN_ATTEMPTS = 2;

  constructor(
    // private utilsService: UtilsService,
    private translate: TranslateService,
    // private segmentService: SegmentService,
    // private foldersService: FoldersService,
    private hclSegmentationLibService: HclSegmentationLibService,
    private HclSegmentationLibDataService: HclSegmentationLibDataService,
    private notificationService: NotificationService,
    private fb: UntypedFormBuilder
  ) { }

  ngOnInit(): void {
    if (this.config.textSizeValidationType.indexOf("unicode") === -1) {
      this.segmentNameMaxLength = 64;
      this.segmentDescriptionMaxLength = 250;
    } else {
      this.segmentNameMaxLength = 64 * 3;
      this.segmentDescriptionMaxLength = 250 * 3;
    }

    for (const [key, value] of Object.entries(this.config.securityPolicies)) {
      this.securityPolicies.push({ label: value, value: +key });
    }

    for (const [key, value] of Object.entries(
      this.config.securityPoliciesForSegment
    )) {
      this.securityPoliciesForSegment.push({ label: value, value: +key });
    }

    this.initializeForm();
    this.setConfiguration();

    this.addTagToUsageCategory();
    // this.initializeDefaultValues();
    this.segmentMetadataState.emit(this.segmentMetadataForm.valid);

    if (this.applicationMode === 'create' || this.applicationMode === 'createWithQbData') {
      this.updateSegmentSecurityPolicy();
    } else {
      if (this.HclSegmentationLibDataService.segmentMetadataForm && this.HclSegmentationLibDataService.segmentMetadataForm.controls.policyId.value) {
        this.segmentMetadataForm.patchValue({
          policyId: this.HclSegmentationLibDataService.segmentMetadataForm.controls.policyId.value
        });
      } else {
        if (this.securityPoliciesForSegment.length === 1) {
          this.securityPolicyConfig.formControl.setValue(
            this.securityPolicyConfig.options[0].value
          );
          this.securityPolicyConfig.disabled = true;
        } else {
          this.securityPolicyConfig.disabled = false;
        }
      }
    }

    this.segmentMetadataForm.valueChanges.subscribe(() => {
      this.segmentMetadataState.emit(this.segmentMetadataForm.valid);
      this.HclSegmentationLibDataService.segmentMetadataForm =
        this.segmentMetadataForm;
    });
  }

  /**
   * Initialize formgroup with default values if any
   */
  // private initializeDefaultValues() {
  //   if (
  //     this.HclSegmentationLibDataService.segmentMetadataForm &&
  //     this.HclSegmentationLibDataService.segmentMetadataForm.valid
  //   ) {
  //     this.segmentMetadataForm.setValue({
  //       displayName:
  //         this.HclSegmentationLibDataService.segmentMetadataForm.controls.displayName
  //           .value,
  //       description:
  //         this.HclSegmentationLibDataService.segmentMetadataForm.controls.description
  //           .value,
  //       policyId:
  //         this.HclSegmentationLibDataService.segmentMetadataForm.controls.policyId.value,
  //       tags: this.HclSegmentationLibDataService.segmentMetadataForm.controls.tags.value,
  //     });
  //     this.usageCategoryTags =
  //       this.HclSegmentationLibDataService.segmentMetadataForm.controls.tags.value;
  //   }
  // }

  updateSegmentSecurityPolicy() {
    const folderId = this.segmentMetadataForm.value.folderId;
    if (this.originalSecurityPolicyOptions.length > 1) {
      this.hclSegmentationLibService
        .getFolderInfo(folderId)
        .subscribe((folder: Folder) => {
          this.loginAttempts = 0;
          if (folder.id !== this.folderConfig.applicationRootFolder) {
            this.securityPolicyConfig.options = this.originalSecurityPolicyOptions.filter(
              (policy) => policy.value === folder.policyId
            );
            this.securityPolicyConfig.formControl.setValue(this.securityPolicyConfig.options[0].value);
            this.securityPolicyConfig.disabled = true;
          } else {
            this.securityPolicyConfig.disabled = false;
          }
        }, this.handleServerError.bind(this, this.updateSegmentSecurityPolicy.bind(this)));
    } else {
      this.securityPolicyConfig.formControl.setValue(
        this.securityPolicyConfig.options[0].value
      );
      this.securityPolicyConfig.disabled = true;
    }
  }

  setConfiguration() {
    this.segmentNameConfig = {
      name: "displayName",
      placeholder: this.config.translations.segmentName,
      formControlName: this.segmentMetadataForm.controls.displayName,
      disabled: false,
      type: "text",
      autofocus: true,
      errorList: [
        {
          errorCondition: "required",
          errorMsg: this.config.translations.segmentNameRequired,
        },
        {
          errorCondition: "maxlength",
          errorMsg: this.config.translations.exceedsMasLimit,
        },
        {
          errorCondition: "pattern",
          errorMsg: this.config.translations.invalidCharacters,
        },
      ],
    };

    const folderPath =
      this.folderConfig.selectedFolderBreadcrumb &&
      this.folderConfig.selectedFolderBreadcrumb.length > 1 &&
      this.folderConfig.selectedFolderBreadcrumb.reduce((accumlator, data) => {
        return accumlator
          ? accumlator + '/' + data.folder.displayName
          : accumlator + data.folder.displayName;
      }, '');

    this.folderNameConfig = {
      name: "displayName",
      placeholder: this.config.translations.selectFolderToSaveSegment,
      formControlName: new UntypedFormControl(
        folderPath || this.folderConfig.translations.rootFolderName,
        Validators.required
      ),
      disabled: true,
      type: "text",
      // autofocus: true,
      errorList: [
        {
          errorCondition: "required",
          errorMsg: this.config.translations.segmentNameRequired,
        },
        // {
        //   errorCondition: "maxlength",
        //   errorMsg: this.config.translations.exceedsMasLimit,
        // },
        // {
        //   errorCondition: "pattern",
        //   errorMsg: this.translate.instant("MESSAGES.INVALID_FOLDER_NAME"),
        // },
      ],
    };

    this.browseButtonConfig = {
      name: "browseFolderButton",
      value: this.config.translations.browseFolder,
      color: "accent",
      buttonType: "stroked",
      type: "button",
      borderRadius: 5,
      styleClass: "hcl-sm-button mat-stroked-button",
    };

    this.segmentDescriptionConfig = {
      name: "description",
      formControlName: this.segmentMetadataForm.controls.description,
      disabled: false,
      // maximumLength: 250,
      // isShowLimit: true,
      autosize: false,
      placeholder: this.config.translations.description,
      errorList: [
        {
          errorCondition: "maxlength",
          errorMsg: this.config.translations.exceedsMasLimit,
        },
      ],
    };

    this.addTagBtnConfig = {
      name: "add_tag",
      value: this.config.translations.add,
      color: "accent",
      buttonType: "stroked",
      type: "button",
      styleClass: "hcl-sm-button",
      borderRadius: 5,
      disabled: true,
    };

    this.usageCategoryConfig = {
      placeholder: this.config.translations.usageCategory,
      value: "",
      autoComplete: "auto",
      canFilter: true,
      name: "tags",
      formControl: new UntypedFormControl(),
      inputType: "text",
      isBlurOrEnter: true,
      isLazyLoad: false,
      suggestions: [],
    };

    this.usageCategoryConfig.formControl.valueChanges.subscribe((data) => {
      this.addTagBtnConfig.disabled = data ? false : true;
    });

    this.securityPolicyConfig = {
      options: this.applicationMode === 'edit'
        ? this.securityPolicies
        : this.securityPoliciesForSegment,
      selectedOption: "",
      placeholder: this.config.translations.securityPolicy,
      name: "securityPolicy",
      formControl: this.segmentMetadataForm.controls.policyId,
      errorList: [
        {
          errorCondition: "required",
          errorMsg: this.config.translations.requiredField,
        },
      ],
      disabled: this.applicationMode === 'edit'
    };
    this.originalSecurityPolicyOptions = [...this.securityPolicyConfig.options];
  }

  /**
   * Initialize create segment metadata form
   */
  private initializeForm() {
    this.segmentMetadataForm = this.fb.group({
      displayName: new UntypedFormControl(null, [
        Validators.required,
        Validators.maxLength(this.segmentNameMaxLength),
        Validators.pattern(/^[^%*<>\"&/\\,$+?|:\t]*$/),
      ]),
      description: new UntypedFormControl(null, [
        Validators.maxLength(this.segmentDescriptionMaxLength),
      ]),
      folderId: new UntypedFormControl(this.getSelectedFolderId()),
      policyId: new UntypedFormControl(null, Validators.required),
      tags: new UntypedFormControl([]),
    });
  }

  getSelectedFolderId() {
    if (this.applicationMode !== 'create' && this.folderConfig.selectedFolderBreadcrumb && this.folderConfig.selectedFolderBreadcrumb.length > 1) {
      return this.folderConfig.selectedFolderBreadcrumb[this.folderConfig.selectedFolderBreadcrumb.length - 1].folder.id
    } else {
      return this.folderConfig.applicationRootFolder;
    }
  }

  /**
   * Method to add options to usage category list on search
   */
  addTagToUsageCategory() {
    this.subscriptionList.push(
      this.usageCategoryConfig.formControl.valueChanges
        .pipe(
          debounceTime(500),
          // filter(value => value),
          distinctUntilChanged(),
          switchMap((value: string) =>
            this.hclSegmentationLibService.getUsageCategoryTags(encodeURIComponent(value))
          )
        )
        .subscribe((list: string[]) => {
          this.loginAttempts = 0;
          // this.usageCategoryConfig.suggestions = [...list];
          this.usageCategoryConfig = {
            ...this.usageCategoryConfig,
            suggestions: [...list],
          };
        }, this.handleServerError.bind(this, this.addTagToUsageCategory.bind(this)))
    );
  }

  /**
   * Method to add tag from usage category list
   * @param data
   */
  addTag(data: any) {
    if (
      !this.usageCategoryTags.includes(data.trim()) &&
      data &&
      data.trim() != ""
    ) {
      if (data.trim().length > this.segmentDescriptionMaxLength) {
        this.notificationService.show({
          message: this.translate.instant(
            "MESSAGES.TAG_LENGTH_SHOULD_BE_LESS_THAN_CHARACTERS",
            { count: this.segmentDescriptionMaxLength }
          ),
          type: "error",
          close: true,
          autoHide: 6000,
        });
      } else {
        this.usageCategoryTags.push(data.trim());
        this.segmentMetadataForm.controls.tags.setValue(this.usageCategoryTags);
        this.usageCategoryConfig.formControl.setValue("");
      }
    }
  }

  /**
   * Method to delete the selected tag
   * @param tag
   */
  deleteTag(tag: string) {
    const index = this.usageCategoryTags.indexOf(tag);
    this.usageCategoryTags.splice(index, 1);
    this.segmentMetadataForm.controls.tags.setValue(this.usageCategoryTags);
  }

  /**
   * Method to add custom tags from user
   * @param event
   */
  onBlurEvnt(event: any) {
    if (
      !this.usageCategoryTags.includes(event.value) &&
      event.value &&
      event.enterPressed &&
      event.value.trim() != ""
    ) {
      this.addTag(event.value);
    }
  }

  openFolderSelection() {
    this.showSidebar = true;
    setTimeout(() => {
      this.sidebarContainer.openSideBar();
    }, 200);
  }

  closeFolderSelection() {
    this.sidebarContainer.close("close");
    this.showSidebar = false;
  }

  folderSelected(event) {
    const selectedFolderId =
      event.breadCrumbData[event.breadCrumbData.length - 1].folder.id;
    this.updateSegmentSecurityPolicy();
    this.folderConfig.selectedFolderBreadcrumb = event.breadCrumbData;
    this.segFolderSelected.emit(event);
    this.hclSegmentationLibService.getfolderPermissions(selectedFolderId).subscribe((permissionList) => {
      this.loginAttempts = 0;
      const createPermission = permissionList.find(
        (el) => el.name === "SEG_NEW"
      );

      if (createPermission && createPermission.permitted) {
        this.segmentMetadataForm.patchValue({
          folderId: selectedFolderId,
        });
        this.segmentMetadataForm.markAsDirty();
        const folderPath = event.breadCrumbData.reduce((accumlator, data) => {
          return accumlator
            ? accumlator + "/" + data.folder.displayName
            : accumlator + data.folder.displayName;
        }, "");
        this.folderNameConfig.formControlName.setValue(folderPath);
        this.closeFolderSelection();
        this.updateSegmentSecurityPolicy();
      } else {
        this.notificationService.show({
          message: this.folderConfig.translations.folderCreateSegError,
          type: "error",
          close: true,
          autoHide: 6000,
        });
      }
    }, this.handleServerError.bind(this, this.folderSelected.bind(this, event)));
  }

  /**
   * In case error from server this function will be called
   * param error
   */
  private handleServerError(callback, err) {
    if (err.status === 401 || err.status === 403) {
      if (this.loginAttempts < this.MAX_LOGIN_ATTEMPTS) {
        // we have to a login again
        this.reLogin(callback);
      } else if (err.status === 400) {
        // bad request error
        const error: any = err.error;

        for (const x in error) {
          if (error.hasOwnProperty(x)) {
            const field: string =
              x.indexOf("[") > -1 ? x.substring(0, x.lastIndexOf("[")) : x,
              errorMessages: string[] = [];

            error[x].forEach((s: any) => {
              errorMessages.push(s);
            });

            this.notificationService.show({
              message: `${errorMessages.join(", ")}`,
              type: "error",
              close: true,
              autoHide: 5000,
            });
          }
        }
      } else {
        this.notificationService.show({
          message: this.config.translations.unableToFetchData,
          type: "error",
          close: true,
          autoHide: 6000,
        });
        // this.cancelAction();
      }
    } else {
      // todo handle other errors
    }
  }

  /**
   * In case there is a un auth error we can do a  relogin to get the new token
   */
  private reLogin(callbackFunction: any): void {
    this.loginAttempts++;
    // check if we have a relogin method
    if (this.config.reLogin) {
      this.config.reLogin(this.reLoginSuccess.bind(this, callbackFunction));
    } else {
      this.notificationService.show({
        message: this.config.translations.unableToFetchData,
        type: "error",
        close: true,
        autoHide: 6000,
      });
    }
  }

  /**
   * called when the relogin is successful from the caller
   */
  public reLoginSuccess(callback): void {
    if (callback) {
      callback();
    }
  }

  ngOnDestroy(): void {
    this.subscriptionList.forEach((sub: SubscriptionLike) => {
      sub.unsubscribe();
    });
  }
}
