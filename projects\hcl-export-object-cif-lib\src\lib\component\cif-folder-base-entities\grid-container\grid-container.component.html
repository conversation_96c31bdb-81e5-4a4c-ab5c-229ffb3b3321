<div class="cif-fbc-grid-container">
    <div class="headerRow">
        <div class="folder-details">
            <p class="selected-folder">{{config.translations.selectedFolder}}</p> <span
                class="folder-label">{{selectedFolderLabel}}</span>
        </div>
        <div class="title-section">
            <div class="grid-title">
                <h3> {{config.translations.gridTitle}}</h3>
            </div>
            <div class="selected-item" *ngIf="selectedItemLable">
                <p><span class="ellipsis" data-position='left-bottom-start'
                        hclTooltip="{{config.translations.selectedEntityLabel}}">{{config.translations.selectedEntityLabel}}</span>
                    <span class="ellipsis" data-position='left-bottom-start'
                        hclTooltip="{{selectedItemLable}}">{{selectedItemLable}}</span>
                </p>
            </div>
        </div>
        <div class="search-section" *ngIf="!config.removeGlobalSearch">
            <div class="input-search-wrapper">
                <hcl-input (iconClick)="onFilterTextBoxChanged()" (keyup.enter)="onFilterTextBoxChanged()"
                    [config]="searchBoxConfig">
                </hcl-input>
            </div>
        </div>
    </div>
    <div class="grid-section" [ngClass]="{'paginated': config.isPaginated, 'withoutSearch':config.removeGlobalSearch}">
        <hcl-data-grid-v2 *ngIf="readyToRenderGrid" class="w-100" #grid [config]="gridConf"
            (gridReady)="onGridReady($event)" (dataLoadedFromUrl)="gridDataLoaded($event)"
            (rowSelected)="rowSelected($event)" (rowUnSelected)="rowUnSelected()">
        </hcl-data-grid-v2>
    </div>
</div>