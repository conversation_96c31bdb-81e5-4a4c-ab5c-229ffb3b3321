//.hcl-gantt-node {
//  -moz-transition: all 0.3s;
//  -webkit-transition: all 0.3s;
//  -o-transition: all 0.3s;
//  transition: all 0.3s;
//}
.hcl-gantt-node:hover{
  fill: #6B80BE;
}
.hcl-gantt-node {
  fill: #E6EDFF;
  stroke-width: 1px;
  stroke: #6B80BE;
}
.hcl-linear-axis {
  stroke: #BCBBBB;
  shape-rendering: crispEdges;
  stroke-dasharray: 4;
}
.hcl-cat-axis {
  stroke: #E0E0E0;
}
.hcl-gantt-node-circle {
  fill : #0078D8;
}
.hcl-gantt-dependency-path {
  stroke: #BCBBBB;
}
.hcl-gantt-dependency-end-marker {
  fill: #BCBBBB;
}
.hcl-gantt-dummy-dependency-path{
  stroke: #BCBBBB;
  stroke-opacity: 0.01;
}
.hcl-linear-ref-line,
.hcl-cat-ref-line {
  stroke: #f5821e;
  stroke-width : 1px;
  stroke-dasharray : 10;
}
.hcl-linear-ref-label {
  font-size: 10px;
}
.hcl-cat-ref-marker,
.hcl-linear-ref-marker {
  fill: #f5821e;
}
.hcl-gantt-dependency-group.active .hcl-gantt-dependency-end-marker,
.hcl-gantt-dependency-group.active .hcl-gantt-dependency-path {
  stroke: #0078D8;
}
.hcl-gantt-dependency-group.active .hcl-gantt-dependency-end-marker {
  fill:#0078D8;
}
// hover on sibling highlight sibling
.hcl-gantt-dummy-dependency-path:hover ~ .hcl-gantt-dependency-path,
.hcl-gantt-dummy-dependency-path:hover ~ .hcl-gantt-dependency-end-marker,
.hcl-gantt-dependency-end-marker:hover ~ .hcl-gantt-dependency-path,
.hcl-gantt-dependency-path:hover {
  -moz-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
  stroke: #0078D8;
}
.hcl-gantt-dummy-dependency-path:hover ~ .hcl-gantt-dependency-end-marker ,
.hcl-gantt-dependency-path:hover ~ .hcl-gantt-dependency-end-marker,
.hcl-gantt-dependency-end-marker:hover {
  -moz-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
  fill: #0078D8;
}
.floating-linear-axis-header {
  width: 1493px;
  height: 56px;
  position: absolute;
  background:#f8f9fa;
  z-index: 1;
}
